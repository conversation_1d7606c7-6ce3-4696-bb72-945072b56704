# ADX 对账功能系统分析（基于分支 `adx_pkg_compare` 最近提交）

- 作者: 自动生成
- 范围: `service-report`, `service-adx` 模块，涉及对账数据采集、比对、导出与调度
- 依据: 代码扫描与最近提交记录（“对接比对数据”“保存数据”“增加校验”等）

## 一、整体架构与职责划分

- **service-report（报表与外部ADX数据侧）**
  - 拉取多机房 DSP 侧的 ADX 消耗明细并聚合。
  - 拉取外部 ADX 平台侧日消耗（Opera/Flat/WebEye/TopOn/YeahMobi/Mobupps/Xmobit/YueAds/Mobking/小米/SilverMob）。
  - 对外提供查询接口与对账数据导出（含“ADX对账报表_日期”命名）。
- **service-adx（内部营收比对与口径统一侧）**
  - 提供内部开放接口汇总 SSP/DSP 侧按包维度的数据（支持日/小时聚合，按时区换算）。
  - 提供“营收对比”功能：上传第三方Excel，自动异步比对并生成结果Excel（含曝光/消耗偏差与偏差比）。

## 二、数据流与关键流程

```mermaid
flowchart TD
  A["上传对账Excel\nRevenueCompareSaveVO"] --> B["保存 m_revenue_compare 记录\ncompareStatus=PENDING"]
  B --> C{"异步执行 compare(id)"}
  C --> D["读取Excel-> RevenuePkgExcelDTO 列表\n校验字段格式/必填"]
  D --> E["构造查询参数: timeZone, revenueId, days/hours"]
  E --> F["按 revenueType 访问多机房接口\n1:/adx/openapi/v1/revenue/ssp/pkg/list\n2:/adx/openapi/v1/revenue/dsp/pkg/list\n3:/report/v1/api/revenue/pkg/list"]
  F --> G["汇总多机房返回并按 day[-hour]-pkg 合并"]
  G --> H["与Excel逐行对齐 -> 计算 idxImpress/idxCost\n及偏差与偏差比"]
  H --> I["补齐未出现在Excel但我方有数据的行"]
  I --> J["生成结果Excel并回填 resultExcel"]
  J --> K["更新 compareStatus=SUCCESS/FAIL\ncompareReason"]
```

- 入口与异步：
  - `POST /revenue/compare/save` 保存任务后异步触发 `compare(id)`；也支持 `GET /revenue/compare/compare/{id}` 手动触发。
  - 结果存储到 `m_revenue_compare` 表字段 `resultExcel`，供前端下载。
- 内部数据聚合：
  - `service-adx` 暴露内部接口，按时区将 UTC+8 对齐后做小时/天聚合，合并多机房数据。
  - DSP口径金额字段使用 `idx_report_cost/1e6`，若配置有 `revenueRatio` 则做 `(idx_report_cost*(100+ratio))/1e8` 的校正；SSP 口径对应 `idx_media_cost`。
- 外部 ADX 口径（service-report）：
  - 多家平台API返回以天维度聚合，标准化后写入对账表（`adx_cost_day`）。

## 三、主要接口与文件

- 比对功能（service-adx）
  - 控制器：`/revenue/compare`
    - `POST /list`：分页查询比对任务
    - `POST /save`：保存并异步比对
    - `GET /compare/{id}`：手动触发比对
  - 实现：`RevenueCompareServiceImpl.compare(id)`
  - 存储：`m_revenue_compare`（实体 `RevenueCompare`）
  - Excel模板字段：日期、小时、包名、曝光、消耗（并输出我方曝光/消耗及偏差指标）
- 内部开放（service-adx）
  - `POST /openapi/v1/revenue/ssp/pkg/list`
  - `POST /openapi/v1/revenue/dsp/pkg/list`
- 报表与外部对接（service-report）
  - `POST /report/v1/api/adxReport/list`：多机房DSP侧ADX消耗
  - `GET /report/v1/api/cost/list`：小米对账报表数据（token 验证）
  - `POST /report/v1/api/revenue/pkg/list`：OVERSEAS ADX 按包数据（供比对Type=3）

## 四、调度与时序

- `service-report` `AdxReportSchedule`
  - 每小时10分：根据“上个整点对应时区”拉取DSP侧ADX消耗并聚合。
  - 09:30、13:30、16:30：拉取外部ADX日消耗数据并入库，供对账。
- `service-adx` `RevenueSchedule`
  - 每小时10分：按时区同步内部 DSP/SSP 营收数据。
  - 09:33、13:33、16:33：拉取“客户数据”保存（外部营收API）。
  - 每10分钟：同步全量EP信息。

## 五、口径与计算细节（核心要点）

- 时区换算：统一使用 `TimeZoneEnum` 做 UTC+8 与业务时区换算；小时聚合时将 `dim_report_hour` 偏移 `formatHour` 后做 `FROM_UNIXTIME` 提取天/小时。
- 多机房合并：相同 key(day[-hour]-pkg) 的记录，`view` 与 `cost` 进行累加，避免重复计数或漏计。
- 偏差计算：
  - 曝光偏差：`diffImpress = idx.view - excel.impress`（内部 - 外部）
  - 消耗偏差：`diffCost = idx.cost - excel.cost`（内部 - 外部）
  - 偏差比：`DoubleUtils.getRate(diff, excel_base)`，以 Excel 原值为基准，外部为0时偏差比为0。
- 金额单位：内部 ClickHouse 金额字段以微单位存储，导出/接口统一除以 1e6（并四舍五入到 3 位小数）。

## 六、最近提交变更要点（分支 `adx_pkg_compare`）

- 新增“营收对比”业务闭环：VO/DTO/Mapper/Entity/Controller/Service 全链路落地。
- Excel 模板与校验增强：日期格式 `@MpDate`、必填项、边界范围等；`ValidatorUtils.validateEntities` 全量校验。
- 异步执行与失败兜底：`CompletableFuture.runAsync`，失败回写 `compareStatus=FAIL` 与 `compareReason`（最多100字符）。
- 接口聚合映射：`revenueType` → 1:SSP, 2:DSP, 3:OVERSEAS ADX；按机房轮询请求，汇总后比对。
- 报表导出命名规范：`ADX对账报表_yyyy-MM-dd[_yyyy-MM-dd]`。
- 定时器时点微调，保证对账链路“我方/对方”数据在相近时窗内稳定可得。

## 七、风控与改进建议

- Token/密钥治理：外部ADX接口密钥散落代码，建议集中配置与脱敏管理（Nacos/密钥管控）。
- 异常监控：对账拉取失败已短信告警；建议补充对关键阶段（多机房汇总为空、Excel为空、差异超阈值）的可观测性指标。
- 时区边界：跨日/跨小时窗口应重点校验（尤其整点±1小时），建议在结果Excel中增加“时区/窗口”辅助列。
- 精度与四舍五入：统一 BigDecimal 精度策略；避免重复多次缩放导致误差叠加。
- 并发与重试：外部API波动大，建议为外部拉取统一做退避重试与超时隔离；比对任务的并发队列与去重控制也可加强。

## 八、关键代码引用

```1:204:/Users/<USER>/dev/java-project/ai_overseas_api/service-adx/src/main/java/com/overseas/service/adx/service/revenue/impl/RevenueCompareServiceImpl.java
// compare 主流程：读取Excel→汇总多机房→匹配计算→生成结果（节选）
@Override
public void compare(Long id) {
    RevenueCompare revenueCompare = this.revenueCompareMapper.selectById(id);
    if (null == revenueCompare) { return; }
    List<RevenuePkgExcelDTO> list = ExcelUtils.read(revenueCompare.getUploadExcel(), RevenuePkgExcelDTO.class);
    if (list.isEmpty()) { return; }
    ValidatorUtils.validateEntities(list);
    Map<String, Object> params = new HashMap<>() {{
        put("timeZone", revenueCompare.getTimeZone());
        put("revenueId", revenueCompare.getRevenueId());
        put("days", list.stream().map(RevenuePkgExcelDTO::getDay).distinct().collect(Collectors.toList()));
    }};
    Boolean isHour;
    if (StringUtils.isNotBlank(list.get(0).getHour())) {
        isHour = true;
        params.put("hours", list.stream()
            .map(u -> String.format("%s %s", u.getDay(), Integer.parseInt(u.getHour()) < 10 ? "0" + u.getHour() : u.getHour()))
            .distinct().collect(Collectors.toList()));
    } else { isHour = false; }
    List<RevenuePkgListDTO> result = new ArrayList<>();
    for (MachineRoomEnum machineRoomEnum : MachineRoomEnum.values()) {
        String resp = HttpUtils.postToMachine(machineRoomEnum, urlMap.get(revenueCompare.getRevenueType()), params, new HashMap<>() {{}});
        FeignR<List<RevenuePkgListDTO>> response = JSONObject.parseObject(resp, new TypeReference<>() {});
        if (response.getCode().equals(0)) { result.addAll(response.getData()); }
    }
    Map<String, RevenuePkgListDTO> resultMap = result.stream().collect(Collectors.toMap(u ->
        generateKey(isHour, u.getDay(), u.getHour(), u.getPkg()), Function.identity(), (o, n) -> {
        o.setCost(o.getCost().add(n.getCost()));
        o.setView(o.getView() + n.getView());
        return o;
    }));
    list.forEach(datum -> {
        String key = generateKey(isHour, datum.getDay(), datum.getHour(), datum.getPkgName());
        if (resultMap.containsKey(key)) { convert(datum, resultMap.get(key)); resultMap.remove(key); }
    });
    resultMap.forEach((k, v) -> list.add(transform(v, isHour)));
    String path = String.format("/revenue/%s.xlsx", Md5CalculateUtils.getStringMd5(revenueCompare.getId().toString()));
    String resultExcel = UploadUtils.getUploadPath(path);
    ExcelUtils.download(resultExcel, "对比数据", RevenuePkgExcelDTO.class, ExcelUtils.setExcelNullToZero(list, RevenuePkgExcelDTO.class));
    RevenueCompare update = new RevenueCompare();
    update.setCompareStatus(RevenueCompareStatusEnum.SUCCESS.getId());
    update.setResultExcel(path);
    this.revenueCompareMapper.update(update, new LambdaUpdateWrapper<RevenueCompare>().eq(RevenueCompare::getId, revenueCompare.getId()));
}
```

```41:146:/Users/<USER>/dev/java-project/ai_overseas_api/service-adx/src/main/java/com/overseas/service/adx/controller/common/InnerController.java
// 内部聚合：DSP 与 SSP 金额口径（含 ratio）与维度设置（节选）
if (ObjectUtils.isNullOrZero(dsp.getRevenueRatio())) {
    dto.setCost(BigDecimal.valueOf(u.getIdxReportCost()).divide(BigDecimal.valueOf(1000000), 3, RoundingMode.HALF_UP));
} else {
    dto.setCost(BigDecimal.valueOf(u.getIdxReportCost())
        .multiply(BigDecimal.valueOf(100).add(BigDecimal.valueOf(dsp.getRevenueRatio())))
        .divide(BigDecimal.valueOf(100000000), 3, RoundingMode.HALF_UP));
}
```

```118:127:/Users/<USER>/dev/java-project/ai_overseas_api/service-report/src/main/java/com/overseas/service/report/service/impl/AdxReportServiceImpl.java
// 插入到adx每日对账报表中（节选）
if (!reportTotalMap.isEmpty()) {
    this.adxCostDayMapper.saveDspDailyCostData(new ArrayList<>(reportTotalMap.values()));
}
```

## 九、数据库表与 Mapper 速览（对账相关）

- 核心任务与结果
  - `m_revenue_compare`：比对任务与结果表（实体 `RevenueCompare`，Mapper `RevenueCompareMapper`）。
- 我方内部数据（用于与Excel对比）
  - `t_ads_dsp_flow_plan_rta_ep_pkg_hour`：包维度小时聚合（Mapper `PackageHourMapper`）。
  - `t_ads_dsp_flow_plan_ssp_hour`、`t_ads_dsp_flow_plan_rta_ep_pkg_hour` 等：SSP/DSP 视图汇总（多 Mapper）。
- ADX 对账明细（外部侧日数据）
  - `m_dsp_flow_adx_cost_day`：ADX 日对账数据（实体 `AdxCostDay`，Mapper `AdxCostDayMapper`）。
- 关联维度
  - `d_ssp`、`d_dsp`、`d_ssp_ep`、`d_dsp_ep`：SSP/DSP 与 EP 维度（多 Mapper）。

---

## 十、验证要点（Smoke Checklist）

- Excel 不含“小时”列时按“天”对齐；含“小时”时按“yyyy-MM-dd HH”对齐。
- `revenueType`=1/2/3 三种来源均可返回数据并合并机房结果。
- Excel 行与我方数据匹配后生成“我方曝光/消耗、偏差及偏差比”；我方有而Excel无的行会补齐输出。
- 生成结果Excel路径可下载且 DB `resultExcel` 正确回填；失败时 `compareStatus` 与 `compareReason` 正确更新。
- 报表侧 09:30/13:30/16:30 定时能拉到外部数据，小时10分聚合能补齐我方口径。

---

本文档面向研发与运维，覆盖对账流程、口径、时序、接口与近期改动，便于排查与扩展。
