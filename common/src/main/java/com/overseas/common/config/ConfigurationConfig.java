package com.overseas.common.config;

import com.overseas.common.configuration.CurrencyConfiguration;
import com.overseas.common.configuration.SmsConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Configuration
@Slf4j
public class ConfigurationConfig {

    @Bean
    @ConfigurationProperties(prefix = "sms")
    public SmsConfiguration smsConfiguration() {
        return new SmsConfiguration();
    }

    @Bean
    @ConfigurationProperties(prefix = "currency")
    public CurrencyConfiguration currencyConfiguration() {
        return new CurrencyConfiguration();
    }

}
