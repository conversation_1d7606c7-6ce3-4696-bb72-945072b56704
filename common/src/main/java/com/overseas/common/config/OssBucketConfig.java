package com.overseas.common.config;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

/**
 * OSS对象存储Bucket对象配置信息
 */
@Getter
@Setter
public class OssBucketConfig {
    // 环境类型，决定文件上传到bucket下哪个目录
    @JSONField(name = "env-type")
    private String envType;

    // bucket所在地址
    private String endpoint;

    @JSONField(name = "bucket-name")
    private String bucketName;

    @JSONField(name = "access-key-id")
    private String accessKeyId;

    @JSONField(name = "access-key-secret")
    private String accessKeySecret;
}
