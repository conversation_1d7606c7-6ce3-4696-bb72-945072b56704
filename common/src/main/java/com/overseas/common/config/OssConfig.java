package com.overseas.common.config;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 多个bucket的配置信息
 */
@Component
@ConfigurationProperties(prefix = "aliyun.oss-config")
@Getter
@Setter
public class OssConfig {

    @JSONField(name = "bucket-material")
    private OssBucketConfig bucketMaterial;

    @JSONField(name = "bucket-label")
    private OssBucketConfig bucketLabel;
}
