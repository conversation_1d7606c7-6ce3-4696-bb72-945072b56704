package com.overseas.common.configuration;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 **/
@Slf4j
@Data
@Configuration
public class MachineConfiguration {

    /**
     * 节点  xjp  us de
     */
    @Value("${machine.room:none}")
    private String machineRoom;
}
