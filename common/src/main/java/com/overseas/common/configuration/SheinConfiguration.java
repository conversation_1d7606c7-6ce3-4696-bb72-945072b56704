package com.overseas.common.configuration;

import com.overseas.common.enums.market.PutEnum;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 **/
@Data
@Configuration
public class SheinConfiguration {

    @Value("${shein.roleId:13}")
    private Integer roleId;

    @Value("${shein.projectId:30}")
    private Long projectId;

    @Value("#{'${shien.user_id:20020,295,20026,20029}'.split(',')}")
    private List<Long> userIds;

    @Value("#{'${shien.master_id:20028,299,301}'.split(',')}")
    private List<Long> masterIds;


    /**
     * 判定是否role
     *
     * @param roleId roleId
     * @return 返回是否role
     */
    public boolean isRole(Integer roleId) {
        return this.roleId.equals(roleId);
    }

    /**
     * 设置 put VO
     *
     * @param isPut  isPut
     * @param roleId 角色
     * @return 角色
     */
    public Integer isPut(Integer isPut, Integer roleId) {
        if (this.isRole(roleId)) {
            return PutEnum.NOT_PUT.getId();
        } else {
            if (null == isPut || -1 == isPut) {
                return PutEnum.IS_PUT.getId();
            }
        }
        return isPut;
    }

    /**
     * 判断是否shein 管家用户
     *
     * @param userId 用户
     * @return 返回睡觉
     */
    public boolean isUser(Long userId) {
        return this.userIds.contains(userId);
    }


    /**
     * 判定是否shein 账户
     *
     * @param masterId 账户ID
     * @return 返回数据
     */
    public boolean isMaster(Integer masterId) {
        return masterIds.contains(masterId.longValue());
    }

}
