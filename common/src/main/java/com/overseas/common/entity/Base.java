package com.overseas.common.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class Base {

    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Integer createUid;

    @TableField(insertStrategy = FieldStrategy.NEVER)
    private Integer updateUid;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Date createTime;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @TableField(updateStrategy = FieldStrategy.NEVER, insertStrategy = FieldStrategy.NEVER)
    private Date updateTime;
}
