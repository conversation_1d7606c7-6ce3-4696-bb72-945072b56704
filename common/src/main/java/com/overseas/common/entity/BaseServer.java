package com.overseas.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@TableName("d_server")
@EqualsAndHashCode(callSuper = true)
public class BaseServer extends Base {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String domain;

    private String serverName;

    private String serverKey;

    private Integer isDel;
}
