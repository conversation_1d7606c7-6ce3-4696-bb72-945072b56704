package com.overseas.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName(value = "u_user")
public class User implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Integer id;

    private String userName;

    private String password;

    private String salt;

    private Integer userType;

    private String realName;

    private String companyName;

    private String address;

    private String phone;

    private String email;

    private Integer roleId;

    private Integer parentId;

    private Integer userStatus;

    /**
     * 用户是否注销
     */
    private Integer isDestroy;

    private String description;

    private Integer createUid;

    private Integer updateUid;

    private Date createTime;

    private Date updateTime;
}
