package com.overseas.common.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@RequiredArgsConstructor
public enum AliCallTtsCodeEnum {

    SHEIN("TTS_307490024", //""TTS_305415103",
            List.of(
                    "15256955546",  // 于遥
//                    "18756052799",
//                    "18956517846",  // 高旅
                    "18721019362",  // 张顺
                    "15209820713"  // 何廷
//                    "18656633534"   // 尧峥
            ),
            "02028307988"
//            List.of("18756052799")
    );

    private final String ttsCode;

    private final List<String> callPhones;

    private final String callShowPhoneNumber;
}
