package com.overseas.common.enums;

import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.SelectDTO2;

import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;
import java.util.Objects;

public interface ICommonEnum {


    Integer getId();

    String getName();

    /**
     * 通过ID找名字
     *
     * @param id
     * @param clazz
     * @param <E>
     * @return
     */
    static <E extends Enum<E> & ICommonEnum> E get(Integer id, Class<E> clazz) {
        Objects.requireNonNull(id);
        EnumSet<E> all = EnumSet.allOf(clazz);
        return all.stream().filter(e -> e.getId().equals(id)).findFirst().orElse(null);
    }

    /**
     * 通过名称找ID
     *
     * @param name
     * @param clazz
     * @param <E>
     * @return
     */
    static <E extends Enum<E> & ICommonEnum> E get(String name, Class<E> clazz) {
        Objects.requireNonNull(name);
        EnumSet<E> all = EnumSet.allOf(clazz);
        return all.stream().filter(e -> e.getName().equals(name)).findFirst().orElse(null);
    }


    /**
     * 通过id 找到名称 String
     *
     * @param id
     * @param clazz
     * @param <E>
     * @return
     */
    static <E extends Enum<E> & ICommonEnum> String getNameById(Integer id, Class<E> clazz) {
        E e = get(id, clazz);
        return null == e ? "" : e.getName();
    }

    /**
     * 通过名称找到 ID Integer
     *
     * @param name
     * @param clazz
     * @param <E>
     * @return
     */
    static <E extends Enum<E> & ICommonEnum> Integer getIdByName(String name, Class<E> clazz) {
        E e = get(name, clazz);
        return null == e ? 0 : e.getId();
    }

    /**
     * Enum集合
     *
     * @param clazz
     * @param <E>
     * @return
     */
    static <E extends Enum<E> & ICommonEnum> List<SelectDTO> list(Class<E> clazz) {
        List<SelectDTO> selectDtos = new ArrayList<>();
        EnumSet<E> all = EnumSet.allOf(clazz);
        all.forEach(e -> selectDtos.add(new SelectDTO(Long.valueOf(e.getId()), e.getName())));
        return selectDtos;
    }


    /**
     * Enum集合
     *
     * @param clazz
     * @param <E>
     * @return
     */
    static <E extends Enum<E> & ICommonEnum> List<SelectDTO2> list2(Class<E> clazz) {
        List<SelectDTO2> selectDtos = new ArrayList<>();
        EnumSet<E> all = EnumSet.allOf(clazz);
        all.forEach(e -> selectDtos.add(new SelectDTO2(Long.valueOf(e.getId()), e.getName())));
        return selectDtos;
    }

}
