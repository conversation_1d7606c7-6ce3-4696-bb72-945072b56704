package com.overseas.common.enums;

import com.overseas.common.exception.CustomException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */

@AllArgsConstructor
@Getter
public enum MachineRoomEnum {

    //
    SG(1, "xjp", "https://openapi.growone.sg", "Access-Token-XJP", "sg", "东南亚"),
    US(2, "us", "https://usopenapi.growone.sg", "Access-Token-US", "us", "美洲"),
    DE(3, "de", "https://deopenapi.growone.sg", "Access-Token-DE", "de", "欧洲");

    private final Integer nodeId;

    private final String machineRoom;

    private final String apiUrl;

    private final String accessTokenName;

    private final String region;

    private final String machineName;

    public static MachineRoomEnum get(String machineRoom) {
        for (MachineRoomEnum machineRoomEnum : values()) {
            if (machineRoomEnum.getMachineRoom().equals(machineRoom)) {
                return machineRoomEnum;
            }
        }
        throw new CustomException("枚举值超出范围");
    }

    /**
     * 根据国家获取机房枚举国家
     *
     * @param region region
     * @return 返回枚举
     */
    public static MachineRoomEnum getByRegion(String region) {
        for (MachineRoomEnum machineRoomEnum : values()) {
            if (machineRoomEnum.getRegion().equals(region)) {
                return machineRoomEnum;
            }
        }
        return SG;
    }

    /**
     * 获取所有list
     *
     * @return 枚举list
     */
    public static List<MachineRoomEnum> listMachineRoomEnum() {
        return new ArrayList<>(Arrays.asList(values()));
    }

}
