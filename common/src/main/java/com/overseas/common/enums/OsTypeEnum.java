package com.overseas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum OsTypeEnum implements ICommonEnum {

    IOS(1, "IOS"),
    ANDROID(2, "Android");

    private final Integer id;
    private final String name;

    public static String getName(Integer id) {
        if (id.equals(0)) {
            return "全部";
        } else {
            return ICommonEnum.getNameById(id, OsTypeEnum.class);
        }
    }
}
