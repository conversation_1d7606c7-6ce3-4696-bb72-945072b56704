package com.overseas.common.enums;

/**
 * 访问返回状态码枚举类
 */
public enum ResultStatusEnum {
    SUCCESS(200, "请求成功"),
    INVALID_REQUEST(400, "请求失败"),
    UNAUTHORIZED(401, "无此权限"),
    FORBIDDEN(403, "访问被禁止"),
    NOT_FOUND(404, "未找到"),
    NO_LOGIN(4011, "未登录，请您重新登录"),
    EXPIRE_LOGIN(4012, "登录失效，请您重新登录"),
    INTERNAL_SERVER_ERROR(500, "服务内部异常"),
    OPERATION_FAILURE(4000, "各类操作失败");

    private int code;
    private String message;

    ResultStatusEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return this.code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return this.message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
