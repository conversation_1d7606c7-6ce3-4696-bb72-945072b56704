package com.overseas.common.enums;

import com.overseas.common.dto.SelectDTO;
import com.overseas.common.utils.DateUtils;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum TimeZoneEnum {

    // 中时区
    UTC_0(0, "UTC-00:00", -8, 8),
    // 东时区
    // 东一区
    UTC_1(1, "UTC+01:00", -7, 7),
    UTC_2(2, "UTC+02:00", -6, 6),
    UTC_3(3, "UTC+03:00", -5, 5),
    UTC_4(4, "UTC+04:00", -4, 4),
    UTC_5(5, "UTC+05:00", -3, 3),
    UTC_6(6, "UTC+06:00", -2, 2),
    UTC_7(7, "UTC+07:00", -1, 1),
    // 东八区
    UTC_8(8, "UTC+08:00", 0, 0),
    UTC_9(9, "UTC+09:00", 1, 23),
    UTC_10(10, "UTC+10:00", 2, 22),
    UTC_11(11, "UTC+11:00", 3, 21),
    // 东西十二时区
    UTC_12(12, "UTC+12:00", 4, 20),
    // 西时区
    // 西十一区
    UTC_13(13, "UTC-11:00", -19, 19),
    UTC_14(14, "UTC-10:00", -18, 18),
    UTC_15(15, "UTC-09:00", -17, 17),
    UTC_16(16, "UTC-08:00", -16, 16),
    UTC_17(17, "UTC-07:00", -15, 15),
    UTC_18(18, "UTC-06:00", -14, 14),
    UTC_19(19, "UTC-05:00", -13, 13),
    UTC_20(20, "UTC-04:00", -12, 12),
    UTC_21(21, "UTC-03:00", -11, 11),
    UTC_22(22, "UTC-02:00", -10, 10),
    // 西一区
    UTC_23(23, "UTC-01:00", -9, 9);

    /**
     * ID
     */
    private final Integer id;

    private final String name;

    /**
     * 格式化时间（该时区与东八区的时差）
     */
    private final Integer formatHour;
    /**
     * 该时区跨天小时对应东八区（UTC+8时区）的当前小时
     */
    private final Integer currentHour;

    public static TimeZoneEnum get(Integer id) {

        for (TimeZoneEnum enumValue : values()) {
            if (enumValue.getId().equals(id)) {
                return enumValue;
            }
        }
        return null;
    }

    public static String getNameById(Integer id) {

        for (TimeZoneEnum enumValue : values()) {
            if (enumValue.getId().equals(id)) {
                return enumValue.getName();
            }
        }
        return null;
    }

    public static List<SelectDTO> list() {
        return Arrays.stream(values())
                .map(enumValue -> new SelectDTO(enumValue.getId().longValue(), enumValue.getName()))
                .collect(Collectors.toList());
    }

    /**
     * 将其他时区的时间，转化为UTC+8的时间的时间
     *
     * @param date     日期
     * @param timeZone 时区
     * @param hour     是否要额外加减小时
     * @return 返回数据
     */
    public static Date getUTC_8Date(Date date, Integer timeZone, Integer hour) {
        return date == null
                ? null
                : DateUtils.formatHour(date,
                -Objects.requireNonNull(TimeZoneEnum.get(timeZone)).getFormatHour() + hour);
    }


    /**
     * 将UTC+8时区时间转换为当前时区的时间
     *
     * @param date     日期
     * @param timeZone 时区
     * @param hour     是否要额外加减小时
     * @return 返回数据
     */
    public static Date getTimeZoneDate(Date date, Integer timeZone, Integer hour) {
        return DateUtils.formatHour(date,
                Objects.requireNonNull(TimeZoneEnum.get(timeZone)).getFormatHour() + hour);
    }

    /**
     * 根据东八区（UTC+8）的当前小时获取当前小时跨天的时区列表
     *
     * @param hour 小时
     * @return 返回数据
     */
    public static TimeZoneEnum getTimeZoneByCurrentHour(Integer hour) {
        for (TimeZoneEnum enumValue : values()) {
            if (enumValue.getCurrentHour().equals(hour)) {
                return enumValue;
            }
        }
        return null;
    }

    public static Long getTimeZoneLongDateByUTC_8(Long date, Integer timeZone, Integer hour) {
        return date + (Objects.requireNonNull(TimeZoneEnum.get(timeZone)).getFormatHour() + hour) * 3600L;
    }
}
