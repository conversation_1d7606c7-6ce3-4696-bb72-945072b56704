package com.overseas.common.enums.market;

import com.overseas.common.enums.ICommonEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum PlanSlotTypeEnum implements ICommonEnum {

    //
    BANNER(100, "Banner"),
    OPEN_SCREEN(200, "Splash"),
    INFORMATION_FLOW(300, "Native"),
    VIDEO(400, "Video"),
    ICON(500, "Icon"),
    PUSH(600, "Push"),
    TABLE_SCREEN(700, "Interstitial");

    private final Integer id;

    private final String name;
}
