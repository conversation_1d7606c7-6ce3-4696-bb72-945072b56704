package com.overseas.common.enums.market.asset;

import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.UploadUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

@AllArgsConstructor
@Getter
public enum AssetTypeEnum implements ICommonEnum {

    VIDEO(3, "视频", 4L, 1024 * 1024 * 200L,
            List.of("mp4", "mov", "flv", "avi", "mpeg4", "3gp", "m4v", "webm", "ts"), "video"),

    IMG(2, "图片", 0L, 1024 * 1024 * 3 / 2L,
            List.of("jpg", "jpeg", "png", "gif"), "img"),

    AUDIO(4, "音频", 100L, 1024 * 1024 * 200L,
            List.of("mp3", "ogg", "wav", "acc", "wma"), "audio"),

    TEXT(1, "文本", 0L, 0L, List.of(), "text");

    //组合
//    COMPOSE(101, "组合", 4L, 1024 * 1024 * 200L, List.of());

    private final Integer id;

    private final String name;

    private final Long duration;

    private final Long maxSize;

    private final List<String> format;

    private final String type;

    public static Integer getType(String fileName) {
        String extension = UploadUtils.getExtension(fileName).toLowerCase();
        for (AssetTypeEnum materialTypeEnum : values()) {
            if (materialTypeEnum.getFormat().contains(extension)) {
                return materialTypeEnum.getId();
            }
        }
        throw new CustomException("素材类型不合法，请确认后再试");
    }

    public static List<Integer> listMediaTypeId() {
        return List.of(IMG.id, VIDEO.id, AUDIO.id);
    }

}
