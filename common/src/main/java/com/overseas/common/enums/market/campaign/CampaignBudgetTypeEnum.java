package com.overseas.common.enums.market.campaign;

import com.overseas.common.enums.ICommonEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum CampaignBudgetTypeEnum implements ICommonEnum {

    BY_COST(1, "按花费（元）"),
    BY_VIEW(2, "按曝光（千次）"),
    BY_CLICK(3, "按点击（次）");

    private final Integer id;

    private final String name;

}