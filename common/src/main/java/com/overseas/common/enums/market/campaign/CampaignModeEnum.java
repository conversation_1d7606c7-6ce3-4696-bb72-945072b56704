package com.overseas.common.enums.market.campaign;

import com.overseas.common.enums.ICommonEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 **/
@AllArgsConstructor
@Getter
public enum CampaignModeEnum implements ICommonEnum {

    //
    NORMAL(1, "投放活动"),
    TEMPLATE(2, "IEP模板活动"),
    EXPERIMENT(3, "IEP实验活动");

    private final Integer id;

    private final String name;

    public static List<Integer> normal() {
        return new ArrayList<>() {{
            add(NORMAL.id);
            add(EXPERIMENT.id);
        }};
    }
}
