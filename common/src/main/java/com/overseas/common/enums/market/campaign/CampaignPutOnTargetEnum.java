package com.overseas.common.enums.market.campaign;

import com.overseas.common.enums.ICommonEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum CampaignPutOnTargetEnum implements ICommonEnum {

    LANDING_PROMOTION(1, "落地页链接"),
    APP_DOWNLOAD(2, "应用下载"),
    DEEPLINK_PROMOTION(3, "deeplink链接");

    private final Integer id;

    private final String name;
}
