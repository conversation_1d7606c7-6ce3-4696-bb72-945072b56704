package com.overseas.common.enums.market.cps;

import com.overseas.common.enums.ICommonEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 **/
@AllArgsConstructor
@Getter
public enum CpsProjectEnum implements ICommonEnum {

    //

    AE(25, "Ae-Cps"),
    AMAZON(26, "Amazon-Cps"),
    LAZADA(18, "Lazada-Cps");

    private final Integer id;

    private final String name;
}
