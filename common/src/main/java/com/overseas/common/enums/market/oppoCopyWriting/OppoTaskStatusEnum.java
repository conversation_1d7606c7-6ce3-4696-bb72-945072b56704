package com.overseas.common.enums.market.oppoCopyWriting;

import com.overseas.common.enums.ICommonEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum OppoTaskStatusEnum implements ICommonEnum {

    WAITING(1, "待处理"),
    RUNNING(2, "处理中"),
    ERROR(3, "处理异常"),
    SUCCESS(4, "处理成功");

    private final Integer id;

    private final String name;
}
