package com.overseas.common.enums.market.plan;

import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.TimeZoneEnum;
import com.overseas.common.utils.DateUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum PlanStatusEnum implements ICommonEnum {

    //
    WAIT(1, "待投放"),
    MARKETING(2, "投放中"),
    STOP(3, "暂停"),
    FINISH(4, "投放结束"),
    WAIT_AUDIT(5, "待审核"),
    AUDIT_FAIL(6, "审核拒绝");

    private final Integer id;
    private final String name;

    public static Integer resettingStatus(Integer timeZone, Integer sourceStatus, Integer putCycle, String startDate, String endDate) {
        // 对于待投放、投放中、投放结束的计划，在编辑时重新根据新设置的时间段重置投放状态
        Date nowDate = DateUtils.formatDate(DateUtils.formatHour(new Date(), Objects.requireNonNull(TimeZoneEnum.get(timeZone)).getFormatHour()), "yyyy-MM-dd");
        if (WAIT.getId().equals(sourceStatus) || MARKETING.getId().equals(sourceStatus)
                || FINISH.getId().equals(sourceStatus)) {
            if (PutCycleEnum.ALL_TIME.getId().equals(putCycle)) {
                // 全周期的直接置为投放中
                return MARKETING.getId();
            } else {
                if (nowDate.compareTo(Objects.requireNonNull(DateUtils.string2Date(endDate))) > 0) {
                    return FINISH.getId();
                } else if (nowDate.compareTo(Objects.requireNonNull(DateUtils.string2Date(startDate))) >= 0) {
                    return MARKETING.getId();
                } else {
                    return WAIT.getId();
                }
            }
        }
        return sourceStatus;
    }
}
