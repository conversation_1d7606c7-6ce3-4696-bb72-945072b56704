package com.overseas.common.enums.market.plan;

import com.overseas.common.enums.ICommonEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-21 15:31
 */

@Getter
@AllArgsConstructor
public enum PutCycleEnum implements ICommonEnum {
    //
    ALL_TIME(1, "全周期"),
    SECTION_TIME(2, "指定周期");

    private final Integer id;

    private final String name;
}
