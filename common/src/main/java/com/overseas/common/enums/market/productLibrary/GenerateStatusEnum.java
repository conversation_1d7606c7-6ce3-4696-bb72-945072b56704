package com.overseas.common.enums.market.productLibrary;

import com.overseas.common.enums.ICommonEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum GenerateStatusEnum implements ICommonEnum {

    WAITING(0, "待处理"),
    QUERY_PRODUCT(1, "查询商品中"),
    SUCCESS(2, "生成成功"),
    PRODUCT_NOT_EXIST(3, "无商品"),
    FAIL(4, "生成失败"),
    ERROR_SIGN(5, "商品标识不合法");

    private final Integer id;

    private final String name;
}
