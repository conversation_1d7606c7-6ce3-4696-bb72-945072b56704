package com.overseas.common.enums.market.reportTask;

import com.overseas.common.enums.ICommonEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum ReportTaskStatusEnum implements ICommonEnum {

    TO_BE_CREATED(1, "待创建"),
    CREATING(2, "创建中"),
    SUCCESS(3, "成功"),
    FAIL(4, "失败"),
    EXECUTING_SQL_SCRIPT(5, "更新SQL");

    private final Integer id;

    private final String name;
}
