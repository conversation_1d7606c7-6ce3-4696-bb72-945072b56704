package com.overseas.common.enums.market.reportTask;

import com.overseas.common.enums.ICommonEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum ReportTaskTypeEnum implements ICommonEnum {

    PACKAGE(11, "包名", "package"),
    TIME_COMPARE(13, "时间对比", "timeCompare"),
    FLOW(15, "流量", "flow"),
    FLOW_SEARCH(21, "流量查询", "flowSearch");

    private final Integer id;

    private final String name;

    private final String filePath;
}
