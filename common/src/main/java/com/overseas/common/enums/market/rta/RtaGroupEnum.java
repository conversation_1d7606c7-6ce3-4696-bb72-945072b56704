package com.overseas.common.enums.market.rta;

import com.overseas.common.enums.ICommonEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum RtaGroupEnum implements ICommonEnum {

    LAZADA_RTA(100, "Lazada_rta"),
    AE(101, "AE"),
    LAZADA_RTB(102, "Lazada_rtb"),
    LAZADA_ORTB(103, "Lazada_ortb"),
    MIRAVIA(104, "Miravia"),
    MIRAVIA_RT(105, "Miravia_RT"),
    MIRAVIA_CPS(107, "Miravia_CPS");

    private final Integer id;

    private final String name;
}