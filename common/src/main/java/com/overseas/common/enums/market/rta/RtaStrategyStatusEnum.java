package com.overseas.common.enums.market.rta;

import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.exception.CustomException;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum RtaStrategyStatusEnum implements ICommonEnum {

    OPEN(1, "开启", "run", "true"),
    STOP(2, "暂停", "pause", "false");

    private final Integer id;

    private final String name;

    private final String lzdStatus;

    private final String aeStatus;


    public static RtaStrategyStatusEnum getByStatusStr(String statusStr, String type) {
        statusStr = statusStr.toLowerCase();
        for (RtaStrategyStatusEnum enumValue : values()) {
            if (type.equals("ae") && enumValue.getAeStatus().equals(statusStr) || type.equals("lazada") && enumValue.getLzdStatus().equals(statusStr)) {
                return enumValue;
            }
        }
        throw new CustomException("无此状态");
    }
}