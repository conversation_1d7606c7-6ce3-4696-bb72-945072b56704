package com.overseas.common.enums.market.textCombine;

import com.overseas.common.enums.ICommonEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 **/
@AllArgsConstructor
@Getter
public enum TextCombineFlagStatusEnum implements ICommonEnum {

    //
    UN_FLAG(-1, "未标记"),
    RECOMMEND(1, "推荐"),
    NOT_RECOMMEND(2, "不推荐");

    private final Integer id;

    private final String name;
}
