package com.overseas.common.enums.monitor.put;

import com.overseas.common.enums.ICommonEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 **/
@AllArgsConstructor
@Getter
public enum MonitorDimensionEnum implements ICommonEnum {

    PROJECT(1, "项目"),
    MASTER(2, "账户"),
    CAMPAIGN(3, "活动"),
    PLAN(4, "计划");

    private final Integer id;

    private final String name;
}
