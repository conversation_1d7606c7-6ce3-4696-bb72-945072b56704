package com.overseas.common.enums.report;

import com.overseas.common.enums.ICommonEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 **/
@AllArgsConstructor
@Getter
public enum AssetReportTypeEnum implements ICommonEnum {

    //
    DAY(1, "reportDate", "dim_day", "天"),
    HOUR(2, "reportHour", "dim_hour", "小时"),
    ASSET(3, "assetId", "dim_asset_id", "素材ID");


    private final Integer id;

    private final String name;

    private final String field;

    private final String fieldName;
}
