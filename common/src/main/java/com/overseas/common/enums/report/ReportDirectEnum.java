package com.overseas.common.enums.report;

import com.overseas.common.enums.ICommonEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ReportDirectEnum implements ICommonEnum {

    ADX(1031, "ADX", ReportTypeEnum.ADX.getId(), 2),
    MEDIA(1025, "媒体", ReportTypeEnum.MEDIA.getId(), 2),
    SLOT(1026, "广告位", ReportTypeEnum.SLOT.getId(), 2),
    PACKAGE(1045, "包名", ReportTypeEnum.PACKAGE_NAME.getId(), 2),
    PACKAGE_PRICE(1051, "包名出价", ReportTypeEnum.PACKAGE_NAME.getId(), 1);

    private final Integer id;

    private final String name;

    private final Integer reportType;

    private final Integer include;
}
