package com.overseas.common.enums.report;

import com.overseas.common.exception.CustomException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum ReportFieldEnum {

    // 基础指标
    REQUEST("request", "请求量", "SUM(idx_request) AS request", ReportFieldTypeEnum.BASIC),
    BID("bid", "竞价量", "SUM(idx_bid) AS bid", ReportFieldTypeEnum.BASIC),
    INNER_BID("innerBid", "内部竞价量", "SUM(idx_inner_bid) AS inner_bid", ReportFieldTypeEnum.BASIC),
    WIN("win", "竞得量", "SUM(idx_win) AS win", ReportFieldTypeEnum.BASIC),
    WIN_RATE("winRate", "竞得率", "ROUND((SUM(idx_win) / SUM(idx_bid)) * 100, 2) AS win_rate", ReportFieldTypeEnum.BASIC),
    RETURN_RATIO("returnRatio", "返还比", "ROUND(((SUM(idx_request) - SUM(idx_bid)) / SUM(idx_request)) * 100, 2) AS return_ratio", ReportFieldTypeEnum.BASIC),
    VIEW("view", "曝光量", "SUM(idx_view) AS `view`", ReportFieldTypeEnum.BASIC),
    VIEW_RATE("viewRate", "曝光率", "ROUND((SUM(idx_view) / SUM(idx_win)) * 100, 2) AS view_rate", ReportFieldTypeEnum.BASIC),
    USER_VIEW("userView", "曝光用户", "SUM(idx_user_view) AS user_view", ReportFieldTypeEnum.BASIC),
    CLICK("click", "点击量", "SUM(idx_click) AS click", ReportFieldTypeEnum.BASIC),
    CLICK_RATE("clickRate", "点击率", "ROUND((SUM(idx_click) / SUM(idx_view)) * 100, 2) AS click_rate", ReportFieldTypeEnum.BASIC),
    USER_CLICK("userClick", "点击用户", "SUM(idx_user_click) AS user_click", ReportFieldTypeEnum.BASIC),
    MASTER_COST("masterCost", "花费", "ROUND((SUM(idx_report_cost) / 1000000), 3) AS master_cost", ReportFieldTypeEnum.BASIC),
    CPM("cpm", "CPM", "ROUND((SUM(idx_report_cost) / 1000000) / (SUM(idx_view) / 1000), 3) AS cpm", ReportFieldTypeEnum.BASIC),
    CPC("cpc", "CPC", "ROUND((SUM(idx_report_cost) / 1000000) / SUM(idx_click), 3) AS cpc", ReportFieldTypeEnum.BASIC),

    // 唤端、首唤指标
    REACH("reach", "到达", "SUM(idx_reach) AS reach", ReportFieldTypeEnum.APP),
    LAUNCH("action15", "唤端", "SUM(idx_action15) AS action15", ReportFieldTypeEnum.APP),
    LAUNCH_COST("actionD1", "唤端成本", "ROUND((SUM(idx_report_cost) / 1000000) / SUM(idx_action15), 3) AS actionD1", ReportFieldTypeEnum.APP),
    LAUNCH_RATE("actionD2", "唤端率", "ROUND(SUM(idx_action15)/SUM(idx_click)*100,2) AS actionD2", ReportFieldTypeEnum.APP),
    FIRST_CALL("action18", "首唤", "SUM(idx_action18) AS action18", ReportFieldTypeEnum.APP),
    FIRST_CALL_COST("actionD3", "首唤成本", "ROUND((SUM(idx_report_cost) / 1000000) / SUM(idx_action18), 3) AS actionD3", ReportFieldTypeEnum.APP),
    FIRST_CALL_RATE("actionD4", "首唤率", "ROUND(SUM(idx_action18)/SUM(idx_click)*100,2) AS actionD4", ReportFieldTypeEnum.APP),

    // 视频相关
    ACTION("action", "激活", "SUM(idx_action) AS action", ReportFieldTypeEnum.APP),
    ACTION1("action1", "创建视图", "SUM(idx_action1) AS action1", ReportFieldTypeEnum.APP),
    ACTION2("action2", "开始播放", "SUM(idx_action2) AS action2", ReportFieldTypeEnum.APP),
    ACTION3("action3", "四分之一播放", "SUM(idx_action3) AS action3", ReportFieldTypeEnum.APP),
    ACTION4("action4", "二分之一播放", "SUM(idx_action4) AS action4", ReportFieldTypeEnum.APP),
    ACTION5("action5", "四分之三播放", "SUM(idx_action5) AS action5", ReportFieldTypeEnum.APP),
    ACTION6("action6", "播放完成", "SUM(idx_action6) AS action6", ReportFieldTypeEnum.APP),
    ACTION7("action7", "静音", "SUM(idx_action7) AS action7", ReportFieldTypeEnum.APP),
    ACTION8("action8", "取消静音", "SUM(idx_action8) AS action8", ReportFieldTypeEnum.APP),
    ACTION9("action9", "暂停", "SUM(idx_action9) AS action9", ReportFieldTypeEnum.APP),
    ACTION10("action10", "继续播放", "SUM(idx_action10) AS action10", ReportFieldTypeEnum.APP),
    ACTION11("action11", "创建endcard视图", "SUM(idx_action11) AS action11", ReportFieldTypeEnum.APP);

    private final String field;

    private final String fieldName;

    private final String rule;

    private final ReportFieldTypeEnum type;

    /**
     * 根据字段获取字段类型枚举
     *
     * @param field 字段
     * @return 枚举
     */
    public static ReportFieldEnum getByField(String field) {
        for (ReportFieldEnum enumValue : values()) {
            if (enumValue.getField().equals(field)) {
                return enumValue;
            }
        }
        throw new CustomException("无此指标");
    }

    /**
     * 获取基础字段计算规则
     *
     * @return 字段计算规则
     */
    public static String getBaseIndicatorRules() {
        List<String> ruleList = new ArrayList<>();
        for (ReportFieldEnum enumValue : values()) {
            if (!(enumValue.equals(REQUEST) || enumValue.equals(RETURN_RATIO))) {
                ruleList.add(enumValue.getRule());
            }
        }
        return StringUtils.join(ruleList.toArray(new String[0]), ",");
    }
}
