package com.overseas.common.enums.report;

import com.overseas.common.exception.CustomException;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ReportFieldTypeEnum {

    BASIC(1, "基础指标"),
    APP(2, "APP指标"),
    H5(3, "H5指标");

    private Integer id;
    private String name;

    public static ReportFieldTypeEnum getById(Integer id) {
        for (ReportFieldTypeEnum statusEnum : values()) {
            if (statusEnum.getId().equals(id)) {
                return statusEnum;
            }
        }
        throw new CustomException("无此报表字段类型");
    }

    public static String getNameById(Integer id) {
        ReportFieldTypeEnum statusEnum = ReportFieldTypeEnum.getById(id);
        return statusEnum.getName();
    }
}
