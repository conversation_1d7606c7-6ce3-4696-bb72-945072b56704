package com.overseas.common.enums.report;

import com.overseas.common.enums.ICommonEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum ReportFlowDataTypeEnum implements ICommonEnum {

    COUNTRY(1, "国家", "dim_area_id"),
    ADX(2, "ADX", "dim_adx_id"),
    EP(3, "EP", "dim_ep_id");

    private final Integer id;

    private final String name;

    private final String groupField;
}
