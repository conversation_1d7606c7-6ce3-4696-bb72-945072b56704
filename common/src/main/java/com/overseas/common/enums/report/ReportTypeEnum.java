package com.overseas.common.enums.report;

import com.overseas.common.exception.CustomException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum ReportTypeEnum {

    //
    AGENT(1, "代理商", "dim_agent_id", ""),
    MASTER(2, "投放账户", "dim_master_id", "masterName,masterId"),
    CAMPAIGN(3, "活动", "dim_campaign_id", "campaignName,campaignId"),
    PLAN(4, "计划", "dim_plan_id", "planName,planId"),
    CREATIVE(5, "创意", "dim_creative_unit_id", "creativeUnitId,creativeUnitName"),
    TIME(6, "时间", "day", "reportDate"),
    ASSET(7, "素材", "dim_asset_id", "assetId"),
    ADX(8, "流量平台", "dim_adx_id", "adxName"),
    MEDIA(9, "媒体", "dim_media_id", "mediaName"),
    SLOT(10, "广告位", "dim_slot_id", "slotName"),
    PACKAGE_NAME(11, "包名", "dim_pkg", "packageName"),
    TIME_HOUR(12, "小时", "dim_report_hour", "reportDate,reportHour"),
    TIME_COMPARE(13, "时间对比", "dim_report_hour", "reportHour"),
    RTA(14, "RTA", "dim_rta_id", "rtaId,rtaName"),
    CUSTOM(15, "自定义", "", ""),
    REGION(16, "地域", "dim_country_id", "countryId,countryName"),
    PLAN_GROUP(17, "计划组", "dim_plan_group_id", "planGroupId,planGroupName"),
    SSP(18, "SSP", "dim_ssp", "ssp"),
    DEAL(20, "DEAL", "dim_deal_id", "deal"),
    FLOW_SSP_PKG(21, "FLOW_SSP_PKG", "", ""),
    SLOT_PKG(22, "SLOT_PKG", "", "");

    private final Integer id;

    private final String name;

    private final String groupField;

    private final String reportFields;

    public static ReportTypeEnum getById(Integer id) {
        for (ReportTypeEnum enumValue : values()) {
            if (enumValue.getId().equals(id)) {
                return enumValue;
            }
        }
        throw new CustomException("报表枚举值超出预设范围");
    }

    public static List<String> translateField(List<String> strList) {
        return strList.stream().map(key -> {
            List<String> pieces = new ArrayList<>(List.of(key.split("dim_")));
            return pieces.size() > 1
                    ? key + " AS " + new ArrayList<>(List.of(key.split("dim_"))).get(1)
                    : key + " AS " + new ArrayList<>(List.of(key.split("dim_"))).get(0);
        }).collect(Collectors.toList());
    }
}
