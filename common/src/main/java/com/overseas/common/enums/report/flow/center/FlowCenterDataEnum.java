package com.overseas.common.enums.report.flow.center;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@AllArgsConstructor
@Getter
public enum FlowCenterDataEnum {

    //
    REQ("req", "请求量"),
    BID("bid", "竞价量"),
    BID_FAILED("bidFailed", "竞价失败量"),
    FAILED_RATIO("failedRatio", "竞价失败率（%）");

    private final String field;

    private final String name;


    /**
     * 获取数据
     *
     * @param fields 字段
     * @return 返回数据
     */
    public static List<FlowCenterDataEnum> getByFields(List<String> fields) {
        return Arrays.stream(FlowCenterDataEnum.values()).filter(u -> fields.contains(u.getField())).collect(Collectors.toList());
    }

}
