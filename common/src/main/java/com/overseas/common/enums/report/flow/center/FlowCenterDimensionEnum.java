package com.overseas.common.enums.report.flow.center;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 **/
@AllArgsConstructor
@Getter
public enum FlowCenterDimensionEnum {

    //
    PROJECT("project", "projectId", "项目"),
    MASTER("master", "masterId", "投放账户"),
    CAMPAIGN("campaign", "campaignId", "活动"),
    PLAN("plan", "planId", "计划"),
    AREA("area", "areaCode", "国家"),
    ADX("adx", "adxId", "ADX"),
    EP("ep", "epId", "EP"),
    PKG("pkg", "pkg", "包名");

    private final String key;

    private final String field;

    private final String name;

    public static FlowCenterDimensionEnum getByKey(String key) {
        for (FlowCenterDimensionEnum val : FlowCenterDimensionEnum.values()) {
            if (val.getKey().equals(key)) {
                return val;
            }
        }
        return null;
    }

}
