package com.overseas.common.enums.report.flow.center;

import com.overseas.common.enums.ICommonEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@AllArgsConstructor
@Getter
public enum FlowCenterLevelEnum implements ICommonEnum {

    //
    REQUEST(1, "request"),
    PROJECT(2, "project"),
    MASTER(3, "master"),
    CAMPAIGN(4, "campaign"),
    PLAN(5, "plan"),
    ADVERT(6, "advert");

    private final Integer id;

    private final String name;

    /**
     * 根据维度获取数据
     *
     * @param dimensions 维度
     * @return 返回数据
     */
    public static FlowCenterLevelEnum findLevel(List<String> dimensions) {
        List<FlowCenterLevelEnum> levels = Arrays.stream(FlowCenterLevelEnum.values())
                .filter(u -> dimensions.contains(u.getName())).collect(Collectors.toList());
        if (levels.isEmpty()) {
            return REQUEST;
        }
        return levels.stream().max(Comparator.comparing(FlowCenterLevelEnum::getId)).get();
    }
}
