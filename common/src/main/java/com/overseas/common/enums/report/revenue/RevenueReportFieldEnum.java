package com.overseas.common.enums.report.revenue;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum RevenueReportFieldEnum {

    // DSP_基础指标
    DSP_BID("dspBid", "DSP竞价量", "SUM(dsp.idx_bid) AS dsp_bid", RevenueReportFieldTypeEnum.DSP_BASIC_FIELD),
    DSP_WIN("dspWin", "DSP竞得量", "SUM(dsp.idx_win) AS dsp_win", RevenueReportFieldTypeEnum.DSP_BASIC_FIELD),
    DSP_WIN_RATE("dspWinRate", "DSP竞得率", "ROUND((SUM(dsp.idx_win) / SUM(dsp.idx_bid)) * 100, 2) AS dsp_win_rate", RevenueReportFieldTypeEnum.DSP_BASIC_FIELD),
    DSP_VIEW("dspView", "DSP曝光量", "SUM(dsp.idx_view) AS `dsp_view`", RevenueReportFieldTypeEnum.DSP_BASIC_FIELD),
    DSP_VIEW_RATE("dspViewRate", "DSP曝光率", "ROUND((SUM(dsp.idx_view) / SUM(dsp.idx_win)) * 100, 2) AS dsp_view_rate", RevenueReportFieldTypeEnum.DSP_BASIC_FIELD),
    DSP_CLICK("dspClick", "DSP点击量", "SUM(dsp.idx_click) AS dsp_click", RevenueReportFieldTypeEnum.DSP_BASIC_FIELD),
    DSP_CLICK_RATE("dspClickRate", "DSP点击率", "ROUND((SUM(dsp.idx_click) / SUM(dsp.idx_view)) * 100, 2) AS dsp_click_rate", RevenueReportFieldTypeEnum.DSP_BASIC_FIELD),
    DSP_MASTER_COST("dspMasterCost", "DSP花费", "ROUND((SUM(dsp.idx_report_cost) / 1000000), 3) AS dsp_master_cost", RevenueReportFieldTypeEnum.DSP_BASIC_FIELD),
    DSP_CPM("dspCpm", "DSP CPM", "ROUND((SUM(dsp.idx_report_cost) / 1000000) / (SUM(dsp.idx_view) / 1000), 3) AS dsp_cpm", RevenueReportFieldTypeEnum.DSP_BASIC_FIELD),
    DSP_CPC("dspCpc", "DSP CPC", "ROUND((SUM(dsp.idx_report_cost) / 1000000) / SUM(dsp.idx_click), 3) AS dsp_cpc", RevenueReportFieldTypeEnum.DSP_BASIC_FIELD),

    // DSP_APP指标
    DSP_LAUNCH("dspLaunch", "DSP唤端", "SUM(dsp.idx_action15) AS dsp_launch", RevenueReportFieldTypeEnum.DSP_APP_FIELD),
    DSP_FIRST_CALL("dspFirstCall", "DSP首唤", "SUM(dsp.idx_action18) AS dsp_first_call", RevenueReportFieldTypeEnum.DSP_APP_FIELD),
    DSP_FIRST_CALL_RATE("dspFirstCallRate", "DSP首唤率", "ROUND(SUM(dsp.idx_action18)/SUM(dsp.idx_click)*100,2) AS dsp_first_call_rate", RevenueReportFieldTypeEnum.DSP_APP_FIELD),
    DSP_SETTLE_ACCOUNT_UV("dspSettleAccountUv", "DSP结算UV", "SUM(dsp.idx_action23) AS dsp_settle_account_uv", RevenueReportFieldTypeEnum.DSP_APP_FIELD),
    DSP_QUALITY_EVENT("dspQualityEvent", "DSP高质量（实时）", "SUM(dsp.idx_action27) AS dsp_quality_event", RevenueReportFieldTypeEnum.DSP_APP_FIELD),
    DSP_SETTLEMENT("dspSettlement", "DSP预估收益", "ROUND(SUM(dsp.idx_settlement) / 1000000,2) AS dsp_settlement", RevenueReportFieldTypeEnum.DSP_APP_FIELD),
    DSP_RECYCLE_RATIO("dspRecycleRatio", "DSP回收比例", "ROUND(SUM(dsp.idx_settlement)/SUM(dsp.idx_report_cost) * 100, 2) AS dsp_recycle_ratio", RevenueReportFieldTypeEnum.DSP_APP_FIELD);

    private final String field;

    private final String fieldName;

    private final String rule;

    private final RevenueReportFieldTypeEnum type;

    public static String getBaseIndicatorRules(List<String> fields) {

        List<String> ruleList = new ArrayList<>();
        for (RevenueReportFieldEnum enumValue : values()) {
            if (fields.isEmpty() || fields.contains(enumValue.getField())) {
                ruleList.add(enumValue.getRule());
            }
        }
        return String.join(",", ruleList);
    }
}
