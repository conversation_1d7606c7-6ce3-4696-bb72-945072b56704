package com.overseas.common.enums.report.revenue;

import com.overseas.common.exception.CustomException;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum RevenueReportFieldTypeEnum {

    DSP_BASIC_FIELD(1, "DSP_基础指标"),
    DSP_APP_FIELD(1, "DSP_APP指标"),
    CUSTOMER_BASIC_FIELD(2, "客户_基础指标"),
    CUSTOMER_CUSTOM_FIELD(3, "客户_自定义指标");

    private final Integer id;

    private final String name;

    public static RevenueReportFieldTypeEnum getById(Integer id) {
        for (RevenueReportFieldTypeEnum statusEnum : values()) {
            if (statusEnum.getId().equals(id)) {
                return statusEnum;
            }
        }
        throw new CustomException("无此报表字段类型");
    }

    public static String getNameById(Integer id) {
        RevenueReportFieldTypeEnum statusEnum = RevenueReportFieldTypeEnum.getById(id);
        return statusEnum.getName();
    }
}
