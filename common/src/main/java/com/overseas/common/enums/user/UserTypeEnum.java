package com.overseas.common.enums.user;

import com.overseas.common.enums.ICommonEnum;
import lombok.Getter;

@Getter
public enum UserTypeEnum implements ICommonEnum {
    AGENT(1, "代理商"),
    MANAGER(2, "管理"),
    MASTER(3, "投放账号");

    private final Integer id;
    private final String name;

    UserTypeEnum(final int id, final String name) {
        this.id = id;
        this.name = name;
    }
}
