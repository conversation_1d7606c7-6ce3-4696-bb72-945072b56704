package com.overseas.common.exception;

import com.overseas.common.enums.ResultStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CustomException extends RuntimeException {

    private int code;

    private String message;

    private String msg;

    private Object data;

    public CustomException(int code, String message) {
        this.code = code;
        this.msg = message;
        this.message = message;
    }

    public CustomException(String message) {
        this.code = ResultStatusEnum.OPERATION_FAILURE.getCode();
        this.msg = message;
        this.message = message;
    }

    public CustomException(String message, Object data) {
        this.code = ResultStatusEnum.OPERATION_FAILURE.getCode();
        this.msg = message;
        this.message = message;
        this.data = data;
    }

    public CustomException(int code, String message, Object data) {
        this.code = code;
        this.msg = message;
        this.message = message;
        this.data = data;
    }

    public CustomException(String message, Throwable e) {
        super(message, e);
        this.code = ResultStatusEnum.OPERATION_FAILURE.getCode();
        this.msg = message;
        this.message = message;
    }

    public CustomException(ResultStatusEnum resultStatusEnum) {
        this.code = resultStatusEnum.getCode();
        this.msg = message;
        this.message = resultStatusEnum.getMessage();
    }
}
