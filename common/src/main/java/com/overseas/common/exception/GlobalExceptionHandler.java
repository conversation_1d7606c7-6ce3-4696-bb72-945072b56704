package com.overseas.common.exception;

import com.alibaba.excel.exception.ExcelAnalysisException;
import com.overseas.common.dto.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.List;
import java.util.Set;

/**
 * 全局异常处理
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler({BindException.class, MethodArgumentNotValidException.class, ConstraintViolationException.class,
            MissingServletRequestParameterException.class, CustomException.class, HttpMessageNotReadableException.class, Exception.class})
    public R handleCustomException(HttpServletRequest request, Exception e) {
        StringBuffer sb = new StringBuffer();
        String msg = "未知错误";
        if (e instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException methodArgumentNotValidException = (MethodArgumentNotValidException) e;
            List<ObjectError> objectErrorList = methodArgumentNotValidException.getBindingResult().getAllErrors();
            msg = objectErrorList.iterator().next().getDefaultMessage();
        }

        if (e instanceof ConstraintViolationException) {
            ConstraintViolationException constraintViolationException = (ConstraintViolationException) e;
            Set<ConstraintViolation<?>> violations = constraintViolationException.getConstraintViolations();
            return R.error(violations.iterator().next().getMessage());
        }

        if (e instanceof MissingServletRequestParameterException) {
            MissingServletRequestParameterException missingServletRequestParameterException = (MissingServletRequestParameterException) e;
            return R.error("参数不合法(缺失参数：" + missingServletRequestParameterException.getParameterName() + ")");
        }

        if (e instanceof BindException) {
            BindException bindException = (BindException) e;
            List<ObjectError> objectErrorList = bindException.getBindingResult().getAllErrors();
            objectErrorList.forEach(oe -> sb.append(oe.getDefaultMessage()).append("，"));
            msg = sb.substring(0, sb.toString().length() - 1);
        }

        if (e instanceof CustomException) {
            CustomException customException = (CustomException) e;
            return R.error(customException.getCode(), customException.getMessage(), customException.getData());
        }

        if (e instanceof HttpMessageNotReadableException) {
            log.error(e.getMessage());
            return R.error("请求体数据格式错误，请检查后重试");
        }

        //easyExcel 解析错误
        if (e instanceof ExcelAnalysisException) {
            return R.error(e.getCause().getMessage());
        }
//        e.printStackTrace();

        if (!("未知错误").equals(msg)) {
            String descStr = "[code:未知错误][msg:" + msg + "]";
            log.info(descStr);
        } else {
            log.info(msg, e);
        }

        return R.error(msg);

//        if (e instanceof Exception) {
//            return R.error(e.getMessage());
//        }

//        if (!("未知错误").equals(msg)) {
//            String descStr = "[code:未知错误][msg:" + msg + "]";
//            log.info(descStr);
//        } else {
//            log.info(msg, e);
//        }
//        return R.error(msg);
    }
}
