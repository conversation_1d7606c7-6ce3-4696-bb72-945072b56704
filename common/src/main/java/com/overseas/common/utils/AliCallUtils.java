package com.overseas.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.dyvmsapi20170525.AsyncClient;
import com.aliyun.sdk.service.dyvmsapi20170525.models.*;
import darabonba.core.client.ClientOverrideConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Component
@Slf4j
public class AliCallUtils {

    public static String accessKeyId;
    public static String accessKeySecret;

    public static String machineRoomRegion;

    @Value("${aliyun.oss-config.bucket-material.access-key-id}")
    public void setAccessKeyId(String accessKeyId) {
        AliCallUtils.accessKeyId = accessKeyId;
    }

    @Value("${aliyun.oss-config.bucket-material.access-key-secret}")
    public void setAccessKeySecret(String accessKeySecret) {
        AliCallUtils.accessKeySecret = accessKeySecret;
    }

    @Value("${aliyun.machine-room-region}")
    public void setMachineRoomRegion(String machineRoomRegion) {
        AliCallUtils.machineRoomRegion = machineRoomRegion;
    }

    public static SingleCallByTtsResponseBody call(String phoneNumber, String ttsCode, Map<String, String> ttsParam) {
        AsyncClient client = AliCallUtils.getAsyncClient();

        // Parameter settings for API request
        SingleCallByTtsRequest singleCallByTtsRequest = SingleCallByTtsRequest.builder()
                .ttsCode(ttsCode)
                .calledShowNumber("02028307988")
                .calledNumber(phoneNumber)
                .ttsParam(JSONObject.toJSONString(ttsParam))
                // Request-level configuration rewrite, can set Http request parameters, etc.
                // .requestConfiguration(RequestConfiguration.create().setHttpHeaders(new HttpHeaders()))
                .build();


        // Asynchronously get the return value of the API request
        CompletableFuture<SingleCallByTtsResponse> response = client.singleCallByTts(singleCallByTtsRequest);
        try {
            // Synchronously get the return value of the API request
            SingleCallByTtsResponse resp = response.get();
            log.info("ali call result : {}", JSONObject.toJSONString(resp));
            if (resp.getStatusCode().equals(200) && resp.getBody().getCode().equals("OK")) {
                return resp.getBody();
            } else {
                return null;
            }
        } catch (Exception e) {
            log.info("ali call error : {}", e.getMessage());
            return null;
        } finally {
            // Finally, close the client
            client.close();
        }
    }

    public static QueryCallDetailByCallIdResponseBody getCallDetail(String callId, Long callDate) {
        AsyncClient client = AliCallUtils.getAsyncClient();

        // Parameter settings for API request
        QueryCallDetailByCallIdRequest queryCallDetailByCallIdRequest = QueryCallDetailByCallIdRequest.builder()
                .prodId(11000000300006L)
                .callId(callId)
                .queryDate(callDate)
                // Request-level configuration rewrite, can set Http request parameters, etc.
                // .requestConfiguration(RequestConfiguration.create().setHttpHeaders(new HttpHeaders()))
                .build();

        // Asynchronously get the return value of the API request
        CompletableFuture<QueryCallDetailByCallIdResponse> response = client.queryCallDetailByCallId(queryCallDetailByCallIdRequest);

        try {
            // Synchronously get the return value of the API request
            QueryCallDetailByCallIdResponse resp = response.get();
            if (resp.getStatusCode().equals(200) && resp.getBody().getCode().equals("OK")) {
                return resp.getBody();
            } else {
                return null;
            }
        } catch (Exception e) {
            log.info("ali call error : {}", e.getMessage());
            return null;
        } finally {
            // Finally, close the client
            client.close();
        }
    }

    private static AsyncClient getAsyncClient() {
        // Configure Credentials authentication information, including ak, secret, token
        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                // Please ensure that the environment variables ALIBABA_CLOUD_ACCESS_KEY_ID and ALIBABA_CLOUD_ACCESS_KEY_SECRET are set.
                .accessKeyId(accessKeyId)
                .accessKeySecret(accessKeySecret)
                //.securityToken(System.getenv("ALIBABA_CLOUD_SECURITY_TOKEN")) // use STS token
                .build());

        // Configure the Client
        return AsyncClient.builder()
                .region(machineRoomRegion) // Region ID
                //.httpClient(httpClient) // Use the configured HttpClient, otherwise use the default HttpClient (Apache HttpClient)
                .credentialsProvider(provider)
                //.serviceConfiguration(Configuration.create()) // Service-level configuration
                // Client-level configuration rewrite, can set Endpoint, Http request parameters, etc.
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                // Endpoint 请参考 https://api.aliyun.com/product/Dyvmsapi
                                .setEndpointOverride("dyvmsapi.aliyuncs.com")
                        //.setConnectTimeout(Duration.ofSeconds(30))
                )
                .build();
    }
}
