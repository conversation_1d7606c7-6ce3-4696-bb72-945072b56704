package com.overseas.common.utils;

import org.apache.commons.lang3.StringUtils;

public class ByteConverterUtils {
    private static final char[] hexCodeUpper = "0123456789ABCDEF".toCharArray();
    private static final char[] hexCodeLower = "0123456789abcdef".toCharArray();

    /* byte[] -> hex string, 需要指定最终大小写 */
    public static String byteToHex(byte[] data, boolean isUpperCase) {
        char[] hexCode = isUpperCase ? hexCodeUpper : hexCodeLower;
        StringBuilder r = new StringBuilder(data.length * 2);
        for (byte b : data) {
            r.append(hexCode[(b >> 4) & 0xF]);
            r.append(hexCode[(b & 0xF)]);
        }
        return r.toString();
    }

    /* hex string -> byte[]，可自动识别大小写 */
    public static byte[] hexToByte(String s) {
        if (StringUtils.isBlank(s))
            return null;
        int len = s.length();
        if (len % 2 != 0)
            return null; // "111" is not a valid hex encoding.

        byte[] out = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            int h = hexToBin(s.charAt(i));
            int l = hexToBin(s.charAt(i + 1));
            if (h == -1 || l == -1)
                return null;
            out[i / 2] = (byte) (h * 16 + l);
        }

        return out;
    }

    private static int hexToBin(char ch) {
        if ('0' <= ch && ch <= '9')
            return ch - '0';
        if ('A' <= ch && ch <= 'F')
            return ch - 'A' + 10;
        if ('a' <= ch && ch <= 'f')
            return ch - 'a' + 10;
        return -1;
    }
}
