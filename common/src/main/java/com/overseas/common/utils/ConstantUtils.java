package com.overseas.common.utils;

public class ConstantUtils {
    /**
     * 当前页
     */
    public static final String PAGE = "page";

    /**
     * 总条目
     */
    public static final String TOTAL = "total";

    /**
     * 分页大小
     */
    public static final String PAGE_NUM = "pageNum";

    /**
     * 排序字段
     */
    public static final String ORDER_FIELD = "orderField";

    /**
     * 排序类型（asc/desc）
     */
    public static final String ORDER_TYPE = "orderType";

    /**
     * Mybatis-Plus Wrapper
     */
    public static final String WRAPPER = "ew";

    /**
     * ADX
     */
    public static final Integer ADX_ID_YOUKU = 9;

    public static final Integer ADX_ID_VOICEADS = 45;

    public static final Integer ADX_ID_BAIDU_BES = 71;

    public static final Integer ADX_ID_IQIYI = 55;

    public static final Integer ADX_ID_KUAI_SHOU = 67;

    /*
     * Start Log 前缀
     */
    public static final String LOG_POST_URL = "post url : ";

    public static final String LOG_GET_URL = "get url : ";

    public static final String LOG_POST_PARAMS = "post params : ";

    public static final String LOG_GET_PARAMS = "get params : ";

    public static final String LOG_POST_SUCCESS_RESPONSE = "post success response : ";

    public static final String LOG_POST_EXCEPTION_RESPONSE = "post exception response : ";

    public static final String LOG_GET_SUCCESS_RESPONSE = "get success response : ";

    public static final String LOG_GET_EXCEPTION_RESPONSE = "get exception response : ";

    /*
     * End Log 前缀
     */

    // 提示信息常量
    public static final String LONG_TIME_DELIVERY = "长期投放";
    public static final String NO_LIMIT = "不限";
    public static final String UNKNOWN = "未知";
    public static final String PLACEHOLDER = "-";
    public static final String PLACEHOLDER_2 = "--";
    public static final String EMPTY = "";
    public static final String ALL = "总计";
    public static final String ALL_2 = "汇总";
    public static final Integer INTEGER_UNKNOWN = 99999999;
    public static final String SPLIT = "、";
    public static final Integer YES = 1;
}
