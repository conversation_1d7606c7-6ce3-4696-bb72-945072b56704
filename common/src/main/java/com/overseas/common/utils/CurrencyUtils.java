package com.overseas.common.utils;

import com.overseas.common.configuration.CurrencyConfiguration;
import com.overseas.common.exception.CustomException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 **/
@Slf4j
@RequiredArgsConstructor
@Component
public class CurrencyUtils {

    private final CurrencyConfiguration currencyConfiguration;

    /**
     * 返回数据
     *
     * @param trans   原数据
     * @param country 国家
     * @return 返回兑换美元
     */
    public BigDecimal transDollar(BigDecimal trans, String country) {
        return this.transRate(country).multiply(trans);
    }


    /**
     * 获取 缩写
     *
     * @param country 国家
     * @return 货币缩写
     */
    public String transCurrency(String country) {
        switch (country) {
            case "de":
                return "EUR";
            case "uk":
                return "GBP";
            case "us":
            default:
                return "USD";
        }
    }

    /**
     * 获取国家汇率
     *
     * @param country 国家
     * @return 返回汇率
     */
    public BigDecimal transRate(String country) {
        String name = this.transCurrency(country);
        Object object = ObjectUtils.getObjectValue(currencyConfiguration, name.toLowerCase());
        if (null == object) {
            throw new CustomException("无法获取到汇率");
        }
        try {
            return new BigDecimal(object.toString());
        } catch (Exception e) {
            log.error("获取到的字段内容是:{}", object);
            log.error(e.getMessage(), e);
        }
        return BigDecimal.ONE;
    }
}
