/**
 * Copyright (c) 2016-2019 人人开源 All rights reserved.
 * <p>
 * https://www.renren.io
 * <p>
 * 版权所有，侵权必究！
 */

package com.overseas.common.utils;

import com.overseas.common.exception.CustomException;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * 日期处理
 *
 * @<NAME_EMAIL>
 */
public class DateUtils {
    /**
     * 时间格式(yyyy-MM-dd)
     */
    public final static String DATE_PATTERN = "yyyy-MM-dd";

    public final static String DATE_MONTH = "yyyy年MM月";

    public final static String DATE_PATTERN_2 = "yyyyMMdd";

    public final static String DATE_PATTERN_3 = "yyyy/MM/dd";

    /**
     * 时间格式(yyyy-MM-dd HH:mm:ss)
     */
    public final static String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * 按照年月日格式化日期
     *
     * @param date 日期
     * @return 年月日格式日期
     */
    public static String format(Date date) {
        return format(date, DATE_PATTERN);
    }

    /**
     * 按照指定格式格式化日期
     *
     * @param date    日期
     * @param pattern 格式
     * @return 格式化后的日期
     */
    public static String format(Date date, String pattern) {
        if (date != null) {
            Instant instant = date.toInstant();
            LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
            return localDateTime.format(DateTimeFormatter.ofPattern(pattern));
        }
        return null;
    }

    /**
     * 获取指定日期之前或之后N天的日期
     *
     * @param date 当前日期
     * @param day  要前移或推后的天数，正数推后，负数前移
     * @return 结果日期
     */
    public static Date format(Date date, Integer day) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, day);
        return calendar.getTime();
    }

    /**
     * 获取指定格式的Date
     *
     * @param date    当前日期
     * @param pattern 转化格式
     * @return 结果日期
     */
    public static Date formatDate(Date date, String pattern) {
        return DateUtils.string2Date(DateUtils.format(date, pattern));
    }

    /**
     * 格式化小时日期，获取当前时间之前或之后N个小时的日期
     *
     * @param date 当前日期
     * @param hour 要前移或推后的小时数，正数推后，负数前移
     * @return 结果日期
     */
    public static Date formatHour(Date date, Integer hour) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.add(Calendar.HOUR_OF_DAY, hour);
        return calendar.getTime();
    }

    /**
     * 格式化小时日期，设置日期某个小时
     *
     * @param date 当前日期
     * @param hour 要前移或推后的小时数，正数推后，负数前移
     * @return 结果日期
     */
    public static Date setHour(Date date, Integer hour) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        return calendar.getTime();
    }

    /**
     * 获取指定时间之前或之后n小时的小时数
     *
     * @param nowDate 时间
     * @param hour    要前移或推后的小时数，正数推后，负数前移
     * @return 小时数
     */
    public static int getDateHour(Date nowDate, Integer hour) {
        Date date = formatHour(nowDate, hour);
        return Integer.parseInt(format(date, "HH"));
    }

    /**
     * 获取指定日期之前或之后N月的日期
     *
     * @param date  当前日期
     * @param mouth 要前移或推后的月数，正数推后，负数前移
     * @return 结果日期
     */
    public static Date afterMouth(Date date, Integer mouth) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, mouth);
        return calendar.getTime();
    }

    /**
     * 获取指定日期之前或之后N天的日期
     *
     * @param date 当前日期
     * @param day  要前移或推后的天数，正数推后，负数前移
     * @return 结果日期
     */
    public static Date afterDay(Date date, Integer day) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, day);
        return calendar.getTime();
    }

    /**
     * 获取指定日期之前或之后N小时的日期
     *
     * @param date 当前日期
     * @param hour 要前移或推后的小时数，正数推后，负数前移
     * @return 结果日期
     */
    public static Date afterHour(Date date, Integer hour) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.add(Calendar.HOUR, hour);
        return calendar.getTime();
    }

    /**
     * 获取指定日期之前或之后N分钟的日期
     *
     * @param date   当前日期
     * @param minute 要前移或推后的天数，正数推后，负数前移
     * @return 结果日期
     */
    public static Date afterMinute(Date date, Integer minute) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, minute);
        return calendar.getTime();
    }


    /**
     * 获取指定日期之前或之后N分钟的日期
     *
     * @param date   当前日期
     * @param minute 要前移或推后的天数，正数推后，负数前移
     * @return 结果日期
     */
    public static Long afterMinuteByTimestamp(Date date, Integer minute) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, minute);
        return calendar.getTimeInMillis() / 1000;
    }


    /**
     * 获取时间 calendar
     *
     * @param timestamp 时间戳
     * @return 返回数据
     */
    public static Calendar getCalendar(Long timestamp) {
        return getCalendar(DateUtils.long2Date(timestamp));
    }

    /**
     * 获取时间 calendar
     *
     * @param date 日期
     * @return 返回数据
     */
    public static Calendar getCalendar(Date date) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        return calendar;
    }


    /**
     * 将日期格式转为秒级时间戳
     *
     * @param date 日期
     * @return 时间戳
     */
    public static Long date2Long(Date date) {
        if (null == date) {
            return 0L;
        }
        String timestamp = String.valueOf(date.getTime() / 1000);
        return Long.valueOf(timestamp);
    }

    /**
     * 时间戳转日期
     *
     * @param date 时间戳
     * @return 日期
     */
    public static Date long2Date(Long date) {
        return new Date(date * 1000);
    }

    /**
     * 字符串格式日期转为日期格式
     *
     * @param dateString 日期字符串
     * @return 日期
     */
    public static Date string2Date(String dateString) {
        if (StringUtils.isBlank(dateString)) {
            return null;
        } else {
            if (dateString.length() < 12) {
                dateString += " 00:00:00";
            }
            LocalDateTime localDateTime = LocalDateTime.parse(dateString,
                    DateTimeFormatter.ofPattern(DATE_TIME_PATTERN).withZone(ZoneId.systemDefault()));
            Instant instant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();
            return Date.from(instant);
        }
    }

    /**
     * 按照年月日格式化日期
     *
     * @param dateString 日期
     * @param pattern    转化格式
     * @return 年月日格式日期
     */
    public static Date string2Date(String dateString, String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        try {
            return sdf.parse(dateString);
        } catch (Exception exception) {
            throw new CustomException("日期格式转化错误");
        }
    }

    /**
     * 含小时字符串格式日期转为日期格式
     *
     * @param dateString 日期字符串
     * @return 日期
     */
    public static Date hourString2Date(String dateString) {
        if (dateString.isEmpty()) {
            return null;
        } else {
            LocalDateTime localDateTime = LocalDateTime.parse(dateString, DateTimeFormatter.ofPattern(DATE_TIME_PATTERN).withZone(ZoneId.systemDefault()));
            Instant instant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();
            return Date.from(instant);
        }
    }


    /**
     * 格式转换为long
     *
     * @param dateString 时间string
     * @param last       后缀数据
     * @return 时间戳
     */
    public static long string2Long(String dateString, String last) {
        if (StringUtils.isBlank(dateString)) {
            return 0L;
        }
        if (StringUtils.isBlank(last)) {
            return string2Long(dateString);
        }
        return string2Long(String.format("%s %s", dateString, last));
    }

    /**
     * 格式转换为long
     *
     * @param dateString 时间string
     * @return 时间戳
     */
    public static long string2Long(String dateString) {
        if (StringUtils.isBlank(dateString)) {
            return 0L;
        }
        return DateUtils.date2Long(DateUtils.string2Date(dateString));
    }

    /**
     * long类型转字符串日期
     *
     * @param dateLong long日期
     * @param format   转换类型
     * @return 字符串日期
     */
    public static String long2String(Long dateLong, String format) {
        return format(long2Date(dateLong), format);
    }

    /**
     * long类型转字符串日期
     *
     * @param dateLong long日期
     * @return 字符串日期
     */
    public static String long2String(Long dateLong) {
        return format(long2Date(dateLong));
    }

    /**
     * 获取今日日期
     *
     * @return 今天
     */
    public static Date getTodayDate() {
        return DateUtils.string2Date(DateUtils.format(new Date()));
    }

    /**
     * 获取今日字符串日期
     *
     * @return 今天的字符串日期
     */
    public static String getTodayStringDate() {
        return format(getTodayDate());
    }

    /**
     * 获取俩 string 日期的差距每日list返回
     *
     * @param startStr 开始时间
     * @param endStr   结束时间
     * @param pattern  日期格式
     * @return 结果集
     */
    public static List<String> getBetweenDate(String startStr, String endStr, String pattern) {
        //指定转换格式
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern(pattern);
        //进行转换
        LocalDate startDate = LocalDate.parse(startStr, fmt);
        LocalDate endDate = LocalDate.parse(endStr, fmt);
        return getBetweenDate(startDate, endDate, pattern);
    }

    /**
     * 获取俩 string 日期的差距每日list返回
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param pattern   日期格式
     * @return 结果集
     */
    public static List<String> getBetweenDate(LocalDate startDate, LocalDate endDate, String pattern) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern(pattern);
        Period period = Period.between(startDate, endDate);
        //预估，会有偏差,所以以一个月31 天
        int days = period.getYears() * 365 + period.getMonths() * 31 + period.getDays();
        if (days < 0) {
            return Collections.emptyList();
        }
        int i = 0;
        List<String> dayList = new ArrayList<>(days);
        dayList.add(startDate.format(fmt));
        while (startDate.isBefore(endDate) && i < days) {
            startDate = startDate.plusDays(1L);
            dayList.add(startDate.format(fmt));
            i++;
        }
        return dayList;
    }

    /**
     * 获取俩 string 日期的差距每日list返回
     *
     * @param dayStr 时间
     * @return 结果集
     */
    public static List<String> getBetweenHour(String dayStr) {
        List<String> hourList = new ArrayList<>();
        int hour = 23;
        if (DateUtils.format(new Date()).equalsIgnoreCase(dayStr)) {
            hour = LocalDateTime.now().getHour() + 1;
        }
        for (int i = 0; i <= hour; i++) {
            hourList.add(formatHour(i));
        }
        return hourList;
    }

    /**
     * 转换小时名称
     *
     * @param hour 小时
     * @return 返回转换后的小时
     */
    public static String formatHour(Integer hour) {
        if (hour >= 0 && hour < 24) {
            return String.format("%s:00", hour >= 10 ? hour.toString() : "0" + hour);
        }
        throw new CustomException("小时超出范围");
    }

    public static Integer formatHourRange(Integer hour) {
        if (hour < 0) {
            return 24 + hour;
        } else if (hour >= 24) {
            return hour - 24;
        } else {
            return hour;
        }
    }

    /**
     * 根据小时获取小时对应的展示数据
     *
     * @param hour 小时值
     * @return 展示名称
     */
    public static String hour2Name(Integer hour) {
        String nextHour = hour < 9 ? "0" + (hour + 1) : String.valueOf(hour + 1);
        if (hour >= 0 && hour < 24) {
            return hour < 10 ? "0" + hour + ":00-" + nextHour + ":00" : hour + ":00-" + nextHour + ":00";
        } else {
            throw new CustomException("小时值超出范围");
        }
    }

    /**
     * @param hour 小时
     * @return 数据
     */
    public static String hour2Str(Integer hour) {
        return hour < 10 ? String.format("0%s:00:00", hour) : String.format("%s:00:00", hour);
    }

    public static int getHour() {
        return Integer.parseInt(format(new Date(), "HH"));
    }

    public static int getHour(Date date) {
        return Integer.parseInt(format(date, "HH"));
    }

    /**
     * 获取两个时间之间差了几天
     *
     * @param date1 第一个时间
     * @param date2 第二个时间
     * @return 返回数据
     */
    public static long diffWithDay(Date date1, Date date2) {
        return (date1.getTime() - date2.getTime()) / (1000 * 3600 * 24);
    }

    /**
     * 根据日期获取该月第一天
     *
     * @param date 日期
     * @return 第一天日期
     */
    public static Date getFirstDayOfMonth(Date date) {
        LocalDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime firstDay = dateTime.with(TemporalAdjusters.firstDayOfMonth());
        return Date.from(firstDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 根据日期获取该月最后一天
     *
     * @param date 日期
     * @return 最后一天日期
     */
    public static Date getLastDayOfMonth(Date date) {
        LocalDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime lastDay = dateTime.with(TemporalAdjusters.lastDayOfMonth());
        return Date.from(lastDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取指定日期所在月的天数
     *
     * @param date 日期
     * @return 天数
     */
    public static long getDayOfMonth(Date date) {
        return diffWithDay(getLastDayOfMonth(date), getFirstDayOfMonth(date)) + 1;
    }


    /**
     * 获取日期是星期几
     *
     * @param date 日期
     * @return 星期几
     */
    public static String getWeekName(Date date) {
        String[] weeks = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        int weekIndex = getDayOfWeek(date) - 1;
        if (weekIndex < 0) {
            weekIndex = 0;
        }
        return weeks[weekIndex];
    }

    /**
     * 获取当前天是日期的第几天
     *
     * @param date 日期
     * @return 第几天
     */
    public static int getDayOfWeek(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal.get(Calendar.DAY_OF_WEEK);
    }

    /**
     * 根据日期获取当前季度
     *
     * @param date 日期
     * @return 季度字符串
     */
    public static String getQuarter(Date date) {
        String year = format(date, "yyyy年");
        String month = format(date, "MM");
        int intMonth = Integer.parseInt(month);
        int quarter = intMonth % 3 == 0 ? (intMonth / 3) : (intMonth / 3 + 1);
        return year + quarter + "季度";
    }

    /**
     * 获取指定日期所在季度第一天
     *
     * @param date 日期
     * @return 季度第一天
     */
    public static Date getFirstDayOfQuarter(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int month = calendar.get(Calendar.MONTH) + 1;
        //根据月份获取所在季度
        int quarter = month % 3 == 0 ? (month / 3) : (month / 3 + 1);
        //所在季度的第一个月
        int startMonth = quarter * 3 - 2;
        Calendar a = Calendar.getInstance();
        a.set(Calendar.YEAR, calendar.get(Calendar.YEAR));
        a.set(Calendar.MONTH, startMonth - 1);
        a.set(Calendar.DATE, 1);
        a.set(Calendar.HOUR_OF_DAY, 0);
        a.set(Calendar.MINUTE, 0);
        a.set(Calendar.SECOND, 0);
        a.set(Calendar.MILLISECOND, 0);
        return a.getTime();
    }


    /**
     * 日期字符串转LocalDate
     *
     * @param str
     * @return
     */
    public static LocalDate string2LocalDate(String str) {
        return LocalDate.parse(str, DateTimeFormatter.ofPattern(DATE_PATTERN));
    }

    /**
     * LocalDateTime格式化
     *
     * @param localDateTime
     * @return
     */
    public static String localDateTime2String(LocalDateTime localDateTime) {
        if (localDateTime != null) {
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern(DATE_TIME_PATTERN);
            return dtf.format(localDateTime);
        }
        return null;
    }

    /**
     * 日期字符串转换为yyyy/MM/dd
     *
     * @param dateStr
     * @return
     */
    public static String formatDateToSlash(String dateStr) {
        return dateStr.replace("-", "/");
    }

    public static LocalDate date2LocalDate(Date date) {
        if (null == date) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static Date localDate2Date(LocalDate localDate) {
        if (null == localDate) {
            return null;
        }
        ZonedDateTime zonedDateTime = localDate.atStartOfDay(ZoneId.systemDefault());
        return Date.from(zonedDateTime.toInstant());
    }

    public static int getDayNumberBetweenDate(String startDate, String endDate) {
        Long days = (string2Long(endDate) - string2Long(startDate)) / (3600 * 24);
        return days.intValue();
    }

    public static List<String> getBetweenDateHourList(Date startDate, Date endDate) {
        long dateLong = date2Long(startDate), endDateLong = date2Long(endDate);
        List<String> res = new ArrayList<>();
        while (dateLong <= endDateLong) {
            res.add(long2String(dateLong, DATE_TIME_PATTERN));
            dateLong += 3600;
        }
        return res;
    }

    /**
     * 获取 偏差周期
     *
     * @param start 开始
     * @param end   结束
     * @return 返回上一周期
     */
    public static Cycle lastCycle(String start, String end) {
        Date startDate = DateUtils.string2Date(start);
        Date endDate = DateUtils.string2Date(end);
        long diff = DateUtils.diffWithDay(startDate, endDate);
        Cycle cycle = new Cycle();
        cycle.setEnd(DateUtils.afterDay(startDate, -1));
        cycle.setStart(DateUtils.afterDay(cycle.getEnd(), (int) diff));
        return cycle;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Cycle {

        private Date start;

        private Date end;
    }


    /**
     * 获取当前时间是今天的多少分钟
     *
     * @param date 时间
     * @return 返回数据
     */
    public static int getDayMinute(Date date) {
        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        return localDateTime.getMinute() + localDateTime.getHour() * 60;
    }
}
