package com.overseas.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class DoubleUtils {
    private static final int DEF_DIV_SCALE = 2;

    /**
     * 两个Double数相加
     *
     * @param d1 加数
     * @param d2 加数
     * @return Double 结果
     */
    public static Double add(Double d1,Double d2){
        BigDecimal b1 = new BigDecimal(d1.toString());
        BigDecimal b2 = new BigDecimal(d2.toString());
        return b1.add(b2).doubleValue();
    }

    /**
     * 两个Double数相减
     *
     * @param d1 被减数
     * @param d2 减数
     * @return Double 结果
     */
    public static Double sub(Double d1,Double d2){
        BigDecimal b1 = new BigDecimal(d1.toString());
        BigDecimal b2 = new BigDecimal(d2.toString());
        return b1.subtract(b2).doubleValue();
    }

    /**
     * 两个Double数相乘
     *
     * @param d1 乘数
     * @param d2 乘数
     * @return Double 结果
     */
    public static Double mul(Double d1,Double d2){
        BigDecimal b1 = new BigDecimal(d1.toString());
        BigDecimal b2 = new BigDecimal(d2.toString());
        return b1.multiply(b2).doubleValue();
    }

    /**
     * 两个Double数相除
     *
     * @param d1 被除数
     * @param d2 除数
     * @return Double 结果
     */
    public static Double div(Double d1,Double d2){
        BigDecimal b1 = new BigDecimal(d1.toString());
        BigDecimal b2 = new BigDecimal(d2.toString());
        return b1.divide(b2, DEF_DIV_SCALE, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * 两个Double数相除，并保留scale位小数
     *
     * @param d1 被除数
     * @param d2 除数
     * @param scale 小数位数
     * @return Double 结果
     */
    public static Double div(Double d1,Double d2,int scale){
        if(scale<0){
            throw new IllegalArgumentException(
                    "The scale must be a positive integer or zero");
        }
        BigDecimal b1 = new BigDecimal(d1.toString());
        BigDecimal b2 = new BigDecimal(d2.toString());
        return b1.divide(b2, scale, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * 获取两个BigDecimal相除比例结果
     * @param a 除数
     * @param b 被除数
     * @return 结果
     */
    public static Double getRate(BigDecimal a, BigDecimal b) {
        return b.signum() == 0
                ? 0.00f : a.multiply(BigDecimal.valueOf(100)).divide(b, 2, RoundingMode.HALF_UP).doubleValue();
    }
}
