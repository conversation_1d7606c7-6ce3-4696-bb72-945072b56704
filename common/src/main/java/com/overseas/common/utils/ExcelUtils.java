package com.overseas.common.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.overseas.common.dto.SelectDTO2;
import com.overseas.common.dto.common.BaseExcelDTO;
import com.overseas.common.dto.common.ExcelExportDTO;
import com.overseas.common.exception.CustomException;
import com.overseas.common.listener.ExcelListener;
import com.overseas.common.vo.common.BaseExcelVO;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

public class ExcelUtils {

    private static final String sheetName = "报表数据";

    /**
     * 下载报表excel
     *
     * @param data 要下载的数据
     * @throws IOException IO异常
     */
    public static void download(HttpServletResponse response, String fileName, Class c, List data,
                                List<String> headers, String sheetName2) throws IOException {
        setDownloadResponse(response, fileName);

        if (!headers.isEmpty()) {
            EasyExcel.write(response.getOutputStream(), c)
                    .includeColumnFiledNames(headers)
                    .sheet(StringUtils.isNotBlank(sheetName2) ? sheetName2 : sheetName).doWrite(data);
        } else {
            EasyExcel.write(response.getOutputStream(), c)
                    .sheet(StringUtils.isNotBlank(sheetName2) ? sheetName2 : sheetName).doWrite(data);
        }

        // 写本地，测试用
//        String filePath = "D://" + fileName + ".xlsx";
//        if (headers.size() > 0) {
//            EasyExcel.write(filePath, c).includeColumnFiledNames(headers).sheet(sheetName).doWrite(data);
//        } else {
//            EasyExcel.write(filePath, c).sheet(sheetName).doWrite(data);
//        }
    }

    /**
     * 下载报表excel
     *
     * @param data 要下载的数据
     * @throws IOException IO异常
     */
    public static void download(HttpServletResponse response, String fileName, Class c, List data,
                                List<String> headers) throws IOException {
        setDownloadResponse(response, fileName);
        ExcelUtils.download(response, fileName, c, data, headers, "");
    }

    public static void download(HttpServletResponse response, String fileName, String sheetName, List data,
                                List<SelectDTO2> headers) throws IOException {
        setDownloadResponse(response, fileName);
        ExcelExportDTO excelExportDTO = formatExcelExportData(data, headers);
        EasyExcel.write(response.getOutputStream()).head(excelExportDTO.getExcelHeaders()).sheet(sheetName)
                .doWrite(excelExportDTO.getExcelData());
    }

    public static void download(HttpServletResponse response, String fileName, String sheetName, List data,
                                List<SelectDTO2> headers, String connector) throws IOException {
        setDownloadResponse(response, fileName);
        ExcelExportDTO excelExportDTO = formatExcelExportData(data, headers, connector);
        EasyExcel.write(response.getOutputStream()).head(excelExportDTO.getExcelHeaders()).sheet(sheetName)
                .doWrite(excelExportDTO.getExcelData());
    }

    public static void download(HttpServletResponse response, String fileName, List data,
                                List<List<String>> headers) throws IOException {
        setDownloadResponse(response, fileName);

        ExcelUtils.download(response, fileName, data, headers, sheetName);
    }

    public static void download(HttpServletResponse response, String fileName, List data,
                                List<List<String>> headers, String sheetName2) throws IOException {
        setDownloadResponse(response, fileName);

        if (!headers.isEmpty()) {
            EasyExcel.write(response.getOutputStream()).head(headers).sheet(sheetName2).doWrite(data);
        } else {
            EasyExcel.write(response.getOutputStream()).sheet(sheetName2).doWrite(data);
        }
    }

    /**
     * 下载多sheet excel
     *
     * @param response 返回
     * @param dataList 参数
     * @param fileName 文件名称
     * @throws IOException 异常
     */
    public static void responseWithSheet(HttpServletResponse response, List<BaseExcelDTO> dataList, String fileName,
                                         List<String> excludeHeaders) throws IOException {
        setDownloadResponse(response, fileName);
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
        dataList.forEach(data ->
                excelWriter.write(data.getData(), EasyExcel.writerSheet(data.getSheetKey(), data.getSheetName())
                        .head(data.getAClass())
                        .excludeColumnFieldNames(excludeHeaders).build())
        );
        excelWriter.finish();
    }


    /**
     * 下载多sheet excel
     *
     * @param dataList 参数
     * @throws IOException 异常
     */
    public static void downloadWithSheet(String filePath, List<BaseExcelDTO> dataList, List<String> excludeHeaders) throws IOException {
        ExcelWriter excelWriter = EasyExcel.write(filePath).build();
        dataList.forEach(data ->
                excelWriter.write(data.getData(), EasyExcel.writerSheet(data.getSheetKey(), data.getSheetName())
                        .head(data.getAClass())
                        .excludeColumnFieldNames(excludeHeaders).build())
        );
        excelWriter.finish();
    }

    /**
     * 下载基本builder
     *
     * @param response 返回数据
     * @param fileName 文件名称
     * @param c        clazz
     * @return 返回数据
     * @throws IOException IO异常
     */
    public static ExcelWriterSheetBuilder downloadBuilder(HttpServletResponse response, String fileName, Class c)
            throws IOException {
        setDownloadResponse(response, fileName);
        return EasyExcel.write(response.getOutputStream(), c).sheet(sheetName);
    }

    /**
     * 基础下载数据
     *
     * @param filePath  文件路径
     * @param sheetName sheet名
     * @param c         数据对象类
     * @param data      数据集合
     */
    public static void download(String filePath, String sheetName, Class c, List data) {
        EasyExcel.write(filePath, c).sheet(sheetName).doWrite(data);
    }


    /**
     * 基础下载数据
     *
     * @param filePath  文件路径
     * @param sheetName sheet名
     * @param data      数据集合
     * @param headers   表头
     */
    public static void download(String filePath, List<?> data, List<List<String>> headers, String sheetName) {
        EasyExcel.write(filePath).head(headers).sheet(sheetName).doWrite(data);
    }

    /**
     * 基础下载数据
     *
     * @param filePath  文件路径
     * @param sheetName sheet名
     * @param c         数据对象类
     * @param data      数据集合
     * @param headers   表头
     */
    public static void download(String filePath, String sheetName, Class c, List data, List<String> headers) {
        EasyExcel.write(filePath, c).includeColumnFiledNames(headers).sheet(sheetName).doWrite(data);
    }

    /**
     * 基础下载数据
     *
     * @param filePath  文件路径
     * @param sheetName sheet名
     * @param data      数据集合
     * @param headers   表头
     */
    public static void download(String filePath, String sheetName, List data, List<SelectDTO2> headers) {
        ExcelExportDTO excelExportDTO = formatExcelExportData(data, headers);
        EasyExcel.write(filePath).head(excelExportDTO.getExcelHeaders()).sheet(sheetName)
                .doWrite(excelExportDTO.getExcelData());
    }

    /**
     * 基础下载数据
     *
     * @param filePath  文件路径
     * @param sheetName sheet名
     * @param data      数据集合
     * @param headers   表头
     */
    public static void download(String filePath, String sheetName, List data, List<SelectDTO2> headers,
                                String connector) {
        ExcelExportDTO excelExportDTO = formatExcelExportData(data, headers, connector);
        EasyExcel.write(filePath).head(excelExportDTO.getExcelHeaders()).sheet(sheetName)
                .doWrite(excelExportDTO.getExcelData());
    }

    /**
     * 读取excel 数据
     *
     * @param fileName 文件名称
     * @param clazz    类型
     * @param <T>      返回数据
     * @return 返回读取的文件list
     */

    public static <T extends BaseExcelVO> List<T> read(String fileName,
                                                       final Class<? extends BaseExcelVO> clazz) {
        fileName = canRead(fileName);
        ExcelListener<T> listener = new ExcelListener<>();
        return EasyExcel.read(fileName, clazz, listener).sheet().doReadSync();
    }

    /**
     * 读取文件指定部分其他内容
     *
     * @param fileName      文件地址
     * @param headRowNumber 文件头行数
     * @param clazz         类型
     * @param <T>           数据
     * @return 读取数据
     */
    public static <T extends BaseExcelVO> List<T> read(String fileName, Integer headRowNumber,
                                                       final Class<? extends BaseExcelVO> clazz) {
        fileName = canRead(fileName);
        ExcelListener<T> listener = new ExcelListener<>();
        return EasyExcel.read(fileName, clazz, listener).headRowNumber(headRowNumber).sheet().doReadSync();
    }

    /**
     * 读取文件指定部分其他内容
     *
     * @param fileName      文件地址
     * @param headRowNumber 文件头行数
     * @param sheetName     sheet页名
     * @param clazz         类型
     * @param <T>           数据
     * @return 读取数据
     */
    public static <T extends BaseExcelVO> List<T> read(String fileName, Integer headRowNumber, String sheetName,
                                                       final Class<T> clazz) {
        fileName = canRead(fileName);
        ExcelListener<T> listener = new ExcelListener<>();
        return EasyExcel.read(fileName, clazz, listener).headRowNumber(headRowNumber).sheet(sheetName).doReadSync();
    }

    /**
     * 判断是否excel
     *
     * @param fileName 文件地址
     * @return 是否
     */
    private static boolean isExcel(String fileName) {
        return List.of("xls", "xlsx", "csv").contains(UploadUtils.getExtension(fileName));
    }

    /**
     * 判断文件是否可读取
     *
     * @param fileName 文件地址
     * @return 返回数据
     */
    private static String canRead(String fileName) {
        if (!ExcelUtils.isExcel(fileName)) {
            throw new CustomException("文件格式不合法，请确认后再试");
        }
        if (fileName.indexOf("http") == 0) {
            return UploadUtils.getUploadPath(UploadUtils.getBasePath(fileName));
        }
        if (StringUtils.isBlank(fileName)) {
            throw new CustomException("文件地址为空，请确认后再试");
        }
        return fileName;
    }

    /**
     * 设置下载时的response内容
     *
     * @param response response
     * @param fileName 文件名
     */
    private static void setDownloadResponse(HttpServletResponse response, String fileName) {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

//        response.setContentType("application/vnd.ms-excel");
//        response.setCharacterEncoding("utf-8");
//        response.setHeader("Content-disposition",
//                "attachment;filename=" + URLEncoder.encode(fileName, String.valueOf(StandardCharsets.UTF_8))
//                        + ".xlsx"
//        );
    }

    /**
     * 获取指定结构的excel导出数据
     *
     * @param data    数据集
     * @param headers 表头
     * @return 结果数据
     */
    private static ExcelExportDTO formatExcelExportData(List data, List<SelectDTO2> headers) {
        // 根据对象类型过滤不存在的表头字段
        if (data.isEmpty()) {
            throw new CustomException("无可导出数据");
        }
        // 过滤不是属性的表头字段
        List<SelectDTO2> finalHeaders = new ArrayList<>();
        for (SelectDTO2 header : headers) {
            Object object = data.get(0);
            if (ObjectUtils.isExistField(header.getKey().toString(), object)) {
                finalHeaders.add(header);
            }
        }

        ExcelExportDTO excelExportDTO = new ExcelExportDTO();
        excelExportDTO.setExcelHeaders(getExcelHeader(finalHeaders));
        excelExportDTO.setExcelData(getExcelData(data, finalHeaders));
        return excelExportDTO;
    }

    /**
     * 获取指定结构的excel导出数据
     *
     * @param data    数据集
     * @param headers 表头
     * @return 结果数据
     */
    private static ExcelExportDTO formatExcelExportData(List data, List<SelectDTO2> headers, String connector) {
        // 根据对象类型过滤不存在的表头字段
        if (data.isEmpty()) {
            throw new CustomException("无可导出数据");
        }
        // 过滤不是属性的表头字段
        List<SelectDTO2> finalHeaders = new ArrayList<>();
        for (SelectDTO2 header : headers) {
            Object object = data.get(0);
            if (ObjectUtils.isExistField(header.getKey().toString(), object)) {
                finalHeaders.add(header);
            }
        }

        ExcelExportDTO excelExportDTO = new ExcelExportDTO();
        excelExportDTO.setExcelHeaders(getExcelHeader(finalHeaders, connector));
        excelExportDTO.setExcelData(getExcelData(data, finalHeaders));
        return excelExportDTO;
    }

    /**
     * 获取导出用的表头结构信息
     *
     * @param headers 回传回来的表头信息
     * @return 可用于导出的表头结构
     */
    private static List<List<String>> getExcelHeader(List<SelectDTO2> headers) {
        List<List<String>> excelHeaders = new ArrayList<>();
        for (SelectDTO2 header : headers) {
            List<String> excelHeader = new ArrayList<>();
            excelHeader.add(header.getTitle().toString());
            excelHeaders.add(excelHeader);
        }
        return excelHeaders;
    }

    /**
     * 获取导出用的表头结构信息
     *
     * @param headers 回传回来的表头信息
     * @return 可用于导出的表头结构
     */
    private static List<List<String>> getExcelHeader(List<SelectDTO2> headers, String connector) {
        List<List<String>> excelHeaders = new ArrayList<>();
        for (SelectDTO2 header : headers) {
            List<String> excelHeader = new ArrayList<>();
            if (StringUtils.isNotBlank(connector)) {
                excelHeader.addAll(new ArrayList<>(List.of(header.getTitle().toString().split(connector))));
            } else {
                excelHeader.add(header.getTitle().toString());
            }
            excelHeaders.add(excelHeader);
        }
        return excelHeaders;
    }

    /**
     * 获取excel导出的信息
     *
     * @param data    原始数据
     * @param headers 回传回来的表头信息
     * @return 导出来的表信息记录
     */
    private static List<List<Object>> getExcelData(List data, List<SelectDTO2> headers) {
        List<List<Object>> excelData = new ArrayList<>();
        for (Object datum : data) {
            List<Object> row = new ArrayList<>();
            for (SelectDTO2 header : headers) {
                row.add(ObjectUtils.getObjectValue(datum, header.getKey().toString()));
            }
            excelData.add(row);
        }
        return excelData;
    }
}
