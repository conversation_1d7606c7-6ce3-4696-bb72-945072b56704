package com.overseas.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.madgag.gif.fmsware.GifDecoder;
import com.overseas.common.dto.common.FFmpegDTO;
import com.overseas.common.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import net.bramp.ffmpeg.FFmpeg;
import net.bramp.ffmpeg.FFmpegExecutor;
import net.bramp.ffmpeg.FFprobe;
import net.bramp.ffmpeg.builder.FFmpegBuilder;
import net.bramp.ffmpeg.probe.FFmpegProbeResult;
import net.bramp.ffmpeg.probe.FFmpegStream;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 *
 */
@Component
@Slf4j
public class FFmpegUtils {

    private static String ffprobe;

    private static String ffmpeg;

    @Value("${com-upload.ffprobe}")
    public void setFfprobePath(String ffprobe) {
        FFmpegUtils.ffprobe = ffprobe;
    }

    @Value("${com-upload.ffmpeg}")
    public void setFfmpegPath(String ffmpeg) {
        FFmpegUtils.ffmpeg = ffmpeg;
    }

    public static FFmpegDTO info(String filePath, String fileName) throws IOException {

        FFmpegDTO fFmpegDTO = new FFmpegDTO();
        String extension = UploadUtils.getExtension(filePath);
        //图片获取尺寸
        if (List.of("img", "png", "jpeg", "gif", "jpg").contains(extension)) {
            String fileType = ImageMimeTypeUtil.readType(filePath);
            File picture = new File(filePath);
            if ("webp".equals(fileType)) {
                fFmpegDTO.setSize((long) Math.ceil(picture.length()));
                setWebpInfo(filePath, fFmpegDTO);
                return fFmpegDTO;
            } else {
                try {
                    fFmpegDTO.setSize((long) Math.ceil(picture.length()));// 源图大小
                    BufferedImage sourceImg;
                    if (extension.equals("gif")) {
                        GifDecoder decoder = new GifDecoder();
                        decoder.read(new FileInputStream(picture));
                        sourceImg = decoder.getImage();
                    } else {
                        sourceImg = ImageIO.read(new FileInputStream(picture));
                    }
                    fFmpegDTO.setWidth(sourceImg.getWidth()); // 源图宽度
                    fFmpegDTO.setHeight(sourceImg.getHeight()); // 源图高度
                } catch (IllegalArgumentException e) {
                    e.printStackTrace();
                    throw new CustomException("文件：" + fileName + " 图片解析错误，请确认后再上传");
                }
            }
        } else {
            //获取其他格式尺寸和大小
            FFmpegProbeResult probeResult = new FFprobe(ffprobe).probe(filePath);
            fFmpegDTO.setDuration(Math.round(probeResult.getFormat().duration));
            fFmpegDTO.setSize(probeResult.getFormat().size);
            probeResult.getStreams().forEach(stream -> {
                if (FFmpegStream.CodecType.VIDEO.equals(stream.codec_type)) {
                    fFmpegDTO.setWidth(stream.width);
                    fFmpegDTO.setHeight(stream.height);
                }
            });
        }
        log.info("文件地址：{} ；解析数据 :{} ", filePath, JSONObject.toJSONString(fFmpegDTO));
        return fFmpegDTO;
    }

    /**
     * 获取连接类型
     *
     * @param filePath 地址
     * @return 返回类型
     */
    public static String getFormat(String filePath) {
        try {
            FFmpegProbeResult probeResult = new FFprobe(ffprobe).probe(filePath);
            String format = probeResult.getFormat().format_name.toLowerCase();
            switch (format) {
                case "jpeg_pipe":
                case "jpeg":
                    return "jpeg";
                case "png":
                case "png_pipe":
                    return "png";
                case "jpg":
                case "jpg_pipe":
                    return "jpg";
                case "gif":
                case "gif_pipe":
                    return "gif";
                default:
                    if (format.contains("mp4")) {
                        return "mp4";
                    }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("文件地址：{} ；解析错误 :{} ", filePath, e.getMessage());
        }
        return "";
    }

    /**
     * 截取视频
     *
     * @param filePath 源视频地址
     * @param start    开始时间（s）
     * @param end      结束时间（s）
     * @param dest     目标视频地址
     * @return 返回执行成功与否
     */
    public static boolean splitVideo(String filePath, Integer start, Integer end, String dest) {
        try {
            FFmpeg fFmpeg = new FFmpeg(ffmpeg);
            FFprobe fFprobe = new FFprobe(ffprobe);
            FFmpegBuilder builder = new FFmpegBuilder().setInput(filePath).addOutput(dest)
                    .setStartOffset(start, TimeUnit.SECONDS).setDuration(end - start, TimeUnit.SECONDS)
                    .setStrict(FFmpegBuilder.Strict.EXPERIMENTAL).done();
            new FFmpegExecutor(fFmpeg, fFprobe).createJob(builder).run();
        } catch (Exception e) {
            log.error("视频文件截取失败：{}", e.getMessage());
            e.printStackTrace();
            throw new CustomException("视频文件截取失败");
        }
        return true;
    }

    /**
     * 视频截图
     *
     * @param videoPath 视频路径
     * @param duration 时长
     * @param dest 输出
     * @return 是否截取成功
     */
    public static boolean coverImg(String videoPath, Integer duration, String dest) {
        try {
            FFmpeg fFmpeg = new FFmpeg(ffmpeg);
            FFprobe fFprobe = new FFprobe(ffprobe);
            FFmpegBuilder builder = new FFmpegBuilder().setInput(videoPath).addOutput(dest)
                    .setStartOffset(duration, TimeUnit.SECONDS)
                    .setFormat("image2")
                    .setFrames(1)
                    .setStrict(FFmpegBuilder.Strict.EXPERIMENTAL).done();
            new FFmpegExecutor(fFmpeg, fFprobe).createJob(builder).run();
        } catch (Exception e) {
            log.error("视频截取封面失败");
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * 根据素材地址，获取素材宽高等信息
     * @param filePath 文件地址
     * @param fFmpegDTO 素材信息对象
     */
    private static void setWebpInfo(String filePath, FFmpegDTO fFmpegDTO) {
        FileInputStream file = null;
        try {
            file = new FileInputStream(filePath);
            byte[] bytes = new byte[64];
            int read = file.read(bytes, 0, bytes.length);
            int width = ((int) bytes[27] & 0xff) << 8 | ((int) bytes[26] & 0xff);
            int height = ((int) bytes[29] & 0xff) << 8 | ((int) bytes[28] & 0xff);
            fFmpegDTO.setWidth(width); // 源图宽度
            fFmpegDTO.setHeight(height); // 源图高度
        } catch (IOException e) {
            log.info("set webp info error 1 : {}", e.getMessage());
        } finally {
            if (file != null) {
                try {
                    file.close();
                } catch (IOException e) {
                    log.info("set webp info error 2 : {}", e.getMessage());
                }
            }
        }
    }
}
