package com.overseas.common.utils;

import com.overseas.common.enums.ResultStatusEnum;
import com.overseas.common.exception.CustomException;
import lombok.Data;

import java.io.Serializable;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2020-09-22 23:18
 */
@Data
public class FeignR<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer code;
    private String msg;
    private T data;
    private Long total;

    public FeignR(Integer code, String msg, T data) {
        if (!code.equals(200)) {
            throw new CustomException(msg);
        }
    }

    public FeignR() {

    }


    /**
     * 错误返回
     *
     * @param message
     * @param code
     * @return
     */
    public static FeignR error(String message, Integer code) {
        FeignR r = new FeignR();
        r.setCode(code);
        r.setMsg(message);

        return r;
    }

    /**
     * 错误方法
     *
     * @param message
     * @return
     */
    public static FeignR error(String message) {
        return error(message, ResultStatusEnum.INTERNAL_SERVER_ERROR.getCode());
    }

    /**
     * 成功方法
     *
     * @param message
     * @return
     */
    public static <T> FeignR<T> ok(String message) {
        FeignR r = new FeignR();
        r.setCode(ResultStatusEnum.SUCCESS.getCode());
        r.setMsg(message);

        return r;
    }

    /**
     * 成功方法
     *
     * @return
     */
    public static FeignR ok() {
        return ok(ResultStatusEnum.SUCCESS.getMessage());
    }

    /**
     * 成功方法
     *
     * @param object
     * @return
     */
    public static <T> FeignR<T> ok(T object) {
        FeignR<T> r = ok(ResultStatusEnum.SUCCESS.getMessage());
        r.setData(object);

        return r;
    }

    public void valid() {
        if (!this.getCode().equals(0)) {
            throw new CustomException(this.getCode(), this.getMsg());
        }
    }


}
