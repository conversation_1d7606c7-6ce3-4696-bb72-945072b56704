package com.overseas.common.utils;

import com.overseas.common.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.util.FileCopyUtils;

import javax.activation.MimetypesFileTypeMap;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class FileUtils {

    /**
     * 下载文件后，保存到指定上传路径下
     *
     * @param urlPath 路径
     * @param type    类型
     * @param md5     文件md5值
     * @param format  文件格式
     * @return 文件地址
     */
    public static String download(String urlPath, String type, String md5, String format) {
        String typePath = UploadUtils.getTypePath(type);
        String filePath = "";

        BufferedInputStream bis = null;
        BufferedOutputStream bos = null;
        File file = null;
        try {
            //输入流获取
            HttpURLConnection httpURLConnection = getConnection(urlPath);
            httpURLConnection.connect();
            bis = new BufferedInputStream(httpURLConnection.getInputStream());
            //输出流文件获取
            filePath = String.format("%s/%s%s", typePath, md5, (StringUtils.isNotBlank(format) ? "." + format : ""));
            String uploadPath = UploadUtils.getUploadPath(filePath);
            file = new File(uploadPath);
            bos = new BufferedOutputStream(new FileOutputStream(file));
            //copy数据
            int len = 2048;
            byte[] b = new byte[len];
            while ((len = bis.read(b)) != -1) {
                bos.write(b, 0, len);
            }
            //关闭流
            bos.flush();
            bis.close();
            httpURLConnection.disconnect();
            //若果文件无后缀，增加文件后缀
            if (StringUtils.isBlank(format)) {
                format = FFmpegUtils.getFormat(uploadPath);
                if (StringUtils.isNotBlank(format)) {
                    File dest = new File(uploadPath + "." + format);
                    FileCopyUtils.copy(file, dest);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (bis != null) {
                    bis.close();
                }
                if (bos != null) {
                    bos.close();
                }
            } catch (IOException ioException) {
                ioException.printStackTrace();
            }
            if (file != null) {
                //无类型,删除数据
                if (StringUtils.isBlank(format)) {
                    file.delete();
                    return "";
                }
                //名称不包含类型，删除文件，返回地址增加类型
                if (!filePath.contains(format)) {
                    file.delete();
                    filePath += "." + format;
                }
            }
        }


        return filePath;
    }

    /**
     * 获取  http connection
     *
     * @param urlPath 地址
     * @throws IOException 异常
     */
    public static HttpURLConnection getConnection(String urlPath) throws IOException {
        URL url = new URL(urlPath);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        String location = findRedirectUrl(connection);
        if ("".equals(location)) {
            return connection;
        }
        return getConnection(location);
    }

    /**
     * 获取 redirect url
     *
     * @param connection 返回数据
     * @return 连接地址
     */
    private static String findRedirectUrl(HttpURLConnection connection) {
        try {
            if (List.of(HttpStatus.MOVED_PERMANENTLY.value(), HttpStatus.FOUND.value())
                    .contains(connection.getResponseCode())) {
                return connection.getHeaderField("Location");
            }
        } catch (IOException ignored) {
        }
        return "";
    }

    /**
     * 文件下载
     *
     * @param uploadUrl 文件服务器地址
     * @param filename  文件名称
     * @throws IOException 错误
     */
    public static void download(String uploadUrl, String filename, HttpServletResponse response) throws IOException {
        if (uploadUrl.contains("..")) {
            throw new CustomException("下载路径不合法");
        }
        // 设置contentType，即告诉客户端所发送的数据属于什么类型
//        response.setHeader("Content-type", Files.probeContentType(Paths.get(uploadUrl)));
        log.info("download file path : {}", uploadUrl);
        String contentType = Files.probeContentType(Paths.get(uploadUrl));
        if (null == contentType) {
            if (uploadUrl.contains(".xlsx")) {
                contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            } else {
                contentType = new MimetypesFileTypeMap().getContentType(new File(uploadUrl));
            }
        }
        log.info("download file content type : {}", contentType);
        response.setContentType(contentType);
        // 设置编码
//        filename = new String(filename.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
        // 设置扩展头，当Content-Type 的类型为要下载的类型时 , 这个信息头会告诉浏览器这个文件的名字和类型。
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, StandardCharsets.UTF_8).replaceAll("\\+", "%20"));
        // 发送给客户端的数据
        OutputStream outputStream = response.getOutputStream();
        byte[] buff = new byte[1024];
        BufferedInputStream bis;
        // 读取filename
        bis = new BufferedInputStream(new FileInputStream(new File(uploadUrl)));
        int i = bis.read(buff);
        while (i != -1) {
            outputStream.write(buff, 0, i);
            outputStream.flush();
            i = bis.read(buff);
        }
        bis.close();
    }
}
