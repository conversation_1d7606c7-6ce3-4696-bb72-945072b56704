package com.overseas.common.utils;

import java.security.MessageDigest;
import java.util.zip.CRC32;

public class HashUtil {
	public static long crc32(String s) {
		CRC32 crc = new CRC32();
		crc.update(s.getBytes());
		return crc.getValue();
	}

	public static String sha1(String s, boolean isUpperCase) {
		try {
			if (s == null)
				return null;
			MessageDigest md = MessageDigest.getInstance("SHA-1");
			byte[] digest = md.digest(s.getBytes("UTF-8"));
			return ByteConverterUtils.byteToHex(digest, isUpperCase);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	public static String md5(String input, boolean isUpperCase) {
		try {
			if (input == null)
				return null;
			MessageDigest md = MessageDigest.getInstance("MD5");
			byte[] digest = md.digest(input.getBytes("UTF-8"));
			return ByteConverterUtils.byteToHex(digest, isUpperCase);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}
}