package com.overseas.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.overseas.common.exception.CustomException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.*;

@Component
@Slf4j
@RequiredArgsConstructor
public class HttpUtils {

    public static final RestTemplate restTemplate = new RestTemplateBuilder().setConnectTimeout(Duration.ofSeconds(60))
            .setReadTimeout(Duration.ofSeconds(60)).build();

    /**
     * 发送http get 请求
     *
     * @param url           参数地址
     * @param requestParams 请求参数
     * @return 返回数据
     */
    public static String get(String url, Map<String, Object> requestParams) {
        return get(url, requestParams, null);
    }

    /**
     * 发送http get 请求
     *
     * @param url           参数地址
     * @param requestParams 请求参数
     * @param headerMap     请求header
     * @return 返回数据
     */
    public static String get(String url, Map<String, Object> requestParams, Map<String, Object> headerMap) {
        return request(HttpMethod.GET, url, requestParams, headerMap);
    }

    /**
     * 发送http get 请求
     *
     * @param url           参数地址
     * @param requestParams 请求参数
     * @param headerMap     请求header
     * @return 返回数据
     */
    public static String delete(String url, Map<String, Object> requestParams, Map<String, Object> headerMap) {
        return request(HttpMethod.DELETE, url, requestParams, headerMap);
    }

    /**
     * 发送http post 请求
     *
     * @param url           参数地址
     * @param requestParams 请求参数
     * @return 返回数据
     */
    public static String post(String url, Map<String, Object> requestParams) {
        return post(url, requestParams, null);
    }

    /**
     * 发送http post 请求
     *
     * @param url           参数地址
     * @param requestParams 请求参数
     * @return 返回数据
     */
    public static String postObject(String url, Object requestParams, Map<String, Object> headerMap) {
        try {
            log.info("POST接口：{}，header: {},参数：{}", url, JSONObject.toJSONString(headerMap), JSONObject.toJSONString(requestParams));
            HttpHeaders requestHeaders = new HttpHeaders();
            setHeaderMap(requestHeaders, headerMap);
            //默认设置 APPLICATION_JSON
            if (null == requestHeaders.getContentType()) {
                requestHeaders.setContentType(MediaType.APPLICATION_JSON);
            }
            ResponseEntity<String> responseEntity;
            HttpEntity<String> formEntity = new HttpEntity<>(JSONObject.toJSONString(requestParams), requestHeaders);
            responseEntity = restTemplate.postForEntity(url, formEntity, String.class);
            if (HttpStatus.OK.equals(responseEntity.getStatusCode())) {
                log.info("返回结果：" + responseEntity.getBody());
                return responseEntity.getBody();
            } else {
                log.error("返回结果：" + responseEntity.getBody());
                throw new CustomException("请求部分接口出错，请联系研发人员");
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new CustomException("请求部分接口出错，请联系研发人员");
        }
    }

    /**
     * 发送http post 请求
     *
     * @param url           参数地址
     * @param requestParams 请求参数
     * @param headerMap     请求header
     * @return 返回数据
     */
    public static String post(String url, Map<String, Object> requestParams, Map<String, Object> headerMap) {
        ResponseEntity<String> responseEntity = postRequest(url, requestParams, headerMap);
        return responseEntity.getBody();
    }

    /**
     * 获取返回对象操作
     *
     * @param url           地址
     * @param requestParams 请求参数
     * @param headerMap     请求header
     * @return 返回数据
     */
    public static ResponseEntity<String> postGetResponse(String url, Map<String, Object> requestParams, Map<String, Object> headerMap) {
        return postRequest(url, requestParams, headerMap);
    }

    /**
     * 发送http post 请求
     *
     * @param url           参数地址
     * @param requestParams 请求参数
     * @param headerMap     请求header
     * @return 返回数据
     */
    private static ResponseEntity<String> postRequest(String url, Map<String, Object> requestParams, Map<String, Object> headerMap) {
        try {
            log.info("POST接口：{}，header: {},参数：{}", url, JSONObject.toJSONString(headerMap), JSONObject.toJSONString(requestParams));
            HttpHeaders requestHeaders = new HttpHeaders();
            setHeaderMap(requestHeaders, headerMap);
            //默认设置 APPLICATION_JSON
            if (null == requestHeaders.getContentType()) {
                requestHeaders.setContentType(MediaType.APPLICATION_JSON);
            }
            ResponseEntity<String> responseEntity;
            if (List.of(MediaType.MULTIPART_FORM_DATA, MediaType.APPLICATION_FORM_URLENCODED).contains(requestHeaders.getContentType())) {
                HttpEntity<MultiValueMap<String, Object>> formEntity = new HttpEntity<>(multiValueMap(requestParams, new LinkedMultiValueMap<>(), ""), requestHeaders);
                responseEntity = restTemplate.postForEntity(url, formEntity, String.class);
            } else {
                HttpEntity<String> formEntity = new HttpEntity<>(JSONObject.toJSONString(requestParams), requestHeaders);
                responseEntity = restTemplate.postForEntity(url, formEntity, String.class);
            }
            if (HttpStatus.OK.equals(responseEntity.getStatusCode())) {
                log.info("返回结果：{}", JSONObject.toJSONString(responseEntity.getBody()));
                return responseEntity;
            } else {
                log.error("返回结果：{}", JSONObject.toJSONString(responseEntity.getBody()));
                throw new CustomException("请求部分接口出错，请联系研发人员");
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new CustomException("请求部分接口出错，请联系研发人员");
        }
    }

    /**
     * 参数生成
     *
     * @return 返回数据
     */
    private static String urlParam(Map<String, Object> requestParams) {
        if (null == requestParams) {
            return "";
        }
        List<String> urlParamsString = new ArrayList<>();
        requestParams.forEach((key, value) -> {
            String urlParamString = key + "=";
            if (value instanceof Collection) {
                urlParamString += JSONObject.toJSONString(value);
            } else {
                urlParamString += value.toString();
            }
            urlParamsString.add(urlParamString);
        });
        return StringUtils.join(urlParamsString, "&");
    }

    /**
     * 发起请求公共方法
     *
     * @param httpMethod    请求类型
     * @param url           接口地址
     * @param requestParams 参数
     * @param headerMap     请求头信息
     * @return 结果
     */
    private static String request(HttpMethod httpMethod, String url, Map<String, Object> requestParams,
                                  Map<String, Object> headerMap) {
        RestTemplate restTemplate = new RestTemplateBuilder().setConnectTimeout(Duration.ofSeconds(30))
                .setReadTimeout(Duration.ofSeconds(30)).build();
        StringBuilder requestUrl = new StringBuilder(url);
        if (MapUtils.isNotEmpty(requestParams)) {
            requestUrl.append("?").append(urlParam(requestParams));
        }
        log.info("请求地址：{}", requestUrl);
        log.info("请求类型：{}", httpMethod.toString());
        try {
            HttpHeaders requestHeaders = new HttpHeaders();
            setHeaderMap(requestHeaders, headerMap);
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestParams, requestHeaders);
            ResponseEntity<String> responseEntity = restTemplate.exchange(requestUrl.toString(),
                    httpMethod, requestEntity, String.class);
            log.info("返回结果状态：" + responseEntity.getStatusCode().value());
            if (HttpStatus.OK.equals(responseEntity.getStatusCode())) {
                log.info("返回结果：" + responseEntity.getBody());
                return responseEntity.getBody();
            } else {
                log.error("返回结果：" + responseEntity.getBody());
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new CustomException("请求部分接口出错，请联系研发人员");
        }
        return null;
    }

    /**
     * 设置请求头内容
     *
     * @param requestHeaders http请求头对象
     * @param headerMap      请求内容
     */
    private static void setHeaderMap(HttpHeaders requestHeaders, Map<String, Object> headerMap) {
        if (null != headerMap && !headerMap.isEmpty()) {
            headerMap.forEach((k, v) -> requestHeaders.set(k, v.toString()));
        }
    }

    /**
     * 给参数加上 MultiValueMap
     *
     * @param maps 参数
     * @return 返回 MultiValueMap 对象
     */
    private static MultiValueMap<String, Object> multiValueMap(Map<String, Object> maps, MultiValueMap<String, Object> linkedMultiValueMap, String fUk) {
        maps.forEach((k, v) -> {
            if (StringUtils.isNotBlank(fUk)) {
                k = String.format("%s[%s]", fUk, k);
            }
            if (v instanceof String || v instanceof Number || v instanceof Boolean) {
                linkedMultiValueMap.add(k, v);
            } else if (v instanceof Map) {
                multiValueMap((Map<String, Object>) v, linkedMultiValueMap, k);
            } else if (v instanceof Collection) {
                Object[] cv = ((Collection) v).toArray();
                for (int i = 0; i < cv.length; i++) {
                    int finalI = i;
                    String finalK = String.format("%s[%s]", k, i);
                    multiValueMap(new HashMap<>() {{
                        put(finalK, cv[finalI]);
                    }}, linkedMultiValueMap, "");
                }
            } else {
                Map<String, Object> objectMap = ObjectUtils.toMap(v);
                if (null != objectMap) {
                    multiValueMap(objectMap, linkedMultiValueMap, k);
                } else {
                    linkedMultiValueMap.add(k, v);
                }
            }
        });
        return linkedMultiValueMap;
    }
}
