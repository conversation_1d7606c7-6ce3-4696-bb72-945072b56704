package com.overseas.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class HumpLineUtils {

    private static Pattern linePattern = Pattern.compile("_(\\w)");

    private static Pattern humpPattern = Pattern.compile("[A-Z]");

    /**
     * 下划线转驼峰
     */
    public static String lineToHump(String str) {
        str = str.toLowerCase();
        Matcher matcher = linePattern.matcher(str);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, matcher.group(1).toUpperCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    /**
     * 驼峰转下划线(简单写法，效率低于humpToLine2
     */
    public static String humpToLine(String str) {
        return str.replaceAll("[A-Z]", "_$0").toLowerCase();
    }

    /**
     * 驼峰转下划线,效率比上面高
     */
    public static String humpToLine2(String str) {
        if (StringUtils.isBlank(str)) {
            return str;
        }
        Matcher matcher = humpPattern.matcher(str);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, "_" + matcher.group(0).toLowerCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    /**
     * 驼峰转下划线(简单写法，效率低于humpToLine2
     */
    public static String humpNumberToLine(String str) {
        return HumpLineUtils.humpToLine(str.replaceAll("([a-z])(\\d+[A-z])", "$1_$2").replaceAll("([a-z])(\\d+)\\$S", "$1_$2")).toLowerCase();
    }

    /**
     * 第一个字母大些
     *
     * @param str
     * @return 返回数据
     */
    public static String firstToUpper(String str) {
        if (StringUtils.isBlank(str)) {
            return str;
        }
        String res = str.substring(0, 1).toUpperCase();
        if (str.length() > 1) {
            res += str.substring(1);
        }
        return res;
    }

}
