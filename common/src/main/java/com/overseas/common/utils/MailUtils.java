package com.overseas.common.utils;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeUtility;
import java.io.File;
import java.util.List;

@Component
@Slf4j
public class MailUtils {
    private static String from;

    @Value("${spring.mail.username}")
    public void setFrom(String from) {
        MailUtils.from = from;
    }

    @Autowired
    private static JavaMailSender mailSender;

    /**
     * 单个附件
     *
     * @param to       to
     * @param subject  主题
     * @param content  内容
     * @param filePath 文件地址
     */
    public static void sendHtmlEmail(List<String> to, String subject, String content, String filePath) {
        if (null == mailSender) {
            mailSender = SpringContextUtils.getContext().getBean(JavaMailSender.class);
        }
        if (to.isEmpty()) {
            log.info("收件人为空");
        } else {
            MimeMessage message = mailSender.createMimeMessage();
            try {
                String[] toAddress = String.join(",", to).split(",");
                MimeMessageHelper helper = new MimeMessageHelper(message, true);
                helper.setFrom(from);
                helper.setTo(toAddress);
                helper.setSubject(subject);
                helper.setText(content, true);
                // 有附件
                if (StringUtils.isNotBlank(filePath)) {
                    //对文件名进行编码，防止出现乱码
                    try {
                        File file = new File(filePath);
                        helper.addAttachment(MimeUtility.encodeWord(file.getName()), file);
                    } catch (Exception e) {
                        log.info(e.getMessage());
                    }
                }
                mailSender.send(message);
                log.info("发送邮件成功；收件人：{}，标题：{}，内容：{}", to, subject, content);
            } catch (MessagingException e) {
                log.error("发送HTML邮件失败！", e);
            }
        }
    }

    /**
     * 单个附件
     *
     * @param to       to
     * @param subject  主题
     * @param content  内容
     * @param filePaths 文件地址
     */
    public static void sendHtmlEmailMultiple(List<String> to, String subject, String content, List<String> filePaths) {
        if (null == mailSender) {
            mailSender = SpringContextUtils.getContext().getBean(JavaMailSender.class);
        }
        if (to.isEmpty()) {
            log.info("收件人为空");
        } else {
            MimeMessage message = mailSender.createMimeMessage();
            try {
                String[] toAddress = String.join(",", to).split(",");
                MimeMessageHelper helper = new MimeMessageHelper(message, true);
                helper.setFrom(from);
                helper.setTo(toAddress);
                helper.setSubject(subject);
                helper.setText(content, true);
                // 有附件
                if (CollectionUtils.isNotEmpty(filePaths)) {
                    filePaths.forEach(filePath -> {
                        //对文件名进行编码，防止出现乱码
                        try {
                            File file = new File(filePath);
                            helper.addAttachment(MimeUtility.encodeWord(file.getName()), file);
                        } catch (Exception e) {
                            log.info(e.getMessage());
                        }
                    });
                }
                mailSender.send(message);
                log.info("发送邮件成功；收件人：{}，标题：{}，内容：{}", to, subject, content);
            } catch (MessagingException e) {
                log.error("发送HTML邮件失败！", e);
            }
        }
    }
}
