package com.overseas.common.utils;

import com.overseas.common.exception.CustomException;
import org.apache.commons.codec.digest.DigestUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

public class Md5CalculateUtils {

    /**
     * 获取一个文件的md5值(可处理大文件)
     */

    public static String getFileMD5(String  file) {
        FileInputStream fileInputStream = null;
        try {
            fileInputStream = new FileInputStream(new File(file));
            return DigestUtils.md5Hex(fileInputStream);
        } catch (Exception e) {
            e.printStackTrace();
            throw new CustomException(String.format("文件地址 %s 获取MD5失败", file));
        } finally {
            try {
                if (fileInputStream != null) {
                    fileInputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 求一个字符串的md5值
     *
     * @param target 字符串
     * @return md5 value
     */
    public static String getStringMd5(String target) {
        return DigestUtils.md5Hex(target);
    }

    /**
     * 求 url 内容的md5
     *
     * @param path 地址
     * @return md5
     */
    public static String getUrlMD5(String path) {
        InputStream inputStream = null;
        try {
            URL url = new URL(path);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(10 * 1000);
            connection.setRequestProperty("User-Agent",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36");
            inputStream = connection.getInputStream();
            return DigestUtils.md5Hex(inputStream);
        } catch (IOException e) {
            e.printStackTrace();
            throw new CustomException(String.format("文件地址 %s 获取MD5失败", path));
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException ignored) {
            }
        }
    }
}
