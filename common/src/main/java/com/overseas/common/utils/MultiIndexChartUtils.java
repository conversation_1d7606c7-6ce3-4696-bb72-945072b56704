package com.overseas.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.overseas.common.dto.chart.LegendDTO;
import com.overseas.common.dto.chart.MultiIndexChartDTO;
import com.overseas.common.dto.chart.SeriesDTO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@Component
public class MultiIndexChartUtils {


    public static MultiIndexChartDTO initChart(List<?> data, String indicator, String comparisonIndicator, Map<String, String> keyMap, String xAxisName) {

        if (data.isEmpty()) {
            return new MultiIndexChartDTO();
        }
        List<JSONObject> jsonData = data.stream().map(u -> JSONObject.parseObject(JSONObject.toJSONString(u))).collect(Collectors.toList());
        MultiIndexChartDTO multiIndexChartDTO = new MultiIndexChartDTO();
        Map<String, JSONObject> dataMap = jsonData.stream().collect(Collectors.toMap(u -> u.getString(xAxisName), Function.identity(), (o, n) -> n));
        List<String> xAxisList = dataMap.keySet().stream().sorted(Comparator.comparingInt(Integer::parseInt)).collect(Collectors.toList());
        SeriesDTO indicatorSeries = new SeriesDTO(keyMap.get(indicator), "line", new ArrayList<>());
        SeriesDTO comparisonIndicatorSeries = new SeriesDTO(keyMap.get(comparisonIndicator), "line", new ArrayList<>());
        xAxisList.forEach(xAxis -> {
            JSONObject jsonObject = dataMap.get(xAxis);
            indicatorSeries.getData().add(jsonObject.getDoubleValue(indicator));
            comparisonIndicatorSeries.getData().add(jsonObject.getDoubleValue(comparisonIndicator));
        });
        multiIndexChartDTO.setSeries(List.of(indicatorSeries, comparisonIndicatorSeries));
        multiIndexChartDTO.setXAxis(List.of(new LegendDTO(xAxisList.stream().map(u -> {
            if (u.length() < 3) {
                return DateUtils.hour2Name(Integer.parseInt(u));
            }
            return DateUtils.long2String(Long.parseLong(u));
        }).collect(Collectors.toList()))));
        multiIndexChartDTO.setLegend(new LegendDTO(List.of(indicatorSeries.getName(), comparisonIndicatorSeries.getName())));
        return multiIndexChartDTO;
    }
}
