package com.overseas.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.overseas.common.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class ObjectUtils {

    private static final Pattern underlinePattern = Pattern.compile("_(\\w)");

    private static final Pattern camelPattern = Pattern.compile("[A-Z]");

    /**
     * Object 转 Map 通用方法
     * 注：未去除为null的字段
     *
     * @param object Object
     * @return 返回数据
     */
    public static Map<String, Object> toMap(Object object) {
        return JSONObject.parseObject(JSONObject.toJSONString(object, SerializerFeature.WRITE_MAP_NULL_FEATURES));
    }

    /**
     * Object 转 Map 通用方法
     * 注：未去除为null的字段
     *
     * @param object Object
     * @return 返回数据
     */
    public static JSONObject toJSONObject(Object object) {
        return JSONObject.parseObject(JSONObject.toJSONString(object, SerializerFeature.WRITE_MAP_NULL_FEATURES));
    }

    /**
     * copy数据
     *
     * @param obj            对象
     * @param tTypeReference 类型
     * @param <T>            类型
     * @return 返回结果
     */
    public static <T> T copyOf(Object obj, TypeReference<T> tTypeReference) {
        return JSONObject.parseObject(JSONObject.toJSONString(obj), tTypeReference);
    }

    /**
     * Object转Map null 字段不加入map
     *
     * @param object object
     * @return map
     */
    public static Map<String, Object> toMapWithoutNull(Object object) {
        return JSONObject.parseObject(JSONObject.toJSONString(object));
    }

    /**
     * 判断是否时 null 或者 0
     *
     * @param obj 被判断数据
     * @return 返回数据
     */
    public static boolean isNullOrZero(Object obj) {
        if (null == obj) {
            return true;
        }
        if (obj instanceof Long) {
            return obj.equals(0L);
        }
        if (obj instanceof String) {
            return "0".equals(obj);
        }
        if (obj instanceof Byte) {
            return obj.equals((byte) 0);
        }
        if (obj instanceof BigDecimal) {
            return BigDecimal.ZERO.equals(obj);
        }
        if (obj instanceof Double) {
            return Math.abs((Double) obj) < 0.000001;
        }
        return obj.equals(0);
    }

    /**
     * 判断是否不是 null 或者 0
     *
     * @param obj 判断数据
     * @return 返回数据
     */
    public static boolean isNotNullOrZero(Object obj) {
        return !ObjectUtils.isNullOrZero(obj);
    }

    /**
     * 移除类属性，不推荐使用
     *
     * @param model 对象
     * @param name  要移除的属性名
     */
    public static void removeObjectField(Object model, String name) {
        try {
            Field field = model.getClass().getDeclaredField(name);
            field.setAccessible(true);
            field.set(model, null);
        } catch (NoSuchFieldException e) {
            log.info("no such field : {}, message : {}", name, e.getMessage());
        } catch (IllegalAccessException e) {
            log.info("illegal access : {}, message : {}", name, e.getMessage());
        }
    }

    /**
     * 根据名称获取Object 字段 value
     *
     * @param model clazz
     * @param name  字段名称
     * @return 返回数据
     */
    public static Object getObjectValue(Object model, String name) {
        Object value;
        try {
            Method method = model.getClass()
                    .getMethod("get" + name.substring(0, 1).toUpperCase() + name.substring(1));
            value = method.invoke(model);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
        return value;
    }

    /**
     * 根据名称设置Object字段
     *
     * @param model clazz
     * @param name  字段名称
     */
    public static void setObjectValue(Object model, String name, Object value) {
        try {
            Method method = model.getClass().getMethod("set" + name.substring(0, 1).toUpperCase() + name.substring(1), value.getClass());
            method.invoke(model, value);
        } catch (Exception e) {
            log.info("set object value error : {}, info : {}", name, e.getMessage());
            throw new CustomException("数据转换错误，请确认后再试");
        }
    }

    /**
     * 驼峰转为带下划线的字符串（AaB => Aa_b）
     *
     * @param value 字符串
     * @return 下划线字符串
     */
    public static String camelToUnderline(String value) {

        if (StringUtils.isEmpty(value)) return "";
        Matcher matcher = camelPattern.matcher(value);
        StringBuilder sb = new StringBuilder();
        while (matcher.find()) {
            matcher.appendReplacement(sb, "_" + matcher.group(0).toLowerCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    /**
     * 下划线转驼峰 （Aa_b => AaB）
     *
     * @param value 字符串
     * @return 驼峰字符串
     */
    public static String underlineToCamel(String value) {
        if (StringUtils.isEmpty(value)) return "";
        Matcher matcher = underlinePattern.matcher(value.toLowerCase());
        StringBuilder sb = new StringBuilder();
        while (matcher.find()) {
            matcher.appendReplacement(sb, matcher.group(1).toUpperCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    /**
     * 千分符格式
     *
     * @param text 内容
     * @return 千分符的数字
     */
    public static String fmtMicrometer(String text) {
        DecimalFormat df;
        if (text.indexOf(".") > 0) {
            int len = text.length() - text.indexOf(".");
            if (len - 1 == 0) {
                df = new DecimalFormat("###,##0.");
            } else if (len - 1 == 1) {
                df = new DecimalFormat("###,##0.0");
            } else if (len - 1 == 2) {
                df = new DecimalFormat("###,##0.00");
            } else {
                df = new DecimalFormat("###,##0.000");
            }
        } else {
            df = new DecimalFormat("###,##0");
        }
        double number;
        try {
            number = Double.parseDouble(text);
        } catch (Exception e) {
            number = 0.0;
        }
        return df.format(number);
    }

    /**
     * 判断某个字段是否是对象的属性
     *
     * @param field  字段
     * @param object 对象
     * @return true:是，false:否
     */
    public static boolean isExistField(String field, Object object) {
        if (null == object || StringUtils.isEmpty(field)) {
            return false;
        }
        Object o = JSON.toJSON(object);
        JSONObject jsonObject = new JSONObject();
        if (o instanceof JSONObject) {
            jsonObject = (JSONObject) o;
        }
        return jsonObject.containsKey(field);
    }

    /**
     * 将Object类型List转为List类型
     *
     * @param object 传入对象
     * @param clazz  类
     * @param <T>    类
     * @return 返回List数据
     */
    public static <T> List<T> toList(Object object, Class<T> clazz) {
        List<T> result = new ArrayList<>();
        if (object instanceof List<?>) {
            for (Object obj : (List<?>) object) {
                result.add(clazz.cast(obj));
            }
            return result;
        }
        return null;
    }

    public static List<Field> getFields(Class clazz) {
        List<Field> fieldList = new ArrayList<>();
        while (clazz != null) {
            Field[] fields = clazz.getDeclaredFields();
            fieldList.addAll(Arrays.asList(fields));
            clazz = clazz.getSuperclass();
        }
        return fieldList;
    }

    /**
     * 判断是否-1 全部
     *
     * @param data 数据
     * @return 返回数据
     */
    public static boolean isAll(Object data) {
        try {
            return null == data || -1 == Integer.parseInt(data.toString());
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 判断是否非-1 全部
     *
     * @param data 数据
     * @return 返回睡觉
     */
    public static boolean isNotAll(Object data) {
        return !ObjectUtils.isAll(data);
    }

    /**
     * 科学计数法转string
     *
     * @param value 数据
     * @return 返回数据
     */
    public static String translateScientificNotationNum(String value) {

        if (!Pattern.matches("^((\\d+\\.?\\d+)[Ee][-+](\\d+))$", value)) {
            return value;
        }
        DecimalFormat df = new DecimalFormat("#.###");
        return df.format(Double.parseDouble(value));
    }

    /**
     * 两个对象数据相加
     *
     * @param object1 对象1
     * @param object2 对象2
     * @return 返回数据
     */
    public static Object getObjectAddition(Object object1, Object object2) {
        if (object1 == null || object2 == null) {
            return 0;
        }
        BigDecimal decimal = new BigDecimal(object1.toString()).add(new BigDecimal(object2.toString()));
        if (object1 instanceof Double && object2 instanceof Double) {
            return decimal.doubleValue();
        }
        if (object1 instanceof Long && object2 instanceof Long) {
            return decimal.longValue();
        }
        return 0;
    }

    public static Object getObjectCalculatedResult(Object object1, Object object2, String type) {
        return ObjectUtils.getObjectCalculatedResult(object1, object2, type, 2);
    }


    /**
     * 两个对象数据相加
     *
     * @param object1 对象1
     * @param object2 对象2
     * @return 返回数据
     */
    public static Object getObjectCalculatedResult(Object object1, Object object2, String type, int round) {
        if (null == object1) {
            object1 = "0";
        }
        if (null == object2) {
            object2 = "0";
        }
        BigDecimal decimal = new BigDecimal("0");
        switch (type) {
            case "+":
                decimal = new BigDecimal(object1.toString()).add(new BigDecimal(object2.toString()));
                break;
            case "-":
                decimal = new BigDecimal(object1.toString()).subtract(new BigDecimal(object2.toString()));
                break;
            case "*":
                decimal = new BigDecimal(object1.toString()).multiply(new BigDecimal(object2.toString()))
                        .setScale(round, RoundingMode.HALF_UP);
                break;
            case "/":
                if (Double.valueOf(object2.toString()).equals(0D)) {
                    return 0D;
                }
                decimal = new BigDecimal(object1.toString()).divide(new BigDecimal(object2.toString()), round, RoundingMode.HALF_UP);
                break;
            default:
                break;
        }
        if (object1 instanceof Double || object2 instanceof Double || type.equals("/")) {
            return decimal.doubleValue();
        }
        if (object1 instanceof Long && object2 instanceof Long) {
            return decimal.longValue();
        }
        if (object1 instanceof BigDecimal) {
            return decimal;
        }
        if (object1 instanceof String) {
            return decimal.toPlainString();
        }
        return 0;
    }
}
