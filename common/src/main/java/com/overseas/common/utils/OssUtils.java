package com.overseas.common.utils;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.PutObjectRequest;
import com.overseas.common.config.OssBucketConfig;
import com.overseas.common.config.OssConfig;
import com.overseas.common.enums.OssBucketTypeEnum;
import com.overseas.common.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;

/**
 * 阿里云OSS服务工具类
 */
@Component
@Slf4j
public class OssUtils {
    private static OssConfig ossConfig;

    @Autowired
    public void setOssConfig(OssConfig ossConfig) {
        OssUtils.ossConfig = ossConfig;
    }

    private static OssBucketConfig getOssBucketConfig(OssBucketTypeEnum bucketTypeEnum) {
        switch (bucketTypeEnum) {
            case MATERIAL:
                return ossConfig.getBucketMaterial();
            case LABEL:
                return ossConfig.getBucketLabel();
            default:
                throw new CustomException("无此类型对象容器");
        }
    }

    public static boolean uploadFile(String filePath, String fileName, OssBucketTypeEnum bucketTypeEnum) {
        boolean result = false;
        // 创建OSSClient实例。
        OssBucketConfig ossBucketConfig = getOssBucketConfig(bucketTypeEnum);
        OSS ossClient = new OSSClientBuilder().build(ossBucketConfig.getEndpoint(), ossBucketConfig.getAccessKeyId(),
                ossBucketConfig.getAccessKeySecret());
        try {
            // 创建PutObjectRequest对象。
            // 加入环境路径
            String realFileName = ossBucketConfig.getEnvType() + fileName;
            PutObjectRequest putObjectRequest = new PutObjectRequest(ossBucketConfig.getBucketName(), realFileName,
                    new File(filePath));
            // 如果需要上传时设置存储类型和访问权限，请参考以下示例代码。
            // ObjectMetadata metadata = new ObjectMetadata();
            // metadata.setHeader(OSSHeaders.OSS_STORAGE_CLASS, StorageClass.Standard.toString());
            // metadata.setObjectAcl(CannedAccessControlList.Private);
            // putObjectRequest.setMetadata(metadata);

            // 上传文件。
            ossClient.putObject(putObjectRequest);
            result = true;
        } catch (OSSException ossException) {
            log.error("Caught an OSSException, which means your request made it to OSS, but was rejected with an error response for some reason.\n" +
                    "Error Message: {}", ossException.getMessage());
//            throw ossException;
        } catch (ClientException clientException) {
            log.error("Caught an ClientException, which means the client encountered a serious internal problem while trying to communicate with OSS, " +
                    "such as not being able to access the network.\n" +
                    "Error Message: {}", clientException.getMessage());
//            throw clientException;
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return result;
    }
}
