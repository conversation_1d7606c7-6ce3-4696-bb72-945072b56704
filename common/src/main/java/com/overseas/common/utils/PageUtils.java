package com.overseas.common.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PageUtils<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 每页记录数（分页大小）
     */
    private Long pageNum;

    /**
     * 总页数
     */
    private Long totalPage;

    /**
     * 当前页
     */
    private Long page;

    /**
     * 列表数据
     */
    private List<T> data;

    /**
     * 构造函数一
     * 传入数据列表、总记录数、分页大小、当前页
     *
     * @param list    数据列表
     * @param total   总记录数
     * @param pageNum 分页大小
     * @param page    当前页
     */
    public PageUtils(List<T> list, Long total, Long pageNum, Long page) {
        this.data = list;
        this.total = total;
        this.pageNum = pageNum;
        this.page = page;
        this.totalPage = (long) Math.ceil((double) total / pageNum);
    }

    /**
     * 构造函数二
     * 传入MyBatis-Plus 中的IPage对象
     *
     * @param page IPage对象
     */
    public PageUtils(IPage<T> page) {
        this.data = page.getRecords();
        this.total = page.getTotal();
        this.pageNum = page.getSize();
        this.page = page.getCurrent();
        this.totalPage = page.getPages();
    }

    /**
     * 构造函数三，返回空结构
     *
     * @param page    页码
     * @param pageNum 每页记录数
     */
    public PageUtils(Long page, Long pageNum) {
        this.data = Collections.emptyList();
        this.total = 0L;
        this.page = page;
        this.pageNum = pageNum;
        this.totalPage = 0L;
    }

    /**
     * 构造函数四，返回前端所需数据和总数
     *
     * @param data  页码
     * @param total 每页记录数
     */
    public PageUtils(List<T> data, Long total) {
        this.data = data;
        this.total = total;
    }

    /**
     * 组合总计到分页数据中
     *
     * @param page      分页数据
     * @param totalData 总计数据
     */
    public PageUtils(IPage<T> page, T totalData) {
        List<T> pageData = new ArrayList<>();
        if (null != totalData) {
            pageData.add(totalData);
        }
        pageData.addAll(page.getRecords());
        this.data = pageData;
        this.total = page.getTotal();

    }

    /**
     * 手动分页
     *
     * @param data    全部结果
     * @param page    页数
     * @param pageNum 分页数
     */
    public PageUtils(List<T> data, Long page, Long pageNum) {
        if (!CollectionUtils.isEmpty(data)) {
            if (data.size() < (page - 1) * pageNum) {
                this.data = List.of();
            } else {
                this.data = data.subList((int) Math.max(0, (page - 1) * pageNum), (int) Math.min(data.size(), page * pageNum));
            }
            this.total = (long) data.size();
        } else {
            this.data = List.of();
            this.total = 0L;
        }
    }
}
