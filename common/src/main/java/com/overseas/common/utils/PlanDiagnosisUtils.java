package com.overseas.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class PlanDiagnosisUtils {

    public static String httpUrl;

    @Value("${market-service.url}")
    public void setHttpUrl(String httpUrl) {
        PlanDiagnosisUtils.httpUrl = httpUrl;
    }

    public static Map<String, Object> planDiagnosis(Map<String, Object> params) {
        return JSONObject.parseObject(HttpUtils.get(httpUrl + "/order/check", params));
    }

    public static <T> T diagnosisList(Map<String, Object> params, TypeReference<T> tTypeReference) {
        return JSONObject.parseObject(HttpUtils.post(httpUrl + "/traffic/req/data", params), tTypeReference);
    }

    public static <T> T diagnosisData(Map<String, Object> params, TypeReference<T> tTypeReference) {
        return JSONObject.parseObject(HttpUtils.post(httpUrl + "/order/check", params), tTypeReference);
    }
}
