package com.overseas.common.utils;

import com.overseas.common.entity.User;
import com.overseas.common.enums.ResultStatusEnum;
import com.overseas.common.exception.CustomException;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.AuthorizationException;

import java.util.List;

/**
 * 用户获取状态
 */
public class SecurityUserUtils {

    /**
     * 获取用户信息
     *
     * @return User 用户信息
     */
    public static User getUser() {
        User user = (User) SecurityUtils.getSubject().getPrincipal();
        if (null == user) {
            throw new CustomException(ResultStatusEnum.NO_LOGIN);
        }
        return user;
    }

    /**
     * 获取用户ID
     *
     * @return Long 用户ID
     */
    public static Long getUserId() {
        return Long.parseLong(getUser().getId().toString());
    }

    /**
     * 获取用户ID
     *
     * @return Long 用户ID
     */
    public static Integer getUserIdInt() {
        return getUser().getId();
    }


    /**
     * 检查当前登录用户是不是admin
     *
     * @return true:是，false:否
     */
//    public static boolean isAdmin() {
//        return UserTypeEnum.ADMIN.getId().equals(SecurityUserUtils.getUser().getUserType());
//    }

    /**
     * 和监测是否含有标注列表数据
     *
     * @param permissionId 权限ID
     * @return 返回数据
     */
    public static boolean checkPermission(String permissionId) {
        try {
            SecurityUtils.getSubject().checkPermission(permissionId);
        } catch (AuthorizationException e) {
            return false;
        }
        return true;
    }

    /**
     * 是否内部用户
     *
     * @param roleId 用户角色ID
     * @return 返回数据
     */
    public static boolean isInner(Integer roleId) {
        return List.of(3, 20, 21).contains(roleId);
    }

}
