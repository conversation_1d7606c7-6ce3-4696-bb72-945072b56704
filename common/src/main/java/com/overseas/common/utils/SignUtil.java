package com.overseas.common.utils;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/11/8
 **/
public class SignUtil {

    private static final String SIGN_CHARSET_UTF8 = "UTF-8";

    private static final String SIGN_METHOD_SHA256 = "HmacSHA256";

    private static final int SIGN_OXFF = 0xFF;

    private static String SIGN_API_NAME = "/general";


    /**
     * 为 cost 请求加签
     * @param bizdate 日期 yyyy-MM-dd
     * @param campaignIds campaign 列表
     * @param channel 渠道名
     * @param timestamp 时间戳
     * @param appSecret 媒体工作台显示的 token
     * @return sign 签名
     */
    public static String signCostRequest(String bizdate, List<String> campaignIds, String channel , Long timestamp, String appSecret) throws IOException {
        Map<String, Object> signParams = new HashMap<>();
        signParams.put("bizdate", bizdate);
        signParams.put("campaignIds", String.format("[%s]", String.join(",", campaignIds)));
        signParams.put("channel", channel);
        signParams.put("timestamp", timestamp);

        String[] keys = signParams.keySet().toArray(new String[0]);
        Arrays.sort(keys);
        StringBuilder query = new StringBuilder();
        query.append(SIGN_API_NAME);
        for (String key : keys) {
            String value = String.valueOf(signParams.get(key));
            query.append(key).append(value);
        }
        byte[] bytes = encryptHMACsha256(query.toString(), appSecret);
        return byte2hex(bytes);
    }

    /**
     * 为 rta api 请求加签
     * @param adid 用户广告 id: iOS 的 idfa 或者 Google 的 aaid
     * @param campaignIds campaign 列表
     * @param channel 渠道名
     * @param timestamp 时间戳
     * @param appSecret 媒体工作台显示的 token
     * @return sign 签名
     */
    public static String signApiRequest(String adid, List<String> campaignIds, String channel, Long timestamp, String appSecret) throws
            IOException {
        Map<String, String> signParams = new HashMap<>();
        signParams.put("adid", adid);
        signParams.put("campaignIds", String.format("[%s]", String.join(",", campaignIds)));
        signParams.put("channel", channel);
        signParams.put("timestamp", String.valueOf(timestamp));

        String[] keys = signParams.keySet().toArray(new String[0]);
        Arrays.sort(keys);
        StringBuilder query = new StringBuilder();
        query.append(SIGN_API_NAME);
        for (String key : keys) {
            String value = signParams.get(key);
            query.append(key).append(value);
        }
        byte[] bytes = encryptHMACsha256(query.toString(), appSecret);
        return byte2hex(bytes);
    }

    /**
     * 为 postback 请求加签
     * @param channel 渠道名
     * @param timestamp 时间戳
     * @param appSecret 媒体工作台显示的 token
     * @return sign 签名
     */
    public static String signPostbackRequest(String channel, Long timestamp, String appSecret) throws
            IOException {
        Map<String, String> signParams = new HashMap<>();
        signParams.put("channel", channel);
        signParams.put("timestamp", String.valueOf(timestamp));

        String[] keys = signParams.keySet().toArray(new String[0]);
        Arrays.sort(keys);
        StringBuilder query = new StringBuilder();
        query.append(SIGN_API_NAME);
        for (String key : keys) {
            String value = signParams.get(key);
            query.append(key).append(value);
        }
        byte[] bytes = encryptHMACsha256(query.toString(), appSecret);
        return byte2hex(bytes);
    }

    private static byte[] encryptHMACsha256(String data, String secret) throws IOException {
        byte[] bytes;
        try {
            SecretKey secretKey = new SecretKeySpec(secret.getBytes(SIGN_CHARSET_UTF8), SIGN_METHOD_SHA256);
            Mac mac = Mac.getInstance(secretKey.getAlgorithm());
            mac.init(secretKey);
            bytes = mac.doFinal(data.getBytes(SIGN_CHARSET_UTF8));
        } catch (GeneralSecurityException gse) {
            throw new IOException(gse.toString());
        }
        return bytes;
    }

    private static String byte2hex(byte[] bytes) {
        StringBuilder sign = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            String hex = Integer.toHexString(bytes[i] & SIGN_OXFF);
            if (hex.length() == 1) {
                sign.append("0");
            }
            sign.append(hex.toUpperCase());
        }
        return sign.toString();
    }
}