package com.overseas.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.overseas.common.configuration.MachineConfiguration;
import com.overseas.common.configuration.SmsConfiguration;
import com.overseas.common.enums.MachineRoomEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@Component
@RequiredArgsConstructor
@Slf4j
public class SmsUtils {

    private final SmsConfiguration smsConfiguration;

    private final MachineConfiguration machineConfiguration;

    /**
     * 发送短信
     *
     * @param title   标题
     * @param content 内容
     */
    public void sendSheinMsg(String title, String content) {
        if (StringUtils.isBlank(smsConfiguration.getPhones())) {
            log.error("shein信息发送账户为空");
            return;
        }
        List<String> receivers = Arrays.stream(smsConfiguration.getPhones().split(",")).distinct().collect(Collectors.toList());
        this.sendMsg(title, content, receivers);
    }


    /**
     * 发送短信
     *
     * @param title   标题
     * @param content 内容
     */
    public void sendSheinCrowdMsg(String title, String content) {
        if (StringUtils.isBlank(smsConfiguration.getCrowdPhones())) {
            log.error("shein人群发送账户为空");
            return;
        }
        List<String> receivers = Arrays.stream(smsConfiguration.getCrowdPhones().split(",")).distinct().collect(Collectors.toList());
        this.sendMsg(title, content, receivers);
    }

    /**
     * 发送短信
     *
     * @param title     标题
     * @param content   内容
     * @param receivers 收件人
     */
    public void sendMsg(String title, String content, List<String> receivers) {
        receivers = receivers.stream().distinct().collect(Collectors.toList());
        SmsEvent smsEvent = SmsEvent.buildSmsEvent(title, content, receivers);
        log.info("send sms message {} ，send to {}", JSONObject.toJSONString(smsEvent), JSONObject.toJSONString(receivers));
        if (!smsConfiguration.isEnable()) {
            log.info(" sms disable, return directly.");
            return;
        }
        URI uri = buildRequestUri(smsEvent);
        HttpUtils.get(uri.toString(), Map.of(), Map.of(HttpHeaders.CONNECTION, "close"));
    }

    /**
     * 发送请求
     *
     * @param smsEvent 信息
     * @return 返回数据
     */
    private URI buildRequestUri(SmsEvent smsEvent) {
        if (StringUtils.isNotBlank(smsEvent.getMsg().getTitle())) {
            if (StringUtils.isNotBlank(machineConfiguration.getMachineRoom())) {
                try {
                    MachineRoomEnum machineRoomEnum = MachineRoomEnum.get(machineConfiguration.getMachineRoom());
                    smsEvent.getMsg().setTitle(String.format("[%s]%s", machineRoomEnum.getMachineName(), smsEvent.getMsg().getTitle()));
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(smsConfiguration.getReqUrl())
                .queryParam("apikey", smsConfiguration.getApiKey())
                .queryParam("password", smsConfiguration.getPassword())
                .queryParam("templateid", smsConfiguration.getTemplateId())
                .queryParam("mobile", String.join(",", smsEvent.getReceivers()))
                .queryParam("templateparams", JSON.toJSONString(smsEvent.getMsg()));
        return builder.build().encode().toUri();
    }


    @Getter
    @Setter
    public static class SmsEvent {
        private SmsMsg msg;
        private List<String> receivers;

        public static SmsEvent buildSmsEvent(String title, String content, List<String> receivers) {
            SmsMsg smsMsg = new SmsMsg();
            smsMsg.setTitle(title);
            smsMsg.setContent(content);
            smsMsg.setDate(DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN));
            SmsEvent event = new SmsEvent();
            event.setMsg(smsMsg);
            event.setReceivers(receivers);
            return event;
        }

        @Getter
        @Setter
        public static class SmsMsg {
            private String title;
            private String content;
            private String date;
        }
    }
}


