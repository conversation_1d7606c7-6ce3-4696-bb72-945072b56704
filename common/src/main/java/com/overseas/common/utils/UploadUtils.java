package com.overseas.common.utils;

import com.overseas.common.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Date;
import java.util.List;

/**
 * 上传文件工具类
 */
@Component
@Slf4j
public class UploadUtils {

    //图片上传路径
    public static String UPLOAD_PATH;

    //图片返回地址
    public static String UPLOAD_URL;

    //允许上传文件归属类型
    public static List<String> UPLOAD_TYPE;

    //允许上传类型
    public static List<String> UPLOAD_EXTENSION;

    //CDN https 地址
    private static String CDN_HTTPS_DOMAIN;

    //CDN path
    private static String CDN_STORE_PATH;

    @Value("${com-upload.path}")
    public void setUploadPath(String uploadPath) {
        UPLOAD_PATH = uploadPath;
    }

    @Value("${com-upload.url}")
    public void setUploadUrl(String uploadUrl) {
        UPLOAD_URL = uploadUrl;
    }

    @Value("#{'${local.upload-type}'.split(',')}")
    public void setUploadType(List<String> uploadType) {
        UPLOAD_TYPE = uploadType;
    }

    @Value("#{'${local.upload-extension}'.split(',')}")
    public void setUploadExtension(List<String> uploadExtension) {
        UPLOAD_EXTENSION = uploadExtension;
    }

    @Value("${local.cdn.https-domain}")
    public void setCdnHttpsDomain(String cdnHttpsDomain) {
        CDN_HTTPS_DOMAIN = cdnHttpsDomain;
    }

    @Value("${local.cdn.path}")
    public void setCdnStorePath(String storePath) {
        CDN_STORE_PATH = storePath;
    }

    /**
     * 文件上传，并范围文件地址
     *
     * @param type 类型
     * @param file 文件
     * @return basePath 基础地址
     * @throws IOException 错误
     */
    public static String uploadFile(String type, MultipartFile file, List<String> allowExtension, Long allowSize)
            throws IOException {
        if (StringUtils.isBlank(type) && !UPLOAD_TYPE.contains(type)) {
            throw new CustomException("上传文件归属类型不合法");
        }
        if (file.isEmpty()) {
            throw new CustomException("上传文件失败，请选择文件");
        }
        String extension = getExtension(file.getOriginalFilename());
        if (StringUtils.isBlank(extension) || !UPLOAD_EXTENSION.contains(extension)
                || (allowExtension != null && !allowExtension.contains(extension))) {
            throw new CustomException("上传文件类型不合法");
        }
        if (allowSize != 0 && file.getSize() > allowSize) {
            throw new CustomException("上传文件大于" + (allowSize / 1024 / 1024) + "MB");
        }
        String path = getTypePath(type);
        String fileName = Md5CalculateUtils.getStringMd5(
                file.getOriginalFilename() + (new Date()).getTime() + Math.random() * 10000) + "." + extension;
        File dest = new File(UPLOAD_PATH + path + File.separator + fileName);
        file.transferTo(dest);
        return (path + File.separator + fileName).replaceAll("\\\\", "/");
    }

    public static String download(String type, String url, String fileName) {
        try (InputStream ins = new UrlResource(url).getInputStream()) {
            String extension = getExtension(url);
            if (StringUtils.isBlank(extension) || !UPLOAD_EXTENSION.contains(extension)) {
                throw new CustomException("上传文件类型不合法");
            }
            fileName = fileName + "." + extension;
            String relativePath = getTypePath(type);
            Path target = Paths.get(UPLOAD_PATH + relativePath, fileName);
            Files.createDirectories(target.getParent());
            Files.copy(ins, target, StandardCopyOption.REPLACE_EXISTING);
            return (relativePath + File.separator + fileName).replaceAll("\\\\", "/");
        } catch (IOException e) {
            log.error("文件下载失败：" + e.getMessage());
            return null;
        }
    }

    /**
     * 获取文件后缀名称
     *
     * @param fileName 文件地址
     * @return 后缀
     */
    public static String getExtension(String fileName) {
        if (null == fileName) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
    }

    /**
     * 获取可展示URL
     *
     * @param basePath 基础地址
     * @return http 路径
     */
    public static String getHttpUrl(String basePath) {
        if (StringUtils.isBlank(basePath)) {
            return basePath;
        }
        return UPLOAD_URL + basePath;
    }

    /**
     * 根据是否已上传返回网络地址
     * @param basePath 文件路径
     * @param isUpload 是否已上传
     * @return 网络地址
     */
    public static String getNetworkUrl(String basePath, Integer isUpload) {
        return getHttpUrl(basePath);
    }

    /**
     * 展示  cdn https Url
     *
     * @param basePath 基础素材地址
     * @return cdn 地址
     */
    public static String getCdnHttpsUrl(String basePath) {
        if (StringUtils.isBlank(basePath)) {
            return basePath;
        }
        return CDN_HTTPS_DOMAIN + CDN_STORE_PATH + basePath;
    }


    /**
     * 展示  cdn 基础 Url
     *
     * @param httpUrl cdn url地址
     * @return cdn 地址
     */
    public static String getCdnBaseUrl(String httpUrl) {
        if (StringUtils.isBlank(httpUrl)) {
            return httpUrl;
        }
        return httpUrl.replace(CDN_HTTPS_DOMAIN + CDN_STORE_PATH, "");
    }

    /**
     * 获取上传的地址
     *
     * @param basePath 基础地址
     * @return 相对路径
     */
    public static String getUploadPath(String basePath) {
        log.info("base path : {}", basePath);
        if (StringUtils.isBlank(basePath)) {
            return basePath;
        }
        return UPLOAD_PATH + basePath;
    }

    /**
     * 获取基础地址
     *
     * @param path 地址
     * @return 去除内容地址
     */
    public static String getBasePath(String path) {
        if (StringUtils.isBlank(path)) {
            return path;
        }
        return path.replace(UPLOAD_PATH, "").replace(UPLOAD_URL, "");
    }

    /**
     * 获取类型路径，如果路径不存在，则创建
     *
     * @param type 类型
     * @return 类型日期文件
     */
    public static String getTypePath(String type) {
        String path = "/" + type + DateUtils.format(new Date(), "/yyyy/MM/dd");
        //创建文件夹
        File pathFile = new File(UPLOAD_PATH + path);
        if (!pathFile.exists()) {
            boolean result = pathFile.mkdirs();
            log.info("get type path result : {}", result);
        }
        return path;
    }


    /**
     * 获取文件大小
     *
     * @param basePath 基础地址
     * @return 返回数据
     */
    public static Long getFileSize(String basePath) {
        return (new File(UploadUtils.getUploadPath(basePath))).length();
    }
}
