package com.overseas.common.utils;

import com.overseas.common.exception.CustomException;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class ValidatorUtils {

    private final static String mobileReg = "^1\\d{10}$";

    private final static String passwordReg = "^(?![0-9]+$)(?![a-zA-Z]+$)[0-9a-zA-Z!@#$%^&*()_+={};':\"\\\\|,.<>?/`~\\\\-\\\\s]{6,15}$";

    private final static String urlReg = "^(http|https):\\/\\/([\\w.]+\\/?)\\S*";

    private final static Pattern ipReg = Pattern.compile("^((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)$");

    private static final Validator validator;

    static {
        validator = Validation.buildDefaultValidatorFactory().getValidator();
    }


    /**
     * 判断是否是phone
     *
     * @param value 验证数据
     * @return 返回数据
     */
    public static boolean isMobile(String value) {
        return Pattern.compile(mobileReg).matcher(value).matches();
    }

    /**
     * 判断是否是password
     *
     * @param value 验证数据
     * @return 返回数据
     */
    public static boolean isPassword(String value) {
        return Pattern.compile(passwordReg).matcher(value).matches();
    }

    /**
     * 判断是否url
     *
     * @param value 验证数据
     * @return 返回数据
     */
    public static boolean isUrl(String value) {
        return Pattern.compile(urlReg).matcher(value).matches();
    }


    /**
     * 判断是否url
     *
     * @param value 验证数据
     * @return 返回数据
     */
    public static boolean isIp(String value) {
        return ipReg.matcher(value).matches();
    }


    /**
     * 校验对象
     *
     * @param object 待校验对象
     * @param groups 待校验的组
     * @throws CustomException 校验不通过，则报 CustomException异常
     */
    public static void validateEntity(Object object, Class<?>... groups)
            throws CustomException {
        Set<ConstraintViolation<Object>> constraintViolations = validator.validate(object, groups);
        if (!constraintViolations.isEmpty()) {
            ConstraintViolation<Object> constraint = constraintViolations.iterator().next();
            throw new CustomException(constraint.getMessage());
        }
    }

    /**
     * 校验对象
     *
     * @param objects 待校验对象
     * @param groups 待校验的组
     * @throws CustomException 校验不通过，则报 CustomException异常
     */
    public static void validateEntities(List<?> objects, Class<?>... groups)
            throws CustomException {
        for (int i = 0; i < objects.size(); i++) {
            try {
                ValidatorUtils.validateEntity(objects.get(i));
            } catch (CustomException e) {
                throw new CustomException(String.format("第%s行数据%s", i + 1, e.getMessage()));
            }
        }
    }

    /**
     * 校验对象 并且返回所有错误，错误 message 以 英文逗号区分
     *
     * @param object 待校验对象
     * @param groups 待校验的组
     * @throws CustomException 校验不通过，则报 CustomException异常
     */
    public static void validateEntityAllError(Object object, Class<?>... groups)
            throws CustomException {
        Set<ConstraintViolation<Object>> constraintViolations = validator.validate(object, groups);
        if (!constraintViolations.isEmpty()) {
            throw new CustomException(
                    String.join(",", constraintViolations
                            .stream().map(ConstraintViolation::getMessage).collect(Collectors.toList())
                    )
            );
        }
    }
}
