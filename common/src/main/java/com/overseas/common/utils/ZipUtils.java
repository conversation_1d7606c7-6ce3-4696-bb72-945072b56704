package com.overseas.common.utils;

import org.springframework.util.FileCopyUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.zip.Adler32;
import java.util.zip.CheckedOutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class ZipUtils {

    /**
     * 压缩zip 并下载
     *
     * @param zipName  zip 名称 （带后缀）
     * @param request  请求
     * @param response 响应
     * @param paths    文件地址  (文件名称 => 地址) 文件名称不可重复
     * @throws IOException 错误
     */
    public static void zipAndDownload(String zipName, HttpServletRequest request,
                                      HttpServletResponse response, Map<String, String> paths) throws IOException {
        File zipFile = zipFiles(zipName, paths);
        ZipUtils.download(zipName, request, response);
        // 删除临时zip文件
        zipFile.delete();
    }

    /**
     * 返回数据
     *
     * @param zipName 返回数据
     * @param paths   内容
     * @return 返回数据
     * @throws IOException 返回数据
     */
    public static File zipFiles(String zipName, Map<String, String> paths) throws IOException {
        File zipFile = getFile(zipName);
        FileOutputStream fos = new FileOutputStream(zipFile);
        // 作用是为任何OutputStream产生校验和
        // 第一个参数是制定产生校验和的输出流，第二个参数是指定Checksum的类型 （Adler32（较快）和CRC32两种）
        CheckedOutputStream cos = new CheckedOutputStream(fos, new Adler32());
        // 用于将数据压缩成zip文件格式
        ZipOutputStream zos = new ZipOutputStream(cos);
        // 循环根据路径从OSS获得对象，存入临时文件zip中
        for (Map.Entry<String, String> entry : paths.entrySet()) {
            InputStream is = new FileInputStream(entry.getValue());
            // 将文件放入zip中，并命名不能重复
            zos.putNextEntry(new ZipEntry(entry.getKey()));
            // 向压缩文件中写数据
            int len;
            while ((len = is.read()) != -1) {
                zos.write(len);
            }
            is.close();
            // 当前文件写完，关闭
            zos.closeEntry();
        }
        zos.close();
        return zipFile;
    }

    /**
     * 下载数据
     *
     * @param zipName  文件地址
     * @param request  请求
     * @param response 返回
     * @throws IOException 错误
     */
    public static void download(String zipName, HttpServletRequest request,
                                HttpServletResponse response) throws IOException {
        // 设置响应类型，以附件形式下载文件
        response.setContentType("application/zip");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Cache-Control", "max-age=0");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(zipName, String.valueOf(StandardCharsets.UTF_8)));
        // 下载流封装
        File zipFile = getFile(zipName);
        FileInputStream fis = new FileInputStream(zipFile);
        BufferedInputStream bis = new BufferedInputStream(fis);
        BufferedOutputStream bos = new BufferedOutputStream(response.getOutputStream());
        // 下载zip文件
        FileCopyUtils.copy(bis, bos);
        // 关闭流
        fis.close();
        bis.close();
        bos.close();
    }

    /**
     * 获取 zipFile
     *
     * @param zipName zip名称
     * @return 返回数据
     */
    private static File getFile(String zipName) {
        return new File(UploadUtils.getUploadPath("/zip/") + zipName);
    }

}
