package com.overseas.common.utils.redis;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
public class RedisUtils {

    private final RedisTemplate<String, String> redisTemplate;

    @Autowired
    public RedisUtils(RedisTemplate<String, String> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public void set(String key, String value) {
        this.redisTemplate.opsForValue().set(key, value);
    }

    public void set(String key, String value, Integer expire) {
        this.redisTemplate.opsForValue().set(key, value, expire, TimeUnit.SECONDS);
    }

    public String get(String key) {
        return key == null ? null : this.redisTemplate.opsForValue().get(key);
    }

    public Set<String> keys(String prefix) {
        return this.redisTemplate.keys(prefix + "*");
    }

    public void del(String key) {
        this.redisTemplate.delete(key);
    }
}
