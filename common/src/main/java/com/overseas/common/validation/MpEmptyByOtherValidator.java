package com.overseas.common.validation;


import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.validation.annotation.MpEmptyByOther;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Collection;

@Slf4j
public class MpEmptyByOtherValidator implements ConstraintValidator<MpEmptyByOther, Object> {

    private MpEmptyByOther mpEmptyByOther;

    @Override

    public void initialize(MpEmptyByOther constraintAnnotation) {
        this.mpEmptyByOther = constraintAnnotation;
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        Object wantValue = ObjectUtils.getObjectValue(value, mpEmptyByOther.field());
        if (wantValue == null) {
            return !mpEmptyByOther.required();
        }
        for (String s : mpEmptyByOther.emptyField()) {
            String[] art = s.split(mpEmptyByOther.splitStr());
            if (art.length > 2) {
                return Boolean.FALSE;
            }
            if (wantValue instanceof Collection) {
                boolean flag = false;
                for (Object val : (Collection) wantValue) {
                    if (val.toString().equals(art[0])) {
                        flag = true;
                    }
                }
                if (!flag) {
                    continue;
                }
            } else if (!wantValue.toString().equals(art[0])) {
                continue;
            }

            Object emptyValue = ObjectUtils.getObjectValue(value, art[art.length - 1]);
            if (emptyValue == null) {
                return !mpEmptyByOther.allowNull();
            }
            if (emptyValue instanceof Collection) {
                return CollectionUtils.isNotEmpty((Collection) emptyValue);
            } else {
                if (emptyValue instanceof Number) {
                    return mpEmptyByOther.zeroIsTrue() || !emptyValue.toString().equals("0");
                } else {
                    return StringUtils.isNotBlank(emptyValue.toString());
                }
            }
        }
        return !mpEmptyByOther.required();

    }


}
