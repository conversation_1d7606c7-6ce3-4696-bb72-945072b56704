package com.overseas.common.validation;

import com.overseas.common.validation.annotation.MpEnum;
import lombok.extern.slf4j.Slf4j;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * Enum验证器
 *
 * <AUTHOR>
 * @since 2020-09-21 10:10
 */
@Slf4j
public class MpEnumValidator implements ConstraintValidator<MpEnum, Object> {

    private MpEnum annotation;

    @Override
    public void initialize(MpEnum constraintAnnotation) {
        this.annotation = constraintAnnotation;
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (value == null) {
            return !annotation.required();
        }
        if ("0".equals(value.toString()) && annotation.zeroIsTrue()) {
            return true;
        }
        //验证value是否含有
        if (annotation.values().length > 0 && Arrays.asList(annotation.values()).contains(value.toString())) {
            return true;
        }
        // 通过反射查找是否存在该数据
        Object[] objects = annotation.clazz().getEnumConstants();
        try {
            Method method = annotation.clazz().getMethod("getId");

            //校验数组
            if (value instanceof Collection) {
                List<String> enumValues = new ArrayList<>();
                //获取enum 所有数据
                for (Object o : objects) {
                    enumValues.add(method.invoke(o).toString());
                }
                for (Object val : (Collection) value) {
                    //验证是否在内,如果不在内，则直接返回false
                    if (!enumValues.contains(val.toString())) {
                        return false;
                    }
                }
                //最后返回true
                return true;
            }

            //校验单个数据
            for (Object o : objects) {
                if (value.toString().equals(method.invoke(o).toString())) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("Validate enum error ：value" + value.toString() + "，class：" + annotation.clazz().getName());
        }
        return false;
    }
}
