package com.overseas.common.validation;

import com.overseas.common.validation.annotation.MpIn;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

public class MpInValidator implements ConstraintValidator<MpIn, Object> {

    private MpIn mpIn;

    @Override
    public void initialize(MpIn constraintAnnotation) {
        this.mpIn = constraintAnnotation;
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (value == null) {
            return Boolean.TRUE;
        }
        //如果指定范围为空，则返回错误
        if (mpIn.values().length == 0) {
            return Boolean.FALSE;
        }
        //判断数组是否全部包含在内
        if (value instanceof Collection) {
            List<Object> inList = Arrays.asList(mpIn.values());
            for (Object val : (Collection) value) {
                if (!inList.contains(val.toString())) {
                    return false;
                }
            }
            return true;
        }
        //判断数据是否在内
        return Arrays.asList(mpIn.values()).contains(value.toString());
    }
}
