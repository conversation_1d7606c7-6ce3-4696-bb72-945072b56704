package com.overseas.common.validation;

import com.overseas.common.validation.annotation.MpPassword;
import com.overseas.common.utils.ValidatorUtils;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class MpPasswordValidator implements ConstraintValidator<MpPassword, String> {

    @Override
    public void initialize(MpPassword constraintAnnotation) {

    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (StringUtils.isEmpty(value)) {
            return false;
        }
        return ValidatorUtils.isPassword(value);
    }
}
