package com.overseas.common.validation;

import com.overseas.common.exception.CustomException;
import com.overseas.common.validation.annotation.MpPattern;
import lombok.extern.slf4j.Slf4j;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.PatternSyntaxException;

/**
 * <AUTHOR>
 */
@Slf4j
public class MpPatternValidator implements ConstraintValidator<MpPattern, String> {

    private MpPattern mpPattern;

    private java.util.regex.Pattern pattern;

    public MpPatternValidator() {

    }


    @Override
    public void initialize(MpPattern constraintAnnotation) {
        mpPattern = constraintAnnotation;
        MpPattern.Flag[] flags = mpPattern.flags();
        int intFlag = 0;
        MpPattern.Flag[] arg$ = flags;

        int len$ = flags.length;

        for (int i$ = 0; i$ < len$; ++i$) {
            MpPattern.Flag flag = arg$[i$];
            intFlag |= flag.getValue();
        }
        try {
            this.pattern = java.util.regex.Pattern.compile(mpPattern.regexp(), intFlag);
        } catch (PatternSyntaxException e) {
            log.error(e.getMessage());
            throw new CustomException("数据校验出错");
        }
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (!mpPattern.required() && (value == null || value.length() == 0)) {
            return true;
        }
        return this.pattern.matcher(value).matches();
    }
}
