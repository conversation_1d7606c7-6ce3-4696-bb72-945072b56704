package com.overseas.common.validation;

import com.overseas.common.validation.annotation.MpPhone;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;

public class MpPhoneValidator implements ConstraintValidator<MpPhone, Object> {

    @Override
    public void initialize(MpPhone constraintAnnotation) {

    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        Pattern pattern = Pattern.compile("^1\\d{10}$"); // 1开头11位数字
        return pattern.matcher(value.toString()).matches();
    }
}
