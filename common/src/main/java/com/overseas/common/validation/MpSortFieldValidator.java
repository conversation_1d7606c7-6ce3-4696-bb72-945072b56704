package com.overseas.common.validation;

import com.overseas.common.validation.annotation.MpSortField;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;

public class MpSortFieldValidator implements ConstraintValidator<MpSortField, Object> {

    @Override
    public void initialize(MpSortField constraintAnnotation) {
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (value.toString().isEmpty()) {
            return true;
        } else {
            Pattern pattern = Pattern.compile("^[0-9a-zA-Z_]{1,50}$");
            return pattern.matcher(value.toString()).matches();
        }
    }
}
