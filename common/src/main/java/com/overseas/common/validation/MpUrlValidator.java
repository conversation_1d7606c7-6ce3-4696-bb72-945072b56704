package com.overseas.common.validation;

import com.overseas.common.utils.ValidatorUtils;
import com.overseas.common.validation.annotation.MpUrl;
import org.springframework.util.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Collection;

public class MpUrlValidator implements ConstraintValidator<MpUrl, Object> {

    private MpUrl mpUrl;

    @Override
    public void initialize(MpUrl constraintAnnotation) {
        this.mpUrl = constraintAnnotation;
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (null == value) {
            return true;
        }
        //字符串校验
        if (value instanceof String) {
            if (StringUtils.isEmpty(value)) {
                return true;
            }
            return valid(value.toString());
        }
        //数组校验
        if (value instanceof Collection) {
            for (Object val : (Collection) value) {
                if (StringUtils.isEmpty(val)) {
                    return false;
                }
                if (!valid(val.toString())) {
                    return false;
                }
            }
            return true;
        }
        return false;

    }

    public boolean valid(String value) {
        if (mpUrl.length() != 0 && value.length() > mpUrl.length()) {
            return false;
        }
        return ValidatorUtils.isUrl(value);
    }
}
