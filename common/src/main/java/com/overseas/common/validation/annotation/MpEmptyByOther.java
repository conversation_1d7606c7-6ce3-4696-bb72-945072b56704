package com.overseas.common.validation.annotation;

import com.overseas.common.validation.MpEmptyByOtherValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 根据 field 字段值 去判断 emptyField 字段不能为空
 */
@Target({ElementType.PARAMETER, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = MpEmptyByOtherValidator.class)
@Repeatable(value = MpEmptyByOther.List.class)
public @interface MpEmptyByOther {

    /**
     * @return 是否必填，如果为true，则可允许 field 与 emptyField 无匹配, 或者 field 字段可为空
     */
    boolean required() default true;

    /**
     * @return emptyField 是否允许Null
     */
    boolean allowNull() default false;

    /**
     * @return 0是否是 true ，当匹配上后 emptyField 如果为 number，则判断是否允许是0
     */
    boolean zeroIsTrue() default true;

    /**
     * @return 校验字段名
     */
    String field() default "";

    /**
     * @return 判断非空数据字段名 为 { field字段值 : 要检查字段}
     */
    String[] emptyField() default {};

    /**
     * @return 截取字段
     */
    String splitStr() default ":";

    /**
     * @return 默认提示
     */
    String message() default "数据不合法";


    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};


    /**
     * Defines several {@link MpEmptyByOther} annotations on the same element.
     *
     * @see MpEmptyByOther
     */
    @Target({TYPE})
    @Retention(RUNTIME)
    @Documented
    @interface List {

        MpEmptyByOther[] value();
    }
}

