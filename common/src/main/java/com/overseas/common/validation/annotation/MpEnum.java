package com.overseas.common.validation.annotation;

import com.overseas.common.validation.MpEnumValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2020-09-21 10:10
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = {MpEnumValidator.class})
public @interface MpEnum {

    boolean required() default true;

    boolean zeroIsTrue() default true;

    String[] values() default {};

    Class<? extends Enum> clazz();

    String message() default "枚举值不正确";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
