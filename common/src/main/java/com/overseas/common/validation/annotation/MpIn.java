package com.overseas.common.validation.annotation;

import com.overseas.common.validation.MpInValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = MpInValidator.class)
public @interface MpIn {

    String[] values() default {};

    String message() default "数据必须在指定范围内";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
