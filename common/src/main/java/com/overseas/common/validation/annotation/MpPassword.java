package com.overseas.common.validation.annotation;

import com.overseas.common.validation.MpPasswordValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = MpPasswordValidator.class)
public @interface MpPassword {

    String message() default "密码须包含数字和字母，数字不能顺序或相同重复";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
