package com.overseas.common.validation.annotation;

import com.overseas.common.validation.MpPhoneValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = {MpPhoneValidator.class})
public @interface MpPhone {

    String message() default "手机号格式不正确，必须是1开头的11位数字";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
