package com.overseas.common.validation.annotation;

import com.overseas.common.validation.MpSortFieldValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = MpSortFieldValidator.class)
public @interface MpSortField {

    String message() default "排序字段不合法";

    int length() default 50;

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
