package com.overseas.common.validation.annotation;

import com.overseas.common.validation.MpUrlValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = MpUrlValidator.class)
public @interface MpUrl {

    String message() default "网址不正确";

    int length() default 2000;

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
