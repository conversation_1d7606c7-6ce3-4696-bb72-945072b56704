package com.overseas.common.vo.report.openApi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel
public class RevenuePkgListVO {

    @ApiModelProperty("天数据")
    private List<String> days = List.of();

    @ApiModelProperty("小时数据")
    private List<String> hours = List.of();

    @ApiModelProperty("指定ID")
    private Long revenueId;

    @ApiModelProperty("时区")
    private Integer timeZone;
}
