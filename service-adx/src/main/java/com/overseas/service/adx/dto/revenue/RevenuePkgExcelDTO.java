package com.overseas.service.adx.dto.revenue;

import com.alibaba.excel.annotation.ExcelProperty;
import com.overseas.service.adx.common.validate.annotation.MpDate;
import io.swagger.annotations.Api;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 **/
@Data
@Api
public class RevenuePkgExcelDTO {

    @ExcelProperty(index = 0, value = "日期")
    @MpDate(message = "日期格式不合法")
    @NotBlank(message = "日期不能为空")
    private String day;

    @ExcelProperty(index = 1, value = "小时")
    private String hour;

    @ExcelProperty(index = 2, value = "包名")
    @NotBlank(message = "包名不能为空")
    private String pkgName;

    @ExcelProperty(index = 3, value = "曝光")
    @NotNull(message = "曝光不能为空")
    private Long impress;

    @ExcelProperty(index = 4, value = "消耗")
    @NotNull(message = "消耗不能为空")
    private BigDecimal cost;

    @ExcelProperty("我方曝光")
    private Long idxImpress;

    @ExcelProperty("我方消耗")
    private BigDecimal idxCost;

    @ExcelProperty("我方曝光偏差")
    private Long diffImpress;

    @ExcelProperty("我方曝光偏差比（%）")
    private Double diffImpressRatio;

    @ExcelProperty("我方消耗偏差")
    private BigDecimal diffCost;

    @ExcelProperty("我方消耗偏差比（%）")
    private Double diffCostRatio;

}
