package com.overseas.service.adx.dto.revenue;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class RevenuePkgListDTO {

    private String pkg;

    private String day;

    private Integer hour;

    private Long view;

    private BigDecimal cost;

}
