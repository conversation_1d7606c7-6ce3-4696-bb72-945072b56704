package com.overseas.service.adx.service.revenue.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.overseas.service.adx.common.utils.*;
import com.overseas.service.adx.dao.adx.RevenueCompareMapper;
import com.overseas.service.adx.dto.common.User;
import com.overseas.service.adx.dto.revenue.RevenueCompareListDTO;
import com.overseas.service.adx.dto.revenue.RevenuePkgExcelDTO;
import com.overseas.service.adx.dto.revenue.RevenuePkgListDTO;
import com.overseas.service.adx.entity.adx.RevenueCompare;
import com.overseas.service.adx.enums.common.ICommonEnum;
import com.overseas.service.adx.enums.common.IsDelEnum;
import com.overseas.service.adx.enums.common.MachineRoomEnum;
import com.overseas.service.adx.enums.common.TimeZoneEnum;
import com.overseas.service.adx.enums.revenue.RevenueCompareStatusEnum;
import com.overseas.service.adx.service.revenue.RevenueCompareService;
import com.overseas.service.adx.vo.revenue.RevenueCompareListVO;
import com.overseas.service.adx.vo.revenue.RevenueCompareSaveVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@Slf4j
@RequiredArgsConstructor
@Service
public class RevenueCompareServiceImpl implements RevenueCompareService {

    private final RevenueCompareMapper revenueCompareMapper;

    private Map<Integer, String> urlMap = new HashMap<>() {{
        put(1, "/adx/openapi/v1/revenue/ssp/pkg/list");
        put(2, "/adx/openapi/v1/revenue/dsp/pkg/list");
        put(3, "/report/v1/api/revenue/pkg/list");
    }};

    @Override
    public PageUtils<?> list(RevenueCompareListVO listVO, User user) {
        IPage<RevenueCompareListDTO> iPage = new Page<>(listVO.getPage(), listVO.getPageNum());
        iPage = this.revenueCompareMapper.list(iPage, new QueryWrapper<>()
                .eq("revenue_type", listVO.getRevenueType())
                .eq("is_del", IsDelEnum.NORMAL.getId())
                .orderByDesc("id")
        );
        iPage.getRecords().forEach(u -> {
            u.setCompareStatusName(ICommonEnum.getNameById(u.getCompareStatus(), RevenueCompareStatusEnum.class));
            u.setTimeZoneName(TimeZoneEnum.getNameById(u.getTimeZone()));
            u.setResultExcel(UploadUtils.getHttpUrl(u.getResultExcel()));
        });
        return new PageUtils<>(iPage);
    }


    @Override
    public void save(RevenueCompareSaveVO saveVO, User user) {
        RevenueCompare revenueCompare = new RevenueCompare();
        BeanUtils.copyProperties(saveVO, revenueCompare);
        revenueCompare.setCreateUid(user.getId());
        revenueCompareMapper.insert(revenueCompare);
        CompletableFuture.runAsync(() -> {
            try {
                this.compare(revenueCompare.getId());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                RevenueCompare update = new RevenueCompare();
                update.setCompareStatus(RevenueCompareStatusEnum.FAIL.getId());
                update.setCompareReason(e.getMessage().substring(0, Math.min(100, e.getMessage().length())));
                this.revenueCompareMapper.update(update, new LambdaQueryWrapper<RevenueCompare>()
                        .eq(RevenueCompare::getId, revenueCompare.getId()));
            }
        });
    }

    @Override
    public void compare(Long id) {
        RevenueCompare revenueCompare = this.revenueCompareMapper.selectById(id);
        if (null == revenueCompare) {
            return;
        }
        List<RevenuePkgExcelDTO> list = ExcelUtils.read(revenueCompare.getUploadExcel(), RevenuePkgExcelDTO.class);
        if (list.isEmpty()) {
            return;
        }
        ValidatorUtils.validateEntities(list);
        Map<String, Object> params = new HashMap<>() {{
            put("timeZone", revenueCompare.getTimeZone());
            put("revenueId", revenueCompare.getRevenueId());
            put("days", list.stream().map(RevenuePkgExcelDTO::getDay).distinct().collect(Collectors.toList()));
        }};
        Boolean isHour;
        if (StringUtils.isNotBlank(list.get(0).getHour())) {
            isHour = true;
            params.put("hours", list.stream()
                    .map(u -> String.format("%s %s", u.getDay(), Integer.parseInt(u.getHour()) < 10 ? "0" + u.getHour() : u.getHour()))
                    .distinct().collect(Collectors.toList()));
        } else {
            isHour = false;
        }
        List<RevenuePkgListDTO> result = new ArrayList<>();
        for (MachineRoomEnum machineRoomEnum : MachineRoomEnum.values()) {
            String resp = HttpUtils.postToMachine(machineRoomEnum, urlMap.get(revenueCompare.getRevenueType()),
                    params, new HashMap<>() {{
                    }}
            );
            FeignR<List<RevenuePkgListDTO>> response = JSONObject.parseObject(resp, new TypeReference<>() {
            });
            if (response.getCode().equals(0)) {
                result.addAll(response.getData());
            }
        }
        Map<String, RevenuePkgListDTO> resultMap = result.stream().collect(Collectors.toMap(u ->
                        generateKey(isHour, u.getDay(), u.getHour(), u.getPkg()), Function.identity(),
                (o, n) -> {
                    o.setCost(o.getCost().add(n.getCost()));
                    o.setView(o.getView() + n.getView());
                    return o;
                }
        ));
        RevenuePkgListDTO zero = new RevenuePkgListDTO(null, null, null, 0L, BigDecimal.ZERO);
        list.forEach(datum -> {
            String key = generateKey(isHour, datum.getDay(), datum.getHour(), datum.getPkgName());
            convert(datum, resultMap.getOrDefault(key, zero));
            resultMap.remove(key);
        });
        resultMap.forEach((k, v) -> list.add(transform(v, isHour)));
        String path = String.format("/revenue/%s.xlsx", Md5CalculateUtils.getStringMd5(revenueCompare.getId().toString()));
        String resultExcel = UploadUtils.getUploadPath(path);
        ExcelUtils.download(resultExcel, "对比数据", RevenuePkgExcelDTO.class, ExcelUtils.setExcelNullToZero(list, RevenuePkgExcelDTO.class));
        //记录结果
        RevenueCompare update = new RevenueCompare();
        update.setCompareStatus(RevenueCompareStatusEnum.SUCCESS.getId());
        update.setResultExcel(path);
        this.revenueCompareMapper.update(update, new LambdaUpdateWrapper<RevenueCompare>().eq(RevenueCompare::getId, revenueCompare.getId()));
    }

    /**
     * 转换数据
     *
     * @param datum             数据
     * @param revenuePkgListDTO 查询数据
     */
    private void convert(RevenuePkgExcelDTO datum, RevenuePkgListDTO revenuePkgListDTO) {
        datum.setIdxImpress(revenuePkgListDTO.getView());
        datum.setIdxCost(revenuePkgListDTO.getCost());
        datum.setDiffImpress(datum.getIdxImpress() - datum.getImpress());
        datum.setDiffCost(datum.getIdxCost().subtract(datum.getCost()));
        datum.setDiffImpressRatio(DoubleUtils.getRate(BigDecimal.valueOf(datum.getDiffImpress()), BigDecimal.valueOf(datum.getImpress())));
        datum.setDiffCostRatio(DoubleUtils.getRate(datum.getDiffCost(), datum.getCost()));
    }

    /**
     * 转换数据
     *
     * @param revenuePkgListDTO 类型
     * @return 返回数据
     */
    public RevenuePkgExcelDTO transform(RevenuePkgListDTO revenuePkgListDTO, Boolean isHour) {
        RevenuePkgExcelDTO datum = new RevenuePkgExcelDTO();
        datum.setDay(revenuePkgListDTO.getDay());
        datum.setPkgName(revenuePkgListDTO.getPkg());
        datum.setIdxImpress(revenuePkgListDTO.getView());
        datum.setIdxCost(revenuePkgListDTO.getCost());
        datum.setDiffImpress(-datum.getIdxImpress());
        datum.setDiffCost(BigDecimal.ZERO.subtract(datum.getIdxCost()));
        if (isHour) {
            datum.setHour(revenuePkgListDTO.getHour().toString());
        }
        return datum;
    }

    /**
     * 生成key
     *
     * @param isHour 数据
     * @param day    天数据
     * @param hour   小时
     * @param pkg    包名
     * @return 返回数据
     */
    private String generateKey(Boolean isHour, String day, Object hour, String pkg) {
        return isHour ? String.format("%s-%s-%s", day, hour, pkg) : String.format("%s-%s", day, pkg);
    }


}
