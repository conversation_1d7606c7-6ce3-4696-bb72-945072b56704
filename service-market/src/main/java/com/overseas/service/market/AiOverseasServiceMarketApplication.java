package com.overseas.service.market;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.ArrayList;
import java.util.List;

@EnableScheduling
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
@ComponentScan("com.overseas")
public class AiOverseasServiceMarketApplication {
    public static void main(String[] args) {
        SpringApplication.run(AiOverseasServiceMarketApplication.class, args);
    }

    @Bean
    public HttpMessageConverters fastJsonHttpMessagesConverters() {
        // 1. 需要定义一个converter转换消息的对象
        FastJsonHttpMessageConverter fastHttpMessageConverter = new FastJsonHttpMessageConverter();
        // 2. 添加fastJson的配置信息，比如:是否需要格式化返回的json的数据
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        fastJsonConfig.setSerializerFeatures(SerializerFeature.PrettyFormat,
                SerializerFeature.WriteNullNumberAsZero,
                SerializerFeature.WriteNullStringAsEmpty,
                SerializerFeature.WriteNullListAsEmpty,
                SerializerFeature.WriteMapNullValue,
                SerializerFeature.DisableCircularReferenceDetect);
        // 3. 在converter中添加配置信息
        fastHttpMessageConverter.setFastJsonConfig(fastJsonConfig);
        // 处理中文乱码
        List<MediaType> fastMediaType = new ArrayList<>();
        fastMediaType.add(MediaType.APPLICATION_JSON);
        fastHttpMessageConverter.setSupportedMediaTypes(fastMediaType);
        return new HttpMessageConverters(fastHttpMessageConverter);
    }

}
