package com.overseas.service.market.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.utils.HttpUtils;
import com.overseas.service.market.dto.ep.EpListDTO;
import com.overseas.service.market.service.AdxService;
import com.overseas.service.market.vo.adx.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/market/adx")
@Api(tags = "ADX管理")
public class AdxController extends AbstractController {

    private final AdxService adxService;

    @GetMapping("/password")
    public void testPassword() {
        List<String> list = List.of("<EMAIL>",
                "<EMAIL>");

        List<String> success = new ArrayList<>();

        list.forEach(name-> {
            Map<String, Object> param = new HashMap<>();
            param.put("password", "1q2w3e");
            param.put("userName", name);
            String sta = HttpUtils.post("https://open-api.voiceads.cn/online/common/login/login", param);
            JSONObject result = JSON.parseObject(sta);
            if (result.getInteger("code") == 0) {
                success.add(name);
            }
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        });
        log.info("success : {}", success);
    }

    @ApiOperation(value = "ADX protocol select", response = SelectDTO.class)
    @PostMapping("/protocol/select")
    public R protocolSelect() {
        return R.data(this.adxService.protocolSelect());
    }

    @ApiOperation(value = "ADX select", response = SelectDTO.class)
    @PostMapping("/select")
    public R select(@RequestBody @Validated AdxSelectVO selectVO) {
        return R.data(this.adxService.select(selectVO));
    }

    @ApiOperation(value = "ADX list", response = EpListDTO.class)
    @PostMapping("/list")
    public R list(@RequestBody @Validated AdxListVO listVO) {
        return R.page(adxService.list(listVO));
    }

    @ApiOperation(value = "ADX 保存", response = R.class)
    @PostMapping("/save")
    public R save(@RequestBody @Validated AdxSaveVO saveVO) {
        this.adxService.save(saveVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "ADX 获取")
    @PostMapping("/get")
    public R get(@RequestBody @Validated AdxGetVO getVO) {
        return R.data(this.adxService.get(getVO));
    }

    @ApiOperation(value = "ADX 更新")
    @PostMapping("/update")
    public R update(@RequestBody @Validated AdxUpdateVO updateVO) {
        this.adxService.update(updateVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "ADX 绑定模板")
    @PostMapping("/bind")
    public R bind(@RequestBody @Validated AdxBindVO bindVO) {
        this.adxService.bind(bindVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "ADX 解绑模板")
    @PostMapping("/unbind")
    public R unbind(@RequestBody @Validated AdxUnbindVO unbindVO) {
        this.adxService.unbind(unbindVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "ADX 删除")
    @PostMapping("/del")
    public R del(@RequestBody @Validated AdxDeleteVO deleteVO) {
        this.adxService.del(deleteVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "ADX 状态修改")
    @PostMapping("/status")
    public R del(@RequestBody @Validated AdxStatusVO statusVO) {
        this.adxService.status(statusVO, this.getUserId());
        return R.ok();
    }
}
