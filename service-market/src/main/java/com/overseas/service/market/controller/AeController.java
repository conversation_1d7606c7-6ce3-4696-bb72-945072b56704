package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.service.market.enums.cps.CpsProductTypeEnum;
import com.overseas.service.market.service.AeAuthorizationService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

@RestController
@RequestMapping("/ae")
@RequiredArgsConstructor
@Slf4j
@Api(value = "AE CPS相关接口")
public class AeController {

    private final AeAuthorizationService aeAuthorizationService;

    @GetMapping("/callback")
    public R getCallback(@RequestParam("code") String code,
                         @RequestParam("clientId") @NotBlank(message = "AppKey不能为空") String appKey) {
        log.info("ae callback code : {} , app key : {}", code, appKey);
        this.aeAuthorizationService.saveAeAuthorization(code, appKey);
        return R.data(code);
    }

    @GetMapping("/tokens/refresh")
    public R refreshToken() {
        this.aeAuthorizationService.refreshToken();
        return R.ok();
    }

    @GetMapping("/products/pull")
    public void pullProduct(@RequestParam("productType") Integer productType,
                            @RequestParam("categoryId") String categoryId,
                            @RequestParam("language") String language,
                            @RequestParam("addOnly") boolean addOnly) {
        if (productType == 5) {
            List<SelectDTO> categories = this.aeAuthorizationService.selectFirstCategory();
            List<String> languages = List.of("DE", "NL");
            categories.forEach(category -> languages.forEach(lan ->
                    this.aeAuthorizationService.pullProduct(CpsProductTypeEnum.HOT_PRODUCT,
                            category.getId().toString(), lan, true)));
        } else {
            CpsProductTypeEnum cpsProductTypeEnum = ICommonEnum.get(productType, CpsProductTypeEnum.class);
            this.aeAuthorizationService.pullProduct(cpsProductTypeEnum, categoryId, language, addOnly);
        }
    }

    @GetMapping("/materials/download")
    public void download() {
        this.aeAuthorizationService.downloadMaterial();
    }

    @GetMapping("/products/check")
    public void checkAeProduct() {
        this.aeAuthorizationService.checkProduct();
    }

    @GetMapping("/dpa/cpsUrls/generate")
    public void generateDpaUrl() {
        this.aeAuthorizationService.generateDpaCpsUrl(this.aeAuthorizationService.getApps());
    }

    @GetMapping("/products/libraries/generate")
    public void generateLibraryUrl() {
        this.aeAuthorizationService.generateProductLibraryCpsUrl(this.aeAuthorizationService.getApps());
    }
}
