package com.overseas.service.market.controller;

import com.overseas.common.dto.market.asset.DesignAssetReportListDTO;
import com.overseas.common.vo.market.asset.*;
import com.overseas.common.vo.report.AssetReportListVO;
import com.overseas.service.market.entity.Asset;
import com.overseas.service.market.vo.assetTask.AssetSizeSelectVO;
import com.overseas.service.market.vo.other.AssertCheckVO;
import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.SelectDTO2;
import com.overseas.common.dto.SelectDTO3;
import com.overseas.common.dto.market.asset.AssetAlgorithmListDTO;
import com.overseas.common.dto.market.asset.AssetCountDTO;
import com.overseas.common.dto.market.asset.AssetListDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.utils.UploadUtils;
import com.overseas.common.vo.market.asset.assetLabel.AssetLabelSaveVO;
import com.overseas.common.vo.market.asset.assetLabel.AssetLabelSelectGetVO;
import com.overseas.service.market.enums.assets.AssetSourceTypeEnum;
import com.overseas.service.market.enums.assets.AssetTypeEnum;
import com.overseas.service.market.service.AssetLabelService;
import com.overseas.service.market.service.AssetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-24 10:54
 */
@Slf4j
@Api(tags = "Market-元素上传")
@RestController
@RequestMapping("/market/assets")
@RequiredArgsConstructor
public class AssetController extends AbstractController {

    private final AssetService assetService;

    private final AssetLabelService assetLabelService;

    @ApiOperation("校验素材md5是否已存在")
    @PostMapping("/check")
    public R checkAssertMd5(@RequestBody @Validated AssertCheckVO checkVO) {
        return R.data(this.assetService.checkAsset(checkVO, this.getUserId()));
    }

    @ApiOperation("获取指定ID的一批素材信息")
    @PostMapping("/get")
    public R getAsset(@RequestBody @Validated AssetGetVO getVO) {
        return R.data(this.assetService.listAsset(getVO));
    }

    @ApiOperation("获取指定ID的一批素材信息")
    @PostMapping("/attribute/get")
    public R getAssetAttribute(@RequestBody @Validated AssetAttributeGetVO getVO) {
        return R.data(this.assetService.getAsset(getVO.getId()));
    }

    @ApiOperation("上传文件")
    @PostMapping("/upload")
    public R uploadAssert(@RequestParam("file") MultipartFile file) throws IOException {
        // 获取素材类型
        Integer assetType = AssetTypeEnum.getType(file.getOriginalFilename());
        // 上传文件
        String basePath = UploadUtils.uploadFile("assets", file, null, 200 * 1024 * 1024L);
        // 生成素材
        Asset asset = assetService.saveAsset(assetType, basePath, file.getOriginalFilename(), getUserId());
        return R.data(asset);
    }

    @ApiOperation(value = "获取素材尺寸下拉", notes = "获取素材尺寸下拉", produces = "application/json", response = SelectDTO3.class)
    @PostMapping("/size/select")
    public R getAssetSizeSelect(@RequestBody @Validated AssetSizeSelectGetVO getVO) {
        return R.data(this.assetService.getAssetSizeSelect(getVO));
    }

    @ApiOperation(value = "获取素材尺寸下拉", notes = "获取素材尺寸下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/size/v2/select")
    public R selectAssetSize(@RequestBody @Validated AssetSizeSelectGetVO getVO) {
        return R.data(this.assetService.selectAssetSize(getVO));
    }

    @ApiOperation(value = "获取素材来源下拉数据", notes = "获取素材来源下拉数据", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/source/select")
    public R getAssetSourceTypeSelect() {
        return R.data(ICommonEnum.list(AssetSourceTypeEnum.class));
    }

    @ApiOperation(value = "获取素材标签下拉数据", notes = "获取素材标签下拉数据", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/label/select")
    public R getAssetLabelSelect(@RequestBody @Validated AssetLabelSelectGetVO getVO) {
        return R.data(this.assetLabelService.getAssetLabelSelect(getVO));
    }

    @ApiOperation(value = "获取素材标签列表分页数据", notes = "获取素材标签列表分页数据", produces = "application/json", response = AssetListDTO.class)
    @PostMapping("/list")
    public R getAssetPage(@RequestBody @Validated AssetListVO listVO) {
        return R.page(this.assetService.getAssetPage(listVO, this.getUserId(), this.getUser()));
    }

    @ApiOperation(value = "新增素材与标签关系", notes = "新增素材与标签关系", produces = "application/json")
    @PostMapping("/label/save")
    public R saveAssetLabel(@RequestBody @Validated AssetLabelSaveVO saveVO) {
        this.assetLabelService.saveAssetLabel(saveVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "删除素材", notes = "删除素材", produces = "application/json")
    @PostMapping("/delete")
    public R deleteAsset(@RequestBody @Validated AssetDeleteGetVO getVO) {
        this.assetService.deleteAsset(getVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "批量下载素材", notes = "批量下载素材", produces = "application/json")
    @PostMapping("/batch/download")
    public R batchDownload(@RequestBody @Validated AssetBatchDownloadGetVO getVO, HttpServletRequest request, HttpServletResponse response) {
        this.assetService.batchDownload(getVO, this.getUserId(), request, response);
        return R.ok();
    }

    @ApiOperation(value = "共享素材", notes = "共享素材", produces = "application/json")
    @PostMapping("/share")
    public R shareAsset(@RequestBody @Validated AssetShareGetVO getVO) {
        this.assetService.shareAsset(getVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "新增素材与用户记录", notes = "新增素材与用户记录", produces = "application/json")
    @PostMapping("/resource/save")
    public R saveAssetResource(@RequestBody @Validated AssetResourceSaveVO saveVO) {
        this.assetService.saveAssetResource(saveVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "新增素材与用户记录V2支持推广账户", notes = "新增素材与用户记录", produces = "application/json")
    @PostMapping("/resource/save/v2")
    public R saveAssetResourceV2(@RequestBody @Validated AssetResourceSaveV2VO saveVO) {
        this.assetService.saveAssetResourceV2(saveVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "删除素材与用户记录", notes = "新增素材与用户记录", produces = "application/json")
    @PostMapping("/resource/delete")
    public R deleteAssetResource(@RequestBody @Validated AssetResourceDeleteGetVO getVO) {
        this.assetService.deleteAssetResource(getVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "校验是否有投放的创意单元", produces = "application/json")
    @PostMapping("/unit/check")
    public R checkExistUnit(@RequestBody @Validated AssetResourceDeleteGetVO getVO) {
        return R.data(this.assetService.checkExistUnit(getVO));
    }

    @ApiOperation(value = "通过素材主元素进行归档（限定条件）", produces = "application/json")
    @PostMapping("/unit/file")
    public R checkExistUnit(@RequestBody @Validated AssetUnitFileVO fileVO) {
        this.assetService.unitFile(fileVO, this.getUser());
        return R.ok();
    }

    @ApiOperation(value = "根据算法获取相关素材推荐比例", notes = "根据算法获取相关素材推荐比例", produces = "application/json", response = AssetAlgorithmListDTO.class)
    @PostMapping("/algorithm/list")
    public R getAlgorithmUrlList(@RequestBody @Validated AssetAlgorithmVO assetAlgorithmVO) {
        return R.data(this.assetService.getAlgorithmUrlList(assetAlgorithmVO, this.getUserId()));
    }

    @ApiOperation(value = "获取指定比例素材", notes = "获取指定比例素材", produces = "application/json")
    @PostMapping("/algorithm/get")
    public R getAlgorithmByScale(@RequestBody @Validated AssetAlgorithmGetVO getVO) {
        return R.data(this.assetService.getAlgorithmByScale(getVO, this.getUserId()));
    }

    @ApiOperation(value = "获取指定素材尺寸比例下拉", notes = "获取指定素材尺寸比例下拉", produces = "application/json", response = SelectDTO2.class)
    @PostMapping("/algorithm/size/select")
    public R getAlgorithmScaleSelect(@RequestBody @Validated AssetAlgorithmVO assetAlgorithmVO) {
        return R.data(this.assetService.getAlgorithmScaleSelect(assetAlgorithmVO));
    }

    @ApiOperation(value = "获取素材数量", produces = "application/json", response = AssetCountDTO.class)
    @PostMapping("/count/get")
    public R getAssetCount(@RequestBody @Validated AssetCountGetVO getVO) {
        return R.data(this.assetService.getAssetCount(getVO));
    }

    @ApiOperation(value = "根据素材ID、名称查询素材", produces = "application/json", response = Asset.class)
    @PostMapping("/search/get")
    public R getAssetIdsBySearch(@RequestBody @Validated AssetSearchGetVO getVO) {
        return R.data(this.assetService.getAssetIdsBySearch(getVO));
    }

    @ApiOperation(value = "根据素材ID获取素材样式", produces = "application/json")
    @PostMapping("/assetInfo/get")
    public R getAssetInfoByIds(@RequestBody @Validated AssetGetVO getVO) {
        return R.data(this.assetService.getAssetInfoByIds(getVO));
    }

    @ApiOperation(value = "更改素材名称", produces = "application/json")
    @PostMapping("/name/update")
    public R updateAssetName(@RequestBody @Validated AssetNameUpdateVO updateVO) {
        this.assetService.updateAssetName(updateVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "压缩素材", produces = "application/json")
    @GetMapping("/compress")
    public R formatVideo(@RequestParam("codeRate") Integer codeRate) {
        this.assetService.compressAsset(codeRate);
        return R.ok();
    }

    @ApiOperation(value = "获取新素材ID", produces = "application/json")
    @PostMapping("/new/ids")
    public R newAssetIds(@RequestBody @Validated AssetNewIdsVO newIdsVO) {
        return R.data(this.assetService.newAssetIds(newIdsVO));
    }

    @ApiOperation(value = "获取素材尺寸下拉数据", produces = "application/json")
    @PostMapping("/sizes/select")
    public R selectAssetSize(@RequestBody @Validated AssetSizeSelectVO selectVO) {
        return R.data(this.assetService.selectAssetSize(selectVO));
    }

    @ApiOperation(value = "素材报表数据", produces = "application/json", response = DesignAssetReportListDTO.class)
    @PostMapping("/report/list")
    public R selectAssetSize(@RequestBody @Validated AssetReportListVO listVO) {
        return R.page(this.assetService.listReport(listVO, this.getUserId().longValue()));
    }

    @GetMapping("/lazada/format/assetName")
    public void formatAssetName() {
        this.assetService.formatAssetInfo();
    }
}
