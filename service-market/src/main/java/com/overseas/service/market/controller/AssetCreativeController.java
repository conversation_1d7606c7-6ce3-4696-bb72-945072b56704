package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.vo.market.assetCreative.AssetCreativeLogListVO;
import com.overseas.common.vo.monitor.put.NoticeDevelopVO;
import com.overseas.service.market.events.creativeUnitAudit.CreativeUnitAuditEvent;
import com.overseas.service.market.feign.FgMonitorService;
import com.overseas.service.market.service.AssetCreativeService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 **/
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/market/assets/creative")
public class AssetCreativeController {

    private final AssetCreativeService assetCreativeService;

    private final FgMonitorService fgMonitorService;

    private final ApplicationContext applicationContext;

    @ApiOperation("根据计划上新素材")
    @PostMapping("/add/plan")
    public R addPlanByAsset(@RequestParam("planId") Long planId, @RequestParam(value = "monitorId", defaultValue = "0") Long monitorId) {
        try {
            int count = this.assetCreativeService.addCreativeByAsset(planId, monitorId);
            if (count > 0) {
                applicationContext.publishEvent(new CreativeUnitAuditEvent(this, planId));
            }
        } catch (Exception e) {
            fgMonitorService.noticeDevelop(NoticeDevelopVO.builder().message(e.getMessage()).build());
            throw e;
        }
        return R.ok();
    }

    @ApiOperation("计划日志")
    @PostMapping("/log/list")
    public R logList(@RequestBody @Validated AssetCreativeLogListVO listVO) {
        return R.page(this.assetCreativeService.list(listVO));
    }
}
