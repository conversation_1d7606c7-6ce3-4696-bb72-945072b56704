package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.vo.market.assetGroup.*;
import com.overseas.service.market.service.AssetGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 **/
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/market/assets/group")
@Api(tags = "素材组")
public class AssetGroupController extends AbstractController {

    private final AssetGroupService assetGroupService;

    @ApiOperation("素材组列表")
    @PostMapping("/list")
    public R list(@RequestBody @Validated AssetGroupListVO listVO) {
        return R.page(this.assetGroupService.list(listVO, this.getUserId()));
    }

    @ApiOperation("素材组保存")
    @PostMapping("/save")
    public R save(@RequestBody @Validated AssetGroupSaveVO saveVO) {
        this.assetGroupService.save(saveVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation("素材组更新")
    @PostMapping("/update")
    public R update(@RequestBody @Validated AssetGroupUpdateVO updateVO) {
        this.assetGroupService.update(updateVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation("素材组删除")
    @PostMapping("/del")
    public R del(@RequestBody @Validated AssetGroupDelVO delVO) {
        this.assetGroupService.del(delVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation("素材批量添加素材组")
    @PostMapping("/batch/set/group")
    public R batchSetGroup(@RequestBody @Validated AssetGroupBatchSetVO setVO) {
        this.assetGroupService.batchAddGroup(setVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation("素材组下拉取（全量）")
    @PostMapping("/select/all")
    public R batchAddGroup(@RequestBody @Validated AssetGroupSelectAllVO selectVO) {
        return R.data(this.assetGroupService.selectAll(selectVO));
    }

    @ApiOperation("素材组下拉取")
    @PostMapping("/select")
    public R batchAddGroup(@RequestBody @Validated AssetGroupSelectVO selectVO) {
        return R.data(this.assetGroupService.select(selectVO));
    }
}
