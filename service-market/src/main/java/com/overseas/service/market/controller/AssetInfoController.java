package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.vo.market.assetInfo.AssetInfoGetVO;
import com.overseas.service.market.service.AssetInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Api(tags = "Market-素材解构模块")
@RestController
@RequestMapping("/market/assetInfos")
@RequiredArgsConstructor
public class AssetInfoController extends AbstractController {

    private final AssetInfoService assetInfoService;

    @ApiOperation("获取指定素材的解构信息")
    @PostMapping("/get")
    public R getAssetInfo(@RequestBody @Validated AssetInfoGetVO getVO) {
        return R.data(this.assetInfoService.getAssetInfo(getVO));
    }
}
