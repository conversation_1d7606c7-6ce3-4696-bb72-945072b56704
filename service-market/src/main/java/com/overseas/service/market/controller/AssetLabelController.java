package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.market.assetLabel.AssetLabelGetDTO;
import com.overseas.common.dto.market.assetLabel.AssetLabelListDTO;
import com.overseas.common.vo.market.assetLabel.*;
import com.overseas.service.market.service.AssetLabelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 **/
@RequestMapping("/market/asset/labels")
@Api(tags = "素材标签")
@RestController
@Slf4j
@RequiredArgsConstructor
public class AssetLabelController extends AbstractController {

    private final AssetLabelService assetLabelService;

    @ApiOperation("分类内容获取")
    @PostMapping("/category/select")
    public R categorySelect(@RequestBody @Validated AssetLabelCategoryVO categoryVO) {
        return R.data(this.assetLabelService.categorySelect(categoryVO));
    }

    @ApiOperation("标签级联数据获取")
    @PostMapping("/cascader")
    public R cascader(@RequestBody @Validated AssetLabelCascaderVO cascaderVO) {
        return R.data(this.assetLabelService.cascader(cascaderVO));
    }

    @ApiOperation(value = "素材标签列表", response = AssetLabelListDTO.class)
    @PostMapping("/list")
    public R list(@RequestBody @Validated AssetLabelListVO listVO) {
        return R.page(this.assetLabelService.list(listVO));
    }

    @ApiOperation(value = "素材标签保存", response = R.class)
    @PostMapping("/save")
    public R save(@RequestBody @Validated AssetLabelSaveVO saveVO) {
        this.assetLabelService.save(saveVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "素材标签获取", response = AssetLabelGetDTO.class)
    @PostMapping("/get")
    public R get(@RequestBody @Validated AssetLabelGetVO getVO) {
        return R.data(this.assetLabelService.get(getVO));
    }

    @ApiOperation(value = "素材标签更新", response = R.class)
    @PostMapping("/update")
    public R update(@RequestBody @Validated AssetLabelUpdateVO updateVO) {
        this.assetLabelService.update(updateVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "素材标签删除", response = R.class)
    @PostMapping("/del")
    public R del(@RequestBody @Validated AssetLabelDelVO delVO) {
        this.assetLabelService.del(delVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "素材标签批量新增", response = R.class)
    @PostMapping("/batch/add")
    public R batchAdd(@RequestBody @Validated AssetLabelBatchAddVO addVO) {
        this.assetLabelService.batchAdd(addVO, this.getUserId());
        return R.ok();
    }


    @ApiOperation(value = "素材标签批量删除", response = R.class)
    @PostMapping("/batch/del")
    public R batchDel(@RequestBody @Validated AssetLabelBatchDelVO delVO) {
        this.assetLabelService.batchDel(delVO, this.getUserId());
        return R.ok();
    }


}
