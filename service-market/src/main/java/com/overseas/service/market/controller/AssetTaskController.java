package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.service.market.dto.assetTask.AssetTaskGetDTO;
import com.overseas.service.market.dto.assetTask.AssetTaskListDTO;
import com.overseas.service.market.service.AssetTaskProductService;
import com.overseas.service.market.service.AssetTaskService;
import com.overseas.service.market.vo.assetTask.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Slf4j
@Api(tags = "Market-选品素材任务管理")
@RestController
@RequestMapping("/market/assetTasks")
@RequiredArgsConstructor
public class AssetTaskController extends AbstractController {

    private final AssetTaskService assetTaskService;

    private final AssetTaskProductService assetTaskProductService;

    @ApiOperation("保存素材制作任务")
    @PostMapping("/save")
    public R saveAssetTask(@RequestBody @Validated AssetTaskSaveVO saveVO) {
        return R.data(this.assetTaskService.saveAssetTask(saveVO, this.getUserId()));
    }

    @ApiOperation("更新素材制作任务")
    @PostMapping("/update")
    public R updateAssetTask(@RequestBody @Validated AssetTaskUpdateVO updateVO) {
        return R.data(this.assetTaskService.updateAssetTask(updateVO, this.getUser()));
    }

    @ApiOperation("素材制作任务账户下拉")
    @PostMapping("/user/select")
    public R assetTaskUserSelect(@RequestBody @Validated AssetTaskUserSelectVO selectVO) {
        return R.data(this.assetTaskService.assetTaskUserSelect(selectVO));
    }

    @ApiOperation(value = "获取素材制作任务列表", response = AssetTaskListDTO.class)
    @PostMapping("/list")
    public R listAssetTask(@RequestBody @Validated AssetTaskListVO listVO) {
        return R.page(this.assetTaskService.listAssetTask(listVO, this.getUser()));
    }

    @ApiOperation(value = "获取素材制作任务详情", response = AssetTaskGetDTO.class)
    @PostMapping("/get")
    public R getAssetTask(@RequestBody @Validated AssetTaskGetVO getVO) {
        return R.data(this.assetTaskService.getAssetTask(getVO));
    }

    @ApiOperation(value = "获取素材制作任务下拉数据", response = SelectDTO.class)
    @PostMapping("/select")
    public R selectAssetTask(@RequestBody @Validated AssetTaskSelectVO selectVO) {
        return R.data(this.assetTaskService.selectAssetTask(selectVO));
    }

    @ApiOperation(value = "素材制作任务绑定商品")
    @PostMapping("/products/bind")
    public R bindProduct(@RequestBody @Validated AssetTaskBindVO bindVO) {
        this.assetTaskService.bindProduct(bindVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "解绑商品下素材")
    @PostMapping("/products/assets/unbind")
    public R unbindProductAsset(@RequestBody @Validated AssetTaskAssetUnbindVO unbindVO) {
        this.assetTaskService.unbindProductAsset(unbindVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "解绑素材任务下素材")
    @PostMapping("/assets/unbind")
    public R unbindAsset(@RequestBody @Validated AssetBatchUnbindVO unbindVO) {
        this.assetTaskService.unbindTaskAsset(unbindVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "解析数据")
    @PostMapping("/import/data")
    public R importData(@RequestBody @Validated AssetTaskImportExcelVO excelVO) {
        return R.data(this.assetTaskService.importExcel(excelVO));
    }

    @ApiOperation(value = "解析数据")
    @PostMapping("/export/product")
    public void exportProduct(@RequestBody @Validated AssetTaskGetVO getVO, HttpServletResponse response)
            throws IOException {
        this.assetTaskService.exportProduct(getVO, response);
    }

    @ApiOperation(value = "素材制作任务生成商品投放链接")
    @PostMapping("/products/cpsUrls/generate")
    public R checkProduct(@RequestBody @Validated CpsUrlGenerateVO generateVO) {
        return R.data(this.assetTaskProductService.generateCpsUrl(generateVO));
    }

    @ApiOperation(value = "检查标签和素材是否重复")
    @PostMapping("/check/data")
    public R checkData(@RequestBody @Validated AssetTaskCheckDataVO dataVO) {
        this.assetTaskService.checkData(dataVO);
        return R.ok();
    }

    @ApiOperation(value = "商品数据同步定时任务")
    @GetMapping("/sync/product")
    public R syncProduct() {
        this.assetTaskProductService.syncProductSchedule();
        return R.ok();
    }
}
