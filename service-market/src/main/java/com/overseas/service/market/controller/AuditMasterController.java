package com.overseas.service.market.controller;

import com.overseas.service.market.dto.audit.master.AuditMasterGetDTO;
import com.overseas.service.market.vo.auditMaster.AuditMasterGetVO;
import com.overseas.service.market.vo.auditMaster.AuditMasterListVO;
import com.overseas.service.market.vo.auditMaster.AuditMasterSaveVO;
import com.overseas.common.dto.R;
import com.overseas.common.dto.market.auditMaster.AuditMasterListDTO;
import com.overseas.service.market.service.AuditAgentMasterService;
import com.overseas.service.market.service.AuditMasterService;
import com.overseas.service.market.service.UserExtraService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-16 20:00
 */
@Api(value = "投放主体", description = "投放主体")
@RestController
@RequestMapping("/market/audit/masters/")
@RequiredArgsConstructor
public class AuditMasterController extends AbstractController {

    private final AuditMasterService auditMasterService;

    private final AuditAgentMasterService auditAgentMasterService;

    private final UserExtraService userExtraService;

    @PostMapping("/select")
    public R selectAuditMaster() {
        return R.data(auditMasterService.selectAuditMaster(getUser(), listMasterId()));
    }

    @ApiOperation(value = "获取投放主体列表", notes = "获取投放主体列表", produces = "application/json", response = AuditMasterListDTO.class)
    @PostMapping("/list")
    public R list(@Validated @RequestBody AuditMasterListVO auditMasterListVO) {
        return R.page(auditMasterService.pageAuditMaster(auditMasterListVO, getUserId()));
    }

    @ApiOperation(value = "保存资质信息", notes = "保存资质信息", produces = "application/json", response = R.class)
    @PostMapping("/save")
    public R save(@Validated @RequestBody AuditMasterSaveVO vo) {
        if (vo.getStep().equals(1)) {
            return R.data(auditAgentMasterService.saveQualification(vo, getUserId()));
        } else if (vo.getStep().equals(2)) {
            return R.data(auditAgentMasterService.saveBankInfo(vo, getUserId()));
        } else {
            return R.data(auditAgentMasterService.validateMoney(vo, getUserId()));
        }
    }

    @ApiOperation(value = "获取资质信息", notes = "获取资质信息", produces = "application/json", response = AuditMasterGetDTO.class)
    @PostMapping("/get")
    public R get(@Validated @RequestBody AuditMasterGetVO vo) {
        return R.data(auditAgentMasterService.getDetail(vo.getId(), getUserId()));
    }

    @ApiOperation(value = "获取当前登录账号资质信息", notes = "获取当前登录账号资质信息", produces = "application/json", response = AuditMasterGetDTO.class)
    @PostMapping("/self/get")
    public R getSelf() {
        return R.data(userExtraService.getDetail(getUserId()));
    }

    @ApiOperation(value = "保存当前登录账号资质信息", notes = "保存当前登录账号资质信息", produces = "application/json", response = R.class)
    @PostMapping("/self/save")
    public R saveSelf(@Validated @RequestBody AuditMasterSaveVO vo) {
        if (vo.getStep().equals(1)) {
            return R.data(userExtraService.saveQualification(vo, getUserId(), getUser().getUserType()));
        } else if (vo.getStep().equals(2)) {
            return R.data(userExtraService.saveBankInfo(vo, getUserId()));
        } else {
            return R.data(userExtraService.validateMoney(vo, getUserId()));
        }
    }
}
