package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.market.behaviorApp.BehaviorAppGetDTO;
import com.overseas.common.dto.market.behaviorApp.BehaviorAppIndustrySelectDTO;
import com.overseas.common.dto.market.behaviorApp.BehaviorAppListDTO;
import com.overseas.common.vo.market.behaviorApp.*;
import com.overseas.service.market.dto.behaviorApp.BehaviorAppSelectDTO;
import com.overseas.service.market.service.BehaviorAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Api(tags = "behaviorApp-应用相关接口")
@RestController
@RequestMapping("/market/behaviorApps")
@RequiredArgsConstructor
public class BehaviorAppController extends AbstractController {

    private final BehaviorAppService behaviorAppService;

    @ApiOperation(value = "获取应用下拉接口", notes = "获取应用下拉接口", produces = "application/json",
            response = BehaviorAppSelectDTO.class)
    @PostMapping("/select")
    public R selectBehaviorApp(@RequestBody @Validated BehaviorAppSelectGetVO getVO) {
        return R.data(this.behaviorAppService.selectBehaviorApp(getVO, this.getUser()));
    }

    @ApiOperation(value = "获取应用下拉接口（含带行业属性）", notes = "获取应用下拉接口（含带行业属性）",
            produces = "application/json", response = BehaviorAppIndustrySelectDTO.class)
    @PostMapping("/industry/select")
    public R selectBehaviorAppAndIndustry(@RequestBody @Validated BehaviorAppSelectGetVO getVO) {
        return R.data(this.behaviorAppService.selectBehaviorAppAndIndustry(getVO, this.getUser()));
    }

    @ApiOperation(value = "获取应用列表分页数据接口", notes = "获取应用列表分页数据接口",
            produces = "application/json", response = BehaviorAppListDTO.class)
    @PostMapping("/list")
    public R getBehaviorAppPage(@RequestBody @Validated BehaviorAppListVO listVO) {
        return R.page(this.behaviorAppService.getBehaviorPage(listVO, this.getUser()));
    }

    @ApiOperation(value = "获取应用数据详情接口", notes = "获取应用数据详情接口", produces = "application/json", response = BehaviorAppGetDTO.class)
    @PostMapping("/get")
    public R getBehaviorApp(@RequestBody @Validated BehaviorAppGetVO getVO) {
        return R.data(this.behaviorAppService.getBehaviorApp(getVO));
    }

    @ApiOperation(value = "新增应用接口", notes = "新增应用接口", produces = "application/json")
    @PostMapping("/save")
    public R saveBehaviorApp(@RequestBody @Validated BehaviorAppSaveVO saveVO) {
        this.behaviorAppService.saveBehaviorApp(saveVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "编辑行为应用", notes = "编辑行为应用", produces = "application/json")
    @PostMapping("/update")
    public R updateBehaviorApp(@RequestBody @Validated BehaviorAppUpdateVO updateVO) {
        this.behaviorAppService.updateBehaviorApp(updateVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "修改行为应用状态", notes = "修改行为应用状态", produces = "application/json")
    @PostMapping("/switch")
    public R changeBehaviorAppStatus(@RequestBody @Validated BehaviorAppStatusGetVO getVO) {
        this.behaviorAppService.changeBehaviorAppStatus(getVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "删除应用接口", notes = "删除应用接口", produces = "application/json")
    @PostMapping("/delete")
    public R deleteBehaviorApp(@RequestBody @Validated BehaviorAppGetVO getVO) {
        this.behaviorAppService.deleteBehaviorApp(getVO, this.getUserId());
        return R.ok();
    }
}
