package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.campaign.CampaignUpdateRecordListDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.market.campaign.CampaignTypeEnum;
import com.overseas.common.vo.market.campaign.*;
import com.overseas.service.market.driver.updateCampaignAttribute.CampaignAttributeUpdater;
import com.overseas.service.market.driver.updateCampaignAttribute.CampaignAttributeUpdaterFactory;
import com.overseas.service.market.dto.campaign.CampaignMasterSelectDTO;
import com.overseas.service.market.entity.Campaign;
import com.overseas.common.enums.market.campaign.CampaignMarketTargetEnum;
import com.overseas.common.enums.market.campaign.CampaignPutOnTargetEnum;
import com.overseas.service.market.events.notifyControl.ControlCampaignEvent;
import com.overseas.service.market.events.notifyControl.ControlContants;
import com.overseas.service.market.service.CampaignService;
import com.overseas.service.market.service.CampaignUpdateRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Api(tags = "campaign-活动相关接口")
@RestController
@RequestMapping("/market/campaigns")
@RequiredArgsConstructor
public class CampaignController extends AbstractController {

    private final CampaignService campaignService;

    private final ApplicationContext applicationContext;

    private final CampaignUpdateRecordService campaignUpdateRecordService;

    @ApiOperation(value = "获取活动下拉接口", notes = "获取活动下拉接口", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/select")
    public R selectCampaign(@Validated @RequestBody CampaignSelectGetVO getVO) {
        return R.data(this.campaignService.selectCampaign(getVO, listMasterId(), getUser()));
    }

    @ApiOperation(value = "获取活动下拉接口", notes = "获取活动下拉接口", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/copy/select")
    public R selectCopyCampaign(@Validated @RequestBody CampaignSelectGetVO getVO) {
        return R.data(this.campaignService.selectCopyCampaign(getVO, listMasterId(), getUser()));
    }

    @ApiOperation(value = "获取活动下拉接口", notes = "获取活动下拉接口", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/noPermission/select")
    public R selectNoPermissionCampaign(@Validated @RequestBody CampaignSelectGetVO getVO) {
        return R.data(this.campaignService.selectCampaign(getVO, getUser()));
    }

    @ApiOperation(value = "获取活动分页下拉接口(包含账户)", notes = "获取活动下拉接口", produces = "application/json", response = CampaignMasterSelectDTO.class)
    @PostMapping("/master/page/select")
    public R selectPageCampaignAndMaster(@Validated @RequestBody CampaignMasterSelectVO selectVO) {
        return R.page(this.campaignService.selectPageCampaignAndMaster(selectVO, listMasterId(), this.getUser()));
    }

    @ApiOperation(value = "获取具有花费的活动下拉接口", notes = "获取具有花费的活动下拉接口", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/hasCost/select")
    public R selectCampaignHasCost(@Validated @RequestBody CampaignSelectGetVO getVO) {
        return R.data(this.campaignService.selectCampaignHasCost(getVO, this.getUser()));
    }

    @ApiOperation(value = "获取权限内的活动下拉接口", notes = "获取权限内的活动下拉接口", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/selectPerm")
    public R selectPerm() {
        return R.data(campaignService.selectCampaign(listMasterId()));
    }

    @ApiOperation(value = "活动属性更新接口", notes = "活动属性更新接口", produces = "application/json", response = R.class)
    @PostMapping("/attributes/update")
    public R updateCampaignAttribute(@Validated @RequestBody CampaignAttributeOperateVO vo) {
        this.checkMasterId(vo.getMasterId());
        CampaignAttributeUpdater updater = CampaignAttributeUpdaterFactory.createUpdater(vo.getOperateType());
        Integer affect = updater.update(vo.getId(), vo.getMasterId(), vo.getValue(), null, getUserId());
        return R.ok().put("data", affect);
    }

    @ApiOperation(value = "获取活动营销目的下拉接口", notes = "获取活动营销目的下拉接口", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/marketTarget/select")
    public R selectMarketTarget() {
        return R.data(ICommonEnum.list(CampaignMarketTargetEnum.class));
    }

    @ApiOperation(value = "获取活动推广目标下拉接口", notes = "获取活动推广目标下拉接口", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/putOnTarget/select")
    public R selectPutOnTarget() {
        return R.data(ICommonEnum.list(CampaignPutOnTargetEnum.class));
    }

    @ApiOperation(value = "获取活动类型下拉接口", notes = "获取活动类型下拉接口", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/campaignTypes/select")
    public R selectCampaignType() {
        return R.data(ICommonEnum.list(CampaignTypeEnum.class));
    }

    @ApiOperation(value = "获取活动数据", notes = "获取活动数据", produces = "application/json", response = Campaign.class)
    @PostMapping("/get")
    public R getCampaign(@Validated @RequestBody CampaignGetVO getVO) {
        return R.data(this.campaignService.getCampaign(getVO.getId(), getVO.getMasterId().intValue()));
    }

    @ApiOperation(value = "获取活动ID数据(如果是客户，则获取实际投放活动，如果非客户，则获取当前活动)", notes = "获取活动数据", produces = "application/json", response = Campaign.class)
    @PostMapping("/copy/get")
    public R getCopyCampaign(@Validated @RequestBody CampaignGetVO getVO) {
        return R.data(this.campaignService.getCopyCampaign(getVO.getId(), getVO.getMasterId().intValue()));
    }

    @ApiOperation(value = "新增活动", notes = "新增活动", produces = "application/json")
    @PostMapping("/save")
    public R saveCampaign(@Validated @RequestBody CampaignSaveVO saveVO) {
        Long id = this.campaignService.saveCampaign(saveVO, this.getUser());
        return R.data(id);
    }

    @ApiOperation(value = "编辑活动", notes = "编辑活动", produces = "application/json")
    @PostMapping("/update")
    public R updateCampaign(@Validated @RequestBody CampaignUpdateVO updateVO) {
        Long id = this.campaignService.updateCampaign(updateVO, this.getUserId());
        // 通知中控
        applicationContext.publishEvent(new ControlCampaignEvent(this, ControlContants.METHOD_UPDATE, id));
        return R.data(id);
    }

    @ApiOperation(value = "批量编辑活动", notes = "批量编辑活动", produces = "application/json")
    @PostMapping("/batch/update")
    public R batchUpdateCampaign(@Validated @RequestBody CampaignBatchUpdateVO batchUpdateVO) {
        batchUpdateVO.setUserId(getUserId());
        boolean result = this.campaignService.batchUpdateCampaign(batchUpdateVO);
        if (result) {
            batchUpdateVO.getCampaignIds().forEach(campaignId -> {
                // 通知中控
                applicationContext.publishEvent(new ControlCampaignEvent(this, ControlContants.METHOD_UPDATE, campaignId));
            });
            return R.ok("批量编辑成功");
        } else {
            return R.error("批量编辑异常");
        }
    }

    @GetMapping("/batch/push")
    public R batchPushCampaign(@RequestParam("masterIds") String masterIds) {
        List<Long> ids = StringUtils.isBlank(masterIds)
                ? List.of()
                : Arrays.stream(masterIds.split(",")).map(s -> Long.parseLong(s.trim()))
                .collect(Collectors.toList());
        List<SelectDTO> campaigns = this.campaignService.selectPutCampaign(ids);
        campaigns.forEach(campaign -> {
            // 通知中控
            applicationContext.publishEvent(new ControlCampaignEvent(this, ControlContants.METHOD_UPDATE, campaign.getId()));
        });
        return R.ok();
    }

    @ApiOperation(value = "获取活动编辑列表", produces = "application/json", response = CampaignUpdateRecordListDTO.class)
    @PostMapping("/updateRecord/list")
    public R listPlanUpdateRecord(@Validated @RequestBody CampaignUpdateRecordListVO listVO) {
        return R.page(this.campaignUpdateRecordService.listUpdateRecord(listVO, this.getUser()));
    }

}
