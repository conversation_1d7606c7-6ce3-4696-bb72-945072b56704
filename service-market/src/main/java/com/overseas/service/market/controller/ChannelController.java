package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.vo.market.channel.ChannelSelectVO;
import com.overseas.service.market.service.ChannelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 **/
@RequestMapping("/market/channel")
@RestController
@RequiredArgsConstructor
@Api(tags = "渠道")
public class ChannelController {

    private final ChannelService channelService;

    @ApiOperation(value = "渠道下拉（key不含分类）")
    @PostMapping("/select")
    public R select(@RequestBody @Validated ChannelSelectVO selectVO) {
        return R.data(channelService.channelSelect(selectVO));
    }

    @ApiOperation(value = "渠道下拉（key=分类-渠道infoID）")
    @PostMapping("/select/key")
    public R selectKey(@RequestBody @Validated ChannelSelectVO selectVO) {
        return R.data(channelService.channelSelectByKey(selectVO));
    }
}
