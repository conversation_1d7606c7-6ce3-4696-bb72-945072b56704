package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.vo.market.copyWriting.*;
import com.overseas.service.market.service.CopyWritingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 */
@Api(tags = "文案管理")
@RestController
@RequestMapping("/market/copyWriting")
@RequiredArgsConstructor
public class CopyWritingController extends AbstractController {

    private final CopyWritingService copyWritingService;

    @ApiOperation(value = "获取文案列表", produces = "application/json")
    @PostMapping("/list")
    public R listCopyWriting(@RequestBody @Validated CopyWritingListVO listVO) {
        this.checkMasterId(listVO.getMasterId());
        return R.page(this.copyWritingService.listCopyWriting(listVO, this.getUser()));
    }

    @ApiOperation(value = "下载文案列表", produces = "application/json")
    @PostMapping("/list/export")
    public void exportCopyWriting(@RequestBody @Validated CopyWritingListVO listVO, HttpServletResponse response)
            throws IOException {
        this.copyWritingService.exportListCopyWriting(listVO, this.getUser(), response);
    }

    @ApiOperation(value = "获取文案", produces = "application/json")
    @PostMapping("/get")
    public R getCopyWriting(@RequestBody @Validated CopyWritingGetVO getVO) {
        return R.data(this.copyWritingService.getCopyWriting(getVO));
    }

    @ApiOperation(value = "新增文案", produces = "application/json")
    @PostMapping("/save")
    public R saveCopyWriting(@RequestBody @Validated CopyWritingSaveVO saveVO) {
        this.copyWritingService.saveCopyWriting(saveVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "批量新增文案", produces = "application/json")
    @PostMapping("/batch/save")
    public R batchSaveCopyWriting(@RequestBody @Validated CopyWritingBatchSaveVO saveVO) {
        this.checkMasterId(saveVO.getMasterId());
        this.copyWritingService.batchSaveCopyWriting(saveVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "更新文案", produces = "application/json")
    @PostMapping("/update")
    public R updateCopyWriting(@RequestBody @Validated CopyWritingUpdateVO updateVO) {
        this.checkMasterId(updateVO.getMasterId());
        this.copyWritingService.updateCopyWriting(updateVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "删除文案", produces = "application/json")
    @PostMapping("/delete")
    public R deleteCopyWriting(@RequestBody @Validated CopyWritingGetVO getVO) {
        this.copyWritingService.deleteCopyWriting(getVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "获取文案级联", produces = "application/json")
    @PostMapping("/cascader")
    public R listCopyWritingCascader(@RequestBody @Validated CopyWritingCascaderGetVO getVO) {
        return R.data(this.copyWritingService.listCopyWritingCascader(getVO));
    }

    @ApiOperation(value = "获取已有文案国家", produces = "application/json")
    @PostMapping("/country/select")
    public R selectCtxCountry(@RequestBody @Validated CopyWritingCountrySelectGetVO getVO) {
        return R.data(this.copyWritingService.selectCountry(getVO));
    }

    @ApiOperation(value = "上传文案", produces = "application/json")
    @PostMapping("/upload")
    public R uploadCopyWriting(@RequestBody @Validated CopyWritingUploadSaveVO saveVO) {
        return R.data(this.copyWritingService.uploadCopyWriting(saveVO, this.getUserId()));
    }

    @ApiOperation(value = "检查文案是否被使用", produces = "application/json")
    @PostMapping("/check")
    public R checkCopyWriting(@RequestBody @Validated CopyWritingCheckGetVO getVO) {
        return R.data(this.copyWritingService.checkCopyWriting(getVO));
    }

    @ApiOperation(value = "共享已有文案国家", produces = "application/json")
    @PostMapping("/share")
    public R shareCopyWriting(@RequestBody @Validated CopyWritingShareSaveVO saveVO) {
        this.copyWritingService.shareCopyWriting(saveVO, this.getUserId());
        return R.ok();
    }

    @PostMapping("/status")
    @ApiOperation("暂停/启用文本库")
    public R changeTextLibraryStatus(@RequestBody CopyWritingStatusVO statusVO) {
        this.copyWritingService.changeStatus(statusVO, getUserId());
        return R.ok();
    }

}
