package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.service.market.dto.cps.app.CpsAppListDTO;
import com.overseas.service.market.service.CpsAppService;
import com.overseas.service.market.service.TrackingIdService;
import com.overseas.service.market.vo.cps.app.*;
import com.overseas.service.market.vo.trackingId.TrackingIdSaveVO;
import com.overseas.service.market.vo.trackingId.TrackingIdSelectVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Api(tags = "Market-CPS关联账号模块")
@RestController
@RequestMapping("/market/cpsApps")
@RequiredArgsConstructor
public class CpsAppController extends AbstractController {

    private final CpsAppService cpsAppService;

    @ApiOperation(value = "查询关联应用分页数据", response = CpsAppListDTO.class)
    @PostMapping("/list")
    public R listCpsApp(@RequestBody @Validated CpsAppListVO listVO) {
        return R.page(this.cpsAppService.listCpsApp(listVO));
    }

    @ApiOperation(value = "创建应用", response = SelectDTO.class)
    @PostMapping("/save")
    public R saveCpsApp(@RequestBody @Validated CpsAppSaveVO saveVO) {
        this.cpsAppService.saveCpsApp(saveVO, this.getUserId());
        return R.ok();
    }
    @ApiOperation(value = "编辑应用")
    @PostMapping("/update")
    public R updateCpsApp(@RequestBody @Validated CpsAppUpdateVO updateVO) {
        this.cpsAppService.updateCpsApp(updateVO, this.getUserId());
        return R.ok();
    }
    @ApiOperation(value = "删除应用")
    @PostMapping("/delete")
    public R deleteCpsApp(@RequestBody @Validated CpsAppDeleteVO deleteVO) {
        this.cpsAppService.deleteCspApp(deleteVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "查询指定项目下关联应用下拉数据", response = SelectDTO.class)
    @PostMapping("/select")
    public R selectCpsApp(@RequestBody @Validated CpsAppSelectVO selectVO) {
        return R.data(this.cpsAppService.selectCpsApp(selectVO));
    }
}
