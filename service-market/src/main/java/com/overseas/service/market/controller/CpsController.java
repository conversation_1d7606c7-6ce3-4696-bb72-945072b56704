package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.vo.market.cps.*;
import com.overseas.service.market.service.CpsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;


/**
 * <AUTHOR>
 */
@Api(tags = "CPS相关接口")
@RestController
@RequestMapping("/market/cps")
@RequiredArgsConstructor
public class CpsController extends AbstractController {

    private final CpsService cpsService;

    @ApiOperation(value = "上传cps订单文件")
    @PostMapping("/upload")
    public R saveCps(@RequestBody @Validated CpsSaveVO saveVO) {
        this.cpsService.saveCps(saveVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "获取列表")
    @PostMapping("/list")
    public R listCps(@RequestBody @Validated CpsListVO listVO) {
        return R.page(this.cpsService.listCps(listVO));
    }

    @ApiOperation(value = "列表下载")
    @PostMapping("/list/export")
    public void listCps(@RequestBody @Validated CpsListExportVO listExportVO, HttpServletResponse response) throws IOException {
        this.cpsService.exportCps(listExportVO, response);
    }

    @ApiOperation(value = "测试服务")
    @PostMapping("/schedule/test")
    public R syncDataToPlanHour() {
        this.cpsService.syncDataToPlanHour();
        return R.ok();
    }

    @ApiOperation(value = "AE渠道下拉")
    @PostMapping("/ae/channel/place")
    public R listAeChannelPlace() {
        return R.data(this.cpsService.aeChannelPlace());
    }

    @ApiOperation(value = "AE渠道列表")
    @PostMapping("/ae/channel/list")
    public R listAeChannelCps(@RequestBody @Validated CpsAeChannelListVO listVO) {
        return R.page(this.cpsService.aeChannelCps(listVO, this.getUser()));
    }

    @ApiOperation(value = "AE渠道列表下载")
    @PostMapping("/ae/channel/list/export")
    public void listAeChannelCpsExport(@RequestBody @Validated CpsAeChannelListVO listVO, HttpServletResponse response) throws IOException {
        this.cpsService.exportAeChannelCps(listVO, this.getUser(), response);
    }

    @ApiOperation(value = "AE渠道列表下载2")
    @PostMapping("/ae/channel/list/export2")
    public void listAeChannelCpsExport2(@RequestBody @Validated CpsAeChannelListVO listVO, HttpServletResponse response) throws IOException {
        this.cpsService.exportAeChannelCps2(listVO, this.getUser(), response);
    }

    @ApiOperation("lazada oppo import ad 接口")
    @PostMapping("/lazada/oppo/import/ad")
    public void importOppoAd(@RequestBody @Validated CpsLazadaImportAdVO importAdVO) {
        this.cpsService.lazadaImportAd(importAdVO, this.getUserId());
    }

    @GetMapping("/ae/xiaomi/export")
    public void aeXiaoMiExport(@RequestParam(value = "day", defaultValue = "") String day)
            throws IOException {
        this.cpsService.aeXiaoMiExport(day);
    }

    @PostMapping("/channel/by/day")
    public void channelByDay(@RequestBody @Validated CpsExportByDayVO byDayVO, HttpServletResponse response) throws IOException {
        this.cpsService.exportAeByDayAndMail(byDayVO, response);
    }

    @GetMapping("/channel/by/day")
    public void channelByDay(@RequestParam(value = "startDate", defaultValue = "") String startDate,
                             @RequestParam(value = "endDate", defaultValue = "") String endDate,
                             @RequestParam(value = "channelPlaceId", defaultValue = "0") Long channelPlaceId) throws IOException {
        this.cpsService.exportAeByDay(startDate, endDate, channelPlaceId, null);
    }
}
