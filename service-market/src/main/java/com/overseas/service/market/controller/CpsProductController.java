package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.service.market.dto.cps.CpsProductListDTO;
import com.overseas.service.market.service.CpsProductService;
import com.overseas.service.market.vo.cps.*;
import com.overseas.service.market.vo.cps.lazada.CpsProductCategorySaveVO;
import com.overseas.service.market.vo.cps.lazada.CpsProductListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Api(tags = "CPS商品选品相关接口")
@RestController
@RequestMapping("/market/cps/products")
@RequiredArgsConstructor
public class CpsProductController extends AbstractController {
    private final CpsProductService cpsProductService;

    @ApiOperation(value = "获取列表", response = CpsProductListDTO.class)
    @PostMapping("/list")
    public R listCpsProduct(@RequestBody @Validated com.overseas.service.market.vo.cps.CpsProductListVO listVO) {
        return R.page(this.cpsProductService.listCpsProduct(listVO));
    }

    @ApiOperation(value = "获取不同项目的商品分类", response = SelectDTO.class)
    @PostMapping("/categories/select")
    public R selectCpsProductCategory(@RequestBody @Validated CpsProductCategorySelectVO selectVO) {
        return R.data(this.cpsProductService.selectCpsProductCategory(selectVO));
    }

    @ApiOperation(value = "批量下载素材")
    @PostMapping("/materials/download")
    public void download(@RequestBody @Validated CpsMaterialDownloadVO downloadVO, HttpServletRequest request,
                         HttpServletResponse response) {
        this.cpsProductService.downloadMaterial(downloadVO, request, response);
    }

    @ApiOperation(value = "回传订单商品分析列表图表")
    @PostMapping("/orders/analysis/chart")
    public R listProductAnalysisChart(@RequestBody @Validated CpsProductAnalysisChartVO analysisListVO) {
        return R.data(this.cpsProductService.chartProductAnalysis(analysisListVO));
    }

    @ApiOperation(value = "回传订单商品分析列表")
    @PostMapping("/orders/analysis")
    public R listProductAnalysis(@RequestBody @Validated CpsProductAnalysisListVO analysisListVO) {
        return R.page(this.cpsProductService.listProductAnalysis(analysisListVO));
    }

    @ApiOperation(value = "回传订单商品分析列表下载")
    @PostMapping("/orders/analysis/export")
    public void exportListProductAnalysis(@RequestBody @Validated CpsProductAnalysisExportVO analysisListVO,
                                          HttpServletResponse response) throws IOException {
        this.cpsProductService.exportProductAnalysis(analysisListVO, response);
    }

    @ApiOperation(value = "导出excel")
    @PostMapping("/export/excel")
    public void listProductAnalysis(@RequestBody @Validated CpsProductExportVO exportVO, HttpServletResponse response)
            throws IOException {
        this.cpsProductService.export(exportVO, response);
    }

    @ApiOperation(value = "根据商品ID获取商品标签")
    @PostMapping("/label/product")
    public R labelProduct(@RequestBody @Validated CpsProductLabelVO labelVO) {
        return R.data(this.cpsProductService.labelByProductIds(labelVO));
    }

    @ApiOperation(value = "按照国家拉取待爬取商品品类的商品列表")
    @PostMapping("/lazada/products/get")
    public R listLazadaProductByCountry(@RequestBody @Validated CpsProductListVO listVO) {
        return R.data(this.cpsProductService.listLazadaProductByCountry(listVO));
    }

    @ApiOperation(value = "保存商品分类")
    @PostMapping("/lazada/products/categories/save")
    public R saveLazadaProductCategory(@RequestBody @Validated CpsProductCategorySaveVO saveVO) {
        this.cpsProductService.saveLazadaCategory(saveVO);
        return R.ok();
    }
}
