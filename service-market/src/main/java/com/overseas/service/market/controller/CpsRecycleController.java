package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.chart.MultiIndexChartDTO;
import com.overseas.common.vo.market.cps.recycle.CpsRecycleChartVO;
import com.overseas.common.vo.market.cps.recycle.CpsRecycleListVO;
import com.overseas.service.market.dto.cps.recycle.CpsRecycleBarDTO;
import com.overseas.service.market.service.CpsRecycleDataService;
import com.overseas.service.market.service.CpsRecycleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 **/
@RequestMapping("/market/cps/recycle")
@Api(tags = "CPS回收数据报表")
@RestController
@Slf4j
@RequiredArgsConstructor
public class CpsRecycleController {

    private final CpsRecycleService cpsRecycleService;

    private final CpsRecycleDataService cpsRecycleDataService;

    @PostMapping("/bar")
    @ApiOperation(value = "CPS回收bar数据", response = CpsRecycleBarDTO.class)
    public R getRecycleBar(@RequestBody @Validated CpsRecycleListVO listVO) {
        return R.data(cpsRecycleService.bar(listVO));
    }

    @PostMapping("/chart")
    @ApiOperation(value = "CPS回收线性图表数据", response = MultiIndexChartDTO.class)
    public R getRecycleChart(@RequestBody @Validated CpsRecycleChartVO chartVO) {
        return R.data(cpsRecycleService.chart(chartVO));
    }

    @PostMapping("/download")
    @ApiOperation(value = "CPS回收线性图表数据下载")
    public void download(@RequestBody @Validated CpsRecycleChartVO chartVO, HttpServletResponse response)
            throws IOException {
        cpsRecycleService.download(chartVO, response);
    }

    @PostMapping("/data/save")
    @ApiOperation(value = "CPS回收数据更新")
    public void dataSave() {
        cpsRecycleDataService.saveCpsRecycleData();
    }
}
