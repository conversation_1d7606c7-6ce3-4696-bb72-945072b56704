package com.overseas.service.market.controller;

import com.overseas.common.vo.market.creative.*;
import com.overseas.common.vo.market.creative.unit.CreativeUnitBatchUpdateVO;
import com.overseas.common.vo.market.creative.unit.CreativeUnitFillSizeVO;
import com.overseas.service.market.driver.updateCreativeUnitAttribute.CreativeUnitAttributeUpdater;
import com.overseas.service.market.driver.updateCreativeUnitAttribute.CreativeUnitAttributeUpdaterFactory;
import com.overseas.service.market.dto.creative.CreativeGetDTO;
import com.overseas.service.market.entity.CreativeUnit;
import com.overseas.service.market.enums.creative.units.CreativeUnitStatusEnum;
import com.overseas.service.market.events.creativeUnitAudit.CreativeUnitAuditEvent;
import com.overseas.service.market.vo.creative.*;
import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.vo.market.creative.unit.CreativeUnitBatchSaveVO;
import com.overseas.common.vo.market.creative.unit.CreativeUnitGetVO;
import com.overseas.common.vo.report.ReportListVO;
import com.overseas.service.market.service.CreativeService;
import com.overseas.service.market.service.CreativeUnitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Api(tags = "creative-创意相关接口")
@RestController
@RequestMapping("/market/creatives")
@RequiredArgsConstructor
@Slf4j
public class CreativeController extends AbstractController {

    private final CreativeService creativeService;

    private final CreativeUnitService creativeUnitService;

    private final ApplicationContext applicationContext;

    @ApiOperation(value = "保存创意", notes = "保存创意", produces = "application/json", response = R.class)
    @PostMapping("/save")
    public R save(@Validated @RequestBody CreativeSaveVO saveVO) {
        checkMasterId(saveVO.getMasterId());
        Long id = creativeService.saveCreative(saveVO, this.getUserId());
        applicationContext.publishEvent(new CreativeUnitAuditEvent(this, saveVO.getPlanId()));
        return R.data(id);
    }

    @ApiOperation(value = "保存创意", notes = "保存创意", produces = "application/json", response = R.class)
    @PostMapping("/audit/byId")
    public R save(@Validated @RequestBody CreativeGetVO creativeGetVO) {
        applicationContext.publishEvent(new CreativeUnitAuditEvent(this, creativeGetVO.getPlanId()));
        return R.ok();
    }

    @ApiOperation(value = "通过计划ID获取单个创意", notes = "通过计划ID获取单个创意", produces = "application/json", response = CreativeGetDTO.class)
    @PostMapping("/getByPlanId")
    public R get(@Validated @RequestBody CreativeGetVO getVO) {
        checkMasterId(getVO.getMasterId());
        return R.data(creativeService.getCreativeDetailByPlanId(getVO.getPlanId(), getVO.getMasterId()));
    }

    @ApiOperation(value = "创意属性更新接口", notes = "创意属性更新接口", produces = "application/json", response = R.class)
    @PostMapping("/attributes/update")
    public R updatePlanAttribute(@Validated @RequestBody CreativeUnitAttributeOperateVO vo) {
        this.checkMasterId(vo.getMasterId());
        CreativeUnitAttributeUpdater updater = CreativeUnitAttributeUpdaterFactory.createUpdater(vo.getOperateType());
        Integer affect = updater.update(vo.getId(), vo.getMasterId(), vo.getValue(), null, getUserId());
        return R.ok().put("data", affect);
    }

    @ApiOperation(value = "创意属性更新接口", notes = "创意属性更新接口", produces = "application/json", response = R.class)
    @PostMapping("/attributes/update/monitor")
    public R updatePlanAttributeMonitor(@Validated @RequestBody CreativeUnitAttributeOperateMonitorVO vo) {
        if (!vo.getSurplusCount().equals(-1)) {
            CreativeUnit creativeUnit = this.creativeUnitService.getById(vo.getId());
            CreativeGetDTO getDTO = this.creativeService.getCreativeDetailByPlanId(creativeUnit.getPlanId(), vo.getMasterId());
            if (getDTO.getUnits().stream().filter(u -> CreativeUnitStatusEnum.MARKETING.getId().equals(u.getCreativeUnitStatus())).count() <= vo.getSurplusCount()) {
                log.error("计划：{} 下所含有创意已经不足 {} 个，无法执行归档或者暂停操作，请去除该计划创意监控功能", creativeUnit.getPlanId(), vo.getSurplusCount());
                return R.error(500024, "");
            }
        }
        this.checkMasterId(vo.getMasterId());
        CreativeUnitAttributeUpdater updater = CreativeUnitAttributeUpdaterFactory.createUpdater(vo.getOperateType());
        Integer affect = updater.update(vo.getId(), vo.getMasterId(), vo.getValue(), null, getUserId());
        return R.ok().put("data", affect);
    }

    @ApiOperation(value = "获取创意单元下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/units/select")
    public R getCreativeUnitSelect(@Validated @RequestBody CreativeUnitSelectGetVO getVO) {
        return R.data(this.creativeUnitService.getCreativeUnitSelect(getVO));
    }

    @PostMapping("/report/list")
    public R getReportList(@Validated @RequestBody ReportListVO listVO) {
        return R.data(this.creativeService.listCreativeUnitAllData(listVO, this.listMasterId()));
    }

    @ApiOperation(value = "批量保存创意单元素材信息", produces = "application/json")
    @PostMapping("/batch/save")
    public R batchSaveCreativeUnit(@Validated @RequestBody CreativeUnitBatchSaveVO saveVO) {
        this.creativeService.batchSaveCreativeUnit(saveVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "根据素材ID获取创意单元列表", produces = "application/json")
    @PostMapping("/byAssetId/list")
    public R listCreativeUnitByAssetId(@Validated @RequestBody CreativeUnitListVO listVO) {
        return R.page(this.creativeUnitService.listCreativeUnitByAssetId(listVO, this.getUser()));
    }

    @ApiOperation(value = "根据创意单元获取其模版信息", produces = "application/json")
    @PostMapping("/templateInfo/get")
    public R getTemplateInfoById(@Validated @RequestBody CreativeUnitGetVO getVO) {
        return R.data(this.creativeUnitService.getTemplateInfoById(getVO));
    }

    @ApiOperation(value = "批量更新创意单元", produces = "application/json")
    @PostMapping("/units/batch/update")
    public R batchUpdateCreativeUnit(@Validated @RequestBody CreativeUnitBatchUpdateVO updateVO) {
        this.creativeUnitService.batchUpdateCreativeUnit(updateVO, this.getUserId());
        return R.ok();
    }

    /**
     * 自动上新
     */
    @ApiOperation(value = "自动上新管理列表", produces = "application/json")
    @PostMapping("/auto/asset/update/list")
    public R assetUpdateList(@Validated @RequestBody AutoAssetUpdateListVO listVO) {
        return R.page(this.creativeService.listAutoAssetUpdate(listVO, this.getUser()));
    }

    @ApiOperation(value = "自动上新批量操作", produces = "application/json")
    @PostMapping("/auto/asset/update/batch")
    public R autoAssetUpdateBatch(@Validated @RequestBody AutoAssetUpdateBatchVO batchVO) {
        this.creativeService.autoAssetUpdateBatch(batchVO, this.getUser());
        return R.ok();
    }

    @ApiOperation(value = "获取待上新数量", produces = "application/json")
    @PostMapping("/asset/wait/count")
    public R updateAssetWaitCount(@Validated @RequestBody CreativeGetVO getVO) {
        return R.data(this.creativeService.autoAssetWaitCount(getVO));
    }

    @ApiOperation(value = "修改待上新数量", produces = "application/json")
    @PostMapping("/update/asset/wait/count")
    public R updateAssetWaitCount(@Validated @RequestBody CreativeUpdateAssetWaitCountVO countVO) {
        this.creativeService.updateAssetWaitCount(countVO);
        return R.ok();
    }

    @ApiOperation(value = "广告单元投放状态", produces = "application/json")
    @PostMapping("/unit/put/status")
    public R creativeUnitPutStatus(@Validated @RequestBody CreativeUnitPutStatusVO unitPutStatusVO) {
        return R.data(this.creativeUnitService.creativeUnitPutStatus(unitPutStatusVO));
    }

    @ApiOperation(value = "广告单元投放状态", produces = "application/json")
    @PostMapping("/fill/size/creative/unit")
    public R creativeUnitFillSize(@Validated @RequestBody CreativeUnitFillSizeVO fillSizeVO) {
        return R.data(this.creativeUnitService.fillSizeToCreativeUnitId(fillSizeVO));
    }
}
