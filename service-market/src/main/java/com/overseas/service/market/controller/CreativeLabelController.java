package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.vo.market.creativeLabel.CreativeLabelSelectGetVO;
import com.overseas.service.market.service.CreativeLabelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Api(tags = "creativeLabel-创意标签")
@RestController
@RequestMapping("/market/creativeLabels")
@RequiredArgsConstructor
public class CreativeLabelController {

    private final CreativeLabelService creativeLabelService;

    @ApiOperation(value = "获取创意标签下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/select")
    public R selectCreativeLabel(@Validated @RequestBody CreativeLabelSelectGetVO getVO) {
        return R.data(this.creativeLabelService.selectCreativeLabel(getVO));
    }

    @ApiOperation(value = "获取创意标签下拉Map", produces = "application/json")
    @PostMapping("/map/select")
    public R selectCreativeLabelMap(@Validated @RequestBody CreativeLabelSelectGetVO getVO) {
        return R.data(this.creativeLabelService.selectCreativeLabelMap(getVO));
    }
}
