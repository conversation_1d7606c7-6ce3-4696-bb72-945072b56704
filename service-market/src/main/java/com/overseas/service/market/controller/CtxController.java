package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.vo.market.ctx.CtxGetVO;
import com.overseas.common.vo.market.ctx.CtxListVO;
import com.overseas.common.vo.market.ctx.CtxSaveVO;
import com.overseas.common.vo.market.ctx.CtxUpdateVO;
import com.overseas.service.market.service.CtxService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "plan-task-计划批量创建任务相关接口")
@RestController
@RequestMapping("/market/ctx")
@RequiredArgsConstructor
public class CtxController extends AbstractController {

    private final CtxService ctxService;

    @ApiOperation(value = "获取CTX列表", produces = "application/json")
    @PostMapping("/list")
    public R listCtx(@RequestBody @Validated CtxListVO listVO) {
        return R.page(this.ctxService.listCtx(listVO));
    }

    @ApiOperation(value = "获取CTX", produces = "application/json")
    @PostMapping("/get")
    public R getCtx(@RequestBody @Validated CtxGetVO getVO) {
        return R.data(this.ctxService.getCtx(getVO));
    }

    @ApiOperation(value = "新增CTX", produces = "application/json")
    @PostMapping("/save")
    public R saveCtx(@RequestBody @Validated CtxSaveVO saveVO) {
        this.ctxService.saveCtx(saveVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "更新CTX", produces = "application/json")
    @PostMapping("/update")
    public R updateCtx(@RequestBody @Validated CtxUpdateVO updateVO) {
        this.ctxService.updateCtx(updateVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "删除CTX", produces = "application/json")
    @PostMapping("/delete")
    public R deleteCtx(@RequestBody @Validated CtxGetVO getVO) {
        this.ctxService.deleteCtx(getVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "获取CTX级联", produces = "application/json")
    @PostMapping("/cascader")
    public R listCtxCascader() {
        return R.data(this.ctxService.listCtxCascader());
    }

    @ApiOperation(value = "获取已有行动号召国家", produces = "application/json")
    @PostMapping("/country/select")
    public R selectCtxCountry() {
        return R.data(this.ctxService.selectCountry());
    }
}
