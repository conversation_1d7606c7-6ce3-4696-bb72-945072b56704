package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.market.deal.*;
import com.overseas.common.vo.market.deal.*;
import com.overseas.service.market.service.DealService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Api(tags = "DealId管理")
@RestController
@AllArgsConstructor
@RequestMapping("/market/deals")
public class DealController extends AbstractController {

    private DealService dealService;

    @ApiOperation(value = "添加deal id", notes = "添加新的deal id信息", produces = "application/json")
    @PostMapping("/save")
    public R saveDeal(@RequestBody @Validated DealSaveVO dealSaveVO) {
        this.checkMasterId(dealSaveVO.getMasterId());
        return R.data(this.dealService.saveDeal(dealSaveVO, this.getUserId()));
    }

    @ApiOperation(value = "获取deal id", response = DealGetDTO.class, produces = "application/json")
    @PostMapping("/get")
    public R getDeal(@RequestBody @Validated DealGetVO dealGetVO) {
        this.checkMasterId(dealGetVO.getMasterId());
        return R.data(this.dealService.getDeal(dealGetVO));
    }

    @ApiOperation(value = "编辑deal id", produces = "application/json")
    @PostMapping("/update")
    public R updateDeal(@RequestBody @Validated DealUpdateVO dealUpdateVO) {
        this.checkMasterId(dealUpdateVO.getMasterId());
        Integer updateResult = this.dealService.updateDeal(dealUpdateVO, this.getUserId());
        return updateResult > 0 ? R.ok() : R.error("编辑失败");
    }

    @ApiOperation(value = "删除deal id", notes = "删除deal id信息", produces = "application/json")
    @PostMapping("/delete")
    public R deleteDeal(@RequestBody @Validated DealGetVO dealGetVO) {
        // 检查所属广告主权限
        this.checkMasterId(dealGetVO.getMasterId());
        int deleteResult = this.dealService.deleteDeal(dealGetVO, this.getUserId());
        return deleteResult > 0 ? R.ok() : R.error("删除deal id失败");
    }

    @ApiOperation(value = "获取deal id分页", response = DealListDTO.class, produces = "application/json")
    @PostMapping("/list")
    public R listDeal(@RequestBody @Validated DealListVO dealListVO) {
        // 校验广告主权限
        this.checkMasterId(dealListVO.getMasterId());
        // 调用分页接口
        return R.page(this.dealService.listDeal(dealListVO));
    }

    @ApiOperation(value = "开关deal id", produces = "application/json")
    @PostMapping("/switch")
    public R switchDeal(@RequestBody @Validated DealSwitchVO dealSwitchVO) {
        this.checkMasterId(dealSwitchVO.getMasterId());
        Integer switchResult = this.dealService.switchDeal(dealSwitchVO, this.getUserId());
        return switchResult > 0 ? R.ok() : R.error("开关deal id失败");
    }

    @ApiOperation(value = "deal id下拉数据", produces = "application/json")
    @PostMapping("/select")
    public R selectDeal(@RequestBody @Validated DealSelectVO dealSelectVO) {
        this.checkMasterId(dealSelectVO.getMasterId());
        return R.data(this.dealService.selectDeal(dealSelectVO));
    }
}
