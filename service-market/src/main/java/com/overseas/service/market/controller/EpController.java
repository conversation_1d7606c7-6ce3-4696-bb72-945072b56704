package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.service.market.dto.ep.EpListDTO;
import com.overseas.service.market.service.EpService;
import com.overseas.service.market.vo.ep.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 **/
@RequiredArgsConstructor
@Slf4j
@RestController
@RequestMapping("/market/ep")
@Api(tags = "EP管理")
public class EpController extends AbstractController {

    private final EpService epService;

    @ApiOperation(value = "EP list", response = EpListDTO.class)
    @PostMapping("/list")
    public R list(@RequestBody @Validated EpListVO listVO) {
        return R.page(epService.list(listVO));
    }

    @ApiOperation(value = "EP 保存", response = R.class)
    @PostMapping("/save")
    public R save(@RequestBody @Validated EpSaveVO saveVO) {
        this.epService.save(saveVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "EP 获取")
    @PostMapping("/get")
    public R get(@RequestBody @Validated EpGetVO getVO) {
        return R.data(this.epService.get(getVO));
    }

    @ApiOperation(value = "EP 更新")
    @PostMapping("/update")
    public R update(@RequestBody @Validated EpUpdateVO updateVO) {
        this.epService.update(updateVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "EP 绑定模板")
    @PostMapping("/bind")
    public R bind(@RequestBody @Validated EpBindVO bindVO) {
        this.epService.bind(bindVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "EP 解绑模板")
    @PostMapping("/unbind")
    public R unbind(@RequestBody @Validated EpUnbindVO unbindVO) {
        this.epService.unbind(unbindVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "EP 删除")
    @PostMapping("/del")
    public R del(@RequestBody @Validated EpDeleteVO deleteVO) {
        this.epService.del(deleteVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "根据adxID更新所有关联计划的ep定向")
    @GetMapping("/plans/direct/update")
    public void updateEpDirectByAdx(@RequestParam("adxId") Integer adxId) {
        this.epService.updateEpDirectByAdx(adxId);
    }
}
