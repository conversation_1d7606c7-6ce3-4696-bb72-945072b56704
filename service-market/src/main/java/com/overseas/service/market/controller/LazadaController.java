package com.overseas.service.market.controller;

import com.overseas.common.utils.DateUtils;
import com.overseas.service.market.service.LazadaService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/market/lazada")
@RequiredArgsConstructor
@Slf4j
@Api(value = "Lazada相关接口")
public class LazadaController extends AbstractController {

    private final LazadaService lazadaService;

    @GetMapping("/products/pull")
    public void pullLazadaProduct() {
        this.lazadaService.pullProducts();
    }

    @GetMapping("/category/pull")
    public void pullLazadaCategory() {
        this.lazadaService.pullCategories();
    }

    @GetMapping("/cps/order/products/pull")
    public void pullLazadaCpsOrderProduct() {
        this.lazadaService.pullProductsByCpsOrder();
    }

    @GetMapping("/conversion/report")
    public void listConversionReport(@RequestParam("start") String start, @RequestParam("end") String end) {
        DateUtils.getBetweenDate(start, end, DateUtils.DATE_PATTERN)
                .forEach(day -> {
                    log.info("lazada cps pull get day :{}", day);
                    this.lazadaService.listConversionReport(day);
                });
    }

    @RequestMapping("/conversion/export/oppo/between")
    public void exportOppo(@RequestParam(name = "start", defaultValue = "") String start,
                           @RequestParam(name = "end", defaultValue = "") String end) {
        this.lazadaService.exportConversion(DateUtils.getBetweenDate(start, end, DateUtils.DATE_PATTERN));
    }

    @RequestMapping("/conversion/export/oppo")
    public void exportOppo(@RequestParam(name = "day", defaultValue = "") String day) {
        this.lazadaService.exportConversion(List.of(this.dealDay(day)));
    }

    @GetMapping("/product/cps/url/generate")
    public void generateProductCpsUrl() {
        this.lazadaService.generateProductCpsUrl();
    }

    @GetMapping("/conversion/export/oppo/byAd")
    public void exportOppoByAd(@RequestParam(name = "day", defaultValue = "") String day) throws IOException {
        this.lazadaService.exportByAd(this.dealDay(day));
    }

    @GetMapping("/conversion/export/oppo/byDay")
    public void exportOppoByDay(@RequestParam("start") String start, @RequestParam("end") String end)
            throws IOException {
        this.lazadaService.exportByDay(start, end);
    }

    /**
     * 返回数据
     *
     * @param day 天
     * @return 数据处理
     */
    private String dealDay(String day) {
        if (StringUtils.isBlank(day)) {
            Date today = new Date();
            day = DateUtils.format(DateUtils.afterDay(today, -1));
        }
        return day;
    }

}
