package com.overseas.service.market.controller;

import com.overseas.common.vo.market.master.MasterPageFirstExportVO;
import com.overseas.service.market.dto.market.MasterPageFirstDTO;
import com.overseas.common.dto.R;
import com.overseas.common.dto.market.market.MarketBatchListDTO;
import com.overseas.common.vo.market.market.MarketBatchListVO;
import com.overseas.common.vo.market.market.MarketBudgetUpdateVO;
import com.overseas.common.vo.market.market.MarketDimensionInfoVO;
import com.overseas.common.vo.market.market.RecordBatchDeleteVO;
import com.overseas.common.vo.market.market.RecordBatchSwitchVO;
import com.overseas.common.vo.market.master.MasterPageFirstVO;
import com.overseas.service.market.service.MarketService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-18 19:42
 */
@Api(tags = "推广相关接口")
@RestController
@RequestMapping("/market/market")
@RequiredArgsConstructor
public class MarketController extends AbstractController {

    private final MarketService marketService;

    @ApiOperation(value = "投放账号推广列表", produces = "application/json", response = MasterPageFirstDTO.class)
    @PostMapping("/list")
    public R listMarketMaster(@Validated @RequestBody MasterPageFirstVO listVO) {
        return R.page(marketService.page(listVO, listMasterId(), getUser()));
    }

    @ApiOperation(value = "导出投放账号推广列表", produces = "application/json", response = MasterPageFirstDTO.class)
    @PostMapping("/list/export")
    public void exportPage(@Validated @RequestBody MasterPageFirstExportVO listVO, HttpServletResponse response)
            throws IOException {
        this.marketService.exportPage(listVO, listMasterId(), getUser(), response);
    }

    @ApiOperation(value = "投放账号推广列表(获取自定义列表项目标识)", produces = "application/json", response = MasterPageFirstDTO.class)
    @PostMapping("/list/identify")
    public R listMarketMasterIdentify(@Validated @RequestBody MasterPageFirstVO listVO) {
        return R.data(marketService.pageIdentify(listVO, listMasterId(), getUser()));
    }

    @ApiOperation(value = "删除投放数据", produces = "application/json")
    @PostMapping("/delete")
    public R deleteRecord(@Validated @RequestBody RecordBatchDeleteVO batchDeleteVO) {
        this.checkMasterId(batchDeleteVO.getMasterId().intValue());
        batchDeleteVO.setUserId(getUserId());
        boolean result = marketService.batchDeleteRecord(batchDeleteVO);
        return result ? R.ok("删除成功，有投放数据的记录不会被删除") : R.error("批量删除异常");
    }

    @ApiOperation(value = "开关投放数据", produces = "application/json")
    @PostMapping("/switch")
    public R switchRecordStatus(@Validated @RequestBody RecordBatchSwitchVO batchSwitchVO) {
        batchSwitchVO.setUserId(getUserId());
        boolean result = marketService.batchSwitchRecord(batchSwitchVO);
        return result ? R.ok("批量开关成功") : R.error("批量开关异常");
    }

    @ApiOperation(value = "测试批量更新投放预算", produces = "application/json")
    @PostMapping("/budget/update/test")
    public R updateMarketBudgetTest(@Validated @RequestBody MarketBudgetUpdateVO updateVO) {
        this.marketService.updateMarketBudgetTest(updateVO);
        return R.ok();
    }

    @ApiOperation(value = "获取批量列表", produces = "application/json", response = MarketBatchListDTO.class)
    @PostMapping("/batch/list")
    public R listMarketBatch(@Validated @RequestBody MarketBatchListVO listVO) {
        return R.page(this.marketService.listMarketBatch(listVO, this.getUser()));
    }

    @ApiOperation(value = "测试批量更新投放预算", produces = "application/json")
    @PostMapping("/dimension/info")
    public R dimensionInfo(@Validated @RequestBody MarketDimensionInfoVO dimensionInfoVO) {
        return R.data(this.marketService.dimensionInfo(dimensionInfoVO));
    }

}

