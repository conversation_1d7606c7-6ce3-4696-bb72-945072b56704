package com.overseas.service.market.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.overseas.common.vo.market.master.*;
import com.overseas.common.configuration.SheinConfiguration;
import com.overseas.service.market.dto.master.OverviewDTO;
import com.overseas.service.market.entity.Master;
import com.overseas.service.market.vo.master.MasterListVO;
import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.master.MasterBindInfoGetDTO;
import com.overseas.common.dto.market.master.MasterGetDTO;
import com.overseas.common.dto.market.master.MasterListDTO;
import com.overseas.common.dto.sys.finance.UserFinanceGetDTO;
import com.overseas.common.utils.DecimalUtils;
import com.overseas.common.utils.FeignR;
import com.overseas.common.validation.group.AddGroup;
import com.overseas.common.vo.common.GetVO;
import com.overseas.common.vo.market.market.MarketMasterGetVO;
import com.overseas.service.market.service.MasterService;
import com.overseas.service.market.vo.master.MasterRatioListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-16 16:06
 */

@Api(tags = "master-投放账号相关接口")
@RestController
@RequestMapping("/market/masters")
@RequiredArgsConstructor
public class MasterController extends AbstractController {

    private final MasterService masterService;

    private final SheinConfiguration sheinConfiguration;

    @ApiOperation(value = "投放账号下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/select")
    public R selectMaster() {
        return R.data(this.masterService.selectMaster(listMasterId()));
    }

    @ApiOperation(value = "投放账号下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/select/bySort")
    public R selectMasterBySort(@RequestBody @Validated MasterSelectVO selectVO) {
        return R.data(this.masterService.selectMasterBySort(selectVO, listMasterId()));
    }

    @ApiOperation(value = "投放账号下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/select/params")
    public R selectMasterParams(MasterSelectVO selectVO) {
        return R.data(this.masterService.selectMasterParams(selectVO,listMasterId()));
    }

    @ApiOperation(value = "投放账号下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/select/by/info")
    public R selectMasterByInfo(@RequestBody @Validated MasterSelectByInfoVO byInfoVO) {
        return R.data(this.masterService.selectMasterByInfo(byInfoVO));
    }

    @ApiOperation(value = "获取全部投放账号下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/all/select")
    public R getAllMasterSelect() {
        return R.data(this.masterService.getAllMasterSelect());
    }

    @ApiOperation(value = "投放账号列表", notes = "投放账号列表", produces = "application/json", response = MasterListDTO.class)
    @PostMapping("/list")
    public R listMaster(@Validated @RequestBody MasterListVO masterListVO) {
        return R.page(this.masterService.pageMaster(masterListVO, listMasterId(), getUser()));
    }

    @ApiOperation(value = "投放账号列表", notes = "投放账号列表", produces = "application/json", response = MasterListDTO.class)
    @PostMapping("/list/ratio")
    public R listMaster(@Validated @RequestBody MasterRatioListVO listVO) {
        return R.data(this.masterService.masterRatios(listVO));
    }

    @ApiOperation(value = "新增投放账号", notes = "新增投放账号", produces = "application/json", response = R.class)
    @PostMapping("/add")
    public R add(@Validated(AddGroup.class) @RequestBody MasterSaveVO masterSaveVO) {
        return R.data(this.masterService.saveMaster(masterSaveVO, getUserId()));
    }

    @ApiOperation(value = "编辑投放账号")
    @PostMapping("/update")
    public R update(@Validated @RequestBody MasterSaveVO saveVO) {
        this.masterService.updateMaster(saveVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "获取投放账号", notes = "获取投放账号", produces = "application/json", response = MasterGetDTO.class)
    @PostMapping("/get")
    public R get(@Validated @RequestBody MasterGetVO masterGetVO) {
        this.checkMasterId(masterGetVO.getId());
        return R.data(masterService.getMaster(masterGetVO, getUserId()));
    }

    @ApiOperation(value = "授权账号", notes = "授权账号", produces = "application/json", response = R.class)
    @PostMapping("/bind")
    public R bindManager(@Validated @RequestBody MasterBindVO masterBindVO) {
        this.checkMasterId(masterBindVO.getId());
        this.masterService.bind(masterBindVO, getUser());
        return R.ok();
    }

    @ApiOperation(value = "获取当前账号的绑定信息", notes = "获取当前账号的绑定信息", produces = "application/json", response = MasterBindInfoGetDTO.class)
    @PostMapping("/bindInfo/get")
    public R getBindInfo(@Validated @RequestBody MasterBindInfoGetVO masterBindInfoGetVO) {
        this.checkMasterId(masterBindInfoGetVO.getId());
        return R.data(this.masterService.getBindInfo(masterBindInfoGetVO));
    }

    @ApiOperation(value = "编辑账户日预算", notes = "编辑账户日预算", produces = "application/json", response = R.class)
    @PostMapping("/budgetDay/update")
    public R updateBudgetDay(@Validated @RequestBody MasterBudgetUpdateVO masterBudgetUpdateVO) {
        this.checkMasterId(masterBudgetUpdateVO.getId());
        this.masterService.updateBudgetDay(masterBudgetUpdateVO, getUserId());
        return R.ok();
    }

    @ApiOperation(value = "编辑花费系数（统一调用）", notes = "编辑花费系数", produces = "application/json", response = R.class)
    @PostMapping("/costRatio/update/route")
    public R updateReportCostRatioRoute(@Validated @RequestBody MasterCostRatioVO costRatioVO) {
        this.masterService.updateReportCostRoute(costRatioVO);
        return R.ok();
    }

    @ApiOperation(value = "编辑花费系数（真实执行）", notes = "编辑花费系数", produces = "application/json", response = R.class)
    @PostMapping("/costRatio/update")
    public R updateReportCostRatio(@Validated @RequestBody MasterCostRatioVO costRatioVO) {
        this.checkMasterId(costRatioVO.getId());
        this.masterService.updateReportCost(costRatioVO);
        return R.ok();
    }


    @ApiOperation(value = "根据指定广告主，获取该广告主所属管理账号下所有广告主数据下拉", notes = "根据指定广告主，获取该广告主所属管理账号下所有广告主数据下拉",
            produces = "application/json", response = SelectDTO.class)
    @PostMapping("/by/master/select")
    public R selectByMasterId(@Validated @RequestBody MasterGetVO getVO) {
        return R.data(this.masterService.selectByMasterId(getVO));
    }

    @ApiOperation(value = "账户概览", notes = "账户概览", produces = "application/json", response = R.class)
    @PostMapping("/overview/get")
    public R getOverview(@Validated @RequestBody MasterGetVO masterGetVO) {
        this.checkMasterId(masterGetVO.getId());
        OverviewDTO overviewDTO = new OverviewDTO();
        FeignR<UserFinanceGetDTO> feignR = this.fgSystemService.getUserFinanceData(new GetVO(masterGetVO.getId().longValue()));
        if (!feignR.getCode().equals(0)) {
            return R.error(feignR.getCode(), feignR.getMsg());
        }
        UserFinanceGetDTO userFinance = feignR.getData();

        overviewDTO.setBalance(userFinance.getAmount());
        Master masterInDb = masterService.getOne(new LambdaQueryWrapper<Master>().eq(Master::getUserId, masterGetVO.getId()));
        overviewDTO.setBudgetDay(DecimalUtils.format3(masterInDb.getBudgetDay()));

        // 获取日消耗
        overviewDTO.setConsumeDay(userFinance.getDayCost());

        if (overviewDTO.getBudgetDay().compareTo(BigDecimal.ZERO) <= 0) {
            overviewDTO.setConsumeRate(BigDecimal.ZERO);
        } else {
            overviewDTO.setConsumeRate(overviewDTO.getConsumeDay().multiply(BigDecimal.valueOf(100))
                    .divide(overviewDTO.getBudgetDay(), 2, RoundingMode.HALF_UP));
        }
        return R.data(overviewDTO);
    }

    @ApiOperation(value = "批量编辑", produces = "application/json")
    @PostMapping("/batch/update")
    public R batchUpdateMaster(@Validated @RequestBody MasterBatchUpdateVO updateVO) {
        this.masterService.batchUpdateMaster(updateVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "获取账户关联深度转化目标事件下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/action/select")
    public R getMasterActionSelect(@Validated @RequestBody MarketMasterGetVO getVO) {
        return R.data(this.masterService.getMasterActionSelect(getVO));
    }

    @ApiOperation(value = "获取账号时区", produces = "application/json")
    @PostMapping("/timezone/get")
    public R getMasterTimezone(@Validated @RequestBody MasterTimeZoneGetVO getVO) {
        return R.data(this.masterService.getMasterTimeZone(getVO));
    }

    @ApiOperation(value = "获取账号时区", produces = "application/json")
    @PostMapping("/timezone/list")
    public R listMasterTimezone(@Validated @RequestBody MasterTimeZoneGetVO getVO) {
        return R.data(this.masterService.getMasterTimeZone(getVO));
    }

    @ApiOperation(value = "获取账号项目", produces = "application/json")
    @PostMapping("/project/get")
    public R listMasterProject(@Validated @RequestBody MasterProjectGetVO getVO) {
        return R.data(this.masterService.listMasterProject(getVO));
    }

    @ApiOperation(value = "获取账号级联下拉", produces = "application/json")
    @PostMapping("/project/cascader")
    public R listMasterCascader(@Validated @RequestBody MasterCascaderGetVO getVO) {
        return R.data(this.masterService.listMasterCascader(getVO));
    }

    @ApiOperation(value = "获取账号穿梭树", produces = "application/json")
    @PostMapping("/project/tree")
    public R listMasterTree(@Validated @RequestBody MasterCascaderGetVO getVO) {
        //TODO shein特殊修改
        if (sheinConfiguration.isRole(this.getUser().getRoleId())) {
            getVO.setProjectId(sheinConfiguration.getProjectId());
        }
        return R.data(this.masterService.listMasterTree(getVO));
    }
}
