package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.vo.market.material.MaterialAssetVO;
import com.overseas.common.vo.market.material.MaterialGetVO;
import com.overseas.common.vo.market.material.MaterialSaveVO;
import com.overseas.service.market.service.MaterialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Api(tags = "推广相关接口")
@RestController
@RequestMapping("/market/materials/")
@RequiredArgsConstructor
public class MaterialController extends AbstractController {

    private final MaterialService materialService;

    @ApiOperation(value = "新增素材组记录", notes = "新增素材组记录", produces = "application/json")
    @PostMapping("/save")
    public R saveMaterial(@RequestBody @Validated MaterialSaveVO saveVO) {
        this.materialService.saveMaterial(saveVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "获取素材组素材列表", notes = "获取素材组素材列表", produces = "application/json", response = MaterialAssetVO.class)
    @PostMapping("/get")
    public R getMaterial(@RequestBody @Validated MaterialGetVO getVO) {
        return R.data(this.materialService.getMaterialDetail(getVO));
    }

    @ApiOperation(value = "获取素材组素材列表", notes = "获取素材组素材列表", produces = "application/json", response = MaterialAssetVO.class)
    @GetMapping("/hash/refresh")
    public R hashRefresh() {
        this.materialService.hashRefresh();
        return R.ok();
    }
}
