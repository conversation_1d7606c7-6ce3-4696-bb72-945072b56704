package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.monitor.MonitorListDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.vo.market.monitor.*;
import com.overseas.service.market.enums.monitor.MonitorTypeEnum;
import com.overseas.service.market.service.MonitorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Api(tags = "监测站点管理")
@RestController
@RequestMapping("/market/monitors")
@RequiredArgsConstructor
public class MonitorController extends AbstractController {

    private final MonitorService monitorService;

    @ApiOperation(value = "获取监测站点下拉数据", notes = "获取监测站点下拉数据", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/select")
    public R getMonitorSelect(@Validated @RequestBody MonitorSelectGetVO getVO) {
        return R.data(this.monitorService.getMonitorSelect(getVO));
    }

    @ApiOperation(value = "监测站点列表", notes = "监测站点列表", produces = "application/json", response = MonitorListDTO.class)
    @PostMapping("/list")
    public R listMonitor(@Validated @RequestBody MonitorListVO listVO) {
        return R.page(this.monitorService.getMonitorPage(listVO));
    }

    @ApiOperation(value = "新增监测站点", notes = "新增监测站点", produces = "application/json")
    @PostMapping("/save")
    public R saveMonitor(@Validated @RequestBody MonitorSaveVO saveVO) {
        return R.data(this.monitorService.saveMonitor(saveVO, this.getUserId()));
    }

    @ApiOperation(value = "获取监测站点数据详情", notes = "获取监测站点数据详情", produces = "application/json")
    @PostMapping("/get")
    public R getMonitor(@Validated @RequestBody MonitorGetVO getVO) {
        return R.data(this.monitorService.getMonitor(getVO));
    }

    @ApiOperation(value = "删除监测站点", notes = "删除监测站点", produces = "application/json")
    @PostMapping("/delete")
    public R deleteMonitor(@Validated @RequestBody MonitorGetVO getVO) {
        this.monitorService.deleteMonitor(getVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "修改监测站点状态", notes = "修改监测站点状态", produces = "application/json")
    @PostMapping("/switch")
    public R changeMonitorStatus(@Validated @RequestBody MonitorStatusGetVO getVO) {
        this.monitorService.changeMonitorStatus(getVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "监测站点类型下拉数据", notes = "监测站点类型下拉数据", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/type/select")
    public R getMonitorTypeSelect() {
        return R.data(ICommonEnum.list(MonitorTypeEnum.class));
    }
}
