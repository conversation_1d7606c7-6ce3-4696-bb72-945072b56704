package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.monitor.monitorEvent.MonitorEventGetDTO;
import com.overseas.common.dto.market.monitor.monitorEvent.MonitorEventListDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.vo.market.monitor.monitorEvent.*;
import com.overseas.service.market.enums.monitor.monitorEvent.MonitorEventSourceTypeEnum;
import com.overseas.service.market.service.MonitorEventService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-23 15:35
 */
@Api(tags = "监测事件管理")
@RestController
@RequestMapping("/market/monitorEvents")
@RequiredArgsConstructor
public class MonitorEventController extends AbstractController {

    private final MonitorEventService monitorEventService;

    @ApiOperation(value = "获取监测事件下拉数据", notes = "获取监测事件下拉数据", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/select")
    public R getMonitorEventSelect(@Validated @RequestBody MonitorEventSelectGetVO getVO) {
        return R.data(this.monitorEventService.getEventSelect(getVO));
    }

    @ApiOperation(value = "监测事件列表", notes = "监测事件列表", produces = "application/json", response = MonitorEventListDTO.class)
    @PostMapping("/list")
    public R listMonitorEvent(@Validated @RequestBody MonitorEventListVO listVO) {
        return R.page(this.monitorEventService.getEventPage(listVO));
    }

    @ApiOperation(value = "获取监测事件数据详情", notes = "获取监测事件数据详情", produces = "application/json", response = MonitorEventGetDTO.class)
    @PostMapping("/get")
    public R getMonitorEvent(@Validated @RequestBody MonitorEventGetVO getVO) {
        return R.data(this.monitorEventService.getEvent(getVO));
    }

    @ApiOperation(value = "新增监测事件", notes = "新增监测事件", produces = "application/json")
    @PostMapping("/save")
    public R saveMonitorEvent(@Validated @RequestBody MonitorEventSaveVO saveVO) {
        return R.data(this.monitorEventService.saveEvent(saveVO, this.getUserId()));
    }

    @ApiOperation(value = "删除监测事件", notes = "删除监测事件", produces = "application/json")
    @PostMapping("/delete")
    public R deleteMonitorEvent(@Validated @RequestBody MonitorEventGetVO getVO) {
        this.monitorEventService.deleteEvent(getVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "修改监测事件状态", notes = "修改监测事件状态", produces = "application/json")
    @PostMapping("/switch")
    public R changeMonitorEventStatus(@Validated @RequestBody MonitorEventStatusGetVO getVO) {
        this.monitorEventService.changeEventStatus(getVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "获取转化来源类型下拉", notes = "获取转化来源类型下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/sourceType/select")
    public R getSourceTypeSelect() {
        return R.data(ICommonEnum.list(MonitorEventSourceTypeEnum.class));
    }

    @ApiOperation(value = "根据行动转化ID获取转化事件ID", produces = "application/json")
    @PostMapping("/eventIds/get")
    public R listMonitorEventIdsByAction(@Validated @RequestBody MonitorEventIdsGetVO getVO) {
        return R.data(this.monitorEventService.listMonitorEventIdsByAction(getVO.getMasterId(), getVO.getActionIds()));
    }
}
