package com.overseas.service.market.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.overseas.common.dto.R;
import com.overseas.common.enums.MachineRoomEnum;
import com.overseas.common.utils.HttpUtils;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.service.market.dto.openApi.campaign.CampaignPageDTO;
import com.overseas.service.market.dto.openApi.campaign.OpenApiCampaignListDTO;
import com.overseas.service.market.dto.openApi.creativeUnit.CreativeUnitPageDTO;
import com.overseas.service.market.dto.openApi.creativeUnit.OpenApiCreativeUnitListDTO;
import com.overseas.service.market.dto.openApi.master.OpenApiMasterListDTO;
import com.overseas.service.market.dto.openApi.plan.OpenApiPlanListDTO;
import com.overseas.service.market.dto.openApi.plan.PlanPageDTO;
import com.overseas.service.market.dto.openApi.report.OpenApiReportListDTO;
import com.overseas.service.market.dto.openApi.report.ReportPageDTO;
import com.overseas.service.market.service.OpenApiService;
import com.overseas.service.market.vo.openApi.campaign.OpenApiCampaignListVO;
import com.overseas.service.market.vo.openApi.creativeUnit.OpenApiCreativeUnitListVO;
import com.overseas.service.market.vo.openApi.master.OpenApiMasterListVO;
import com.overseas.service.market.vo.openApi.plan.OpenApiPlanListVO;
import com.overseas.service.market.vo.openApi.report.OpenApiReportListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * OpenApi模块
 */
@RestController
@RequestMapping("/api/v1")
@RequiredArgsConstructor
@Slf4j
@Api(value = "OpenApi对外接口模块")
public class OpenApiController {

    private final OpenApiService openApiService;

    @ApiOperation(value = "获取广告主列表", produces = "application/json", response = OpenApiMasterListDTO.class)
    @PostMapping("/master/list")
    public R listOpenApiMaster(@Validated @RequestBody OpenApiMasterListVO listVO, HttpServletRequest request) {
        return R.page(this.openApiService.listMaster(listVO, Integer.parseInt(request.getHeader("appKey"))));
    }

    @ApiOperation(value = "获取活动列表", produces = "application/json", response = OpenApiCampaignListDTO.class)
    @PostMapping("/campaign/list")
    public R listOpenApiCampaign(@Validated @RequestBody OpenApiCampaignListVO listVO, HttpServletRequest request) {
        CampaignPageDTO result = new CampaignPageDTO();
        List<MachineRoomEnum> enumList = MachineRoomEnum.listMachineRoomEnum();
        for (MachineRoomEnum machineRoomEnum : enumList) {
            listVO.setRegion(machineRoomEnum.getRegion());
            String requestResult = this.getData(machineRoomEnum, request, ObjectUtils.toMap(listVO));
            CampaignPageDTO temp = JSONObject.parseObject(requestResult, new TypeReference<>(){});
            result.setTotal(result.getTotal() > temp.getTotal() ? result.getTotal() : temp.getTotal());
            if (null == result.getData()) {
                result.setData(temp.getData());
            } else {
                result.getData().addAll(temp.getData());
            }
        }
        return R.page(result.getData(), result.getTotal());
    }

    @ApiOperation(value = "获取活动列表", produces = "application/json", response = OpenApiCampaignListDTO.class)
    @PostMapping("/data/campaign/list")
    public PageUtils<OpenApiCampaignListDTO> listOpenApiCampaignData(
            @Validated @RequestBody OpenApiCampaignListVO listVO, HttpServletRequest request) {
        return this.openApiService.listCampaign(listVO, Integer.parseInt(request.getHeader("appKey")));
    }

    @ApiOperation(value = "获取计划列表", produces = "application/json", response = OpenApiPlanListDTO.class)
    @PostMapping("/plan/list")
    public R listOpenApiPlan(@Validated @RequestBody OpenApiPlanListVO listVO, HttpServletRequest request) {
        PlanPageDTO result = new PlanPageDTO();
        List<MachineRoomEnum> enumList = MachineRoomEnum.listMachineRoomEnum();
        for (MachineRoomEnum machineRoomEnum : enumList) {
            listVO.setRegion(machineRoomEnum.getRegion());
            String requestResult = this.getData(machineRoomEnum, request, ObjectUtils.toMap(listVO));
            PlanPageDTO temp = JSONObject.parseObject(requestResult, new TypeReference<>(){});
            result.setTotal(result.getTotal() > temp.getTotal() ? result.getTotal() : temp.getTotal());
            if (null == result.getData()) {
                result.setData(temp.getData());
            } else {
                result.getData().addAll(temp.getData());
            }
        }
        return R.page(result.getData(), result.getTotal());
    }

    @ApiOperation(value = "获取计划列表", produces = "application/json", response = OpenApiPlanListDTO.class)
    @PostMapping("/data/plan/list")
    public PageUtils<OpenApiPlanListDTO> listOpenApiPlanData(
            @Validated @RequestBody OpenApiPlanListVO listVO, HttpServletRequest request) {
        return this.openApiService.listPlan(listVO, Integer.parseInt(request.getHeader("appKey")));
    }

    @ApiOperation(value = "获取创意单元列表", produces = "application/json", response = OpenApiCreativeUnitListDTO.class)
    @PostMapping("/creativeUnit/list")
    public R listOpenApiCreativeUnit(
            @Validated @RequestBody OpenApiCreativeUnitListVO listVO, HttpServletRequest request) {
        CreativeUnitPageDTO result = new CreativeUnitPageDTO();
        List<MachineRoomEnum> enumList = MachineRoomEnum.listMachineRoomEnum();
        for (MachineRoomEnum machineRoomEnum : enumList) {
            listVO.setRegion(machineRoomEnum.getRegion());
            String requestResult = this.getData(machineRoomEnum, request, ObjectUtils.toMap(listVO));
            CreativeUnitPageDTO temp = JSONObject.parseObject(requestResult, new TypeReference<>(){});
            result.setTotal(result.getTotal() > temp.getTotal() ? result.getTotal() : temp.getTotal());
            if (null == result.getData()) {
                result.setData(temp.getData());
            } else {
                result.getData().addAll(temp.getData());
            }
        }
        return R.page(result.getData(), result.getTotal());
    }

    @ApiOperation(value = "获取创意单元列表", produces = "application/json", response = OpenApiCreativeUnitListDTO.class)
    @PostMapping("/data/creativeUnit/list")
    public PageUtils<OpenApiCreativeUnitListDTO> listOpenApiCreativeUnitData(
            @Validated @RequestBody OpenApiCreativeUnitListVO listVO, HttpServletRequest request) {
        return this.openApiService.listCreativeUnit(listVO, Integer.parseInt(request.getHeader("appKey")));
    }

    @ApiOperation(value = "获取报表数据列表", produces = "application/json", response = OpenApiReportListDTO.class)
    @PostMapping("/report/list")
    public R listOpenApiReport(@Validated @RequestBody OpenApiReportListVO listVO, HttpServletRequest request) {
        ReportPageDTO result = new ReportPageDTO();
        List<MachineRoomEnum> enumList = MachineRoomEnum.listMachineRoomEnum();
        for (MachineRoomEnum machineRoomEnum : enumList) {
            listVO.setRegion(machineRoomEnum.getRegion());
            String requestResult = this.getData(machineRoomEnum, request, ObjectUtils.toMap(listVO));
            ReportPageDTO temp = JSONObject.parseObject(requestResult, new TypeReference<>(){});
            result.setTotal(result.getTotal() > temp.getTotal() ? result.getTotal() : temp.getTotal());
            if (null == result.getData()) {
                result.setData(temp.getData());
            } else {
                result.getData().addAll(temp.getData());
            }
        }
        return R.page(result.getData(), result.getTotal());
    }

    @ApiOperation(value = "获取报表数据列表", produces = "application/json", response = OpenApiReportListDTO.class)
    @PostMapping("/data/report/list")
    public PageUtils<OpenApiReportListDTO> listOpenApiReportData(
            @Validated @RequestBody OpenApiReportListVO listVO, HttpServletRequest request) {
        return this.openApiService.listReport(listVO, Integer.parseInt(request.getHeader("appKey")));
    }

    private String getData(MachineRoomEnum machineRoomEnum, HttpServletRequest request,
                                        Map<String, Object> params) {
        return HttpUtils.post(machineRoomEnum.getApiUrl()
                        + request.getRequestURI().replace("/v1/", "/v1/data/"),
                        params, this.getHeaderMap(request));
    }

    private Map<String, Object> getHeaderMap(HttpServletRequest request) {
        Map<String, Object> headerMap = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            headerMap.put(headerName, request.getHeader(headerName));
        }
        return headerMap;
    }
}
