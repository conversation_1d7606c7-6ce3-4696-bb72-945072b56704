package com.overseas.service.market.controller;

import com.overseas.service.market.dto.operate.log.OperateLogListDTO;
import com.overseas.service.market.vo.operate.log.OperateLogListVO;
import com.overseas.common.dto.R;
import com.overseas.service.market.service.OperateLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-09-15 14:35
 */
@Api(tags = "master-操作日志相关接口")
@RestController
@RequestMapping("/market/logs")
@RequiredArgsConstructor
public class OperateLogController extends AbstractController {

    private final OperateLogService operateLogService;

    @ApiOperation(value = "操作日志列表", notes = "操作日志列表", produces = "application/json", response = OperateLogListDTO.class)
    @PostMapping("/list")
    public R listLogs(@Validated @RequestBody OperateLogListVO vo) {
        return R.page(this.operateLogService.pageLog(vo, getUser()));
    }
}
