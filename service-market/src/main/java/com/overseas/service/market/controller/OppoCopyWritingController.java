package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.vo.market.oppoCopyWriting.OppoCopyWritingListVO;
import com.overseas.common.vo.market.oppoCopyWriting.OppoCopyWritingSaveVO;
import com.overseas.service.market.service.OppoCopyWritingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * OPPO文案管理控制器
 * <AUTHOR>
 */
@Api(tags = "OPPO文案管理")
@RestController
@RequestMapping("/market/oppo/copyWritings")
@RequiredArgsConstructor
public class OppoCopyWritingController extends AbstractController {

    private final OppoCopyWritingService oppoCopyWritingService;

    @ApiOperation(value = "获取OPPO文案列表", produces = "application/json")
    @PostMapping("/list")
    public R listOppoCopyWriting(@RequestBody @Validated OppoCopyWritingListVO listVO) {
        return R.page(this.oppoCopyWritingService.listOppoCopyWriting(listVO));
    }

    @ApiOperation(value = "新增OPPO文案", produces = "application/json")
    @PostMapping("/save")
    public R batchSaveOppoCopyWriting(@RequestBody @Validated OppoCopyWritingSaveVO saveVO) {
        this.oppoCopyWritingService.saveOppoCopyWriting(saveVO, getUserId());
        return R.ok();
    }
}