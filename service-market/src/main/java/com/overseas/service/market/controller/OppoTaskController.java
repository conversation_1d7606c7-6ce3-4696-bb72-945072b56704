package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.vo.market.oppoTask.OppoTaskListVO;
import com.overseas.common.vo.market.oppoTask.OppoTaskSaveVO;
import com.overseas.service.market.service.OppoTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * OPPO任务管理控制器
 * <AUTHOR>
 */
@Api(tags = "OPPO任务管理")
@RestController
@RequestMapping("/market/oppo/tasks")
@RequiredArgsConstructor
public class OppoTaskController extends AbstractController {

    private final OppoTaskService oppoTaskService;

    @ApiOperation(value = "获取OPPO任务列表", produces = "application/json")
    @PostMapping("/list")
    public R listOppoTask(@RequestBody @Validated OppoTaskListVO listVO) {
        return R.page(this.oppoTaskService.listOppoTask(listVO));
    }

    @ApiOperation(value = "创建OPPO任务", produces = "application/json")
    @PostMapping("/save")
    public R saveOppoTask(@RequestBody @Validated OppoTaskSaveVO saveVO) {
        this.oppoTaskService.saveOppoTask(saveVO, getUserId());
        return R.ok();
    }

    @GetMapping("/generate")
    public void testGenerate(@RequestParam("taskId") Integer taskId) {
        this.oppoTaskService.generateOppoAdCampaigns(taskId);
    }
}