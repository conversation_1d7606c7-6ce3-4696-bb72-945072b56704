package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.TreeNodeDTO;
import com.overseas.common.dto.market.plan.PlanUpdateRecordListDTO;
import com.overseas.common.dto.market.plan.PlanUpdateRecordOneDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.market.PlanSlotTypeEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.DateUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.utils.PlanDiagnosisUtils;
import com.overseas.common.vo.common.GetVO;
import com.overseas.common.vo.market.plan.*;
import com.overseas.service.market.driver.updatePlanAttribute.PlanAttributeUpdater;
import com.overseas.service.market.driver.updatePlanAttribute.PlanAttributeUpdaterFactory;
import com.overseas.service.market.dto.plan.PlanAndCampaignAndMasterSelectDTO;
import com.overseas.service.market.dto.plan.PlanGetDTO;
import com.overseas.service.market.dto.plan.PlanInspectionDTO;
import com.overseas.service.market.dto.plan.PlanSaveDTO;
import com.overseas.service.market.entity.Plan;
import com.overseas.service.market.entity.ReportDirect;
import com.overseas.service.market.enums.plan.PlanFlowDetectionStateEnum;
import com.overseas.service.market.enums.plan.PlanLearningStateEnum;
import com.overseas.service.market.events.creativeUnitAudit.CreativeUnitAuditEvent;
import com.overseas.service.market.events.notifyControl.ControlContants;
import com.overseas.service.market.events.notifyControl.ControlPlanEvent;
import com.overseas.service.market.service.PlanService;
import com.overseas.service.market.service.PlanUpdateRecordService;
import com.overseas.service.market.service.ReportDirectService;
import com.overseas.service.market.vo.plan.PlanBatchUpdateVO;
import com.overseas.service.market.vo.plan.PlanGetVO;
import com.overseas.service.market.vo.plan.PlanInspectionVO;
import com.overseas.service.market.vo.plan.PlanSaveVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Api(tags = "plan-计划相关接口")
@RestController
@RequestMapping("/market/plans")
@RequiredArgsConstructor
@Slf4j
public class PlanController extends AbstractController {

    private final PlanService planService;

    private final ApplicationContext applicationContext;

    private final ReportDirectService reportDirectService;

    private final PlanUpdateRecordService planUpdateRecordService;

    @ApiOperation(value = "获取计划下拉接口", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/select")
    public R selectPlan(@Validated @RequestBody PlanSelectGetVO getVO) {
        return R.data(this.planService.selectPlan(getVO, listMasterId(), getUser()));
    }

    @ApiOperation(value = "获取计划下拉接口", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/copy/select")
    public R selectCopyPlan(@Validated @RequestBody PlanSelectGetVO getVO) {
        return R.data(this.planService.selectCopyPlan(getVO, listMasterId(), getUser()));
    }

    @ApiOperation(value = "获取计划下拉接口", produces = "application/json", response = PlanAndCampaignAndMasterSelectDTO.class)
    @PostMapping("/campaign/master/page/select")
    public R selectPagePlanAndCampaignAndMaster(@Validated @RequestBody PlanAndCampaignAndMasterSelectVO selectVO) {
        return R.page(this.planService.selectPagePlanAndCampaignAndMaster(selectVO, listMasterId(), this.getUser()));
    }

    @ApiOperation(value = "获取有花费的计划下拉接口", notes = "获取有花费的计划下拉接口", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/hasCost/select")
    public R selectPlanHasCost(@Validated @RequestBody PlanHasCostSelectGetVO getVO) {
        return R.data(this.planService.selectPlanHasCost(getVO, this.getUser()));
    }

    @ApiOperation(value = "计划属性更新接口", produces = "application/json", response = R.class)
    @PostMapping("/attributes/update")
    public R updatePlanAttribute(@Validated @RequestBody PlanAttributeOperateVO vo) {
        this.checkMasterId(vo.getMasterId());
        PlanAttributeUpdater updater = PlanAttributeUpdaterFactory.createUpdater(vo.getOperateType());
        Integer affect = updater.update(vo.getId(), vo.getMasterId(), vo.getValue(), null, getUserId());
        return R.ok().put("data", affect);
    }

    @ApiOperation(value = "获取计划广告类型下拉接口", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/slotType/select")
    public R selectSlotType() {
        return R.data(ICommonEnum.list(PlanSlotTypeEnum.class));
    }

    @ApiOperation(value = "保存计划", produces = "application/json", response = R.class)
    @PostMapping("/save")
    public R save(@Validated @RequestBody PlanSaveVO saveVO) {
        checkMasterId(saveVO.getMasterId());
        PlanSaveDTO planSave = planService.savePlan(saveVO, getUser());
        // 通知中控
        applicationContext.publishEvent(new CreativeUnitAuditEvent(this, planSave.getPlanId()));
        return R.data(planSave.getPlanId()).put("campaignId", planSave.getCampaignId());
    }

    @ApiOperation(value = "获取单个计划", produces = "application/json", response = PlanGetDTO.class)
    @PostMapping("/get")
    public R get(@Validated @RequestBody PlanGetVO getVO) {
        checkMasterId(getVO.getMasterId());
        return R.data(planService.getPlanDetail(getVO.getId(), getVO.getMasterId(), this.getUser()));
    }

    @ApiOperation(value = "获取计划下拉接口", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/get/info")
    public R selectPlan(@Validated @RequestBody PlanGetVO getVO) {
        return R.data(this.planService.getPlan(getVO.getId(), null, getVO.getMasterId()));
    }

    @PostMapping("/report/direct/get")
    public R getPlanDirect(@Validated @RequestBody PlanDirectGetVO getVO) {
        return R.data(this.reportDirectService.getPlanDirect(getVO));
    }

    @ApiOperation(value = "报表中修改计划定向数据", produces = "application/json")
    @PostMapping("/report/direct/update")
    public R changePlanDirect(@Validated @RequestBody PlanDirectionChangeGetVO getVO) {
        List<ReportDirect> reportDirects = this.reportDirectService.changePlanDirect(getVO, this.getUserId());
        reportDirects.forEach(reportDirect -> {
            //通知中控
            this.reportDirectService.noticeCenterControl(reportDirect.getCampaignId(), reportDirect.getPlanId());
        });
        return R.data(reportDirects);
    }

    @ApiOperation(value = "更新计划中已排除的报表定向数据", produces = "application/json")
    @PostMapping("/report/direct/market/update")
    public R changeMarketDirect(@Validated @RequestBody PlanMarketDirectUpdateGetVO getVO) {
        this.reportDirectService.changeMarketDirect(getVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "获取当前未删除所有计划ID", produces = "application/json", response = Long.class)
    @PostMapping("/ids/get")
    public R getPlanIds(@Validated @RequestBody PlanIdsGetVO getVO) {
        return R.data(this.planService.getAllPlanIds(getVO));
    }

    @ApiOperation(value = "计划诊断", produces = "application/json")
    @PostMapping("/diagnosis")
    public R diagnosePlan(@Validated @RequestBody Map<String, Object> params) {
        Plan plan = this.planService.getPlan(Long.parseLong(params.get("pid").toString()), Integer.parseInt(params.get("masterId").toString()));
        if (plan == null) {
            throw new CustomException("计划不存在，请确认后再试");
        }
        return R.data(PlanDiagnosisUtils.planDiagnosis(params));
    }


    @ApiOperation(value = "批量更新计划信息", produces = "application/json")
    @PostMapping("/batch/update")
    public R batchUpdatePlan(@Validated @RequestBody PlanBatchUpdateVO batchUpdateVO) {
        batchUpdateVO.setUserId(this.getUserId());
//        this.checkMasterId(batchUpdateVO.getMasterId().intValue());
        boolean result = this.planService.batchUpdatePlan(batchUpdateVO, this.getUser());
        if (result) {
            // 通知中控
            batchUpdateVO.getPlanIds().forEach(planId -> applicationContext.publishEvent(new CreativeUnitAuditEvent(this, planId)));
            return R.ok("批量编辑成功");
        } else {
            return R.error("批量编辑异常");
        }
    }

    @ApiOperation(value = "批量更新计划信息（链接）", produces = "application/json")
    @PostMapping("/batch/link/update")
    public R batchUpdatePlanLink(@Validated @RequestBody PlanLinkBatchUpdateVO batchUpdateVO) {
        this.planService.batchUpdatePlanLink(batchUpdateVO, this.getUser());
        return R.ok();
    }

    @ApiOperation(value = "获取账户下不同类型计划数量", produces = "application/json")
    @PostMapping("/count/get")
    public R getMasterPlanCount(@Validated @RequestBody GetVO getVO) {
        return R.data(this.planService.getMasterPlanCount(getVO, this.getUser()));
    }

    @ApiOperation(value = "获取账户下auto link计划集合", produces = "application/json")
    @PostMapping("/autoLink/list")
    public R listAutoLinkPlan(@Validated @RequestBody AutoLinkPlanListVO listVO) {
        return R.data(this.planService.listAutoLinkPlan(listVO));
    }

    @ApiOperation(value = "获取流量探测开关下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/detection/select")
    public R getPlanFlowDetectionStateSelect() {
        return R.data(ICommonEnum.list(PlanFlowDetectionStateEnum.class));
    }

    @ApiOperation(value = "获取计划学习状态下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/learning/select")
    public R getPlanLearningSelect() {
        return R.data(ICommonEnum.list(PlanLearningStateEnum.class));
    }

    @ApiOperation(value = "获取活动、计划树", produces = "application/json", response = TreeNodeDTO.class)
    @PostMapping("/tree")
    public R getPlanTree(@Validated @RequestBody PlanTreeGetVO getVO) {
        return R.data(this.planService.getPlanTree(getVO));
    }

    @ApiOperation(value = "根据模板获取计划列表", produces = "application/json", response = Plan.class)
    @PostMapping("/by/template/list")
    public R listPlanByTemplate(@Validated @RequestBody PlanListByTemplateGetVO getVO) {
        return R.data(this.planService.listPlanByTemplate(getVO));
    }

    @ApiOperation(value = "计划状态更新定时器")
    @PostMapping("/status/schedule/test")
    public R updatePlanStatusHandler(@RequestParam("reportDate") String reportDate) {
        this.planService.updatePlanStatusHandler(DateUtils.date2Long(DateUtils.hourString2Date(reportDate)));
        return R.ok();
    }

    @ApiOperation(value = "计划状态更新定时器")
    @PostMapping("/dimension/select")
    public R dimensionSelect(@RequestBody @Validated PlanDimensionSelectVO selectVO) {
        return R.data(this.planService.listPlanByDimension(selectVO));
    }

    @ApiOperation(value = "获取计划编辑列表", produces = "application/json", response = PlanUpdateRecordListDTO.class)
    @PostMapping("/updateRecord/list")
    public R listPlanUpdateRecord(@Validated @RequestBody PlanUpdateRecordListVO listVO) {
        return R.page(this.planUpdateRecordService.listPlanUpdateRecord(listVO, this.getUser()));
    }

    @ApiOperation(value = "获取计划编辑日志详情展示", produces = "application/json", response = PlanUpdateRecordOneDTO.class)
    @PostMapping("/updateRecord/list/one")
    public R listPlanUpdateRecord(@Validated @RequestBody PlanUpdateRecordOneVO oneVO) {
        return R.data(this.planUpdateRecordService.oneInfo(oneVO));
    }

    @ApiOperation(value = "获取投放检查计划列表", produces = "application/json", response = PlanInspectionDTO.class)
    @PostMapping("/inspectionList")
    public R listInspectionPlan(@Validated @RequestBody PlanInspectionVO listVO) {
        PageUtils<PlanInspectionDTO> pageData = this.planService.listInspectionPlans(listVO);
        return R.page(pageData);
    }

    @ApiOperation(value = "导出广告链接数据", produces = "application/json")
    @PostMapping("/inspectionList/export")
    public void exportPlans(@RequestBody @Validated PlanInspectionVO planInspectionVO, HttpServletResponse response)
            throws IOException {
        this.planService.exportPlans(planInspectionVO, response);
    }

    @ApiOperation(value = "按比例编辑计划出价", produces = "application/json")
    @PostMapping("/bidPrice/batch/update")
    public R updateBidPriceByRate(@Validated @RequestBody PlanBidPriceUpdateVO updateVO) {
        this.planService.updateBidPriceByRate(updateVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "根据信息获取计划数据", produces = "application/json")
    @PostMapping("/get/plan/by/info")
    public R getPlansByInfo(@RequestBody @Validated PlanByInfoGetVO planByInfoGetVO) {
        return R.data(planService.getPlanByInfo(planByInfoGetVO));
    }

    @ApiOperation(value = "shein审核计划", produces = "application/json")
    @PostMapping("/shein/audit")
    public R sheinAudit(@RequestBody @Validated PlanSheinAuditVO sheinAuditVO) {
        planService.sheinAudit(sheinAuditVO, this.getUser());
        this.applicationContext.publishEvent(new ControlPlanEvent(this, ControlContants.METHOD_UPDATE, sheinAuditVO.getId()));
        return R.ok();
    }

    @ApiOperation(value = "shein批量审核计划", produces = "application/json")
    @PostMapping("/shein/batch/audit")
    public R sheinBatchAudit(@RequestBody @Validated PlanSheinBatchAuditVO sheinBatchAuditVO) {
        this.checkMasterId(sheinBatchAuditVO.getMasterId());
        planService.sheinBatchAudit(sheinBatchAuditVO, this.getUser());
        sheinBatchAuditVO.getPrices().forEach(priceVO -> {
            try {
                this.applicationContext.publishEvent(new ControlPlanEvent(this, ControlContants.METHOD_UPDATE, priceVO.getSheinPlanId()));
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
        return R.ok();
    }

    @ApiOperation(value = "shein批量修改计划组预算", produces = "application/json")
    @PostMapping("/shein/batch/budgetDaily")
    public R sheinBatchBudgetDaily(@RequestBody @Validated PlanSheinBatchBudgetDailyVO sheinBatchBudgetDailyVO) {
        this.checkMasterId(sheinBatchBudgetDailyVO.getMasterId());
        planService.sheinBatchBudgetDaily(sheinBatchBudgetDailyVO, this.getUser());
        sheinBatchBudgetDailyVO.getIds().forEach(id -> {
            try {
                this.applicationContext.publishEvent(new ControlPlanEvent(this, ControlContants.METHOD_UPDATE, id));
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
        return R.ok();
    }

    @ApiOperation(value = "shein审核计划时获取计划列表", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/shein/audit/plan/list")
    public R sheinAuditPlanList(@RequestBody @Validated PlanSheinAuditListPlanVO listPlanVO) {
        this.checkMasterId(listPlanVO.getMasterId());
        return R.data(planService.sheinAuditListPlan(listPlanVO, this.getUser()));
    }

    @ApiOperation(value = "shein创建影子计划，先创建投放活动", produces = "application/json")
    @PostMapping("/shein/audit/init")
    public R sheinAuditInit(@RequestBody @Validated PlanSheinAuditListPlanVO listPlanVO) {
        this.checkMasterId(listPlanVO.getMasterId());
        planService.sheinAuditCampaign(listPlanVO, this.getUser());
        return R.ok();
    }

    @ApiOperation(value = "shein计划审核验证逻辑", produces = "application/json")
    @PostMapping("/shein/audit/valid")
    public R sheinAuditValid(@RequestBody @Validated PlanSheinAuditValidVO validVO) {
        this.checkMasterId(validVO.getMasterId());
        return R.data(planService.sheinAuditValid(validVO));
    }

    @ApiOperation(value = "shein计划修改计划出价系数", produces = "application/json")
    @PostMapping("/shein/audit/price")
    public R sheinAuditPrice(@RequestBody @Validated PlanSheinAuditPriceVO priceVO) {
        this.checkMasterId(priceVO.getMasterId());
        planService.sheinAuditPrice(priceVO, this.getUser());
        return R.ok();
    }
}
