package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.TreeNodeDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.vo.market.plan.direct.*;
import com.overseas.service.market.enums.plan.PlanBidTypeEnum;
import com.overseas.service.market.events.creativeUnitAudit.CreativeUnitAuditEvent;
import com.overseas.service.market.service.AdxService;
import com.overseas.service.market.service.PlanDirectService;
import com.overseas.service.market.service.RtaStrategyService;
import com.overseas.service.market.vo.plan.DirectResourceVO;
import com.overseas.service.market.vo.rta.RtaStrategyCascaderGetVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-23 15:49
 */
@Api(tags = "plan-计划定向相关接口")
@RestController
@RequestMapping("/market/planDirects")
@RequiredArgsConstructor
public class PlanDirectController extends AbstractController {

    private final PlanDirectService planDirectService;

    private final AdxService adxService;

    private final RtaStrategyService rtaStrategyService;

    private final ApplicationContext applicationContext;

    @PostMapping("/directResource")
    @ApiOperation("根据权限ID获取订单下其他资源数据")
    public R directResource(@RequestBody @Validated DirectResourceVO orderDirectVO) {
        if (!List.of("country", "languages").contains(orderDirectVO.getFieldName())) {
            this.checkMasterId(orderDirectVO.getMasterId());
        }
        orderDirectVO.setUserId(this.getUserId());
        if (orderDirectVO.getFieldName().equals("crowdLabel")) {
            Map<String, Object> res = ObjectUtils.toMap(this.planDirectService.getDirectResource(orderDirectVO, listMasterId()));
            return R.data(res.get("data")).put("total", res.get("total"));
        }
        return R.data(this.planDirectService.getDirectResource(orderDirectVO, listMasterId()));
    }

    @ApiOperation(value = "获取ADX下EP下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/ep/select")
    public R getEpSelect(@Validated @RequestBody EpSelectGetVO getVO) {
        return R.data(this.adxService.getEpSelect(getVO));
    }

    @ApiOperation(value = "获取ADX下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/adx/select")
    public R getAdxSelect(@Validated @RequestBody AdxSelectGetVO getVO) {
        return R.data(this.adxService.getAdxSelect(getVO));
    }

    @ApiOperation(value = "获取ADX及EP", produces = "application/json", response = TreeNodeDTO.class)
    @PostMapping("/adx/tree")
    public R getAdxEpTree() {
        return R.data(this.adxService.getAdxEpTree());
    }

    @ApiOperation(value = "获取RTA", produces = "application", response = TreeNodeDTO.class)
    @PostMapping("/rta/tree")
    public R getRtaStrategyTree(@Validated @RequestBody RtaStrategyCascaderGetVO getVO) {
        return R.data(this.rtaStrategyService.getRtaStrategyTree(getVO));
    }

    @ApiOperation(value = "获取对接的ADX下拉数据", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/dock/adx/select")
    public R getDockingAdxSelect() {
        return R.data(this.adxService.getDockingAdxSelect());
    }

    @ApiOperation(value = "批量保存计划定向")
    @PostMapping("/batch/save")
    public R batchSavePlanDirect(@Validated @RequestBody PlanDirectBatchSaveVO saveVO) {
        this.planDirectService.batchSavePlanDirect(saveVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "获取RTA下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/rta/select")
    public R selectRtaStrategy(@Validated @RequestBody RtaSelectGetVO getVO) {
        return R.data(this.rtaStrategyService.selectRtaStrategy(getVO));
    }

    @ApiOperation(value = "获取优化目标下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/optimizeTarget/select")
    public R selectOptimize(@Validated @RequestBody PlanOptimizeTargetSelectGetVO getVO) {
        return R.data(this.planDirectService.selectOptimizeTarget(getVO));
    }

    @ApiOperation(value = "获取出价类型", produces = "application/json")
    @PostMapping("/bidType/select")
    public R selectBidType() {
        return R.data(ICommonEnum.list(PlanBidTypeEnum.class));
    }

    @ApiOperation(value = "批量更新计划定向")
    @PostMapping("/batch/update")
    public R batchUpdatePlanDirect(@Validated @RequestBody PlanDirectBatchUpdateVO updateVO) {
        List<String> list = this.planDirectService.batchUpdatePlanDirect(updateVO, this.getUserId());
        // 通知中控
        updateVO.getIds().forEach(id -> this.applicationContext.publishEvent(new CreativeUnitAuditEvent(this, id)));
        return R.data(list);
    }
}
