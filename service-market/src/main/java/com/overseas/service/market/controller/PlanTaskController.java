package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.market.plan.task.PlanTaskListDTO;
import com.overseas.common.dto.market.plan.task.PlanTaskTempListDTO;
import com.overseas.common.vo.market.plan.task.PlanTaskCompletionListVO;
import com.overseas.common.vo.market.plan.task.PlanTaskListVO;
import com.overseas.common.vo.market.plan.task.PlanTaskSaveVO;
import com.overseas.common.vo.market.plan.task.PlanTaskTempListVO;
import com.overseas.service.market.service.PlanTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Api(tags = "plan-task-计划批量创建任务相关接口")
@RestController
@RequestMapping("/market/planTasks")
@RequiredArgsConstructor
public class PlanTaskController extends AbstractController {

    private final PlanTaskService planTaskService;

    @ApiOperation(value = "获取批量创建计划任务列表", produces = "application/json", response = PlanTaskListDTO.class)
    @PostMapping("/list")
    public R listPlanTask(@Validated @RequestBody PlanTaskListVO listVO) {
        return R.page(this.planTaskService.listPlanTask(listVO));
    }

    @ApiOperation(value = "获取计划临时列表", produces = "application/json", response = PlanTaskTempListDTO.class)
    @PostMapping("/temp/list")
    public R listPlanTaskTemp(@Validated @RequestBody PlanTaskTempListVO listVO) {
        return R.data(this.planTaskService.listPlanTaskTemp(listVO));
    }

    @ApiOperation(value = "创建任务", produces = "application/json")
    @PostMapping("/save")
    public R savePlanTask(@Validated @RequestBody PlanTaskSaveVO saveVO) {
        this.planTaskService.savePlanTask(saveVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "测试执行定时器任务", produces = "application/json")
    @PostMapping("/schedule/test")
    public R executeSchedule() {
        this.planTaskService.savePlanByTask();
        return R.ok();
    }

    @ApiOperation(value = "获取创建完成计划列表", produces = "application/json")
    @PostMapping("/completion/list")
    public R listCompletion(@Validated @RequestBody PlanTaskCompletionListVO listVO) {
        return R.data(this.planTaskService.listCompletePlan(listVO));
    }
}
