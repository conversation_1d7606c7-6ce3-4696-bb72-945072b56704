package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.service.market.dto.productLibrary.ProductLibraryListDTO;
import com.overseas.service.market.service.ProductLibraryService;
import com.overseas.service.market.vo.productLibrary.ProductLibraryListVO;
import com.overseas.service.market.vo.productLibrary.ProductLibrarySaveVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "商品库模块接口")
@RestController
@RequestMapping("/market/productLibraries")
@RequiredArgsConstructor
public class ProductLibraryController extends AbstractController {

    private final ProductLibraryService productLibraryService;

    @ApiOperation(value = "商品库列表", produces = "application/json", response = ProductLibraryListDTO.class)
    @PostMapping("/list")
    public R listProductLibrary(@Validated @RequestBody ProductLibraryListVO listVO) {
        return R.page(this.productLibraryService.listProductLibrary(listVO));
    }

    @ApiOperation(value = "新增商品库", produces = "application/json")
    @PostMapping("/save")
    public R saveTag(@Validated @RequestBody ProductLibrarySaveVO saveVO) {
        return R.data(this.productLibraryService.saveProductLibrary(saveVO, this.getUserId()));
    }
}
