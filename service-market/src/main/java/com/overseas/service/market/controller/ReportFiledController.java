package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.market.reportField.ReportFieldDTO;
import com.overseas.common.dto.sys.customIndex.CustomIndexParentColumnDTO;
import com.overseas.common.vo.market.reportField.ReportFieldGetVO;
import com.overseas.service.market.service.ReportFieldService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Api(tags = "plan-计划相关接口")
@RestController
@RequestMapping("/market/reportFields")
@RequiredArgsConstructor
public class ReportFiledController {

    private final ReportFieldService reportFieldService;

    @ApiOperation(value = "根据字段获取自定义规则", produces = "application/json", response = ReportFieldDTO.class)
    @PostMapping("/list")
    public R listReportField(@Validated @RequestBody ReportFieldGetVO getVO) {
        return R.data(this.reportFieldService.listReportField(getVO));
    }

    @ApiOperation(value = "获取自定义列", produces = "application/json", response = CustomIndexParentColumnDTO.class)
    @PostMapping("/custom/get")
    public R getCustomIndexColumns(@Validated @RequestBody ReportFieldGetVO getVO) {
        return R.data(this.reportFieldService.getCustomIndexColumns(getVO));
    }
}
