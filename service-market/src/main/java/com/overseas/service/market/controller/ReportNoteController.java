package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.vo.market.reportNote.ReportNoteListVO;
import com.overseas.common.vo.market.reportNote.ReportNoteSaveVO;
import com.overseas.service.market.service.ReportNoteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Api(tags = "plan-计划相关接口")
@RestController
@RequestMapping("/market/reportNotes")
@RequiredArgsConstructor
public class ReportNoteController extends AbstractController {

    private final ReportNoteService reportNoteService;

    @ApiOperation(value = "编辑备注", produces = "application/json")
    @PostMapping("/save")
    public R saveReportNote(@Validated @RequestBody ReportNoteSaveVO saveVO) {
        this.reportNoteService.saveReportNote(saveVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "获取备注", produces = "application/json")
    @PostMapping("/list")
    public R listReportNote(@Validated @RequestBody ReportNoteListVO listVO) {
        return R.data(this.reportNoteService.listReportNote(listVO));
    }
}
