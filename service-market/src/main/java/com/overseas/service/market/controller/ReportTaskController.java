package com.overseas.service.market.controller;

import com.overseas.common.vo.market.reportTask.ReportExportTaskGetVO;
import com.overseas.common.dto.R;
import com.overseas.common.dto.market.reportTask.ReportExportTaskListDTO;
import com.overseas.common.vo.market.reportTask.ReportExportTaskListVO;
import com.overseas.common.vo.market.reportTask.ReportExportTaskSaveVO;
import com.overseas.common.vo.market.reportTask.ReportExportTaskUpdateVO;
import com.overseas.service.market.service.ReportExportTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Api(tags = "reportTask-报表任务相关接口")
@RestController
@RequestMapping("/market/reportTasks")
@RequiredArgsConstructor
public class ReportTaskController extends AbstractController {

    private final ReportExportTaskService reportExportTaskService;

    @ApiOperation(value = "获取离线任务列表数据", produces = "application/json", response = ReportExportTaskListDTO.class)
    @PostMapping("/list")
    public R getReportExportTaskPage(@RequestBody @Validated ReportExportTaskListVO listVO) {
        return R.page(this.reportExportTaskService.getReportExportTaskPage(listVO, this.getUserId()));
    }

    @ApiOperation(value = "新增离线任务", produces = "application/json")
    @PostMapping("/save")
    public R saveReportExportTask(@RequestBody @Validated ReportExportTaskSaveVO saveVO) {
        this.reportExportTaskService.saveReportExportTask(saveVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "新增离线任务", produces = "application/json")
    @PostMapping("/test")
    public R exportAllReportTask() {
        this.reportExportTaskService.exportAllReportTask();
        return R.ok();
    }

    @ApiOperation(value = "获取待执行创建流程的任务", produces = "application/json")
    @PostMapping("/waiting/get")
    public R getWaitToCreateTask(@RequestBody @Validated ReportExportTaskGetVO getVO) {
        return R.data(this.reportExportTaskService.getWaitToCreateTask(getVO));
    }

    @ApiOperation(value = "更新离线任务记录信息", produces = "application/json")
    @PostMapping("/update")
    public R updateReportTask(@RequestBody @Validated ReportExportTaskUpdateVO updateVO) {
        this.reportExportTaskService.updateReportTask(updateVO);
        return R.ok();
    }

}
