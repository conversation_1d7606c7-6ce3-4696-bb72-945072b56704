package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.market.PlanFlowTypeEnum;
import com.overseas.common.enums.market.PlanSlotTypeEnum;
import com.overseas.common.vo.market.media.MediaSelectGetVO;
import com.overseas.common.vo.market.slot.SlotSelectGetVO;
import com.overseas.common.enums.OsTypeEnum;
import com.overseas.service.market.enums.assets.AssetWebTypeEnum;
import com.overseas.common.enums.market.campaign.CampaignMarketTargetEnum;
import com.overseas.common.enums.market.campaign.CampaignPutOnTargetEnum;
import com.overseas.common.enums.market.campaign.CampaignWebStatusEnum;
import com.overseas.service.market.enums.creative.units.CreativeUnitStatusEnum;
import com.overseas.service.market.enums.creative.units.CreativeUnitWebStatusEnum;
import com.overseas.service.market.enums.master.MasterAuditMasterStatusEnum;
import com.overseas.service.market.enums.master.MasterUserStatusEnum;
import com.overseas.common.enums.market.FrequencyCycleEnum;
import com.overseas.common.enums.market.plan.PlanStatusEnum;
import com.overseas.service.market.enums.plan.PlanWebStatusEnum;
import com.overseas.service.market.service.AdxService;
import com.overseas.service.market.service.CtxService;
import com.overseas.service.market.service.QualificationCategoryService;
import com.overseas.service.market.service.MediaService;
import com.overseas.service.market.service.SlotService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-16 19:05
 */
@Api(value = "投放资源", description = "投放资源")
@RestController
@RequestMapping("/market/resources")
@RequiredArgsConstructor
public class ResourceController {

    private final AdxService adxService;

    private final CtxService ctxService;

    private final QualificationCategoryService qualificationCategoryService;

    private final MediaService mediaService;

    private final SlotService slotService;

    @ApiOperation(value = "广告主禁用状态下拉", notes = "广告主禁用状态下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/masters/denyStatus/select")
    public R selectDenyStatus() {
        return R.data(ICommonEnum.list(MasterUserStatusEnum.class));
    }

    @ApiOperation(value = "资质主体审核状态下拉", notes = "资质主体审核状态下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/auditMasters/status/select")
    public R selectAuditMasterStatus() {
        return R.data(ICommonEnum.list(MasterAuditMasterStatusEnum.class));
    }

    @ApiOperation(value = "活动状态下拉（前端使用）", notes = "活动状态下拉（前端使用）", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/campaign/webStatus/select")
    public R selectWebCampaignStatus() {
        return R.data(ICommonEnum.list(CampaignWebStatusEnum.class));
    }

    @ApiOperation(value = "活动营销目的下拉", notes = "活动营销目的下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/campaign/marketTarget/select")
    public R selectCampaignMarketTarget() {
        return R.data(ICommonEnum.list(CampaignMarketTargetEnum.class));
    }

    @ApiOperation(value = "活动推广目标下拉", notes = "活动推广目标下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/campaign/putOnTarget/select")
    public R selectCampaignPutOnTarget() {
        return R.data(ICommonEnum.list(CampaignPutOnTargetEnum.class));
    }

    @ApiOperation(value = "计划状态下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/plan/status/select")
    public R getPlanStatusSelect() {
        return R.data(ICommonEnum.list(PlanStatusEnum.class));
    }

    @ApiOperation(value = "计划状态下拉（前端使用）", notes = "计划状态下拉（前端使用）", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/plan/webStatus/select")
    public R selectWebPlanStatus() {
        return R.data(ICommonEnum.list(PlanWebStatusEnum.class));
    }

    @ApiOperation(value = "创意状态下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/creative/status/select")
    public R getCreativeUnitStatus() {
        return R.data(ICommonEnum.list(CreativeUnitStatusEnum.class));
    }

    @ApiOperation(value = "创意状态下拉（前端使用）", notes = "创意状态下拉（前端使用）", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/creative/webStatus/select")
    public R selectWebCreativeStatus() {
        return R.data(ICommonEnum.list(CreativeUnitWebStatusEnum.class));
    }

    @ApiOperation(value = "广告形式下拉", notes = "广告形式下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/slotType/select")
    public R selectSlotType() {
        return R.data(ICommonEnum.list(PlanSlotTypeEnum.class));
    }

    @ApiOperation(value = "流量类型下拉", notes = "流量类型下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/flowType/select")
    public R selectFlowType() {
        return R.data(ICommonEnum.list(PlanFlowTypeEnum.class));
    }

    @ApiOperation(value = "平台下拉", notes = "平台下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/osType/select")
    public R selectOsType() {
        return R.data(ICommonEnum.list(OsTypeEnum.class));
    }

    @ApiOperation(value = "系统中正常使用ADX下拉", notes = "系统中正常使用ADX下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/adx/selectWorkable")
    public R selectWorkableAdx() {
        return R.data(adxService.selectWorkable());
    }

    @ApiOperation(value = "系统中正常使用行动号召下拉", notes = "系统中正常使用行动号召下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/ctx/select")
    public R selectCtx() {
        return R.data(ctxService.selectCtx());
    }

    @ApiOperation(value = "计划频次周期下拉", notes = "计划频次周期下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/plan/frequencyCycle/select")
    public R selectFrequencyCycle() {
        return R.data(ICommonEnum.list(FrequencyCycleEnum.class));
    }

    @ApiOperation(value = "素材样式下拉（前端使用）", notes = "素材样式下拉（前端使用）", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/asset/type/select")
    public R selectWebAssetType() {
        return R.data(ICommonEnum.list(AssetWebTypeEnum.class));
    }

    @ApiOperation("资质类型数据（分级）")
    @PostMapping("/quaCategory")
    public R quaCategory() {
        return R.data(this.qualificationCategoryService.categoryLinkage());
    }

    @ApiOperation(value = "获取媒体下拉数据", notes = "获取媒体下拉数据", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/media/select")
    public R getMediaSelect(@RequestBody @Validated MediaSelectGetVO getVO) {
        return R.data(this.mediaService.getMediaSelect(getVO));
    }

    @ApiOperation(value = "获取广告位下拉数据", notes = "获取广告位下拉数据", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/slot/select")
    public R getSlotSelect(@RequestBody @Validated SlotSelectGetVO getVO) {
        return R.data(this.slotService.getSlotSelect(getVO));
    }
}
