package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.rtaStrategy.RtaStrategyListDTO;
import com.overseas.common.vo.market.plan.direct.RtaSelectGetVO;
import com.overseas.common.vo.market.rtaStrategy.*;
import com.overseas.service.market.entity.RtaStrategy;
import com.overseas.service.market.service.RtaStrategyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "rta 策略")
@RestController
@RequestMapping("/market/rtaStrategies")
@RequiredArgsConstructor
public class RtaStrategyController extends AbstractController {

    private final RtaStrategyService rtaStrategyService;

    @ApiOperation(value = "获取ae的rta列表", produces = "application/json")
    @PostMapping("/ae/list")
    public R tagShareTest() {
        return R.data(this.rtaStrategyService.listAeRtaStrategy());
    }

    @ApiOperation(value = "获取RTA列表", produces = "application/json", response = RtaStrategyListDTO.class)
    @PostMapping("/list")
    public R listRtaStrategy(@Validated @RequestBody RtaStrategyListVO listVO) {
        listVO.setMasterIds(this.listMasterId());
        return R.page(this.rtaStrategyService.listRtaStrategy(listVO));
    }

    @ApiOperation(value = "更新RTA消费比例", produces = "application/json")
    @PostMapping("/update")
    public R updateRtaStrategy(@Validated @RequestBody RtaStrategyUpdateVO updateVO) {
        this.rtaStrategyService.updateRtaStrategyCostRate(updateVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "更新RTA消费比例", produces = "application/json", response = RtaStrategy.class)
    @PostMapping("/get")
    public R getRtaStrategy(@Validated @RequestBody RtaStrategyGetVO getVO) {
        return R.data(this.rtaStrategyService.getRtaStrategy(getVO));
    }

    @ApiOperation(value = "更新RTA消费比例", produces = "application/json", response = RtaStrategy.class)
    @PostMapping("/get/by/planIds")
    public R getRtaStrategy(@Validated @RequestBody RtaStrategyGetByPlanIdsVO getByPlanIdsVO) {
        return R.data(this.rtaStrategyService.getRtaStrategyByPlanIds(getByPlanIdsVO));
    }

    @ApiOperation(value = "获取RTA下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/select")
    public R selectRtaStrategy(@Validated @RequestBody RtaSelectGetVO getVO) {
        return R.data(this.rtaStrategyService.selectRtaStrategy(getVO));
    }

    @ApiOperation(value = "获取RTA列表", produces = "application/json", response = RtaStrategyListDTO.class)
    @PostMapping("/byId/list")
    public R listRtaStrategyById(@Validated @RequestBody RtaSelectGetVO getVO) {
        return R.data(this.rtaStrategyService.listRtaStrategy(getVO));
    }

    @ApiOperation(value = "获取全部RTA列表", produces = "application/json", response = RtaStrategyListDTO.class)
    @PostMapping("/all/byId/list")
    public R listRtaStrategyAllById(@Validated @RequestBody RtaSelectGetVO getVO) {
        return R.data(this.rtaStrategyService.listRtaStrategyAll(getVO));
    }

    @ApiOperation(value = "批量保存RTA", produces = "application/json")
    @PostMapping("/batch/save")
    public R batchSaveRtaStrategy(@Validated @RequestBody RtaStrategyBatchSaveVO saveVO) {
        this.rtaStrategyService.batchSaveRtaStrategy(saveVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "获取国家对应RTA", produces = "application/json")
    @PostMapping("/country/get")
    public R listRtaCountry() {
        return R.data(this.rtaStrategyService.listRtaCountry());
    }

    @ApiOperation(value = "获取RTA策略组下拉", produces = "application/json")
    @PostMapping("/group/select")
    public R selectRtaGroup() {
        return R.data(this.rtaStrategyService.selectRtaGroup());
    }

    @ApiOperation("拉取所有rta 策略信息")
    @PostMapping("/pull2All")
    public R pull2All() {
        this.rtaStrategyService.pull2All();
        return R.ok();
    }
}
