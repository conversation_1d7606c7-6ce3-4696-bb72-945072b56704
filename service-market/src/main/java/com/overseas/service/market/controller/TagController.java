package com.overseas.service.market.controller;

import com.overseas.common.vo.market.tag.*;
import com.overseas.service.market.enums.tag.TagDeviceTypeEnum;
import com.overseas.service.market.enums.tag.TagOriginEnum;
import com.overseas.service.market.enums.tag.TagStatusEnum;
import com.overseas.service.market.enums.tag.TagTypeEnum;
import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.tag.TagGetDTO;
import com.overseas.common.dto.market.tag.TagListDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.service.market.service.TagService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Api(tags = "tag-人群标签相关接口")
@RestController
@RequestMapping("/market/tags")
@RequiredArgsConstructor
public class TagController extends AbstractController {

    private final TagService tagService;

    @ApiOperation(value = "人群标签列表", notes = "人群标签列表", produces = "application/json", response = TagListDTO.class)
    @PostMapping("/list")
    public R listTag(@Validated @RequestBody TagListVO listVO) {
        return R.page(this.tagService.getTagPage(listVO, listMasterId()));
    }

    @ApiOperation(value = "获取人群标签数据", notes = "获取人群标签数据", produces = "application/json", response = TagGetDTO.class)
    @PostMapping("/get")
    public R getTag(@Validated @RequestBody TagGetVO getVO) {
        return R.data(this.tagService.getTag(getVO));
    }

    @ApiOperation(value = "新增人群标签", notes = "新增人群标签", produces = "application/json")
    @PostMapping("/save")
    public R saveTag(@Validated @RequestBody TagSaveVO saveVO) {
        this.tagService.saveTag(saveVO, this.getUser());
        return R.ok();
    }

    @ApiOperation(value = "更新人群标签", notes = "更新人群标签", produces = "application/json")
    @PostMapping("/update")
    public R updateTag(@Validated @RequestBody TagUpdateVO updateVO) {
        this.tagService.updateTag(updateVO, this.getUser());
        return R.ok();
    }

    @ApiOperation(value = "获取人群标签状态下拉", notes = "获取人群标签状态下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/status/select")
    public R getTagStatusSelect() {
        return R.data(ICommonEnum.list(TagStatusEnum.class));
    }

    @ApiOperation(value = "获取人群标签类型下拉", notes = "获取人群标签类型下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/type/select")
    public R getTagTypeSelect() {
        return R.data(ICommonEnum.list(TagTypeEnum.class));
    }

    @ApiOperation(value = "获取人群标签来源下拉", notes = "获取人群标签来源下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/origin/select")
    public R getTagOriginSelect() {
        return R.data(ICommonEnum.list(TagOriginEnum.class));
    }

    @ApiOperation(value = "获取人群标签设备号下拉", notes = "获取人群标签设备号下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/device/select")
    public R getTagDeviceSelect() {
        return R.data(ICommonEnum.list(TagDeviceTypeEnum.class));
    }

    @ApiOperation(value = "编辑人群标签与用户关系", notes = "编辑人群标签与用户关系", produces = "application/json")
    @PostMapping("/resource/save")
    public R saveTagResource(@Validated @RequestBody TagResourceSaveVO saveVO) {
        this.tagService.saveTagResource(saveVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "标签共享测试", produces = "application/json")
    @PostMapping("/share/test")
    public void tagShareTest() {
        this.tagService.checkNewMaster();
    }
}
