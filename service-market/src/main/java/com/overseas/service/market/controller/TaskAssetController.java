package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.exception.CustomException;
import com.overseas.common.vo.market.asset.AssetResourceSaveVO;
import com.overseas.common.vo.market.asset.AssetVO;
import com.overseas.service.market.dto.assetTask.AssetTaskMaterialDTO;
import com.overseas.service.market.enums.assets.AssetSourceTypeEnum;
import com.overseas.service.market.service.AssetService;
import com.overseas.service.market.service.AssetTaskService;
import com.overseas.service.market.service.TaskProductAssetService;
import com.overseas.service.market.vo.assetTask.AssetTaskGetVO;
import com.overseas.service.market.vo.assetTask.AssetTaskListVO;
import com.overseas.service.market.vo.assetTask.AssetTaskSaveVO;
import com.overseas.service.market.vo.assetTask.AssetTaskUpdateVO;
import com.overseas.service.market.vo.taskAsset.TaskAssetSaveVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@Api(tags = "Market-选品任务素材管理")
@RestController
@RequestMapping("/market/taskAssets")
@RequiredArgsConstructor
public class TaskAssetController extends AbstractController {

    private final TaskProductAssetService taskProductAssetService;

    private final AssetService assetService;

    @ApiOperation("保存素材制作任务和素材关联关系")
    @PostMapping("/save")
    public R saveTaskAsset(@RequestBody @Validated TaskAssetSaveVO saveVO) {
        List<AssetTaskMaterialDTO> result = this.taskProductAssetService.saveTaskAsset(saveVO, this.getUserId());
        if (null != result) {
            AssetResourceSaveVO assetResourceSaveVO = new AssetResourceSaveVO();
            assetResourceSaveVO.setMasterIds(List.of(saveVO.getMasterId()));
            assetResourceSaveVO.setAssetList(saveVO.getAssets());
            assetResourceSaveVO.setSourceType(AssetSourceTypeEnum.ACCOUNT_SHARING.getId());
            this.assetService.saveAssetResource(assetResourceSaveVO, this.getUserId());
            return R.data(result);
        } else {
            throw new CustomException("保存任务商品和素材关系失败，可能由于素材在当前账户已被其他商品使用");
        }
    }
}
