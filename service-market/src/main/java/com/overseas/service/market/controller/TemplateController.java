package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.template.TemplateListDTO;
import com.overseas.common.dto.market.template.TemplatePreviewDTO;
import com.overseas.common.vo.market.template.*;
import com.overseas.service.market.service.TemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Api(tags = "Template-JS模板相关接口")
@RestController
@RequestMapping("/market/templates")
@RequiredArgsConstructor
public class TemplateController extends AbstractController {

    private final TemplateService templateService;

    @ApiOperation(value = "获取JS模板下拉数据", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/select")
    public R getTemplateSelect(@RequestBody @Validated TemplateSelectVO selectVO) {
        return R.data(this.templateService.getTemplateSelect(selectVO));
    }

    @ApiOperation(value = "获取模板数据", produces = "application/json", response = TemplateController.class)
    @PostMapping("/get")
    public R getTemplateSelect(@RequestBody @Validated TemplateGetVO getVO) {
        return R.data(this.templateService.getTemplate(getVO.getId()));
    }

    @ApiOperation(value = "模板列表数据", produces = "application/json", response = TemplateListDTO.class)
    @PostMapping("/list")
    public R listTemplate(@RequestBody @Validated TemplateListVO listVO) {
        return R.page(this.templateService.listTemplate(listVO));
    }

    @ApiOperation(value = "获取模板数据", produces = "application/json")
    @PostMapping("/get/v2")
    public R getTemplate(@RequestBody @Validated TemplateGetVO getVO) {
        return R.data(this.templateService.getTemplateV2(getVO));
    }

    @ApiOperation(value = "保存模板数据", produces = "application/json")
    @PostMapping("/save")
    public R saveTemplate(@RequestBody @Validated TemplateSaveVO saveVO) {
        this.templateService.saveTemplate(saveVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "更新模板数据", produces = "application/json")
    @PostMapping("/update")
    public R updateTemplate(@RequestBody @Validated TemplateUpdateVO updateVO) {
        this.templateService.updateTemplate(updateVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "删除模板数据", produces = "application/json")
    @PostMapping("/del")
    public R delTemplate(@RequestBody @Validated TemplateDelVO delVO) {
        this.templateService.delTemplate(delVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "预览模板数据", produces = "application/json", response = TemplatePreviewDTO.class)
    @PostMapping("/preview")
    public R previewTemplate(@RequestBody @Validated TemplatePreviewVO previewVO) {
        return R.data(this.templateService.preview(previewVO));
    }

    @ApiOperation(value = "监测数据获取", produces = "application/json")
    @PostMapping("/monitor/data")
    public R monitorDataTemplate(@RequestBody @Validated TemplateMonitorDataVO monitorDataVO) {
        return R.data(this.templateService.monitorData(monitorDataVO));
    }

    @ApiOperation(value = "发布模板", produces = "application/json")
    @PostMapping("/publish")
    public R publishTemplate(@RequestBody @Validated TemplatePublishVO publishVO) {
        this.templateService.publishTemplate(publishVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "获取可用宏", produces = "application/json")
    @PostMapping("/use/macro")
    public R useMacro() {
        return R.data(this.templateService.useMacro());
    }

    @ApiOperation(value = "监测上报", produces = "application/json")
    @RequestMapping("/monitor")
    public R monitorTemplate(@RequestParam("nonce") String nonce, @RequestParam("templateId") Long templateId,
                             @RequestParam("type") String type) {
        this.templateService.monitor(nonce, templateId, type);
        return R.ok();
    }

}
