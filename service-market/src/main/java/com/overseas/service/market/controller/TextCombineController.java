package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.vo.market.creative.CreativeUnitListVO;
import com.overseas.common.vo.market.textCombine.TextCombineCheckVO;
import com.overseas.common.vo.market.textCombine.TextCombineListVO;
import com.overseas.common.vo.market.textCombine.TextCombineSaveVO;
import com.overseas.service.market.service.TextCombineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 文本组合管理控制器
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Slf4j
@RestController
@RequestMapping("/market/textCombine")
@Api(tags = "文案组合管理")
@RequiredArgsConstructor
public class TextCombineController extends AbstractController {

    private final TextCombineService textCombineService;

    @PostMapping("/list")
    @ApiOperation("获取文案组合列表")
    public R listTextCombine(@RequestBody @Validated TextCombineListVO listVO) {
        this.checkMasterId(listVO.getMasterId());
        return R.page(textCombineService.listTextCombine(listVO, this.getUser()));
    }

    @PostMapping("/list/export")
    @ApiOperation("下载文案组合列表")
    public void exportListTextCombine(@RequestBody @Validated TextCombineListVO listVO,
                                      HttpServletResponse response) throws IOException {
        textCombineService.exportListTextCombine(listVO, this.getUser(), response);
    }

    @PostMapping("/save")
    @ApiOperation("新增文案组合")
    public R saveTextCombine(@RequestBody @Validated TextCombineSaveVO saveVO) {
        this.checkMasterId(saveVO.getMasterId());
        textCombineService.saveTextCombine(saveVO, getUserId());
        return R.ok();
    }

    @PostMapping("/creative/unit/list")
    @ApiOperation("组合下创意详情")
    public R creativeUnitListByCombine(@RequestBody @Validated CreativeUnitListVO listVO) {
        this.checkMasterId(listVO.getMasterId());
        return R.page(textCombineService.listCreativeUnitByCombine(listVO, getUser()));
    }

    @PostMapping("/check")
    @ApiOperation("组合推荐状态")
    public R checkTextCombine(@RequestBody @Validated TextCombineCheckVO checkVO) {
        this.checkMasterId(checkVO.getMasterId());
        return R.data(textCombineService.checkTextCombine(checkVO));
    }

}