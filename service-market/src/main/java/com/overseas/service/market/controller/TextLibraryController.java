package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.vo.market.textLibrary.*;
import com.overseas.service.market.service.TextLibraryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 文案库管理控制器
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Slf4j
@RestController
@RequestMapping("/market/textLibrary")
@Api(tags = " 文案库管理")
@RequiredArgsConstructor
public class TextLibraryController extends AbstractController {

    private final TextLibraryService textLibraryService;

    @PostMapping("/select")
    @ApiOperation("获取文案库下拉")
    public R selectTextLibrary(@RequestBody @Validated TextLibrarySelectVO selectVO) {
        this.checkMasterId(selectVO.getMasterId());
        return R.data(textLibraryService.selectTextLibrary(selectVO));
    }

    @PostMapping("/list")
    @ApiOperation("获取文案库列表")
    public R listTextLibrary(@RequestBody @Validated TextLibraryListVO listVO) {
        this.checkMasterId(listVO.getMasterId());
        return R.page(textLibraryService.listTextLibrary(listVO));
    }

    @PostMapping("/save")
    @ApiOperation("新增文案库")
    public R saveTextLibrary(@RequestBody @Validated TextLibrarySaveVO saveVO) {
        this.checkMasterId(saveVO.getMasterId());
        return R.data(textLibraryService.saveTextLibrary(saveVO, getUserId()));
    }

    @PostMapping("/update")
    @ApiOperation("更新文案库")
    public R updateTextLibrary(@RequestBody @Validated TextLibraryUpdateVO updateVO) {
        this.checkMasterId(updateVO.getMasterId());
        textLibraryService.updateTextLibrary(updateVO, getUserId());
        return R.ok();
    }

    @PostMapping("/del")
    @ApiOperation("删除文案库")
    public R deleteTextLibrary(@RequestBody @Validated TextLibraryDelVO delVO) {
        this.checkMasterId(delVO.getMasterId());
        textLibraryService.deleteTextLibrary(delVO, getUserId());
        return R.ok();
    }

    @PostMapping("/status")
    @ApiOperation("暂停/启用文案库")
    public R changeTextLibraryStatus(@RequestBody @Validated TextLibraryStatusVO statusVO) {
        this.checkMasterId(statusVO.getMasterId());
        textLibraryService.changeTextLibraryStatus(statusVO, getUserId());
        return R.ok();
    }


    @PostMapping("/info/save")
    @ApiOperation("保存文案信息")
    public R saveTextLibrary(@RequestBody @Validated TextLibraryInfoSaveVO saveVO) {
        this.checkMasterId(saveVO.getMasterId());
        int count =textLibraryService.saveTextLibrary(saveVO.getLibraryId(), saveVO.getMasterId(),
                saveVO.getLibraryInfos(), getUserId());
        return R.data(count);
    }

    @PostMapping("/info/del")
    @ApiOperation("删除文案库文案内容")
    public R deleteTextLibraryInfo(@RequestBody @Validated TextLibraryInfoDelVO delVO) {
        this.checkMasterId(delVO.getMasterId());
        textLibraryService.deleteTextLibraryInfo(delVO, getUserId());
        return R.ok();
    }

    @PostMapping("/info/upload")
    @ApiOperation("上传文案库文案内容")
    public R uploadTextLibraryInfo(@RequestBody @Validated TextLibraryInfoUploadVO uploadVO) {
        return R.data(textLibraryService.uploadTextLibraryInfo(uploadVO));
    }

    @PostMapping("/info/list")
    @ApiOperation("获取文案库文案信息列表")
    public R listTextLibraryInfo(@RequestBody @Validated TextLibraryInfoListVO listVO) {
        this.checkMasterId(listVO.getMasterId());
        return R.data(textLibraryService.listTextLibraryInfo(listVO));
    }

    @PostMapping("/info/export")
    @ApiOperation("下载文案库文案内容")
    public void exportTextLibraryInfo(@RequestBody @Validated TextLibraryInfoListVO listVO,
                                      HttpServletResponse response) throws IOException {
        textLibraryService.exportTextLibraryInfo(listVO, response);
    }
} 