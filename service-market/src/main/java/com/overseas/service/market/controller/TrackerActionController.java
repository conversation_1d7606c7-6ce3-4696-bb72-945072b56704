package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO2;
import com.overseas.common.vo.market.monitor.action.TrackerActionByProjectSelectVO;
import com.overseas.common.vo.market.monitor.action.TrackerActionGetProjectActionMapVO;
import com.overseas.common.vo.market.monitor.action.TrackerActionSelectVO;
import com.overseas.common.vo.sys.project.ProjectSetActionMapVO;
import com.overseas.service.market.service.TrackerActionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Api(tags = "监测事件上报管理")
@RestController
@RequestMapping("/market/monitors/action")
@RequiredArgsConstructor
public class TrackerActionController extends AbstractController {

    private final TrackerActionService trackerActionService;

    @ApiOperation(value = "获取上报事件下拉", notes = "获取上报事件下拉", produces = "application/json", response = SelectDTO2.class)
    @PostMapping("/select")
    public R listMonitor(@Validated @RequestBody TrackerActionSelectVO selectVO) {
        return R.data(this.trackerActionService.getActionSelect(selectVO));
    }

    @ApiOperation(value = "根据项目ID获取上报事件下拉", notes = "项目ID获取上报事件下拉", produces = "application/json", response = SelectDTO2.class)
    @PostMapping("/by/project/select")
    public R selectByProject(@Validated @RequestBody TrackerActionByProjectSelectVO selectVO) {
        return R.data(this.trackerActionService.getActionSelectByProject(selectVO));
    }

    @ApiOperation(value = "根据项目ID获取内部事件映射", notes = "根据项目ID获取内部事件映射", produces = "application/json", response = SelectDTO2.class)
    @PostMapping("/map/by/project")
    public R mapByProject(@Validated @RequestBody TrackerActionByProjectSelectVO selectVO) {
        return R.data(this.trackerActionService.mapByProject(selectVO));
    }

    @ApiOperation(value = "获取项目事件映射", notes = "获取项目事件映射", produces = "application/json", response = SelectDTO2.class)
    @PostMapping("/get/project/map")
    public R selectByProject(@RequestBody @Validated TrackerActionGetProjectActionMapVO getProjectActionVO) {
        return R.data(this.trackerActionService.getSetAction(getProjectActionVO.getProject()));
    }

    @ApiOperation(value = "保存项目事件映射", notes = "保存项目事件映射", produces = "application/json")
    @PostMapping("/save/project/map")
    public R selectByProject(@Validated @RequestBody ProjectSetActionMapVO setActionMapVO) {
        this.trackerActionService.saveProjectTrackAction(setActionMapVO, this.getUserId());
        return R.ok();
    }
}
