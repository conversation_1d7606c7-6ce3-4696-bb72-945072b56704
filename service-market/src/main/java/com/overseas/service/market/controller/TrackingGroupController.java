package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.service.market.dto.trackingGroup.TrackingGroupListDTO;
import com.overseas.service.market.dto.trackingGroupApp.TrackingGroupAppListDTO;
import com.overseas.service.market.dto.trackingGroupProduct.TrackingGroupProductListDTO;
import com.overseas.service.market.service.TrackingGroupAppService;
import com.overseas.service.market.service.TrackingGroupProductService;
import com.overseas.service.market.service.TrackingGroupService;
import com.overseas.service.market.vo.trackingGroup.*;
import com.overseas.service.market.vo.trackingGroupApp.TrackingGroupAppBindVO;
import com.overseas.service.market.vo.trackingGroupApp.TrackingGroupAppListVO;
import com.overseas.service.market.vo.trackingGroupProduct.TrackingGroupProductBindVO;
import com.overseas.service.market.vo.trackingGroupProduct.TrackingGroupProductDeleteVO;
import com.overseas.service.market.vo.trackingGroupProduct.TrackingGroupProductListVO;
import com.overseas.service.market.vo.trackingGroupUrl.TrackingGroupUrlListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@Slf4j
@Api(tags = "Market-trackingGroup模块")
@RestController
@RequestMapping("/market/trackingGroups")
@RequiredArgsConstructor
public class TrackingGroupController extends AbstractController {

    private final TrackingGroupService trackingGroupService;

    private final TrackingGroupAppService trackingGroupAppService;

    private final TrackingGroupProductService trackingGroupProductService;

    @ApiOperation(value = "获取tracking Group 分页数据", response = TrackingGroupListDTO.class)
    @PostMapping("/list")
    public R listTrackingGroup(@RequestBody @Validated TrackingGroupListVO listVO) {
        return R.page(this.trackingGroupService.listTrackingGroup(listVO, this.getUserId()));
    }

    @ApiOperation(value = "获取tracking Group 下拉数据", response = SelectDTO.class)
    @PostMapping("/select")
    public R selectTrackingGroup(@RequestBody @Validated TrackingGroupSelectVO saveVO) {
        return R.data(this.trackingGroupService.selectTrackingGroup(saveVO));
    }

    @ApiOperation(value = "重命名tracking组")
    @PostMapping("/rename")
    public R renameTrackingGroup(@RequestBody @Validated TrackingGroupRenameVO renameVO) {
        this.trackingGroupService.renameTrackingGroup(renameVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "修改tracking组备注")
    @PostMapping("/remark")
    public R remarkTrackingGroup(@RequestBody @Validated TrackingGroupRemarkVO remarkVO) {
        this.trackingGroupService.remarkTrackingGroup(remarkVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "获取tracking Group 绑定账号数据", response = TrackingGroupAppListDTO.class)
    @PostMapping("/apps/list")
    public R listTrackingGroupApp(@RequestBody @Validated TrackingGroupAppListVO listVO) {
        return R.data(this.trackingGroupAppService.listTrackingGroupApp(listVO));
    }

    @ApiOperation(value = "在tracking组下绑定账号")
    @PostMapping("/apps/bind")
    public R bindTrackingGroupApp(@RequestBody @Validated TrackingGroupAppBindVO bindVO) {
        this.trackingGroupAppService.bindApp2TrackingGroup(bindVO, this.getUserId());
        this.trackingGroupService.generateTrackingGroupUrl(bindVO.getTrackingGroupId(), this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "获取tracking Group绑定的商品信息", response = TrackingGroupProductListDTO.class)
    @PostMapping("/products/list")
    public R listTrackingGroupProduct(@RequestBody @Validated TrackingGroupProductListVO listVO) {
        return R.page(this.trackingGroupProductService.listTrackingGroupProduct(listVO));
    }

    @ApiOperation(value = "在tracking组下绑定商品")
    @PostMapping("/products/bind")
    public R bindTrackingGroupProduct(@RequestBody @Validated TrackingGroupProductBindVO bindVO) {
        this.trackingGroupProductService.bindProduct2TrackingGroup(bindVO, this.getUserId());
        this.trackingGroupService.generateTrackingGroupUrl(bindVO.getTrackingGroupId(), this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "删除tracking组下绑定商品")
    @PostMapping("/products/delete")
    public R deleteTrackingGroupProduct(@RequestBody @Validated TrackingGroupProductDeleteVO deleteVO) {
        this.trackingGroupProductService.deleteTrackingGroupProduct(deleteVO, this.getUserId());
        this.trackingGroupService.generateTrackingGroupUrl(deleteVO.getTrackingGroupId(),
                this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "导出tracking组下链接")
    @PostMapping("/cpsUrls/export")
    public void exportTrackingGroupCpsUrl(@RequestBody @Validated TrackingGroupUrlListVO listVO,
                                       HttpServletResponse response) {
        this.trackingGroupService.exportTrackingGroupCpsUrl(listVO, response);
    }

    @ApiOperation(value = "导出tracking组下链接")
    @GetMapping("/cpsUrls/test")
    public void generateUrls() {
        this.trackingGroupService.generateTrackingGroupUrl(0, this.getUserId());
    }
}
