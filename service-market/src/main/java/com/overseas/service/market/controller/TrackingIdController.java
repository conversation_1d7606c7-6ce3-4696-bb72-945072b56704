package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.service.market.dto.trackingId.TrackingIdListDTO;
import com.overseas.service.market.service.TrackingIdService;
import com.overseas.service.market.vo.trackingId.TrackingIdListVO;
import com.overseas.service.market.vo.trackingId.TrackingIdSaveVO;
import com.overseas.service.market.vo.trackingId.TrackingIdSelectVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Api(tags = "Market-trackingId模块")
@RestController
@RequestMapping("/market/trackingIds")
@RequiredArgsConstructor
public class TrackingIdController extends AbstractController {

    private final TrackingIdService trackingIdService;

    @ApiOperation(value = "获取tracking id 分页数据", response = TrackingIdListDTO.class)
    @PostMapping("/list")
    public R listTrackingId(@RequestBody @Validated TrackingIdListVO listVO) {
        return R.page(this.trackingIdService.listTrackingId(listVO, this.getUserId()));
    }

    @ApiOperation(value = "保存tracking id 信息")
    @PostMapping("/save")
    public R saveTrackingId(@RequestBody @Validated TrackingIdSaveVO saveVO) {
        this.trackingIdService.saveTrackingId(saveVO);
        return R.ok();
    }

    @ApiOperation(value = "查询tracking id 下拉数据", response = SelectDTO.class)
    @PostMapping("/select")
    public R selectTrackingId(@RequestBody @Validated TrackingIdSelectVO selectVO) {
        return R.data(this.trackingIdService.selectTrackingId(selectVO));
    }
}
