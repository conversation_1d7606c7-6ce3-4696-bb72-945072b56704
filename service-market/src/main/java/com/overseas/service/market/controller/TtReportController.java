package com.overseas.service.market.controller;

import com.overseas.common.dto.R;
import com.overseas.service.market.dto.ttReport.TtReportListDTO;
import com.overseas.service.market.dto.ttReport.TtReportUploadResultDTO;
import com.overseas.service.market.service.TtReportService;
import com.overseas.service.market.vo.ttReport.TtReportListVO;
import com.overseas.service.market.vo.ttReport.TtReportUploadVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * TtReport控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Api(tags = "TT报表管理")
@RestController
@RequestMapping("/market/ttReports")
@RequiredArgsConstructor
@Slf4j
public class TtReportController extends AbstractController {

    private final TtReportService ttReportService;

    @ApiOperation(value = "上传Excel文件", notes = "支持上传TtReport和TtProtect数据", produces = "application/json",
            response = TtReportUploadResultDTO.class)
    @PostMapping("/upload")
    public R uploadExcel(@RequestBody @Validated TtReportUploadVO uploadVO) {
        TtReportUploadResultDTO result = ttReportService.uploadExcel(uploadVO, this.getUser());
        return R.data(result);
    }

    @ApiOperation(value = "获取报表数据列表", notes = "分页查询聚合后的报表数据", produces = "application/json",
            response = TtReportListDTO.class)
    @PostMapping("/list")
    public R listTtReport(@RequestBody @Validated TtReportListVO listVO) {
        return R.page(ttReportService.pageReportData(listVO));
    }

    @ApiOperation(value = "下载excel报表", notes = "下载分页查询聚合后的报表数据", produces = "application/json",
            response = TtReportListDTO.class)
    @PostMapping("/export")
    public void exportTtReport(@RequestBody @Validated TtReportListVO listVO, HttpServletResponse response) {
        this.ttReportService.exportReportData(listVO, response);
    }
} 