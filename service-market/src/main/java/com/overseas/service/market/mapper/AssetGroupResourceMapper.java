package com.overseas.service.market.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.overseas.service.market.entity.AssetGroupResource;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 **/
public interface AssetGroupResourceMapper extends BaseMapper<AssetGroupResource> {

    /**
     * 批量插入数据 根据uk 更改
     *
     * @param list 返回数据
     */

    @Insert("<script> " +
            "INSERT INTO `m_asset_group_resource` (`asset_id`, `asset_group_id`, `create_uid`) " +
            "VALUES " +
            "<foreach collection='list' item='item' index='index' separator=','> " +
            "(#{item.assetId}, #{item.assetGroupId}, #{item.createUid}) " +
            "</foreach> " +
            "ON DUPLICATE KEY UPDATE " +
            "is_del = 0, " +
            "update_uid = VALUES(create_uid) " +
            "</script>")
    void insertByUk(@Param("list") List<AssetGroupResource> list);

}
