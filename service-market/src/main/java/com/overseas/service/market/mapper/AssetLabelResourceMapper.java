package com.overseas.service.market.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.overseas.service.market.entity.AssetLabelResource;
import com.overseas.common.dto.market.asset.assetLabel.AssetLabelNameDTO;
import com.overseas.common.utils.ConstantUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AssetLabelResourceMapper extends BaseMapper<AssetLabelResource> {

    /**
     * 新增素材标签关联关系
     *
     * @param list   素材标签关系列表
     * @param userId 用户ID
     */
    @Select("<script> " +
            "INSERT INTO m_asset_label_resource (`asset_id`,`label_id`,`master_id`,`is_del`,`create_uid`) " +
            "VALUES " +
            "<foreach collection='list' item='item' index='index' separator=','> " +
            "(#{item.assetId},#{item.labelId},#{item.masterId},${item.isDel},#{userId}) " +
            "</foreach> " +
            "ON DUPLICATE KEY UPDATE " +
            "asset_id = VALUES(asset_id), " +
            "label_id = VALUES(label_id), " +
            "master_id = VALUES(master_id), " +
            "is_del = VALUES(is_del), " +
            "update_uid = #{userId} " +
            "</script>")
    void saveAssetLabelResource(@Param("list") List<AssetLabelResource> list, @Param("userId") Integer userId);

    /**
     * 标签资源获取
     *
     * @param wrapper 条件
     * @return 返回数据
     */
    @Select("SELECT malr.asset_id,mal.label_name, mal.id as label_id " +
            "FROM `m_asset_label_resource` AS malr " +
            "LEFT JOIN `m_asset_label` AS mal ON mal.id = malr.label_id " +
            "${ew.customSqlSegment}")
    List<AssetLabelNameDTO> getAssetLabelResource(@Param(ConstantUtils.WRAPPER) Wrapper<AssetLabelResource> wrapper);

    /**
     * 获取素材数量
     *
     * @param wrapper 条件
     * @return 返回数据
     */
    @Select("SELECT count(distinct asset_id) from  m_asset_label_resource ${ew.customSqlSegment}")
    Long getLabelMaterialCount(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);
}
