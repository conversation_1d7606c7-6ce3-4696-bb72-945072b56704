package com.overseas.service.market.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.dto.market.campaign.CampaignUpdateRecordListDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.market.entity.CampaignUpdateRecord;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CampaignUpdateRecordMapper extends BaseMapper<CampaignUpdateRecord> {

    /**
     * 插入列表数据
     *
     * @param list   列表
     * @param userId 用户
     */
    @Insert("<script> " +
            "INSERT INTO `m_campaign_update_record` (`campaign_id`,`record_type`,`content`,`md5`,`create_uid`) " +
            "VALUES " +
            "<foreach collection='list' item='item' index='index' separator=','> " +
            "(#{item.campaignId},#{item.recordType},#{item.content},#{item.md5},#{userId}) " +
            "</foreach> " +
            "ON DUPLICATE KEY UPDATE " +
            "update_date = VALUES(update_date), " +
            "update_uid = #{userId} " +
            "</script>")
    void saveUpdateRecord(@Param("list") List<CampaignUpdateRecord> list, @Param("userId") Integer userId);

    /**
     * 活动修改日志获取
     *
     * @param page    分页
     * @param wrapper 条件
     * @return 返回数据
     */
    @Select("SELECT record.id, record.campaign_id, record.content, record.record_type, record.create_time AS `date`, mc.campaign_name," +
            "IFNULL(user.company_name, '系统机器人') AS operator_name " +
            "FROM m_campaign_update_record AS record " +
            "INNER JOIN m_campaign AS mc ON mc.id = record.campaign_id " +
            "LEFT JOIN u_user AS user ON record.create_uid = user.id " +
            "${ew.customSqlSegment}")
    IPage<CampaignUpdateRecordListDTO> listUpdateRecord(IPage<CampaignUpdateRecord> page, @Param(ConstantUtils.WRAPPER) Wrapper<CampaignUpdateRecord> wrapper);

}
