package com.overseas.service.market.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.dto.market.assetCreative.AssetCreativeLogListDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.market.entity.PlanAssetUpdateLog;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 **/
public interface PlanAssetUpdateLogMapper extends BaseMapper<PlanAssetUpdateLog> {

    /**
     * 素材自动上新日志
     *
     * @param iPage   分页
     * @param wrapper 条件
     * @return 返回数据
     */
    @Select("SELECT mpaul.*, IFNULL(ma.asset_type, 1) as asset_type , ma.content, ma.is_upload, ma.cover_img_id " +
            "FROM m_plan_asset_update_log mpaul " +
            "LEFT JOIN m_asset ma ON mpaul.asset_id = ma.id " +
            " ${ew.customSqlSegment}")
    IPage<AssetCreativeLogListDTO> list(IPage<?> iPage, @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);


}
