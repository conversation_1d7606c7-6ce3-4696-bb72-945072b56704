package com.overseas.service.market.schedule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.overseas.service.market.entity.AuditUnitStatus;
import com.overseas.service.market.enums.audit.AuditAdxStatusEnum;
import com.overseas.service.market.mapper.AuditUnitStatusMapper;
import com.overseas.service.market.service.CreativeUnitAuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 **/
@Component
@Slf4j
@RequiredArgsConstructor
public class AdxCreativeUnitSchedule {

    private final AuditUnitStatusMapper auditUnitStatusMapper;

    private final CreativeUnitAuditService vioohCreativeUnitAuditServiceImpl;

    @Scheduled(cron = "0 */5 * * * ?")
    public void info() {
        this.auditUnitStatusMapper.selectList(new LambdaQueryWrapper<AuditUnitStatus>()
                .eq(AuditUnitStatus::getAdxStatus, AuditAdxStatusEnum.PENDING.getId())
        ).forEach(auditUnitStatus -> {
            try {
                if (auditUnitStatus.getAdxId() == 159) {
                    this.vioohCreativeUnitAuditServiceImpl.info(auditUnitStatus);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });

    }
}
