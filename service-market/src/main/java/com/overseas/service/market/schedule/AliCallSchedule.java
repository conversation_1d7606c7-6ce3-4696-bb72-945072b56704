package com.overseas.service.market.schedule;

import com.overseas.service.market.service.CallTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
@Profile({"online"})
public class AliCallSchedule {

    private final CallTaskService callTaskService;

    /**
     * 每十分钟检查是否有Shein计划变更
     */
    @Scheduled(fixedDelay = 600000)
    public void execCall() {
        this.callTaskService.saveSheinCallTask();
        this.callTaskService.execSheinCall();
    }

    /**
     * 每五分钟检查一下外呼成功的是否已接听
     */
    @Scheduled(fixedDelay = 310000)
    public void checkCallResult() {
        this.callTaskService.checkCallStatus();
    }
}
