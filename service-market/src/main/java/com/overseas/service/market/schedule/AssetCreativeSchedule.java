package com.overseas.service.market.schedule;

import com.alibaba.fastjson.JSONObject;
import com.overseas.service.market.events.creativeUnitAudit.CreativeUnitAuditEvent;
import com.overseas.service.market.mapper.CreativeUnitMapper;
import com.overseas.service.market.service.AssetCreativeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 **/
@Component
@RequiredArgsConstructor
@Slf4j
@Profile({"test2", "online"})
public class AssetCreativeSchedule {

    private final CreativeUnitMapper creativeUnitMapper;

    private final AssetCreativeService assetCreativeService;

    private final ApplicationContext applicationContext;

    @Scheduled(cron = "0 15,35,55 * * * ?")
    public void schedule() {
        log.info("素材自动上新脚本执行开始");
        List<Long> planIds = this.creativeUnitMapper.creativeUpdatePlans();
        log.info("素材自动上新计划:{}", JSONObject.toJSONString(planIds));
        planIds.forEach(planId -> {
            try {
                int count = assetCreativeService.addCreativeByAsset(planId, 0L);
                log.info("素材自动上新计划:{}, 新增数量:{}", planId, count);
                if (count > 0) {
                    applicationContext.publishEvent(new CreativeUnitAuditEvent(this, planId));
                }
            } catch (Exception e) {
                log.error("素材自动上新脚本异常: {}", e.getMessage(), e);
            }
        });
        log.info("素材自动上新脚本执行结束");
    }
}
