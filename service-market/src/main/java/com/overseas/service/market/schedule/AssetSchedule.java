package com.overseas.service.market.schedule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.overseas.service.market.entity.Asset;
import com.overseas.common.enums.OssBucketTypeEnum;
import com.overseas.common.utils.OssUtils;
import com.overseas.common.utils.UploadUtils;
import com.overseas.service.market.enums.assets.AssetTypeEnum;
import com.overseas.common.enums.market.asset.IsUploadEnum;
import com.overseas.service.market.service.AssetService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
@Profile({"online", "test2"})
public class AssetSchedule {

    private final AssetService assetService;

    private Integer compressCodeRate;

    @Value("${com-upload.compress-code-rate}")
    public void setCompressCodeRate(Integer compressCodeRate) {
        this.compressCodeRate = compressCodeRate;
    }

    /**
     * 上传素材到cdn
     */
    @Scheduled(fixedDelay = 60000)
    public void uploadAsset2Cdn() {// 获取当前需要上传cdn的素材结合，取20条
        log.info("Upload asset to cdn, starting ");
        Long startTime = System.currentTimeMillis();
        List<Asset> assets = assetService.list(new LambdaQueryWrapper<Asset>()
                .in(Asset::getAssetType, AssetTypeEnum.listMediaTypeId())
                .eq(Asset::getIsUpload, IsUploadEnum.NOT_UPLOAD.getId())
                .last(" limit 20"));
        assets.forEach(asset -> {
            try {
                boolean uploadResult = OssUtils.uploadFile(UploadUtils.getUploadPath(asset.getContent()),
                        asset.getContent(), OssBucketTypeEnum.MATERIAL);
                if (uploadResult) {
                    asset.setIsUpload(IsUploadEnum.SUCCESS.getId());
                    assetService.updateById(asset);
                }
            } catch (Exception e) {
                log.error(e.getMessage());
                asset.setIsUpload(IsUploadEnum.OVERDUE.getId());
            }
        });
        Long endTime = System.currentTimeMillis();
        log.info("Upload asset to cdn, end, cost: {} ms", (endTime - startTime));
    }

    @Scheduled(fixedDelay = 60000)
    public void uploadCompressAsset2Cdn() {// 获取当前需要上传cdn的素材结合，取20条
        log.info("Upload compress asset to cdn, starting ");
        Long startTime = System.currentTimeMillis();
        List<Asset> assets = assetService.list(new LambdaQueryWrapper<Asset>()
                .in(Asset::getAssetType, 3)
                .eq(Asset::getIsCompress, 1)
                .last(" limit 50"));
        assets.forEach(asset -> {
            Asset updateAsset = new Asset();
            updateAsset.setId(asset.getId());
            try {
                asset.setContent(asset.getContent().replace(".mp4", "_"+ asset.getCodeRate() + ".mp4"));
                boolean uploadResult = OssUtils.uploadFile(UploadUtils.getUploadPath(asset.getContent()),
                        asset.getContent(), OssBucketTypeEnum.MATERIAL);
                if (uploadResult) {
                    updateAsset.setIsCompress(5);
                    updateAsset.setContent(asset.getContent());
                }
            } catch (Exception e) {
                log.error(e.getMessage());
                updateAsset.setIsCompress(4);
            }
            assetService.updateById(updateAsset);
        });
        Long endTime = System.currentTimeMillis();
        log.info("Upload compress asset to cdn, end, cost: {} ms", (endTime - startTime));
    }

    /**
     * 定时扫描视频进行压缩
     */
    @Scheduled(fixedDelay = 100000)
    public void compressAsset() {
        this.assetService.compressAsset(this.compressCodeRate);
    }
}
