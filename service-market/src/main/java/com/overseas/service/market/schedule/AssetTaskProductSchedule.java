package com.overseas.service.market.schedule;

import com.overseas.service.market.service.AssetTaskProductService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 **/
@Component
@RequiredArgsConstructor
@Slf4j
@Profile({"online"})
public class AssetTaskProductSchedule {

    private final AssetTaskProductService assetTaskProductService;

    /**
     * 补充商品，10分钟执行一次
     */
    @Scheduled(cron = "0 */10 *  * * ?")
    public void syncProduct() {
        log.info("素材任务商品同步信息开始");
        assetTaskProductService.syncProductSchedule();
        log.info("素材任务商品同步信息结束");
    }


}
