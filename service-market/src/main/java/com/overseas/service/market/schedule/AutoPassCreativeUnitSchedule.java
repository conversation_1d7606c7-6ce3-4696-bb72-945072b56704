package com.overseas.service.market.schedule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.overseas.service.market.entity.CreativeUnit;
import com.overseas.service.market.enums.creative.units.CreativeUnitAuditStatusEnum;
import com.overseas.service.market.events.notifyControl.ControlContants;
import com.overseas.service.market.events.notifyControl.ControlCreativeUnitEvent;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.service.market.service.CreativeUnitService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
@Profile({"online", "test2"})
public class AutoPassCreativeUnitSchedule {

    private final CreativeUnitService creativeUnitService;

    private final ApplicationContext applicationContext;

    /**
     * 自动通过素材
     */
    @Scheduled(fixedDelay = 100000)
    public void updateTagStatus() {
        log.info("Auto pass creative unit, starting ");
        Long startTime = System.currentTimeMillis();
        List<CreativeUnit> list = creativeUnitService.list(new LambdaQueryWrapper<CreativeUnit>()
                .eq(CreativeUnit::getAuditStatus, CreativeUnitAuditStatusEnum.WAIT.getId())
                .eq(CreativeUnit::getIsDel, IsDelEnum.NORMAL.getId()));
        list.forEach(unit -> {
            unit.setAuditStatus(CreativeUnitAuditStatusEnum.PASS.getId());
            creativeUnitService.updateById(unit);
            applicationContext.publishEvent(new ControlCreativeUnitEvent(this, ControlContants.METHOD_UPDATE, unit.getId()));
        });
        Long endTime = System.currentTimeMillis();
        log.info("Auto pass creative unit, end, cost: {} ms", (endTime - startTime));
    }
}
