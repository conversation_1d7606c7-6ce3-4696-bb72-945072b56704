package com.overseas.service.market.schedule;

import com.overseas.common.utils.DateUtils;
import com.overseas.service.market.service.CampaignService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Profile({"online", "test1"})
public class CampaignSchedule {

    private final CampaignService campaignService;

    /**
     * 每小时执行更新次日预算
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void updateCampaignBudgetByNextDay() {

        this.campaignService.updateCampaignBudgetByNextDay(DateUtils.long2Date(System.currentTimeMillis() / 1000));
    }
}
