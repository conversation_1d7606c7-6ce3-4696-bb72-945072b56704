package com.overseas.service.market.schedule;

import com.overseas.common.configuration.MachineConfiguration;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.enums.MachineRoomEnum;
import com.overseas.common.utils.DateUtils;
import com.overseas.service.market.entity.ae.AeApp;
import com.overseas.service.market.enums.cps.CpsProductTypeEnum;
import com.overseas.service.market.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
@RequiredArgsConstructor
@Profile({"online"})
@Slf4j
public class CpsSchedule {

    private final CpsService cpsService;

    private final AeAuthorizationService aeAuthorizationService;

    private final CpsProductService cpsProductService;

    private final LazadaService lazadaService;

    private final CpsRecycleDataService cpsRecycleDataService;

    @Value("${lazada.cps.pull:false}")
    private Boolean lazadaCps;

    private final MachineConfiguration machineConfiguration;

    /**
     * 16:05 拉取数据
     * lazada cps order
     */
    @Scheduled(cron = "0 05 16 * * ?")
    public void lazadaCpsOrder() {
        if (!lazadaCps) {
            return;
        }
        Date today = new Date();
        for (int i = 7; i >= 1; i--) {
            String day = DateUtils.format(DateUtils.afterDay(today, -i));
            log.info("lazada cps report pull day :{}", day);
            this.lazadaService.listConversionReport(day);
        }
    }

    /**
     * 16:40 发送 oppo数据
     * lazada cps order 下载
     */
    @Scheduled(cron = "0 40 16 * * ?")
    public void lazadaCpsOrderOppo() {
        if (!lazadaCps) {
            return;
        }
        Date today = new Date();
        this.lazadaService.exportConversion(Stream.of(1).map(u -> DateUtils.format(DateUtils.afterDay(today, -u))).collect(Collectors.toList()));
    }

    /**
     * Ae xiaomi 数据导出
     */
    @Scheduled(cron = "0 0 17 * * ?")
    public void aeXiaomiCpsOrder() {
        log.info("ae xiao mi report start");
        if (MachineRoomEnum.US.getMachineRoom().equals(machineConfiguration.getMachineRoom())) {
            log.info("ae xiao mi report send");
            Date today = new Date();
            try {
                this.cpsService.aeXiaoMiExport(DateUtils.format(DateUtils.afterDay(today, -1)));
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        log.info("ae xiao mi report end");
    }

    /**
     * 每小时检查ROI回收数据是否有变化
     */
    @Scheduled(cron = "0 0 * * * *")
//    @Scheduled(fixedDelay = 300000)
    public void saveCpsRecycleData() {
        this.cpsRecycleDataService.saveCpsRecycleData();
    }

//    /**
//     * 亚马逊订单数据入库，暂停投放
//     */
//    @Scheduled(fixedDelay = 300000)
//    public void syncDataToPlanHour() {
//        this.cpsService.syncDataToPlanHour();
//    }

//    /**
//     * 每小时检查一次是否有授权token需要刷新
//     */
//    @Scheduled(cron = "0 0 * * * *")
//    public void refreshToken() {
//        this.aeAuthorizationService.refreshToken();
//    }

    /**
     * 每天凌晨1点启动各个分类商品拉取
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void pullProduct() {
        List<SelectDTO> categories = this.aeAuthorizationService.selectFirstCategory();
        List<String> languages = List.of("EN", "RU", "PT", "ES", "FR", "KO", "IT", "CL", "DE", "NL", "PL");
        log.info("pull ae product start");
        categories.forEach(category -> languages.forEach(language -> {
            log.info("pull ae product start, category id : {}, language : {}", category, language);
            this.aeAuthorizationService.pullProduct(CpsProductTypeEnum.HOT_PRODUCT, category.getId().toString(),
                    language, true);
        }));
        log.info("pull ae product end");
    }

//    /**
//     * 定时拉取回传订单中的商品信息
//     */
//    @Scheduled(fixedDelay = 10000)
//    public void pullOrderProduct() {
//        this.aeAuthorizationService.pullProduct(CpsProductTypeEnum.PRODUCT_DETAIL, "",
//                "", true);
//    }

//    /**
//     * 定时更新cps order额外信息
//     */
//    @Scheduled(fixedDelay = 600000)
//    public void updateCpsOrder() {
//        this.cpsProductService.updateCpsOrder();
//    }

    /**
     * 定时下载商品库中素材文件到本地
     */
//    @Scheduled(fixedDelay = 60000)
//    public void downloadMaterial() {
//        this.aeAuthorizationService.downloadMaterial();
//    }

    /**
     * 定时拉取AE用增的商品信息
     */
    @Scheduled(fixedDelay = 300000)
    public void checkAeProduct() {
        this.aeAuthorizationService.checkProduct();
    }

    /**
     * 定时生成用增商品cps链接
     */
    @Scheduled(fixedDelay = 600000)
    public void generateDpaCpsUrl() {
        List<AeApp> aeApps = this.aeAuthorizationService.getApps();
//        this.aeAuthorizationService.generateDpaCpsUrl(aeApps);
        this.aeAuthorizationService.generateProductLibraryCpsUrl(aeApps);
    }
}
