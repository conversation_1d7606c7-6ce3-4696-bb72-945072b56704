package com.overseas.service.market.schedule;

import com.overseas.service.market.service.AssetService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
@Profile({"online"})
public class LazadaCidSchedule {

    private final AssetService assetService;

    /**
     * 每隔1分钟扫描是否有包含lzd_的素材，解析其中的cid等信息
     */
    @Scheduled(fixedDelay = 60000)
    public void formatLazadaCid() {
        this.assetService.formatAssetInfo();
    }

    @Scheduled(cron = "0 0 * * * *")
//    @Scheduled(fixedDelay = 60000)
    public void checkLazadaAssetCid() {
        this.assetService.checkLazadaCidAsset();
    }
}
