package com.overseas.service.market.schedule;

import com.overseas.service.market.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Profile({"online", "ali"})
@Slf4j
public class LazadaSchedule {

    private final LazadaService lazadaService;

    /**
     * 生成lazada商品cps链接
     */
//    @Scheduled(fixedDelay = 5000)
//    public void generateProductCpsUrl() {
//        this.lazadaService.generateProductCpsUrl();
//    }

//    /**
//     * 每天凌晨1点启动各个分类商品拉取
//     */
//    @Scheduled(cron = "0 10/* * * * ?")
//    public void pullProductLazada() {
//        log.info("pull lazada cps product start");
//        lazadaService.pullProductsByCpsOrder();
//        log.info("pull lazada cps product end");
//    }
}
