package com.overseas.service.market.schedule;

import com.overseas.service.market.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Profile({"online"})
@Slf4j
public class OppoSchedule {

    private final OppoTaskService oppoTaskService;

    /**
     * 定时拉取AE用增的商品信息
     */
    @Scheduled(fixedDelay = 600000)
    public void generateOppoTask() {
        this.oppoTaskService.generateOppoAdCampaigns(0);
    }
}
