package com.overseas.service.market.schedule;

import com.overseas.common.utils.DateUtils;
import com.overseas.service.market.service.PlanService;
import com.overseas.service.market.service.PlanTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Profile({"online", "test2"})
public class PlanSchedule {

    private final PlanService planService;

    private final PlanTaskService planTaskService;

    /**
     * 每小时执行更新次日预算
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void updatePlanBudgetByNextDay() {

        this.planService.updatePlanBudgetByNextDay(DateUtils.long2Date(System.currentTimeMillis() / 1000));
    }

    /**
     * 每隔20秒扫描是否有待生成计划，如果有，则执行创建计划流程
     */
    @Scheduled(fixedDelay = 20000)
    public void executeCreatePlan() {

        this.planTaskService.savePlanByTask();
    }
}
