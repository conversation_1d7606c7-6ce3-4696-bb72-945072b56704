package com.overseas.service.market.schedule;

import com.overseas.service.market.service.PlanService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
@Profile({"online", "test1"})
public class PlanStatusSchedule {

    private final PlanService planService;

    /**
     * 每小时执行更新计划状态
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void updatePlanStatus() {
        this.planService.updatePlanStatusHandler(System.currentTimeMillis() / 1000);
    }
}
