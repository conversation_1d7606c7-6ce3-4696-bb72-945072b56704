package com.overseas.service.market.schedule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.overseas.service.market.entity.AuditAgentMaster;
import com.overseas.service.market.entity.UserExtra;
import com.overseas.service.market.events.qualificationExpire.QualificationExpireEvent;
import com.overseas.common.utils.DateUtils;
import com.overseas.service.market.enums.master.MasterAuditMasterStatusEnum;
import com.overseas.service.market.service.AuditAgentMasterService;
import com.overseas.service.market.service.UserExtraService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class QualificationExpireSchedule {

    private final AuditAgentMasterService auditAgentMasterService;

    private final UserExtraService userExtraService;

    private final ApplicationContext applicationContext;

    /**
     * 更新计划状态
     */
    @Scheduled(fixedDelay = 600000)
    public void updateTagStatus() {
        log.info("Auto update qualification, starting ");
        Long startTime = System.currentTimeMillis();
        this.expireQualificationDateHandler();
        Long endTime = System.currentTimeMillis();
        log.info("Auto update qualification, end, cost: {} ms", (endTime - startTime));
    }

    private void expireQualificationDateHandler() {
        // 第一步：先扫描待处理的投放账号资质数据
        List<AuditAgentMaster> expireList = auditAgentMasterService.list(new LambdaQueryWrapper<AuditAgentMaster>()
                .eq(AuditAgentMaster::getAuditStatus, MasterAuditMasterStatusEnum.AUDIT_PASS.getId())
                .ne(AuditAgentMaster::getExpireDate, "")
                .lt(AuditAgentMaster::getExpireDate, DateUtils.getTodayStringDate()));
        // 处理过期的资质信息
        expireList.forEach(auditAgentMaster -> {
            auditAgentMaster.setAuditStatus(MasterAuditMasterStatusEnum.EXPIRE_DATE.getId());
            auditAgentMasterService.updateById(auditAgentMaster);

            // 发布事件，把当前资质关联的广告主给置为不可用
            applicationContext.publishEvent(new QualificationExpireEvent(
                    this,
                    auditAgentMaster.getUserId(),
                    auditAgentMaster.getAuditMasterId(),
                    MasterAuditMasterStatusEnum.EXPIRE_DATE.getId()));
        });

        // 第二步：处理代理商和管理员的资质数据
        List<UserExtra> userExtraExpireList = userExtraService.list(new LambdaQueryWrapper<UserExtra>()
                .eq(UserExtra::getAuditStatus, MasterAuditMasterStatusEnum.AUDIT_PASS.getId())
                .ne(UserExtra::getExpireDate, "")
                .lt(UserExtra::getExpireDate, DateUtils.getTodayStringDate()));
        userExtraExpireList.forEach(userExtra -> {
            userExtra.setAuditStatus(MasterAuditMasterStatusEnum.EXPIRE_DATE.getId());
            userExtraService.updateById(userExtra);
            // 此处不需要通知中控
        });
    }
}
