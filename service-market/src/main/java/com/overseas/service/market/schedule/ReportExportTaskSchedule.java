package com.overseas.service.market.schedule;

import com.overseas.common.dto.sys.UserLoginDTO;
import com.overseas.common.utils.redis.RedisUtils;
import com.overseas.common.vo.sys.UserLoginVO;
import com.overseas.service.market.feign.FgMarketService;
import com.overseas.service.market.feign.FgSystemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
@Profile({"online"})
public class ReportExportTaskSchedule {

    private final FgMarketService fgMarketService;

    private final FgSystemService fgSystemService;

    private final RedisUtils redisUtils;

    /**
     * 每两分钟执行一次扫描，如果有待创建的任务，则执行
     */
    @Scheduled(fixedDelay = 120000)
    public void exportTask() {
        this.fgMarketService.exportAllReportTask(getUserToken());
    }

    /**
     * 用户登陆信息
     *
     * @return 返回数据
     */
    private String getUserToken() {
        String key = "monitor_user_token";
        String token = redisUtils.get(key);
        if (StringUtils.isNotBlank(token)) {
            return token;
        }
        UserLoginVO userLoginVO = new UserLoginVO();
        userLoginVO.setUserName("<EMAIL>");
        userLoginVO.setPassword("xylx1dtdsp");
        UserLoginDTO userLoginDTO = fgSystemService.authLogin(userLoginVO).getData();
        //存储半个小时
        redisUtils.set(key, userLoginDTO.getAuthToken().toString(), 1800);
        return userLoginDTO.getAuthToken().toString();
    }
}
