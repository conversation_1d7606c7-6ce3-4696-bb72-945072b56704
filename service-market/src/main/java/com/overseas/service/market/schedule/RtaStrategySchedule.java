package com.overseas.service.market.schedule;

import com.overseas.service.market.service.RtaStrategyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 **/
@Component
@Slf4j
@RequiredArgsConstructor
public class RtaStrategySchedule {

    private final RtaStrategyService rtaStrategyService;

    @Scheduled(cron = "0 0 */1 * * ?")
    public void pull2All() {
        rtaStrategyService.pull2All();
    }

}
