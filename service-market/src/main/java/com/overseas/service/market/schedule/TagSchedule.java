package com.overseas.service.market.schedule;

import com.overseas.service.market.service.TagService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Profile({"online", "test1"})
public class TagSchedule {

    private final TagService tagService;

    /**
     * 更新人群标签上传状态
     */
    @Scheduled(fixedDelay = 20000)
    public void updateTagStatus() throws Exception {

        this.tagService.updateTagStatus();
    }

    @Scheduled(fixedDelay = 3600000)
    public void checkTagShareType() {

        this.tagService.checkNewMaster();
    }
}
