package com.overseas.service.market.schedule;

import com.overseas.common.utils.SmsUtils;
import com.overseas.service.market.mapper.PlanDirectMapper;
import com.overseas.service.market.mapper.PlanMapper;
import com.overseas.service.market.service.AdxService;
import com.overseas.service.market.service.EpService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 **/
@Component
@RequiredArgsConstructor
@Profile({"test1", "online"})
@Slf4j
public class TemplateSchedule {

    private final PlanMapper planMapper;

    private final PlanDirectMapper planDirectMapper;

    private final AdxService adxService;

    private final EpService epService;

    private final ApplicationContext applicationContext;

    private final SmsUtils smsUtils;

    @Scheduled(cron = "0 */10 * * * ?")
    public void schedule() {
//        log.info("ADX模板校验任务执行开始");
//        List<Plan> planList = planMapper.selectList(new LambdaQueryWrapper<Plan>()
//                .ne(Plan::getTemplateId, 0)
//                //去除时间限制逻辑
//                //.gt(Plan::getUpdateTime, DateUtils.afterMinute(new Date(), -25))
//                .eq(Plan::getIsDel, IsDelEnum.NORMAL.getId())
//                .eq(Plan::getPlanStatus, PlanStatusEnum.MARKETING.getId())
//        );
//        if (planList.isEmpty()) {
//            log.info("ADX模板校验任务执行结束，无计划符合条件");
//            return;
//        }
//        List<String> errPlanIds = new ArrayList<>();
//        planList.forEach(plan -> {
//            Map<Long, List<PlanDirect>> planDirectMap = planDirectMapper.selectList(new LambdaQueryWrapper<PlanDirect>()
//                    .eq(PlanDirect::getPlanId, plan.getId())
//                    .in(PlanDirect::getDirectId, List.of(1031, 1032))
//            ).stream().collect(Collectors.groupingBy(PlanDirect::getPlanId));
//            planDirectMap.forEach((planId, directs) -> {
//                try {
//                    directs.forEach(direct -> {
//                        if (StringUtils.isBlank(direct.getDirectValue())) {
//                            return;
//                        }
//                        if (direct.getDirectId().equals(1032)) {
//                            epService.checkEpTemplate(JSONObject.parseArray(direct.getDirectValue(), Long.class), List.of(plan.getTemplateId()));
//                        }
//                        if (direct.getDirectId().equals(1031)) {
//                            adxService.checkAdxTemplate(JSONObject.parseArray(direct.getDirectValue(), Long.class), List.of(plan.getTemplateId()));
//                        }
//                    });
//                } catch (CustomException e) {
//                    String msg = "模板ID：" + plan.getTemplateId() + " 计划ID：" + plan.getId() + " 错误信息：" + e.getMessage();
//                    log.info(msg);
//                    //更新计划暂停
//                    Plan update = new Plan();
//                    update.setPlanStatus(PlanStatusEnum.STOP.getId());
//                    planMapper.update(update, new LambdaQueryWrapper<Plan>().eq(Plan::getId, plan.getId()));
//                    applicationContext.publishEvent(new ControlPlanEvent(this, ControlContants.METHOD_UPDATE, plan.getId()));
//                    errPlanIds.add(planId.toString());
//                }
//            });
//        });
//        if (CollectionUtils.isNotEmpty(errPlanIds)) {
//            log.info("ADX模板校验，计划ID：{} 模板不匹配", String.join(",", errPlanIds));
//            smsUtils.sendMsg("ADX与JS模板不匹配", String.format("计划总数:%s，具体请查看日志", errPlanIds.size()), List.of("15556985735", "18756052799"));
//        }
//        log.info("ADX模板校验任务执行结束");
    }
}
