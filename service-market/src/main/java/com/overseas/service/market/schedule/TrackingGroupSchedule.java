package com.overseas.service.market.schedule;

import com.overseas.service.market.service.AeAuthorizationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Profile({"online"})
@Slf4j
public class TrackingGroupSchedule {

    private final AeAuthorizationService aeAuthorizationService;

    /**
     * 定时生成TrackingGroup商品cps链接
     */
    @Scheduled(fixedDelay = 100000)
    public void generateTrackingGroupCpsUrl() {
        this.aeAuthorizationService.generateTrackingGroupCpsUrl();
    }
}
