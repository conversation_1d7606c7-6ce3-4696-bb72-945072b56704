package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.SelectDTO2;
import com.overseas.common.dto.TreeNodeDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.plan.direct.AdxSelectGetVO;
import com.overseas.common.vo.market.plan.direct.EpSelectGetVO;
import com.overseas.service.market.dto.adx.AdxGetDTO;
import com.overseas.service.market.entity.Adx;
import com.overseas.service.market.vo.adx.*;

import java.util.List;

public interface AdxService extends IService<Adx> {

    /**
     * 获取当前正常状态的ADX下拉
     *
     * @return 返回正常使用adx
     */
    List<SelectDTO> selectWorkable();

    List<SelectDTO> getEpSelect(EpSelectGetVO getVO);


    List<SelectDTO> getAdxSelect(AdxSelectGetVO getVO);

    List<TreeNodeDTO> getAdxEpTree();

    List<SelectDTO> getDockingAdxSelect();


    List<SelectDTO> select(AdxSelectVO selectVO);


    List<SelectDTO2> protocolSelect();

    /**
     * adx 列表
     *
     * @param listVO 条件
     * @return
     */
    PageUtils<?> list(AdxListVO listVO);

    /**
     * 保存 adx
     *
     * @param saveVO     数据
     * @param operateUid 操作用户
     */
    void save(AdxSaveVO saveVO, Integer operateUid);


    /**
     * 获取 adx 数据
     *
     * @param getVO 条件
     * @return 数据
     */
    AdxGetDTO get(AdxGetVO getVO);

    /**
     * 编辑 adx 数据
     *
     * @param updateVO   数据
     * @param operateUid 操作用户
     */
    void update(AdxUpdateVO updateVO, Integer operateUid);

    /**
     * 绑定模板
     *
     * @param bindVO     条件
     * @param operateUid 操作用户
     */
    void bind(AdxBindVO bindVO, Integer operateUid);

    /**
     * 解绑模板
     *
     * @param unbindVO     条件
     * @param operateUid 操作用户
     */
    void unbind(AdxUnbindVO unbindVO, Integer operateUid);

    /**
     * 删除 adx
     *
     * @param deleteVO   删除数据
     * @param operateUid 操作用户
     */
    void del(AdxDeleteVO deleteVO, Integer operateUid);

    /**
     * 修改 ADX状态
     *
     * @param statusVO   状态
     * @param operateUid 操作用户
     */
    void status(AdxStatusVO statusVO, Integer operateUid);

    /**
     * 校验模板是否支持adx
     *
     * @param adxIds      adx Ids
     * @param templateIds 模板ID
     */
    void checkAdxTemplate(List<Long> adxIds, List<Long> templateIds);
}
