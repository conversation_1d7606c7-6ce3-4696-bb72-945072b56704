package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.dto.SelectDTO;
import com.overseas.service.market.entity.ae.AeApp;
import com.overseas.service.market.entity.ae.AeAuthorization;
import com.overseas.service.market.enums.cps.CpsProductTypeEnum;

import java.util.List;

public interface AeAuthorizationService extends IService<AeAuthorization> {

    /**
     * 根据授权信息，保存生成token相关信息
     *
     * @param code ae后台授权时生成的code，一次性使用
     */
    void saveAeAuthorization(String code, String appKey);

    /**
     * 更新token
     */
    void refreshToken();

    List<SelectDTO> selectFirstCategory();

    void pullProduct(CpsProductTypeEnum typeEnum, String categoryId, String language, boolean addOnly);

    void pullProduct(CpsProductTypeEnum typeEnum, String categoryId, String language, boolean addOnly, List<String> searchProductIds);

    /**
     * 下载商品素材到本地
     */
    void downloadMaterial();

    void checkProduct();

    /**
     * 生成AE DPA模式投放用cps链接
     */
    void generateDpaCpsUrl(List<AeApp> aeApps);

    List<AeApp> getApps();

    /**
     * 生成Tracking组下cps链接
     */
    void generateTrackingGroupCpsUrl();

    /**
     * 生成商品库中商品链接
     */
    void generateProductLibraryCpsUrl(List<AeApp> aeApps);
}

