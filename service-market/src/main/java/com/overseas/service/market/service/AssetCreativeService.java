package com.overseas.service.market.service;

import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.assetCreative.AssetCreativeLogListVO;

/**
 * <AUTHOR>
 **/
public interface AssetCreativeService {


    /**
     * 上新日志
     *
     * @param listVO 条件
     * @return 返回数据
     */
    PageUtils<?> list(AssetCreativeLogListVO listVO);

    /**
     * 添加计划素材
     *
     * @param planId    计划ID
     * @param monitorId 监控ID
     * @return 返回数据
     */
    int addCreativeByAsset(Long planId, Long monitorId);


}
