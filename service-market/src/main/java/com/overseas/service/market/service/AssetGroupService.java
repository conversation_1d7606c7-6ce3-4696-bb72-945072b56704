package com.overseas.service.market.service;

import com.overseas.common.dto.SelectDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.assetGroup.*;

import java.util.List;

/**
 * <AUTHOR>
 **/

public interface AssetGroupService {

    /**
     * 素材列表
     *
     * @param listVO 数据
     * @param userId 用户ID
     * @return 返回数据
     */
    PageUtils<?> list(AssetGroupListVO listVO, Integer userId);

    /**
     * 保存素材组
     *
     * @param saveVO 素材组信息
     * @param userId 用户ID
     */
    void save(AssetGroupSaveVO saveVO, Integer userId);

    /**
     * 编辑素材组
     *
     * @param updateVO 素材组编辑信息
     * @param userId   用户ID
     */
    void update(AssetGroupUpdateVO updateVO, Integer userId);

    /**
     * 删除素材组
     *
     * @param delVO  数据
     * @param userId 用户ID
     */
    void del(AssetGroupDelVO delVO, Integer userId);

    /**
     * 根据素材删除素材组
     *
     * @param assetIds 素材ID
     * @param userId   用户
     */
    void batchDeleteGroupByAsset(List<Long> assetIds, Integer userId);

    /**
     * 批量添加素材组
     *
     * @param addVO  数据
     * @param userId 用户ID
     */
    void batchAddGroup(AssetGroupBatchSetVO addVO, Integer userId);

    /**
     * 通过素材的下发账户，添加素材组下发账户
     *
     * @param assetIds  素材ID
     * @param masterIds 账户ID
     * @param userId    用户ID
     */
    void batchAddMasterByAssetIds(List<Long> assetIds, List<Integer> masterIds, Integer userId);

    /**
     * 给素材组添加账户
     *
     * @param assetGroupIds 分组
     * @param masterIds     账户
     * @param userId        账户ID
     */
    void batchAddMaster(List<Long> assetGroupIds, List<Integer> masterIds, Integer userId);

    /**
     * 素材组数据获取
     *
     * @param selectVO 筛选数据
     * @return 返回数据
     */
    List<SelectDTO> selectAll(AssetGroupSelectAllVO selectVO);

    /**
     * 素材组数据获取
     *
     * @param selectVO 筛选数据
     * @return 返回数据
     */
    List<SelectDTO> select(AssetGroupSelectVO selectVO);
}
