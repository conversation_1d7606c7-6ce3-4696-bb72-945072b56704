package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.dto.market.assetInfo.AssetInfoDTO;
import com.overseas.common.vo.market.assetInfo.AssetInfoGetVO;
import com.overseas.service.market.entity.AssetInfo;

public interface AssetInfoService extends IService<AssetInfo> {
    AssetInfoDTO getAssetInfo(AssetInfoGetVO getVO);
}
