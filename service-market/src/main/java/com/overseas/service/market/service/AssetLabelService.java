package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.dto.CascaderDTO2;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.asset.assetLabel.AssetLabelSelectDTO;
import com.overseas.common.dto.market.assetLabel.AssetLabelGetDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.asset.assetLabel.AssetLabelSaveVO;
import com.overseas.common.vo.market.asset.assetLabel.AssetLabelSelectGetVO;
import com.overseas.common.vo.market.assetLabel.*;
import com.overseas.service.market.entity.AssetLabel;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AssetLabelService extends IService<AssetLabel> {

    /**
     * 获取素材标签下拉数据
     *
     * @param getVO 传入参数
     * @return 返回数据
     */
    List<AssetLabelSelectDTO> getAssetLabelSelect(AssetLabelSelectGetVO getVO);

    /**
     * 新增素材标签
     *
     * @param saveVO 传入参数
     * @param userId 用户ID
     */
    void saveAssetLabel(AssetLabelSaveVO saveVO, Integer userId);

    /**
     * 获取分类数据
     *
     * @param categoryVO 条件
     * @return 返回数据
     */
    List<SelectDTO> categorySelect(AssetLabelCategoryVO categoryVO);

    /**
     * 标签 列表
     *
     * @param listVO 条件
     * @return 返回数据
     */
    PageUtils<?> list(AssetLabelListVO listVO);

    /**
     * 保存标签
     *
     * @param saveVO 数据
     * @param userId 用户
     */
    void save(com.overseas.common.vo.market.assetLabel.AssetLabelSaveVO saveVO, Integer userId);

    /**
     * 更新标签
     *
     * @param updateVO 数据
     * @param userId   用户
     */
    void update(AssetLabelUpdateVO updateVO, Integer userId);

    /**
     * 获取标签
     *
     * @param getVO 条件
     * @return 返回数据
     */
    AssetLabelGetDTO get(AssetLabelGetVO getVO);

    /**
     * 删除标签
     *
     * @param delVO  条件
     * @param userId 用户
     */
    void del(AssetLabelDelVO delVO, Integer userId);

    /**
     * 批量添加素材标签
     *
     * @param addVO  条件
     * @param userId 用户
     */
    void batchAdd(AssetLabelBatchAddVO addVO, Integer userId);

    /**
     * 批量添加素材标签
     *
     * @param delVO  条件
     * @param userId 用户
     */
    void batchDel(AssetLabelBatchDelVO delVO, Integer userId);

    /**
     * 级联数据
     *
     * @param cascaderVO 条件
     * @return 返回数据
     */
    List<CascaderDTO2> cascader(AssetLabelCascaderVO cascaderVO);
}
