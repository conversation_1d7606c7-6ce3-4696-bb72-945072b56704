package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.dto.market.asset.*;
import com.overseas.common.vo.market.asset.*;
import com.overseas.common.vo.report.AssetReportListVO;
import com.overseas.service.market.entity.Asset;
import com.overseas.service.market.entity.User;
import com.overseas.service.market.vo.assetTask.AssetSizeSelectVO;
import com.overseas.service.market.vo.other.AssertCheckVO;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.SelectDTO2;
import com.overseas.common.dto.SelectDTO3;
import com.overseas.common.utils.PageUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-24 9:25
 */
public interface AssetService extends IService<Asset> {

    Asset getByMd5(String md5);

    Asset checkAsset(AssertCheckVO checkVO, Integer userId);

    Asset getAsset(Long id);

    void formatAsset(Asset assertInDb);

    Asset saveAsset(Integer assetType, String basePath, String fileName, Integer loginUserId) throws IOException;

    Asset saveTextAsset(String text, Integer loginUserId);

    /**
     * 获取素材尺寸下拉数据
     *
     * @param getVO 传入参数
     * @return 返回数据
     */
    List<SelectDTO3> getAssetSizeSelect(AssetSizeSelectGetVO getVO);

    /**
     * 获取素材尺寸下拉数据
     *
     * @param getVO 传入参数
     * @return 返回数据
     */
    List<SelectDTO> selectAssetSize(AssetSizeSelectGetVO getVO);

    /**
     * 获取素材列表分页数据
     *
     * @param listVO 传入参数
     * @param userId 用户ID
     * @return 返回数据
     */
    PageUtils<AssetListDTO> getAssetPage(AssetListVO listVO, Integer userId, User user);

    /**
     * 获取所有创意单元数量
     *
     * @param masterId  账户ID
     * @param assetIds  素材ID
     * @param fieldType 类型
     * @param user      用户
     * @return 返回数据
     */
    Map<Long, Integer> getUnitCountOfAsset(Long masterId, List<Long> assetIds, Integer fieldType, User user);

    /**
     * 获取投放创意单元数量
     *
     * @param masterId  账户ID
     * @param assetIds  素材ID
     * @param fieldType 类型
     * @param user      用户
     * @return 返回数据
     */
    Map<Long, Integer> getUnitPutCountOfAsset(Long masterId, List<Long> assetIds, Integer fieldType, User user);

    /**
     * 新增素材与用户记录
     *
     * @param saveVO 传入参数
     * @param userId 用户ID
     */
    void saveAssetResource(AssetResourceSaveVO saveVO, Integer userId);

    /**
     * 新增素材与用户记录
     *
     * @param saveVO 传入参数
     * @param userId 用户ID
     */
    void saveAssetResourceV2(AssetResourceSaveV2VO saveVO, Integer userId);

    /**
     * 校验是否存在广告但愿
     *
     * @param getVO 条件
     * @return 返回数据
     */
    List<Long> checkExistUnit(AssetResourceDeleteGetVO getVO);

    /**
     * 素材归档
     *
     * @param fileVO 归档
     * @param user   操作用户
     */
    void unitFile(AssetUnitFileVO fileVO, User user);

    /**
     * 删除素材资源
     *
     * @param getVO  条件
     * @param userId 用户ID
     */
    void deleteAssetResource(AssetResourceDeleteGetVO getVO, Integer userId);

    /**
     * 删除素材
     *
     * @param getVO  传入参数
     * @param userId 用户ID
     */
    void deleteAsset(AssetDeleteGetVO getVO, Integer userId);

    /**
     * 批量下载素材
     *
     * @param getVO    传入参数
     * @param request  request
     * @param response response
     */
    void batchDownload(AssetBatchDownloadGetVO getVO, Integer userId, HttpServletRequest request, HttpServletResponse response);

    /**
     * 共享素材
     *
     * @param getVO  传入参数
     * @param userId 用户ID
     */
    void shareAsset(AssetShareGetVO getVO, Integer userId);

    /**
     * 根据算法获取衍生素材
     *
     * @param assetAlgorithmVO 传入参数
     * @param userId           用户ID
     * @return 返回数据
     */
    List<AssetSizeDTO> getAlgorithmUrlList(AssetAlgorithmVO assetAlgorithmVO, Integer userId);

    /**
     * 根据算法获取素材合适比例
     *
     * @param assetAlgorithmVO 传入参数
     * @return 返回数据
     */
    List<SelectDTO2> getAlgorithmScaleSelect(AssetAlgorithmVO assetAlgorithmVO);

    /**
     * 根据指定素材和比例获取新素材
     *
     * @param getVO  传入参数
     * @param userId 用户ID
     * @return 返回数据
     */
    AssetSizeDTO getAlgorithmByScale(AssetAlgorithmGetVO getVO, Integer userId);

    List<SelectDTO> listAsset(AssetGetVO getVO);

    AssetCountDTO getAssetCount(AssetCountGetVO getVO);

    List<Long> getAssetIdsBySearch(AssetSearchGetVO getVO);

    Map<Long, AssetBaseDTO> getAssetInfoByIds(AssetGetVO getVO);

    List<String> listAssetCIdByMasterId(AssetCIdGetVO getVO);

    void updateAssetName(AssetNameUpdateVO updateVO, Integer userId);

    void compressAsset(Integer codeRte);

    List<Long> newAssetIds(AssetNewIdsVO newIdsVO);

    List<SelectDTO> selectAssetSize(AssetSizeSelectVO selectVO);

    PageUtils<DesignAssetReportListDTO> listReport(AssetReportListVO listVO, Long userId);

    void formatAssetInfo();

    void checkLazadaCidAsset();
}
