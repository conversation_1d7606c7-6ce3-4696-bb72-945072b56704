package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.enums.CpsProjectEnum;
import com.overseas.service.market.dto.assetTask.AssetTaskProductDTO;
import com.overseas.service.market.entity.assetTask.AssetTaskProduct;
import com.overseas.service.market.vo.assetTask.CpsUrlGenerateVO;

import java.util.List;

public interface AssetTaskProductService extends IService<AssetTaskProduct> {

    List<AssetTaskProductDTO> generateCpsUrl(CpsUrlGenerateVO generateVO);

    void syncProductSchedule();

    String getProductIdByUrl(CpsProjectEnum projectEnum, String productUrl);
}
