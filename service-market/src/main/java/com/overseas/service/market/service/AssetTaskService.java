package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.service.market.dto.assetTask.AssetTaskGetDTO;
import com.overseas.service.market.dto.assetTask.AssetTaskImportExcelDTO;
import com.overseas.service.market.dto.assetTask.AssetTaskListDTO;
import com.overseas.service.market.entity.User;
import com.overseas.service.market.entity.assetTask.AssetTask;
import com.overseas.service.market.vo.assetTask.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface AssetTaskService extends IService<AssetTask> {

    AssetTask saveAssetTask(AssetTaskSaveVO assetTaskSaveVO, Integer loginUserId);

    AssetTask updateAssetTask(AssetTaskUpdateVO assetTaskUpdateVO, User user);

    List<SelectDTO> assetTaskUserSelect(AssetTaskUserSelectVO selectVO);

    PageUtils<AssetTaskListDTO> listAssetTask(AssetTaskListVO listVO, User loginUser);

    AssetTaskGetDTO getAssetTask(AssetTaskGetVO getVO);

    List<SelectDTO> selectAssetTask(AssetTaskSelectVO selectVO);

    void bindProduct(AssetTaskBindVO bindVO, Integer loginUserId);

    List<AssetTaskImportExcelDTO> importExcel(AssetTaskImportExcelVO excelVO);

    void exportProduct(AssetTaskGetVO getVO, HttpServletResponse response) throws IOException ;

    void checkData(AssetTaskCheckDataVO dataVO);

    void unbindProductAsset(AssetTaskAssetUnbindVO unbindVO, Integer loginUserId);

    void unbindTaskAsset(AssetBatchUnbindVO unbindVO, Integer loginUserId);
}
