package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.service.market.dto.audit.master.AuditMasterGetDTO;
import com.overseas.service.market.entity.AuditAgentMaster;
import com.overseas.service.market.vo.auditMaster.AuditMasterSaveVO;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-09-14 16:09
 */
public interface AuditAgentMasterService extends IService<AuditAgentMaster> {

    Long saveQualification(AuditMasterSaveVO vo, Integer loginUserId);

    Long saveBankInfo(AuditMasterSaveVO vo, Integer loginUserId);

    Long validateMoney(AuditMasterSaveVO vo, Integer loginUserId);

    AuditMasterGetDTO getDetail(Long id, Integer loginUserId);

    AuditAgentMaster getAgentMaster(Long auditMasterId, Integer userId);
}
