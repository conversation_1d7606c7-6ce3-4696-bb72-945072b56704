package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.service.market.entity.AuditMaster;
import com.overseas.service.market.entity.User;
import com.overseas.service.market.vo.auditMaster.AuditMasterListVO;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.auditMaster.AuditMasterListDTO;
import com.overseas.common.utils.PageUtils;

import java.util.List;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-16 20:12
 */
public interface AuditMasterService extends IService<AuditMaster> {

    /**
     * 获取审核广告主的下拉数据
     *
     * @return
     */
    List<SelectDTO> selectAuditMaster(User loginUser, List<Integer> permissionMasterIds);

    /**
     * 获取分页数据
     *
     * @param auditMasterListVO
     * @param loginUserId
     * @return
     */
    PageUtils<AuditMasterListDTO> pageAuditMaster(AuditMasterListVO auditMasterListVO, Integer loginUserId);

    Long saveMasterMainInfo(AuditMaster auditMaster);

    AuditMaster getMaster(Long id);
}
