package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.vo.market.behaviorApp.*;
import com.overseas.service.market.entity.BehaviorApp;
import com.overseas.common.dto.market.behaviorApp.BehaviorAppGetDTO;
import com.overseas.common.dto.market.behaviorApp.BehaviorAppIndustrySelectDTO;
import com.overseas.common.dto.market.behaviorApp.BehaviorAppListDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.service.market.dto.behaviorApp.BehaviorAppSelectDTO;
import com.overseas.service.market.entity.User;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BehaviorAppService extends IService<BehaviorApp> {

    /**
     * 获取应用下拉
     *
     * @param getVO 传入参数
     * @return 返回数据
     */
    List<BehaviorAppSelectDTO> selectBehaviorApp(BehaviorAppSelectGetVO getVO, User user);

    /**
     * 获取应用下拉（含带行业属性）
     *
     * @param getVO 传入参数
     * @return 返回数据
     */
    List<BehaviorAppIndustrySelectDTO> selectBehaviorAppAndIndustry(BehaviorAppSelectGetVO getVO, User user);

    /**
     * 获取行为应用列表分页数据
     *
     * @param listVO 传入参数
     * @return 返回数据
     */
    PageUtils<BehaviorAppListDTO> getBehaviorPage(BehaviorAppListVO listVO, User user);

    /**
     * 获取行为应用数据详情
     *
     * @param getVO 传入参数
     * @return 返回数据
     */
    BehaviorAppGetDTO getBehaviorApp(BehaviorAppGetVO getVO);

    /**
     * 新增行为应用
     *
     * @param saveVO 传入参数
     * @param userId 用户ID
     */
    void saveBehaviorApp(BehaviorAppSaveVO saveVO, Integer userId);

    /**
     * 编辑行为应用
     *
     * @param updateVO 传入参数
     * @param userId   用户ID
     */
    void updateBehaviorApp(BehaviorAppUpdateVO updateVO, Integer userId);

    /**
     * 修改行为应用状态
     *
     * @param getVO  传入参数
     * @param userId 用户ID
     */
    void changeBehaviorAppStatus(BehaviorAppStatusGetVO getVO, Integer userId);

    /**
     * 删除行为应用
     *
     * @param getVO  传入参数
     * @param userId 用户ID
     */
    void deleteBehaviorApp(BehaviorAppGetVO getVO, Integer userId);
}
