package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.vo.market.campaign.*;
import com.overseas.service.market.dto.campaign.CampaignMasterSelectDTO;
import com.overseas.service.market.entity.Campaign;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.service.market.entity.User;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface CampaignService extends IService<Campaign> {

    /**
     * 获取活动下拉
     *
     * @param getVO 传入参数
     * @return 返回数据
     */
    List<SelectDTO> selectCampaign(CampaignSelectGetVO getVO, List<Integer> permissionMasterIds, User user);

    /**
     * 获取活动下啦
     *
     * @param getVO               条件
     * @param permissionMasterIds 权限
     * @param user                用户
     * @return 返回数据
     */
    List<SelectDTO> selectCopyCampaign(CampaignSelectGetVO getVO, List<Integer> permissionMasterIds, User user);

    /**
     * 活动下啦
     *
     * @param getVO 条件
     * @return 返回数据
     */
    List<SelectDTO> selectCampaign(CampaignSelectGetVO getVO, User user);

    /**
     * 获取指定账号下在投的活动
     *
     * @param masterIds 广告主ID集合
     * @return 结果集
     */
    List<SelectDTO> selectPutCampaign(List<Long> masterIds);

    /**
     * 获取活动和账户下拉
     *
     * @param selectVO            参数
     * @param permissionMasterIds 有权限账户
     * @param user                用户
     * @return 返回数据
     */
    PageUtils<CampaignMasterSelectDTO> selectPageCampaignAndMaster(CampaignMasterSelectVO selectVO,
                                                                   List<Integer> permissionMasterIds, User user);

    List<SelectDTO> selectCampaign(List<Integer> masterIds);

    /**
     * 获取账户下有花费得活动
     *
     * @param getVO 传入参数
     * @param user  用户
     * @return 返回数据
     */
    List<SelectDTO> selectCampaignHasCost(CampaignSelectGetVO getVO, User user);

    boolean checkRepeatName(String name, Integer masterId, Long campaignId);

    Campaign getCampaign(Long id, Integer masterId);

    /**
     * 活动信息
     *
     * @param id       活动ID
     * @param masterId 账户ID
     * @return 返回数据
     */
    Campaign getCopyCampaign(Long id, Integer masterId);

    /**
     * 保存活动数据
     *
     * @param saveVO 传入参数
     * @param user   用户
     */
    Long saveCampaign(CampaignSaveVO saveVO, User user);

    /**
     * 更新活动数据
     *
     * @param updateVO 传入参数
     * @param userId   用户ID
     */
    Long updateCampaign(CampaignUpdateVO updateVO, Integer userId);

    /**
     * 批量更新活动预算信息
     *
     * @param batchUpdateVO 更新参数对象
     * @return 更新结果
     */
    boolean batchUpdateCampaign(CampaignBatchUpdateVO batchUpdateVO);

    /**
     * 通过计划ID 更新活动信息
     *
     * @param campaignId 活动ID
     */
    Object syncSheinCampaign(Long campaignId, User user);

    /**
     * 更新次日预算
     *
     * @param date 日期
     */
    void updateCampaignBudgetByNextDay(Date date);
}
