package com.overseas.service.market.service;

import com.overseas.common.dto.market.campaign.CampaignUpdateRecordListDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.campaign.CampaignUpdateRecordListVO;
import com.overseas.service.market.entity.CampaignUpdateRecord;
import com.overseas.service.market.entity.User;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CampaignUpdateRecordService {

    /**
     * 保存数据
     *
     * @param campaignUpdateRecords 数据
     * @param userId                用户
     * @return 返回数据
     */
    List<CampaignUpdateRecord> saveUpdateRecord(List<CampaignUpdateRecord> campaignUpdateRecords, Integer userId);

    /**
     * 筛选数据
     *
     * @param listVO 条件
     * @param user  用户
     * @return 返回数据
     */
    PageUtils<CampaignUpdateRecordListDTO> listUpdateRecord(CampaignUpdateRecordListVO listVO, User user);
}
