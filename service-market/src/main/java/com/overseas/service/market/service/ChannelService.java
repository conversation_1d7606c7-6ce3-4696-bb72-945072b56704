package com.overseas.service.market.service;

import com.overseas.common.dto.SelectDTO3;
import com.overseas.common.vo.market.channel.ChannelSelectVO;

import java.util.List;

/**
 * <AUTHOR>
 **/
public interface ChannelService {


    /**
     * 下拉数据
     *
     * @param selectVO 条件
     * @return 返回数据
     */
    List<SelectDTO3> channelSelect(ChannelSelectVO selectVO);

    /**
     * 下拉数据
     *
     * @param selectVO 条件
     * @return 返回数据
     */
    List<SelectDTO3> channelSelectByKey(ChannelSelectVO selectVO);

}
