package com.overseas.service.market.service;

import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.copyWriting.CopyWritingCascaderDTO;
import com.overseas.common.dto.market.copyWriting.CopyWritingDTO;
import com.overseas.common.dto.market.copyWriting.CopyWritingListDTO;
import com.overseas.common.dto.market.copyWriting.CopyWritingUploadDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.copyWriting.*;
import com.overseas.service.market.entity.User;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface CopyWritingService {

    void saveCopyWriting(CopyWritingSaveVO saveVO, Integer userId);

    void batchSaveCopyWriting(CopyWritingBatchSaveVO saveVO, Integer userId);

    void batchCreativeSaveCopyWriting(CopyWritingBatchSaveVO saveVO, Integer userId);

    void updateCopyWriting(CopyWritingUpdateVO updateVO, Integer userId);

    void deleteCopyWriting(CopyWritingGetVO getVO, Integer userId);

    CopyWritingDTO getCopyWriting(CopyWritingGetVO getVO);

    PageUtils<CopyWritingListDTO> listCopyWriting(CopyWritingListVO listVO, User user);

    /**
     * 下载数据
     *
     * @param listVO              条件
     * @param user                用户
     * @param httpServletResponse response
     * @throws IOException 异常
     */
    void exportListCopyWriting(CopyWritingListVO listVO, User user, HttpServletResponse httpServletResponse) throws IOException;

    List<CopyWritingCascaderDTO> listCopyWritingCascader(CopyWritingCascaderGetVO getVO);

    List<SelectDTO> selectCountry(CopyWritingCountrySelectGetVO getVO);

    List<String> checkCopyWriting(CopyWritingCheckGetVO getVO);

    void shareCopyWriting(CopyWritingShareSaveVO saveVO, Integer userId);


    void changeStatus(CopyWritingStatusVO statusVO, Integer userId);


    /**
     * 上传文案
     *
     * @param saveVO 条件
     * @param userId 用户ID
     * @return 返回数据
     */
    List<CopyWritingUploadDTO> uploadCopyWriting(CopyWritingUploadSaveVO saveVO, Integer userId);
}
