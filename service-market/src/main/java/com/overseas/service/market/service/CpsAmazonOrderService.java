package com.overseas.service.market.service;


import com.overseas.common.vo.market.cps.CpsSaveVO;

/**
 * <AUTHOR>
 **/
public interface CpsAmazonOrderService {


    /**
     * 保存订单信息
     *
     * @param saveVO 数据
     * @param userId 用户
     */
    void saveOrderInfo(CpsSaveVO saveVO, Integer userId);

    /**
     * 匹配订单和佣金
     */
    void mateOrderAndEarning();

    /**
     * 转化订单数据
     */
    void transformCpsOrder();
}
