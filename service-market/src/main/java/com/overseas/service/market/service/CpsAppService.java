package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.service.market.dto.cps.app.CpsAppListDTO;
import com.overseas.service.market.entity.ae.AeApp;
import com.overseas.service.market.vo.cps.app.*;

import java.util.List;

public interface CpsAppService extends IService<AeApp> {
    
    void saveCpsApp(CpsAppSaveVO saveVO, Integer loginUserId);

    void updateCpsApp(CpsAppUpdateVO updateVO, Integer loginUserid);

    PageUtils<CpsAppListDTO> listCpsApp(CpsAppListVO listVO);

    void deleteCspApp(CpsAppDeleteVO deleteVO, Integer loginUserId);

    List<SelectDTO> selectCpsApp(CpsAppSelectVO selectVO);
}
