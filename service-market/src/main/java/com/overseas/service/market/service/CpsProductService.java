package com.overseas.service.market.service;

import com.overseas.common.dto.SelectDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.service.market.dto.cps.CpsProductAnalysisChartDTO;
import com.overseas.service.market.dto.cps.CpsProductAnalysisListDTO;
import com.overseas.service.market.dto.cps.CpsProductListDTO;
import com.overseas.service.market.entity.cps.CpsProduct;
import com.overseas.service.market.vo.cps.*;
import com.overseas.service.market.vo.cps.lazada.CpsProductCategorySaveVO;
import com.overseas.service.market.vo.cps.lazada.CpsProductListVO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface CpsProductService {
    PageUtils<CpsProductListDTO> listCpsProduct(com.overseas.service.market.vo.cps.CpsProductListVO listVO);

    List<SelectDTO> selectCpsProductCategory(CpsProductCategorySelectVO selectVO);

    void downloadMaterial(CpsMaterialDownloadVO downloadVO, HttpServletRequest request, HttpServletResponse response);

    List<CpsProductAnalysisChartDTO> chartProductAnalysis(CpsProductAnalysisChartVO analysisListVO);

    PageUtils<CpsProductAnalysisListDTO> listProductAnalysis(CpsProductAnalysisListVO analysisListVO);

    void exportProductAnalysis(CpsProductAnalysisExportVO analysisListVO, HttpServletResponse response) throws IOException;

    void updateCpsOrder();

    /**
     * @param exportVO 参数
     * @throws IOException 异常
     */
    void export(CpsProductExportVO exportVO, HttpServletResponse response) throws IOException;

    /**
     * 根据商品ID获取标签
     *
     * @param labelVO 条件
     * @return 返回标签ID
     */
    List<Long> labelByProductIds(CpsProductLabelVO labelVO);

    CpsProduct listLazadaProductByCountry(CpsProductListVO listVO);

    void saveLazadaCategory(CpsProductCategorySaveVO saveVO);
}
