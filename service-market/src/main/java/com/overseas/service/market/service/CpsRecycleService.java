package com.overseas.service.market.service;

import com.overseas.common.dto.chart.MultiIndexChartDTO;
import com.overseas.common.vo.market.cps.recycle.CpsRecycleChartVO;
import com.overseas.service.market.dto.cps.recycle.CpsRecycleBarDTO;
import com.overseas.common.vo.market.cps.recycle.CpsRecycleListVO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @description：回传数据
 * @author：tkwang2
 * @date：2025/2/7
 */
public interface CpsRecycleService {

    /**
     * total bar
     *
     * @param listVO 条件
     * @return 返回数据
     */
    CpsRecycleBarDTO bar(CpsRecycleListVO listVO);

    /**
     * chart
     *
     * @param chartVO 条件
     * @return 返回数据
     */
    MultiIndexChartDTO chart(CpsRecycleChartVO chartVO);

    /**
     * chart 下载
     *
     * @param chartVO  条件
     * @param response 返回
     * @return 返回数据
     * @throws IOException 异常
     */
    void download(CpsRecycleChartVO chartVO, HttpServletResponse response) throws IOException;

}
