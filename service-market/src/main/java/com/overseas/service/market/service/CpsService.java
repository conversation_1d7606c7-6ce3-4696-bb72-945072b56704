package com.overseas.service.market.service;

import com.overseas.common.dto.SelectDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.cps.*;
import com.overseas.service.market.entity.User;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface CpsService {

    /**
     * 保存 cps 数据
     *
     * @param saveVO 条件
     * @param userId 返回数据
     */
    void saveCps(CpsSaveVO saveVO, Integer userId);

    /**
     * 保存亚马逊数据
     *
     * @param saveVO 条件
     * @param userId 用户
     */
    void saveCpsAmazon(CpsSaveVO saveVO, Integer userId);

    /**
     * 保存
     *
     * @param saveVO 条件
     * @param userId 用户
     */
    void saveCpsLazada(CpsSaveVO saveVO, Integer userId);

    /**
     * 列表数据展示
     *
     * @param listVO 条件
     * @return 返回数据
     */
    PageUtils<?> listCps(CpsListVO listVO);

    /**
     * 列表下载
     *
     * @param listExportVO 条件
     * @param response     resp
     * @throws IOException 异常
     */
    void exportCps(CpsListExportVO listExportVO, HttpServletResponse response) throws IOException;

    /**
     * 同步亚马逊数据
     */
    void syncDataToPlanHour();

    /**
     * ae channel place
     *
     * @return 返回数据
     */
    List<SelectDTO> aeChannelPlace();

    /**
     * ae 渠道 cps list
     *
     * @param listVO list 条件
     * @param user   用户
     * @return 返回数据
     */
    PageUtils<?> aeChannelCps(CpsAeChannelListVO listVO, User user);

    /**
     * 下载ae渠道数据
     *
     * @param listVO   条件
     * @param user     用户
     * @param response 返回
     * @throws IOException 异常
     */
    void exportAeChannelCps(CpsAeChannelListVO listVO, User user, HttpServletResponse response) throws IOException;

    /**
     * 下载ae渠道数据2
     *
     * @param listVO   条件
     * @param user     用户
     * @param response 返回
     * @throws IOException 异常
     */
    void exportAeChannelCps2(CpsAeChannelListVO listVO, User user, HttpServletResponse response) throws IOException;

    /**
     * 导入广告
     *
     * @param importAdVO 文件路径
     * @param userId     用户
     */
    void lazadaImportAd(CpsLazadaImportAdVO importAdVO, Integer userId);

    /**
     * 导出小米数据
     *
     * @param day 日期
     * @throws IOException 异常
     */
    void aeXiaoMiExport(String day) throws IOException;

    /**
     * 导出AE每日数据，并发送邮件
     *
     * @param byDayVO  参数
     * @param response 结果
     * @throws IOException 异常
     */
    void exportAeByDayAndMail(CpsExportByDayVO byDayVO, HttpServletResponse response) throws IOException;

    /**
     * 导出AE每日数据
     *
     * @param startDate      开始日期
     * @param endDate        结束日期
     * @param channelPlaceId 渠道标识
     * @param response       请求结果
     * @throws IOException 异常
     */
    void exportAeByDay(String startDate, String endDate, Long channelPlaceId, HttpServletResponse response) throws IOException;
}
