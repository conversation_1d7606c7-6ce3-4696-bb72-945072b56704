package com.overseas.service.market.service;

import com.overseas.common.dto.SelectDTO;
import com.overseas.common.vo.market.creativeLabel.CreativeLabelSelectGetVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface CreativeLabelService {

    List<SelectDTO> selectCreativeLabel(CreativeLabelSelectGetVO getVO);

    Map<String, List<SelectDTO>> selectCreativeLabelMap(CreativeLabelSelectGetVO getVO);
}
