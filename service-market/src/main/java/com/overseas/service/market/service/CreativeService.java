package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.creative.CreativeUpdateAssetWaitCountVO;
import com.overseas.service.market.dto.creative.CreativeGetDTO;
import com.overseas.service.market.entity.Creative;
import com.overseas.service.market.entity.User;
import com.overseas.service.market.vo.creative.AutoAssetUpdateBatchVO;
import com.overseas.service.market.vo.creative.AutoAssetUpdateListVO;
import com.overseas.service.market.vo.creative.CreativeGetVO;
import com.overseas.service.market.vo.creative.CreativeSaveVO;
import com.overseas.common.dto.market.creativeUnit.CreativeUnitAllDataListDTO;
import com.overseas.common.vo.market.creative.unit.CreativeUnitBatchSaveVO;
import com.overseas.common.vo.report.ReportListVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CreativeService extends IService<Creative> {

    /**
     * 保存创意
     */
    Long saveCreative(CreativeSaveVO creativeSaveVO, Integer userId);

    void saveCreativeUnit(CreativeSaveVO creativeSaveVO, Integer userId);

    void batchSaveCreativeUnit(CreativeUnitBatchSaveVO saveVO, Integer userId);

    /**
     * 获取单个创意的详细信息
     */
    CreativeGetDTO getCreativeDetailByPlanId(Long planId, Integer masterId);

    List<CreativeUnitAllDataListDTO> listCreativeUnitAllData(ReportListVO listVO, List<Integer> masterIds);

    void deleteByPlanId(Long planId, Integer operateUserId);


    PageUtils<?> listAutoAssetUpdate(AutoAssetUpdateListVO listVO, User user);

    void autoAssetUpdateBatch(AutoAssetUpdateBatchVO batchVO, User user);

    Integer autoAssetWaitCount(CreativeGetVO creativeGetVO);

    void updateAssetWaitCount(CreativeUpdateAssetWaitCountVO countVO);

    void checkPlanChannelPool(Long planId, String channelPoolDirect);
}
