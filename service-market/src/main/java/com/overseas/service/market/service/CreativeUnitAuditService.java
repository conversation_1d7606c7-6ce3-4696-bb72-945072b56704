package com.overseas.service.market.service;

import com.overseas.service.market.entity.AuditUnitStatus;
import com.overseas.service.market.entity.Plan;

/**
 * <AUTHOR>
 **/
public interface CreativeUnitAuditService {


    /**
     * 上传
     *
     * @param auditUnit 审核单元状态
     * @param adxId     adx id
     * @param plan      计划信息
     * @return 返回数据
     */
    void upload(AuditUnitStatus auditUnit, Integer adxId, Plan plan);

    /**
     * 审核
     *
     * @param auditUnit 审核信息
     */
    void info(AuditUnitStatus auditUnit);


}
