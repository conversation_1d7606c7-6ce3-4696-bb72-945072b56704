package com.overseas.service.market.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.dto.market.creativeUnit.CreativeUnitMaterialSaveDTO;
import com.overseas.common.dto.market.creativeUnit.CreativeUnitPutStatusDTO;
import com.overseas.common.vo.market.creative.CreativeUnitPutStatusVO;
import com.overseas.common.vo.market.creative.unit.CreativeUnitBatchUpdateVO;
import com.overseas.common.vo.market.creative.unit.CreativeUnitFillSizeVO;
import com.overseas.service.market.entity.CreativeUnit;
import com.overseas.service.market.entity.User;
import com.overseas.service.market.vo.creative.CreativeSaveVO;
import com.overseas.service.market.vo.creative.CreativeUnitSelectGetVO;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.creativeUnit.CreativeUnitAssetDTO;
import com.overseas.common.dto.market.creativeUnit.CreativeUnitListDTO;
import com.overseas.common.dto.market.market.MarketBatchListDTO;
import com.overseas.common.dto.market.material.MaterialDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.creative.CreativeUnitListVO;
import com.overseas.common.vo.market.creative.unit.CreativeUnitGetVO;
import com.overseas.common.vo.market.market.MarketBatchListVO;

import java.util.List;
import java.util.Map;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-25 10:32
 */
public interface CreativeUnitService extends IService<CreativeUnit> {

    void batchSaveCreativeUnit(CreativeSaveVO creativeSaveVO, Map<String, CreativeUnitMaterialSaveDTO> materialMap, Integer userId);

    CreativeUnit getCreativeUnit(Long id, Integer masterId);

    void deleteByPlanId(Long planId, Integer operateUserId);

    List<SelectDTO> getCreativeUnitSelect(CreativeUnitSelectGetVO getVO);

    List<CreativeUnitAssetDTO> listCreativeUnitAsset(CreativeUnitListVO listVO);

    IPage<MarketBatchListDTO> listCreativeUnitAsset(MarketBatchListVO listVO);

    Map<Long, Map<Long, List<CreativeUnitAssetDTO>>> getPlanUnitAssetMap(List<CreativeUnitAssetDTO> unitAssetDTOList);

    MaterialDTO getCreativeUnitMaterial(List<CreativeUnitAssetDTO> unitAssets);

    PageUtils<CreativeUnitListDTO> listCreativeUnitByAssetId(CreativeUnitListVO listVO, User user);

    String getTemplateInfoById(CreativeUnitGetVO getVO);

    void batchUpdateCreativeUnit(CreativeUnitBatchUpdateVO updateVO, Integer userId);

    /***
     * 获取创意单元状态
     * @param unitPutStatusVO 条件
     * @return 返回数据
     */
    List<CreativeUnitPutStatusDTO> creativeUnitPutStatus(CreativeUnitPutStatusVO unitPutStatusVO);

    /**
     * 获取当前尺寸的创意单元
     *
     * @param fillSizeVO 条件
     * @return 返回创意单元ID
     */
    List<Long> fillSizeToCreativeUnitId(CreativeUnitFillSizeVO fillSizeVO);
}
