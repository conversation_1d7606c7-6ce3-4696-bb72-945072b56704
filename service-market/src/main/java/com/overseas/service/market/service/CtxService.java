package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.service.market.entity.Ctx;
import com.overseas.common.dto.CascaderDTO;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.ctx.CtxGetDTO;
import com.overseas.common.dto.market.ctx.CtxListDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.ctx.CtxGetVO;
import com.overseas.common.vo.market.ctx.CtxListVO;
import com.overseas.common.vo.market.ctx.CtxSaveVO;
import com.overseas.common.vo.market.ctx.CtxUpdateVO;

import java.util.List;

public interface CtxService extends IService<Ctx> {
    /**
     * 行动按钮下拉
     *
     * @return 结果集
     */
    List<SelectDTO> selectCtx();

    void saveCtx(CtxSaveVO saveVO, Integer userId);

    void updateCtx(CtxUpdateVO updateVO, Integer userId);

    void deleteCtx(CtxGetVO getVO, Integer userId);

    CtxGetDTO getCtx(CtxGetVO getVO);

    PageUtils<CtxListDTO> listCtx(CtxListVO listVO);

    List<CascaderDTO> listCtxCascader();

    List<SelectDTO> selectCountry();
}
