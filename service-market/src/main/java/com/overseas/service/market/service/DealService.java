package com.overseas.service.market.service;

import com.overseas.common.dto.market.deal.*;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.deal.*;
import com.overseas.service.market.entity.Deal;

import java.util.List;

public interface DealService {
    /**
     * 保存dealId
     *
     * @param dealSaveVO  要保存的对象信息
     * @param loginUserId 登录用户id
     * @return 保存后的deal id
     */
    Deal saveDeal(DealSaveVO dealSaveVO, Integer loginUserId);

    /**
     * 获取deal id详情接口
     *
     * @param dealGetVO 获取详情参数对象
     * @return deal id详情数据
     */
    DealGetDTO getDeal(DealGetVO dealGetVO);

    /**
     * 编辑dealId
     *
     * @param dealUpdateVO 编辑参数
     * @param loginUserId  登录用户id
     * @return 编辑结果
     */
    Integer updateDeal(DealUpdateVO dealUpdateVO, Integer loginUserId);

    /**
     * 删除deal id
     *
     * @param dealGetVO   删除参数
     * @param loginUserId 操作用户id
     * @return 删除结果
     */
    Integer deleteDeal(DealGetVO dealGetVO, Integer loginUserId);

    /**
     * 查询deal分页数据
     *
     * @param dealListVO 查询参数
     * @return 分页数据
     */
    PageUtils<?> listDeal(DealListVO dealListVO);

    /**
     * 开关deal
     *
     * @param dealSwitchVO 操作参数
     * @param loginUserId  操作用户id
     * @return 开关结果
     */
    Integer switchDeal(DealSwitchVO dealSwitchVO, Integer loginUserId);

    /**
     * 获取deal下拉数据
     *
     * @param dealSelectVO 查询参数
     * @return 结果集
     */
    List<DealSelectDTO> selectDeal(DealSelectVO dealSelectVO);
}
