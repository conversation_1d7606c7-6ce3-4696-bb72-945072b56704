package com.overseas.service.market.service;

import com.overseas.common.utils.PageUtils;
import com.overseas.service.market.entity.Ep;
import com.overseas.service.market.vo.ep.*;

import java.util.List;

/**
 * <AUTHOR>
 **/
public interface EpService {

    /**
     * ep 列表
     *
     * @param listVO 列表
     * @return 返回数据
     */
    PageUtils<?> list(EpListVO listVO);

    /**
     * ep 保存
     *
     * @param saveVO     保存数据
     * @param operateUid 操作用户
     */
    void save(EpSaveVO saveVO, Integer operateUid);

    /**
     * 获取 ep 信息
     *
     * @param getVO 条件
     * @return 返回数据
     */
    Ep get(EpGetVO getVO);

    /**
     * ep update
     *
     * @param updateVO   编辑数据
     * @param operateUid 操作用户
     */
    void update(EpUpdateVO updateVO, Integer operateUid);

    /**
     * 绑定模板
     *
     * @param bindVO     条件
     * @param operateUid 操作用户
     */
    void bind(EpBindVO bindVO, Integer operateUid);

    /**
     * 解绑模板
     *
     * @param unbindVO   条件
     * @param operateUid 操作用户
     */
    void unbind(EpUnbindVO unbindVO, Integer operateUid);

    /**
     * ep 删除
     *
     * @param deleteVO   删除数据
     * @param operateUid 操作用户
     */
    void del(EpDeleteVO deleteVO, Integer operateUid);

    /**
     * 检查 ep 模板是否在白名单中
     *
     * @param epIds       ep Ids
     * @param templateIds 模板 Ids
     */
    void checkEpTemplate(List<Long> epIds, List<Long> templateIds);

    void updateEpDirectByAdx(Integer adxId);
}
