package com.overseas.service.market.service;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 **/
public interface LazadaService {


    /**
     * 拉取分类数据
     */
    void pullCategories();

    /**
     * 拉取数据
     */
    void pullProducts();

    /**
     * 根据订单拉取商品数据
     */
    void pullProductsByCpsOrder();

    /**
     * 数据拉取
     *
     * @param productIds 商品ID
     */
    void pullProductByIds(List<String> productIds);

    /**
     * 回传数据拉取
     *
     * @param day 天数据
     */
    void listConversionReport(String day);

    /**
     * 优化数据
     *
     * @param days 天数据
     */
    void exportConversion(List<String> days);

    /**
     * 拉取数据
     */
    void generateProductCpsUrl();


    /**
     * 根据广告导出数据
     *
     * @param day 天数据
     * @return 导出结果
     * @throws IOException 异常
     */
    String exportByAd(String day) throws IOException;

    /**
     * 根据天导出数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 导出结果
     * @throws IOException 异常
     */
    String exportByDay(String startDate, String endDate) throws IOException;

}
