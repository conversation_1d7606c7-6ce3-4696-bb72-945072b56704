package com.overseas.service.market.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.service.market.dto.market.MasterPageFirstDTO;
import com.overseas.service.market.dto.plan.WebPlanStatusContainerDTO;
import com.overseas.common.vo.market.market.RecordBatchDeleteVO;
import com.overseas.common.vo.market.market.RecordBatchSwitchVO;
import com.overseas.common.dto.report.ReportListDTO;
import com.overseas.common.vo.market.master.MasterPageFirstVO;

import java.util.List;
import java.util.Map;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-20 17:41
 */
public interface MarketPageListService {

    /**
     * 获取分页数据
     */
    IPage<MasterPageFirstDTO> pageMarket(MasterPageFirstVO listVO, List<Integer> permissionMasterIds, List<Long> ids,
                                         WebPlanStatusContainerDTO webPlanStatusContainerDTO);

    /**
     * 获取符合条件的全量的ID数据
     */
    List<Long> listAllIdMarket(MasterPageFirstVO listVO, List<Integer> permissionMasterIds,
                               WebPlanStatusContainerDTO webPlanStatusContainerDTO);

    /**
     * 获取符合条件的全量账户ID数据
     *
     * @param listVO                    筛选条件
     * @param permissionMasterIds       权限账户
     * @param webPlanStatusContainerDTO 计划状态列表
     * @return 返回数据
     */
    default List<Long> listAllMasterIdMarket(MasterPageFirstVO listVO, List<Integer> permissionMasterIds,
                                             WebPlanStatusContainerDTO webPlanStatusContainerDTO) {
        return List.of();
    }

    Map<Long, ReportListDTO> getReportMapMarket(List<ReportListDTO> reportList);

    void formatListMarket(List<MasterPageFirstDTO> list, WebPlanStatusContainerDTO webPlanStatusContainerDTO, Long masterId);

    List<Long> listReportIdMarket(List<ReportListDTO> reportList);

    Long getValueById(ReportListDTO reportDTO);

    boolean batchDelete(RecordBatchDeleteVO batchDeleteVO);

    boolean batchSwitch(RecordBatchSwitchVO batchSwitchVO);
}
