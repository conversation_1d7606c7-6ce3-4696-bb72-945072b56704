package com.overseas.service.market.service;

import com.overseas.common.vo.market.master.MasterPageFirstExportVO;
import com.overseas.service.market.dto.market.MasterPageFirstDTO;
import com.overseas.service.market.entity.User;
import com.overseas.common.dto.market.market.MarketBatchListDTO;
import com.overseas.common.dto.market.market.MarketDimensionInfoDTO;
import com.overseas.common.dto.sys.project.ProjectByMasterDTO;
import com.overseas.common.vo.market.market.MarketBatchListVO;
import com.overseas.common.vo.market.market.MarketBudgetUpdateVO;
import com.overseas.common.vo.market.market.MarketDimensionInfoVO;
import com.overseas.common.vo.market.market.RecordBatchDeleteVO;
import com.overseas.common.vo.market.market.RecordBatchSwitchVO;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.master.MasterPageFirstVO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-20 17:14
 */
public interface MarketService {

    PageUtils<MasterPageFirstDTO> page(MasterPageFirstVO listVO, List<Integer> permissionMasterIds, User loginUser);

    /**
     * 下载数据
     *
     * @param listVO              条件
     * @param permissionMasterIds 账户权限
     * @param loginUser           用户
     * @param response            response
     * @throws IOException 异常
     */
    void exportPage(MasterPageFirstExportVO listVO, List<Integer> permissionMasterIds,
                    User loginUser, HttpServletResponse response) throws IOException;

    /**
     * 获取 分页列表的标识
     *
     * @param listVO              筛选条件
     * @param permissionMasterIds 有权限账户ID
     * @param loginUser           登陆用户
     * @return 返回数据
     */
    ProjectByMasterDTO pageIdentify(MasterPageFirstVO listVO, List<Integer> permissionMasterIds, User loginUser);

    boolean batchDeleteRecord(RecordBatchDeleteVO batchDeleteVO);

    boolean batchSwitchRecord(RecordBatchSwitchVO batchSwitchVO);

    void updateMarketBudgetTest(MarketBudgetUpdateVO updateVO);

    PageUtils<MarketBatchListDTO> listMarketBatch(MarketBatchListVO listVO, User user);

    List<MarketDimensionInfoDTO> dimensionInfo(MarketDimensionInfoVO dimensionInfoVO);

    /**
     * 根据 shein 处理
     *
     * @param id       id
     * @param user     用户
     * @param listType 返回数据
     */
    void doAfterNoticeByShein(Long id, User user, String listType);

}
