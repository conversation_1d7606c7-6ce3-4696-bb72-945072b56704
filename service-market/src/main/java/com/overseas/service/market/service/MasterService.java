package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.dto.CascaderDTO;
import com.overseas.common.dto.TreeNodeDTO;
import com.overseas.common.dto.market.master.*;
import com.overseas.common.vo.market.master.*;
import com.overseas.service.market.entity.Master;
import com.overseas.service.market.entity.User;
import com.overseas.service.market.vo.master.MasterListVO;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.market.MarketMasterGetVO;
import com.overseas.service.market.vo.master.MasterRatioListVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface MasterService extends IService<Master> {

    /**
     * 根据广告主ID获取广告主下拉数据
     *
     * @param permissionMasterIds 广告主ID
     * @return 返回数据
     */
    List<SelectDTO> selectMaster(List<Integer> permissionMasterIds);

    /**
     * 根据广告主ID获取广告主下拉数据
     *
     * @param permissionMasterIds 广告主ID
     * @return 返回数据
     */
    List<SelectDTO> selectMasterBySort(MasterSelectVO selectVO, List<Integer> permissionMasterIds);


    /**
     * 根据广告主ID获取广告主下拉数据
     *
     * @param selectVO            条件
     * @param permissionMasterIds 广告主ID
     * @return 返回数据
     */
    List<SelectDTO> selectMasterParams(MasterSelectVO selectVO, List<Integer> permissionMasterIds);

    /**
     * 根据信息获取广告主下拉
     *
     * @param byInfoVO 数据
     * @return 返回数据
     */
    List<SelectDTO> selectMasterByInfo(MasterSelectByInfoVO byInfoVO);

    /**
     * 获取所有广告主下拉
     *
     * @return 所有广告主数据
     */
    List<SelectDTO> getAllMasterSelect();

    /**
     * 获取首页列表
     *
     * @param masterListVO 列表数据
     * @return 返回数据
     */
    PageUtils<MasterListDTO> pageMaster(MasterListVO masterListVO, List<Integer> permissionMasterIds, User loginUser);


    /**
     * 获取系数数据
     *
     * @param listVO 系数列表
     * @return 返回数据
     */
    List<MasterRatioDTO> masterRatios(MasterRatioListVO listVO);

    /**
     * 保存广告主
     *
     * @param masterSaveVO 保存对象
     * @param loginUserId  登录用户id
     */
    Map<String, Object> saveMaster(MasterSaveVO masterSaveVO, Integer loginUserId);

    /**
     * 更新账号
     *
     * @param saveVO 保存数据
     * @param userId 用户ID
     */
    void updateMaster(MasterSaveVO saveVO, Integer userId);

    /**
     * 获取广告主信息
     *
     * @param masterGetVO 查询参数对象
     * @param loginUserId 登录用户ID
     * @return 广告主信息
     */
    MasterGetDTO getMaster(MasterGetVO masterGetVO, Integer loginUserId);

    /**
     * 绑定/解绑账号
     *
     * @param masterBindVO 广告主绑定信息
     * @param loginUser    登录用户信息
     */
    void bind(MasterBindVO masterBindVO, User loginUser);

    /**
     * 获取绑定信息
     *
     * @param masterBindInfoGetVO 绑定信息查询参数
     * @return 绑定结果
     */
    MasterBindInfoGetDTO getBindInfo(MasterBindInfoGetVO masterBindInfoGetVO);


    /**
     * 更新投放账户日预算
     *
     * @param masterBudgetUpdateVO 更新参数
     * @param loginUserId          登录用户ID
     */
    void updateBudgetDay(MasterBudgetUpdateVO masterBudgetUpdateVO, Integer loginUserId);


    /**
     * 请求处理 花费系数修改
     *
     * @param costRatioVO 系数参数
     */
    void updateReportCostRoute(MasterCostRatioVO costRatioVO);


    /**
     * 花费系数修改
     *
     * @param costRatioVO 系数参数
     */
    void updateReportCost(MasterCostRatioVO costRatioVO);

    /**
     * 根据指定广告主，获取该广告主所属管理账号下所有广告主数据下拉
     *
     * @param getVO 传入参数
     * @return 返回数据
     */
    List<SelectDTO> selectByMasterId(MasterGetVO getVO);

    /**
     * 批量编辑账户信息
     *
     * @param updateVO 传入参数
     * @param userId   用户ID
     */
    void batchUpdateMaster(MasterBatchUpdateVO updateVO, Integer userId);

    /**
     * 获取广告主关联的
     *
     * @param getVO 获取参数
     * @return 返回数据
     */
    List<SelectDTO> getMasterActionSelect(MarketMasterGetVO getVO);

    List<MasterTimeZoneDTO> getMasterTimeZone(MasterTimeZoneGetVO getVO);

    List<MasterProjectDTO> listMasterProject(MasterProjectGetVO getVO);

    List<CascaderDTO> listMasterCascader(MasterCascaderGetVO getVO);

    List<TreeNodeDTO> listMasterTree(MasterCascaderGetVO getVO);
}
