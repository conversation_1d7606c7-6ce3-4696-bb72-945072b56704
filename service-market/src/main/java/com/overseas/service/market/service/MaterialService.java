package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.dto.market.creativeUnit.CreativeUnitMaterialSaveDTO;
import com.overseas.common.vo.market.material.*;
import com.overseas.service.market.entity.Material;

import java.util.List;
import java.util.Map;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-25 11:16
 */
public interface MaterialService extends IService<Material> {
    /**
     * 保存素材组
     *
     * @param saveVO 传入参数
     * @param userId 用户ID
     * @return 素材ID
     */
    Map<String, CreativeUnitMaterialSaveDTO> saveMaterial(MaterialSaveVO saveVO, Integer userId);

    /**
     * 获取素材组内素材平铺列表
     *
     * @param getVO 素材组
     * @return 返回数据
     */
    List<MaterialAssetVO> getMaterialDetail(MaterialGetVO getVO);

    /**
     * 获取创意下关联的素材组
     *
     * @param listVO 传入参数
     * @return 返回数据
     */
    List<MaterialVO> getCreativeMaterialList(MaterialListVO listVO);

    /**
     * 根据尺寸获取创意
     *
     * @param byIdVO 条件
     * @return 返回数据
     */
    MaterialVO getMaterialByUnitId(MaterialByIdVO byIdVO);

    /**
     * 根据创意单元ID获取list
     *
     * @param unitIds  创意单元ID
     * @param masterId 账户ID
     * @return 返回数据
     */
    List<MaterialVO> getMaterialByUnitIds(List<Long> unitIds, Long masterId);

    /**
     * 根据素材组ID获取创意信息
     *
     * @param materialIds 素材组ID
     * @return 返回数据
     */
    Map<Long, List<MaterialAssetVO>> getMaterialMap(List<Long> materialIds, Long masterId);

    /**
     * 重新生成hash
     */
    void hashRefresh();
}
