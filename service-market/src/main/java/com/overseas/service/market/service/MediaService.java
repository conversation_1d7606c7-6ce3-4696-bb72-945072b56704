package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.service.market.dto.media.MediaDirectDTO;
import com.overseas.service.market.entity.Media;
import com.overseas.service.market.vo.plan.DirectResourceVO;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.media.MediaSelectGetVO;

import java.util.List;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-23 20:05
 */
public interface MediaService extends IService<Media> {
    /**
     * 在计划定向下获取媒体列表
     *
     * @param directResourceVO 定向信息
     * @return 定向数据
     */
    PageUtils<MediaDirectDTO> pageMediaDirect(DirectResourceVO directResourceVO);

    /**
     * 获取媒体下拉数据
     *
     * @param getVO 传入参数
     * @return 返回数据
     */
    List<SelectDTO> getMediaSelect(MediaSelectGetVO getVO);
}
