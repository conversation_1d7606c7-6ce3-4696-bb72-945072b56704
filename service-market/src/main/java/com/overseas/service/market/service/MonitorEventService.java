package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.vo.market.monitor.monitorEvent.*;
import com.overseas.service.market.entity.MonitorEvent;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.common.SaveDTO;
import com.overseas.common.dto.market.monitor.monitorEvent.MonitorEventGetDTO;
import com.overseas.common.dto.market.monitor.monitorEvent.MonitorEventListDTO;
import com.overseas.common.utils.PageUtils;

import java.util.List;

public interface MonitorEventService extends IService<MonitorEvent> {

    /**
     * 获取监测事件下拉
     *
     * @param getVO 传入参数
     * @return 返回数据
     */
    List<SelectDTO> getEventSelect(MonitorEventSelectGetVO getVO);

    /**
     * 获取监测事件列表数据
     *
     * @param listVO 传入参数
     * @return 返回数据
     */
    PageUtils<MonitorEventListDTO> getEventPage(MonitorEventListVO listVO);

    /**
     * 新增监测事件
     *
     * @param saveVO 传入参数
     * @param userId 用户ID
     * @return 返回数据
     */
    SaveDTO saveEvent(MonitorEventSaveVO saveVO, Integer userId);

    /**
     * 删除监测事件
     *
     * @param getVO  传入参数
     * @param userId 用户ID
     */
    void deleteEvent(MonitorEventGetVO getVO, Integer userId);

    /**
     * 修改监测事件状态
     *
     * @param getVO  传入参数
     * @param userId 用户ID
     */
    void changeEventStatus(MonitorEventStatusGetVO getVO, Integer userId);

    /**
     * 获取监测事件详情
     *
     * @param getVO 传入参数
     * @return 返回数据
     */
    MonitorEventGetDTO getEvent(MonitorEventGetVO getVO);

    /**
     * 通过行动转化ID获取监测事件ID
     *
     * @param masterId  账号ID
     * @param actionIds 传入参数
     * @return 返回数据
     */
    List<Long> listMonitorEventIdsByAction(Long masterId, List<Long> actionIds);
}
