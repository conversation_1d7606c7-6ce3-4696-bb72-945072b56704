package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.vo.market.monitor.*;
import com.overseas.service.market.entity.Monitor;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.common.SaveDTO;
import com.overseas.common.dto.market.monitor.MonitorGetDTO;
import com.overseas.common.dto.market.monitor.MonitorListDTO;
import com.overseas.common.utils.PageUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MonitorService extends IService<Monitor> {

    /**
     * 获取监测站点分页列表
     *
     * @param listVO 传入参数
     * @return 返回数据
     */
    PageUtils<MonitorListDTO> getMonitorPage(MonitorListVO listVO);

    /**
     * 获取监测站点数据详情
     *
     * @param getVO 传入参数
     * @return 返回数据
     */
    MonitorGetDTO getMonitor(MonitorGetVO getVO);

    /**
     * 新增监测站点
     *
     * @param saveVO 传入参数
     * @param userId 用户ID
     */
    SaveDTO saveMonitor(MonitorSaveVO saveVO, Integer userId);

    /**
     * 删除监测站点数据
     *
     * @param getVO  传入参数
     * @param userId 用户ID
     */
    void deleteMonitor(MonitorGetVO getVO, Integer userId);

    /**
     * 修改监测站点状态
     *
     * @param getVO  传入参数
     * @param userId 用户ID
     */
    void changeMonitorStatus(MonitorStatusGetVO getVO, Integer userId);

    /**
     * 获取监测站点下拉数据
     *
     * @param getVO 传入参数
     * @return 返回数据
     */
    List<SelectDTO> getMonitorSelect(MonitorSelectGetVO getVO);
}
