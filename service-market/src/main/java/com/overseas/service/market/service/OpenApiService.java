package com.overseas.service.market.service;

import com.overseas.common.utils.PageUtils;
import com.overseas.service.market.dto.openApi.campaign.OpenApiCampaignListDTO;
import com.overseas.service.market.dto.openApi.creativeUnit.OpenApiCreativeUnitListDTO;
import com.overseas.service.market.dto.openApi.master.OpenApiMasterListDTO;
import com.overseas.service.market.dto.openApi.plan.OpenApiPlanListDTO;
import com.overseas.service.market.dto.openApi.report.OpenApiReportListDTO;
import com.overseas.service.market.vo.openApi.campaign.OpenApiCampaignListVO;
import com.overseas.service.market.vo.openApi.creativeUnit.OpenApiCreativeUnitListVO;
import com.overseas.service.market.vo.openApi.master.OpenApiMasterListVO;
import com.overseas.service.market.vo.openApi.plan.OpenApiPlanListVO;
import com.overseas.service.market.vo.openApi.report.OpenApiReportListVO;

public interface OpenApiService {

    PageUtils<OpenApiMasterListDTO> listMaster(OpenApiMasterListVO listVO, Integer appKey);

    PageUtils<OpenApiCampaignListDTO> listCampaign(OpenApiCampaignListVO listVO, Integer appKey);

    PageUtils<OpenApiPlanListDTO> listPlan(OpenApiPlanListVO listVO, Integer appKey);

    PageUtils<OpenApiCreativeUnitListDTO> listCreativeUnit(OpenApiCreativeUnitListVO listVO, Integer appKey);

    PageUtils<OpenApiReportListDTO> listReport(OpenApiReportListVO listVO, Integer appKey);
}
