package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.service.market.dto.operate.log.OperateLogListDTO;
import com.overseas.service.market.entity.OperateLog;
import com.overseas.service.market.entity.User;
import com.overseas.service.market.vo.operate.log.OperateLogListVO;
import com.overseas.common.utils.PageUtils;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-09-15 11:26
 */
public interface OperateLogService extends IService<OperateLog> {
    PageUtils<OperateLogListDTO> pageLog(OperateLogListVO vo, User loginUser);
}
