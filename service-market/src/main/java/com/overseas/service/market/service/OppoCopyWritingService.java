package com.overseas.service.market.service;

import com.overseas.common.dto.market.oppoCopyWriting.OppoCopyWritingListDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.oppoCopyWriting.OppoCopyWritingListVO;
import com.overseas.common.vo.market.oppoCopyWriting.OppoCopyWritingSaveVO;

/**
 * OPPO文案服务接口
 * <AUTHOR>
 */
public interface OppoCopyWritingService {

    /**
     * 保存OPPO文案
     *
     * @param saveVO 文案信息
     * @param userId          用户ID
     */
    void saveOppoCopyWriting(OppoCopyWritingSaveVO saveVO, Integer userId);

    /**
     * 分页查询OPPO文案列表
     *
     * @param listVO        查询对象信息
     * @return 分页结果
     */
    PageUtils<OppoCopyWritingListDTO> listOppoCopyWriting(OppoCopyWritingListVO listVO);
}