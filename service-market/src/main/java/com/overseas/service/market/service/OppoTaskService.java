package com.overseas.service.market.service;

import com.overseas.common.dto.market.oppoCopyWriting.OppoTaskListDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.oppoTask.OppoTaskListVO;
import com.overseas.common.vo.market.oppoTask.OppoTaskSaveVO;
import com.overseas.service.market.entity.OppoTask;

/**
 * OPPO任务服务接口
 * <AUTHOR>
 */
public interface OppoTaskService {

    /**
     * 保存OPPO任务
     *
     * @param saveVO 任务信息
     * @param userId 用户ID
     */
    void saveOppoTask(OppoTaskSaveVO saveVO, Integer userId);

    /**
     * 分页查询OPPO任务列表
     *
     * @param listVO 查询对象信息
     * @return 分页结果
     */
    PageUtils<OppoTaskListDTO> listOppoTask(OppoTaskListVO listVO);

    void generateOppoAdCampaigns(Integer userId);
} 