package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.service.market.entity.PlanCycle;
import com.overseas.service.market.vo.plan.PlanCycleVO;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-25 19:24
 */
public interface PlanCycleService extends IService<PlanCycle> {

    void savePlanCycle(PlanCycleVO cycleVO, Long planId, Integer loginUserId);

    PlanCycleVO getPlanCycle(Long planId, Integer putCycle);
}
