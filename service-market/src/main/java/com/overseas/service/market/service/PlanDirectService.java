package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.vo.market.plan.direct.PlanDirectBatchUpdateVO;
import com.overseas.service.market.entity.Plan;
import com.overseas.service.market.entity.PlanDirect;
import com.overseas.service.market.vo.plan.DirectResourceVO;
import com.overseas.service.market.vo.plan.PlanDirectSaveVO;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.vo.market.plan.direct.PlanDirectBatchSaveVO;
import com.overseas.common.vo.market.plan.direct.PlanOptimizeTargetSelectGetVO;

import java.util.List;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-23 15:53
 */
public interface PlanDirectService extends IService<PlanDirect> {

    /**
     * 获取定向资源
     *
     * @param directResourceVO    查询条件
     * @param permissionMasterIds 账号ID集合
     * @return 返回数据
     */
    Object getDirectResource(DirectResourceVO directResourceVO, List<Integer> permissionMasterIds);


    void savePlanDirects(PlanDirectSaveVO directSaveVO, Long planId, Integer loginUserId);

    PlanDirectSaveVO getPlanDirects(Long planId, Plan planInfo);

    SelectDTO getOptimizeTarget(Long id, Integer campaignMarketTarget, Plan plan);

    void batchSavePlanDirect(PlanDirectBatchSaveVO saveVO, Integer userId);

    List<SelectDTO> selectOptimizeTarget(PlanOptimizeTargetSelectGetVO getVO);

    List<String> batchUpdatePlanDirect(PlanDirectBatchUpdateVO updateVO, Integer userId);
}
