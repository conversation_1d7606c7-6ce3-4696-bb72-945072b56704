package com.overseas.service.market.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.dto.market.plan.PlanByInfoGetDTO;
import com.overseas.common.dto.market.plan.SheinPlanAuditValidDTO;
import com.overseas.common.vo.market.market.RecordBatchSwitchVO;
import com.overseas.common.vo.market.plan.*;
import com.overseas.service.market.dto.plan.PlanGetDTO;
import com.overseas.service.market.dto.plan.PlanInspectionDTO;
import com.overseas.service.market.dto.plan.PlanSaveDTO;
import com.overseas.service.market.entity.Plan;
import com.overseas.service.market.entity.PlanUpdateRecord;
import com.overseas.service.market.entity.User;
import com.overseas.service.market.vo.plan.PlanBatchUpdateVO;
import com.overseas.service.market.vo.plan.PlanInspectionVO;
import com.overseas.service.market.vo.plan.PlanSaveVO;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.TreeNodeDTO;
import com.overseas.common.dto.market.market.MarketBatchListDTO;
import com.overseas.common.dto.market.plan.PlanCountDTO;
import com.overseas.common.dto.market.plan.PlanDimensionDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.common.GetVO;
import com.overseas.common.vo.market.market.MarketBatchListVO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface PlanService extends IService<Plan> {

    /**
     * 获取计划下拉
     *
     * @param getVO               传入参数
     * @param permissionMasterIds 权限ID
     * @param user                用户
     * @return 返回数据
     */
    List<SelectDTO> selectPlan(PlanSelectGetVO getVO, List<Integer> permissionMasterIds, User user);

    /**
     * 获取计划下拉
     *
     * @param getVO               传入参数
     * @param permissionMasterIds 权限ID
     * @param user                用户
     * @return 返回数据
     */
    List<SelectDTO> selectCopyPlan(PlanSelectGetVO getVO, List<Integer> permissionMasterIds, User user);

    /**
     * 获取计划下拉
     *
     * @param selectVO            传入参数
     * @param permissionMasterIds 账户ID
     * @param user                用户
     * @return 返回数据
     */
    PageUtils<?> selectPagePlanAndCampaignAndMaster(PlanAndCampaignAndMasterSelectVO selectVO,
                                                    List<Integer> permissionMasterIds, User user);

    List<SelectDTO> selectPlanHasCost(PlanHasCostSelectGetVO getVO, User user);

    Plan getPlan(Long id, Integer masterId);

    boolean checkRepeatName(String name, Long campaignId, Long planId);

    Plan getPlan(Long id, Long campaignId, Integer masterId);

    PlanSaveDTO savePlan(PlanSaveVO planSaveVO, User user);

    PlanGetDTO getPlanDetail(Long id, Integer masterId, User user);

    List<Long> getAllPlanIds(PlanIdsGetVO getVO);

    boolean batchUpdatePlan(PlanBatchUpdateVO batchUpdateVO, User user);

    void batchUpdatePlanLink(PlanLinkBatchUpdateVO batchUpdateVO, User user);

    PlanCountDTO getMasterPlanCount(GetVO getVO, User user);

    void updatePlanBudgetByNextDay(Date date);

    List<Long> listAutoLinkPlan(AutoLinkPlanListVO listVO);

    List<TreeNodeDTO> getPlanTree(PlanTreeGetVO getVO);

    List<Long> listPlanByTemplate(PlanListByTemplateGetVO getVO);

    List<Plan> listPlanByTimeZone(Integer timeZone);

    void updatePlanStatusHandler(Long date);

    IPage<MarketBatchListDTO> listLinkBatch(MarketBatchListVO listVO);

    IPage<MarketBatchListDTO> listCreativeUnitBatch(MarketBatchListVO listVO);

    List<PlanDimensionDTO> listPlanByDimension(PlanDimensionSelectVO selectVO);

    PageUtils<PlanInspectionDTO> listInspectionPlans(PlanInspectionVO listVO);

    void exportPlans(PlanInspectionVO planInspectionVO, HttpServletResponse response) throws IOException;

    void updateBidPriceByRate(PlanBidPriceUpdateVO updateVO, Integer userId);

    /**
     * 根据计划信息获取计划，活动，账户信息
     *
     * @param getVO 筛选条件
     * @return 返回数据
     */
    List<PlanByInfoGetDTO> getPlanByInfo(PlanByInfoGetVO getVO);

    /**
     * shein 计划审核
     *
     * @param auditVO 审核数据
     * @param user    用户
     */
    void sheinAudit(PlanSheinAuditVO auditVO, User user);

    /**
     * shein 批量计划审核
     *
     * @param batchAuditVO 审核数据
     * @param user         用户
     */
    void sheinBatchAudit(PlanSheinBatchAuditVO batchAuditVO, User user);


    /**
     * shein 批量修改计划组日预算
     *
     * @param batchBudgetDailyVO 计划组日预算
     * @param user               用户
     */
    void sheinBatchBudgetDaily(PlanSheinBatchBudgetDailyVO batchBudgetDailyVO, User user);

    /**
     * shein 审核计划列表
     *
     * @param listPlanVO 条件
     * @param user       用户数据
     * @return 返回数据
     */
    List<SelectDTO> sheinAuditListPlan(PlanSheinAuditListPlanVO listPlanVO, User user);

    /**
     * 创意shein 计划活动和1:1计划
     *
     * @param listPlanVO 条件
     * @param user       用户数据
     * @return 返回数据
     */
    void sheinAuditShadow(PlanSheinAuditListPlanVO listPlanVO, User user);

    /**
     * 创建 shein 计划投放活动
     *
     * @param listPlanVO 计划ID
     * @param user       返回数据
     */
    void sheinAuditCampaign(PlanSheinAuditListPlanVO listPlanVO, User user);


    /**
     * shein 审核校验
     *
     * @param validVO 条件数据
     */
    List<SheinPlanAuditValidDTO> sheinAuditValid(PlanSheinAuditValidVO validVO);

    /**
     * 修改出价
     *
     * @param priceVO 价格
     * @param user    用户
     */
    void sheinAuditPrice(PlanSheinAuditPriceVO priceVO, User user);

    /**
     * shein 计划同步
     *
     * @param planId 计划ID
     * @param user   用户
     */
    Object noticeSheinPlan(Long planId, User user);


    /**
     * 修改数据日志
     *
     * @param planUpdateRecords 日志
     * @param user              用户
     */
    void savePlanUpdateRecordAndAfter(List<PlanUpdateRecord> planUpdateRecords, User user);

    /**
     * 是否是shein 的计划
     *
     * @param plan 计划信息
     * @return 返回数据
     */
    boolean isSheinPlan(Plan plan);

    /**
     * 状态切换
     *
     * @param batchSwitchVO 切换状态
     * @return 返回数据
     */
    boolean batchSwitch(RecordBatchSwitchVO batchSwitchVO);
}
