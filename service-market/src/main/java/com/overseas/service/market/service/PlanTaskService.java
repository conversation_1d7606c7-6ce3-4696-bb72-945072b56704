package com.overseas.service.market.service;

import com.overseas.common.dto.market.plan.task.PlanTaskCompletionListDTO;
import com.overseas.common.dto.market.plan.task.PlanTaskListDTO;
import com.overseas.common.dto.market.plan.task.PlanTaskTempDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.plan.task.PlanTaskCompletionListVO;
import com.overseas.common.vo.market.plan.task.PlanTaskListVO;
import com.overseas.common.vo.market.plan.task.PlanTaskSaveVO;
import com.overseas.common.vo.market.plan.task.PlanTaskTempListVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface PlanTaskService {

    /**
     * 获取批量创建计划任务列表
     *
     * @param listVO 传入参数
     * @return 返回数据
     */
    PageUtils<PlanTaskListDTO> listPlanTask(PlanTaskListVO listVO);

    /**
     * 获取计划临时列表
     *
     * @param listVO 传入参数
     * @return 返回数据
     */
    PlanTaskTempDTO listPlanTaskTemp(PlanTaskTempListVO listVO);

    /**
     * 创建任务
     *
     * @param saveVO 传入参数
     * @param userId 用户ID
     */
    void savePlanTask(PlanTaskSaveVO saveVO, Integer userId);

    /**
     * 通过任务执行创建计划流程
     */
    void savePlanByTask();

    /**
     * 获取已创建的计划
     *
     * @param listVO 传入参数
     * @return 返回数据
     */
    List<PlanTaskCompletionListDTO> listCompletePlan(PlanTaskCompletionListVO listVO);
}
