package com.overseas.service.market.service;

import com.overseas.common.dto.market.plan.PlanUpdateRecordListDTO;
import com.overseas.common.dto.market.plan.PlanUpdateRecordOneDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.plan.PlanUpdateRecordListVO;
import com.overseas.common.vo.market.plan.PlanUpdateRecordOneVO;
import com.overseas.service.market.entity.PlanUpdateRecord;
import com.overseas.service.market.entity.User;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface PlanUpdateRecordService {

    /**
     * 保存计划数据
     *
     * @param planUpdateRecords 计划数据
     * @param userId            用户ID
     * @return 保存结果
     */
    List<PlanUpdateRecord> savePlanUpdateRecord(List<PlanUpdateRecord> planUpdateRecords, Integer userId);

    /**
     * 获取计划日志数据
     *
     * @param listVO 条件
     * @return 返回数据
     */
    PageUtils<PlanUpdateRecordListDTO> listPlanUpdateRecord(PlanUpdateRecordListVO listVO, User user);

    /**
     * 获取单条数据
     *
     * @param oneVO 修改内容
     * @return 返回数据
     */
    PlanUpdateRecordOneDTO oneInfo(PlanUpdateRecordOneVO oneVO);


}
