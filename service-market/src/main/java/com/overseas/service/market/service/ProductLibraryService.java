package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.utils.PageUtils;
import com.overseas.service.market.entity.productLibrary.ProductLibrary;
import com.overseas.service.market.vo.productLibrary.ProductLibraryListVO;
import com.overseas.service.market.vo.productLibrary.ProductLibrarySaveVO;

public interface ProductLibraryService extends IService<ProductLibrary> {

    ProductLibrary saveProductLibrary(ProductLibrarySaveVO saveVO, Integer loginUserId);

    PageUtils<?> listProductLibrary(ProductLibraryListVO listVO);
}
