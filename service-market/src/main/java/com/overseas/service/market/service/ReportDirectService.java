package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.service.market.entity.ReportDirect;
import com.overseas.common.dto.market.plan.PlanDirectValueDTO;
import com.overseas.common.vo.market.plan.PlanDirectGetVO;
import com.overseas.common.vo.market.plan.PlanDirectionChangeGetVO;
import com.overseas.common.vo.market.plan.PlanMarketDirectUpdateGetVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ReportDirectService extends IService<ReportDirect> {

    /**
     * 获取计划报表定向信息
     *
     * @param getVO 传入参数
     * @return 返回数据
     */
    List<PlanDirectValueDTO> getPlanDirect(PlanDirectGetVO getVO);

    /**
     * 修改计划报表定向信息
     *
     * @param getVO  传入参数
     * @param userId 用户ID
     * @return 返回执行的结果
     */
    List<ReportDirect> changePlanDirect(PlanDirectionChangeGetVO getVO, Integer userId);

    /**
     * 投放模块修改报表定向信息
     *
     * @param getVO  传入参数
     * @param userId 用户ID
     */
    void changeMarketDirect(PlanMarketDirectUpdateGetVO getVO, Integer userId);

    /**
     * 通知中控
     *
     * @param campaignId 活动ID
     * @param planId     计划ID
     */
    void noticeCenterControl(Long campaignId, Long planId);
}
