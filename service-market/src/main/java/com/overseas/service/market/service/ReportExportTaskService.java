package com.overseas.service.market.service;

import com.overseas.common.dto.market.reportTask.ReportExportTaskDTO;
import com.overseas.common.dto.market.reportTask.ReportExportTaskListDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.reportTask.*;

/**
 * <AUTHOR>
 */
public interface ReportExportTaskService {

    /**
     * 获取离线任务列表数据
     *
     * @param listVO 传入参数
     * @param userId 用户ID
     * @return 返回数据
     */
    PageUtils<ReportExportTaskListDTO> getReportExportTaskPage(ReportExportTaskListVO listVO, Integer userId);

    /**
     * 新增离线任务
     *
     * @param saveVO 传入参数
     * @param userId 用户ID
     */
    void saveReportExportTask(ReportExportTaskSaveVO saveVO, Integer userId);

    /**
     * 更新离线任务状态
     *
     * @param updateVO 传入参数
     */
    void updateReportExportTaskStatus(ReportExportTaskStatusUpdateVO updateVO);

    /**
     * 导出报表离线任务
     */
    void exportAllReportTask();

    /**
     * 获取待执行创建流程的任务
     *
     * @param getVO 传入参数
     * @return 返回任务信息
     */
    ReportExportTaskDTO getWaitToCreateTask(ReportExportTaskGetVO getVO);

    /**
     * 更新离线任务记录信息
     *
     * @param updateVO 更新信息
     */
    void updateReportTask(ReportExportTaskUpdateVO updateVO);

}
