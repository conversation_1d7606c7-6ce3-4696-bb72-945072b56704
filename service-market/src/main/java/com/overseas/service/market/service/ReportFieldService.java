package com.overseas.service.market.service;

import com.overseas.common.dto.market.reportField.ReportFieldDTO;
import com.overseas.common.dto.sys.customIndex.CustomIndexParentColumnDTO;
import com.overseas.common.vo.market.reportField.ReportFieldGetVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ReportFieldService {

    List<ReportFieldDTO> listReportField(ReportFieldGetVO getVO);

    List<CustomIndexParentColumnDTO> getCustomIndexColumns(ReportFieldGetVO getVO);
}
