package com.overseas.service.market.service;

import com.overseas.common.dto.market.reportNote.ReportNoteDTO;
import com.overseas.common.vo.market.reportNote.ReportNoteListVO;
import com.overseas.common.vo.market.reportNote.ReportNoteSaveVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ReportNoteService {

    void saveReportNote(ReportNoteSaveVO saveVO, Integer userId);

    List<ReportNoteDTO> listReportNote(ReportNoteListVO listVO);
}
