package com.overseas.service.market.service;

import com.overseas.common.dto.market.rtaStrategy.RtaStrategyGetDTO;
import com.overseas.common.vo.market.rtaStrategy.*;
import com.overseas.service.market.entity.RtaStrategy;
import com.overseas.service.market.vo.rta.RtaStrategyCascaderGetVO;
import com.overseas.common.dto.CascaderDTO;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.rtaStrategy.RtaCountryDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.plan.direct.RtaSelectGetVO;
import com.overseas.common.dto.TreeNodeDTO;
import com.overseas.common.dto.market.rtaStrategy.RtaStrategyListDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface RtaStrategyService {

    PageUtils<RtaStrategyListDTO> listRtaStrategy(RtaStrategyListVO listVO);

    List<CascaderDTO> getRtaStrategyCascader(RtaStrategyCascaderGetVO getVO);

    List<SelectDTO> selectRtaStrategy(RtaSelectGetVO getVO);

    List<RtaStrategyListDTO> listAeRtaStrategy();

    void updateRtaStrategyCostRate(RtaStrategyUpdateVO updateVO, Integer userId);

    List<TreeNodeDTO> getRtaStrategyTree(RtaStrategyCascaderGetVO getVO);

    List<RtaStrategyListDTO> listRtaStrategy(RtaSelectGetVO getVO);

    List<RtaStrategyListDTO> listRtaStrategyAll(RtaSelectGetVO getVO);

    void batchSaveRtaStrategy(RtaStrategyBatchSaveVO saveVO, Integer userId);

    RtaStrategy getRtaStrategy(RtaStrategyGetVO getVO);

    List<RtaStrategyGetDTO> getRtaStrategyByPlanIds(RtaStrategyGetByPlanIdsVO getVO);

    void fillGoodsId(RtaStrategy rtaStrategy);

    String getRtaStrategyCId(RtaStrategy rtaStrategy, Integer assetType);

    String replaceMacroOfUrl(String url, Map<String, Object> macroMap, String cId);

    List<RtaCountryDTO> listRtaCountry();

    List<SelectDTO> selectRtaGroup();

    void pull2All();

}
