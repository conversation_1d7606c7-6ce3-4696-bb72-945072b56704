package com.overseas.service.market.service;

import com.overseas.service.market.entity.User;

import java.util.List;

/**
 * <AUTHOR>
 **/
public interface SheinCreativeService {


    /**
     * 根据创意单元修改创意 记录
     *
     * @param unitIds    创意单元ID
     * @param operateUid 用户
     */
    void logByUnitIds(List<Long> unitIds, Integer operateUid);


    /**
     * 记录创意修改
     *
     * @param planIds    计划ID
     * @param operateUid 操作用户
     */
    void logCreativeUpdate(List<Long> planIds, Integer operateUid);


    /**
     * 记录创意修改
     *
     * @param planId     创意ID
     * @param operateUid 操作用户ID
     */
    void logCreativeUpdate(Long planId, Integer operateUid);
}
