package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.service.market.dto.slot.SlotDirectDTO;
import com.overseas.service.market.entity.Slot;
import com.overseas.service.market.vo.plan.DirectResourceVO;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.slot.SlotSelectGetVO;

import java.util.List;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-23 20:37
 */
public interface SlotService extends IService<Slot> {

    PageUtils<SlotDirectDTO> pageSlotDirect(DirectResourceVO directResourceVO);

    /**
     * 获取广告位下拉数据
     *
     * @param getVO 传入参数
     * @return 返回数据
     */
    List<SelectDTO> getSlotSelect(SlotSelectGetVO getVO);
}
