package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.vo.market.tag.*;
import com.overseas.service.market.entity.Tag;
import com.overseas.common.dto.market.tag.TagGetDTO;
import com.overseas.common.dto.market.tag.TagListDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.service.market.entity.User;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TagService extends IService<Tag> {

    /**
     * 获取人群标签列表
     *
     * @param listVO 传入参数
     * @return 返回数据
     */
    PageUtils<TagListDTO> getTagPage(TagListVO listVO, List<Integer> permissionMasterIds);

    /**
     * 获取人群标签数据
     *
     * @param getVO 传入参数
     * @return 返回数据
     */
    TagGetDTO getTag(TagGetVO getVO);

    /**
     * 新增人群标签
     *
     * @param saveVO 传入参数
     * @param user   用户
     */
    void saveTag(TagSaveVO saveVO, User user);

    /**
     * 更新人群标签
     *
     * @param updateVO 传入参数
     * @param user     用户
     */
    void updateTag(TagUpdateVO updateVO, User user);

    /**
     * 保存人群标签用户关系
     *
     * @param saveVO 传入参数
     * @param userId 用户ID
     */
    void saveTagResource(TagResourceSaveVO saveVO, Integer userId);

    /**
     * 更新人群标签状态
     */
    void updateTagStatus() throws Exception;

    /**
     * 检查是否有新广告主注册
     */
    void checkNewMaster();
}
