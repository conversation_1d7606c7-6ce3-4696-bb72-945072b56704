package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.service.market.dto.assetTask.AssetTaskMaterialDTO;
import com.overseas.service.market.entity.assetTask.TaskProductAsset;
import com.overseas.service.market.vo.taskAsset.TaskAssetSaveVO;

import java.util.List;

public interface TaskProductAssetService extends IService<TaskProductAsset> {

    List<AssetTaskMaterialDTO> saveTaskAsset(TaskAssetSaveVO saveVO, Integer loginUserId);

    /**
     * 判断素材是否被使用
     *
     * @param masterId 账户ID
     * @param assetIds 素材ID
     */
    void checkAssetIsUse(Integer masterId, List<Long> assetIds);
}
