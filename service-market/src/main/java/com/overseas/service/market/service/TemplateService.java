package com.overseas.service.market.service;

import com.overseas.common.dto.market.template.TemplateMonitorDataDTO;
import com.overseas.common.dto.market.template.TemplatePreviewDTO;
import com.overseas.common.dto.market.template.TemplateSelectDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.template.*;
import com.overseas.service.market.entity.TemplateConfig;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TemplateService {
    /**
     * 根据是否是autoLink获取模版列表
     *
     * @param selectVO 查询参数
     * @return 模版列表
     */
    List<TemplateSelectDTO> getTemplateSelect(TemplateSelectVO selectVO);

    /**
     * 模板获取数据
     *
     * @param templateId 模板ID
     * @return 返回数据
     */
    TemplateConfig getTemplate(Long templateId);

    /**
     * 模板列表
     *
     * @param listVO 条件
     * @return 数据
     */
    PageUtils<?> listTemplate(TemplateListVO listVO);

    /**
     * 保存模板
     *
     * @param saveVO 条件
     */
    void saveTemplate(TemplateSaveVO saveVO, Integer userId);

    /**
     * 更新模板
     *
     * @param updateVO 更新
     * @param userId   用户ID
     */
    void updateTemplate(TemplateUpdateVO updateVO, Integer userId);

    /**
     * 删除模板
     *
     * @param delVO  删除
     * @param userId 用户ID
     */
    void delTemplate(TemplateDelVO delVO, Integer userId);

    /**
     * 获取模板
     *
     * @param getVO 条件
     * @return 结果
     */
    TemplateConfig getTemplateV2(TemplateGetVO getVO);

    /**
     * 模板预览
     *
     * @param previewVO 预览数据
     * @return 返回数据
     */
    TemplatePreviewDTO preview(TemplatePreviewVO previewVO);

    /**
     * 获取监测数据
     *
     * @param monitorDataVO 条件
     * @return 返回监测数据
     */
    TemplateMonitorDataDTO monitorData(TemplateMonitorDataVO monitorDataVO);

    /**
     * 发布模板
     *
     * @param publishVO 条件
     * @param userId    用户ID
     */
    void publishTemplate(TemplatePublishVO publishVO, Integer userId);

    /**
     * 监测记录
     *
     * @param nonce      标识
     * @param templateId 模板ID
     * @param type       类型
     */
    void monitor(String nonce, Long templateId, String type);

    /**
     * 可用宏
     *
     * @return 返回数据
     */
    List<String> useMacro();
}
