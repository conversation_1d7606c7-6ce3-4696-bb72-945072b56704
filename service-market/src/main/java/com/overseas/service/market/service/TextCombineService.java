package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.dto.market.creativeUnit.CreativeUnitListDTO;
import com.overseas.common.dto.market.textCombine.TextCombineCheckDTO;
import com.overseas.common.dto.market.textCombine.TextCombineListDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.creative.CreativeUnitListVO;
import com.overseas.service.market.entity.TextCombine;
import com.overseas.service.market.entity.User;
import com.overseas.common.vo.market.textCombine.TextCombineCheckVO;
import com.overseas.common.vo.market.textCombine.TextCombineListVO;
import com.overseas.common.vo.market.textCombine.TextCombineSaveVO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 文本组合服务接口
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
public interface TextCombineService extends IService<TextCombine> {

    /**
     * 新增文本组合
     *
     * @param saveVO 保存参数
     * @param userId 用户ID
     * @return 返回新增的文本组合
     */
    void saveTextCombine(TextCombineSaveVO saveVO, Integer userId);

    /**
     * 获取文本组合列表
     *
     * @param listVO 查询参数
     * @param user   用户
     * @return 返回分页数据
     */
    PageUtils<TextCombineListDTO> listTextCombine(TextCombineListVO listVO, User user);

    /**
     * 根据素材ID获取创意单元列表
     *
     * @param listVO 条件
     * @param user   用户
     * @return 返回数据
     */
    PageUtils<CreativeUnitListDTO> listCreativeUnitByCombine(CreativeUnitListVO listVO, User user);

    /**
     * 下载数据
     *
     * @param listVO   条件
     * @param user     用户
     * @param response 结果
     * @throws IOException 异常
     */
    void exportListTextCombine(TextCombineListVO listVO, User user, HttpServletResponse response) throws IOException;

    /**
     * 文案组合校验
     *
     * @param combineCheckVO 条件
     * @return 结果
     */

    TextCombineCheckDTO checkTextCombine(TextCombineCheckVO combineCheckVO);
} 