package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.textLibrary.TextLibraryInfoListDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.textLibrary.*;
import com.overseas.service.market.entity.TextLibrary;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface TextLibraryService extends IService<TextLibrary> {

    /**
     * 筛选数据
     *
     * @param selectVO 筛选
     * @return 结果
     */
    List<SelectDTO> selectTextLibrary(TextLibrarySelectVO selectVO);

    /**
     * 新增文本库
     *
     * @param saveVO 保存参数
     * @param userId 用户ID
     * @return 返回新增的文本库
     */
    TextLibrary saveTextLibrary(TextLibrarySaveVO saveVO, Integer userId);

    /**
     * 更新文本库
     *
     * @param updateVO 更新参数
     * @param userId   用户ID
     */
    void updateTextLibrary(TextLibraryUpdateVO updateVO, Integer userId);

    /**
     * 保存 文本信息
     *
     * @param libraryId    商品ID
     * @param masterId     账户ID
     * @param libraryInfos 数据信息
     * @param userId       用户ID
     * @return 返回数量
     */
    int saveTextLibrary(Long libraryId, Long masterId, List<TextLibraryInfoVO> libraryInfos, Integer userId);

    /**
     * 删除文本库
     *
     * @param delVO  删除信息
     * @param userId 用户ID
     */
    void deleteTextLibrary(TextLibraryDelVO delVO, Integer userId);

    /**
     * 暂停/启用文本库
     *
     * @param statusVO 状态条件
     * @param userId   用户ID
     */
    void changeTextLibraryStatus(TextLibraryStatusVO statusVO, Integer userId);

    /**
     * 获取文本库列表
     *
     * @param listVO 查询参数
     * @return 返回分页数据
     */
    PageUtils<?> listTextLibrary(TextLibraryListVO listVO);

    /**
     * 删除文案库文案内容
     *
     * @param delVO  内容
     * @param userId 返回数据
     */
    void deleteTextLibraryInfo(TextLibraryInfoDelVO delVO, Integer userId);

    /**
     * 数据上传
     *
     * @param uploadVO 上传数据
     * @return 返回结果
     */
    List<TextLibraryInfoVO> uploadTextLibraryInfo(TextLibraryInfoUploadVO uploadVO);

    /**
     * 获取文案库 文案信息列表
     *
     * @param listVO 条件
     * @return 返回数据
     */
    List<TextLibraryInfoListDTO> listTextLibraryInfo(TextLibraryInfoListVO listVO);

    /**
     * 完善数据
     *
     * @param list     list
     * @param masterId 账户
     * @return 返回数据
     */
    List<TextLibraryInfoListDTO> completeInfos(List<TextLibraryInfoListDTO> list, Long masterId);

    /**
     * 下载文案库内容
     *
     * @param listVO   条件
     * @param response 返回结果
     * @throws IOException 异常
     */
    void exportTextLibraryInfo(TextLibraryInfoListVO listVO, HttpServletResponse response) throws IOException;
} 