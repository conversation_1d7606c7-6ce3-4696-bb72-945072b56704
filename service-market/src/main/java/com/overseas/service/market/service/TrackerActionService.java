package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.service.market.entity.TrackerAction;
import com.overseas.common.dto.SelectDTO2;
import com.overseas.common.dto.market.trackAction.TrackActionSelectByProjectDTO;
import com.overseas.common.vo.market.monitor.action.TrackerActionByProjectSelectVO;
import com.overseas.common.vo.market.monitor.action.TrackerActionSelectVO;
import com.overseas.common.vo.sys.project.ProjectSetActionMapVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TrackerActionService extends IService<TrackerAction> {

    /**
     * 获取监测事件上报下拉
     *
     * @param selectVO 传入参数
     * @return 返回数据
     */
    List<SelectDTO2> getActionSelect(TrackerActionSelectVO selectVO);


    /**
     * 根据项目获取可选择事件
     *
     * @param selectVO 下拉条件
     * @return 返回数据
     */
    List<TrackActionSelectByProjectDTO> getActionSelectByProject(TrackerActionByProjectSelectVO selectVO);

    /**
     * 获取映射关系
     * @param selectVO 条件
     * @return 返回数据
     */
    List<SelectDTO2> mapByProject(TrackerActionByProjectSelectVO selectVO);

    /**
     * 根据项目标识获取设置的指标内容
     *
     * @param project 返回项目数据
     * @return 返回结果
     */
    List<ProjectSetActionMapVO.ActionMapVO> getSetAction(String project);


    /**
     * 保存项目标识关系
     *
     * @param setActionMapVO 保存项目标识关系
     * @param operatorUid    操作用户
     */
    void saveProjectTrackAction(ProjectSetActionMapVO setActionMapVO, Integer operatorUid);
}
