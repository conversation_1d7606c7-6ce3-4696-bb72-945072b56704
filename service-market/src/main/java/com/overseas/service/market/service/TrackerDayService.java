package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.service.market.entity.TrackerDay;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TrackerDayService extends IService<TrackerDay> {

    /**
     * 获取监测事件上报数
     *
     * @param sqlSelect   Sql查询
     * @param monitorIds  监测站点ID
     * @param targetTypes 监测事件
     * @param groupField  聚合字段
     * @return 返回数据
     */
    List<TrackerDay> getTrackerDayList(String sqlSelect, List<Long> monitorIds, List<String> targetTypes, String groupField);
}
