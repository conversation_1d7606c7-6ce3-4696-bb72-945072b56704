package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.service.market.dto.trackingGroupApp.TrackingGroupAppListDTO;
import com.overseas.service.market.entity.tracking.TrackingGroupApp;
import com.overseas.service.market.vo.trackingGroupApp.TrackingGroupAppBindVO;
import com.overseas.service.market.vo.trackingGroupApp.TrackingGroupAppListVO;

import java.util.List;

public interface TrackingGroupAppService extends IService<TrackingGroupApp> {

    List<TrackingGroupAppListDTO> listTrackingGroupApp(TrackingGroupAppListVO listVO);

    void bindApp2TrackingGroup(TrackingGroupAppBindVO bindVO, Integer loginUserId);
}
