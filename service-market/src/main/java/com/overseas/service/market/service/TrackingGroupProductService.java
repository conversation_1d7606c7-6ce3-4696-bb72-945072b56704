package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.utils.PageUtils;
import com.overseas.service.market.dto.trackingGroupProduct.TrackingGroupProductListDTO;
import com.overseas.service.market.entity.tracking.TrackingGroupProduct;
import com.overseas.service.market.vo.trackingGroupProduct.TrackingGroupProductBindVO;
import com.overseas.service.market.vo.trackingGroupProduct.TrackingGroupProductDeleteVO;
import com.overseas.service.market.vo.trackingGroupProduct.TrackingGroupProductListVO;

public interface TrackingGroupProductService extends IService<TrackingGroupProduct> {

    PageUtils<TrackingGroupProductListDTO> listTrackingGroupProduct(TrackingGroupProductListVO listVO);

    void bindProduct2TrackingGroup(TrackingGroupProductBindVO bindVO, Integer loginUserId);

    void deleteTrackingGroupProduct(TrackingGroupProductDeleteVO deleteVO, Integer loginUserId);
}
