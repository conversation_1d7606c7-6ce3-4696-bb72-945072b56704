package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.service.market.dto.trackingGroup.TrackingGroupListDTO;
import com.overseas.service.market.entity.tracking.TrackingGroup;
import com.overseas.service.market.vo.trackingGroup.TrackingGroupListVO;
import com.overseas.service.market.vo.trackingGroup.TrackingGroupRemarkVO;
import com.overseas.service.market.vo.trackingGroup.TrackingGroupRenameVO;
import com.overseas.service.market.vo.trackingGroup.TrackingGroupSelectVO;
import com.overseas.service.market.vo.trackingGroupUrl.TrackingGroupUrlListVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface TrackingGroupService extends IService<TrackingGroup> {
    PageUtils<TrackingGroupListDTO> listTrackingGroup(TrackingGroupList<PERSON> listVO, Integer userId);

    List<SelectDTO> selectTrackingGroup(TrackingGroupSelectVO selectVO);

    void renameTrackingGroup(TrackingGroupRenameVO renameVO, Integer loginUserId);

    void remarkTrackingGroup(TrackingGroupRemarkVO remarkVO, Integer loginUserId);

    void exportTrackingGroupCpsUrl(TrackingGroupUrlListVO listVO, HttpServletResponse response);

    void generateTrackingGroupUrl(Integer trackingGroupId, Integer userId);
}
