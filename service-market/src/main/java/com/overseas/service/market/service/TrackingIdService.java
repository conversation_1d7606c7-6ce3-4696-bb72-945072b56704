package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.service.market.dto.trackingId.TrackingIdListDTO;
import com.overseas.service.market.entity.assetTask.TrackingId;
import com.overseas.service.market.vo.trackingId.TrackingIdListVO;
import com.overseas.service.market.vo.trackingId.TrackingIdSaveVO;
import com.overseas.service.market.vo.trackingId.TrackingIdSelectVO;

import java.util.List;

public interface TrackingIdService extends IService<TrackingId> {
    void saveTrackingId(TrackingIdSaveVO saveVO);

    PageUtils<TrackingIdListDTO> listTrackingId(TrackingIdListVO listVO, Integer userId);

    List<SelectDTO> selectTrackingId(TrackingIdSelectVO selectVO);
}
