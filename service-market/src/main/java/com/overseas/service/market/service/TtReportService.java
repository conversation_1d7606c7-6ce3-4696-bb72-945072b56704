package com.overseas.service.market.service;

import com.overseas.common.utils.PageUtils;
import com.overseas.service.market.dto.ttReport.TtReportListDTO;
import com.overseas.service.market.dto.ttReport.TtReportUploadResultDTO;
import com.overseas.service.market.entity.User;
import com.overseas.service.market.vo.ttReport.TtReportListVO;
import com.overseas.service.market.vo.ttReport.TtReportUploadVO;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * TtReport服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
public interface TtReportService {

    /**
     * 上传Excel文件并批量插入数据
     *
     * @param uploadVO Excel上传参数
     * @param user     当前用户
     * @return 上传结果
     */
    TtReportUploadResultDTO uploadExcel(TtReportUploadVO uploadVO, User user);

    /**
     * 分页查询聚合报表数据
     * 将TtReport和TtProtect表数据按照日期聚合后查询
     *
     * @param listVO 查询参数
     * @return 分页结果
     */
    PageUtils<TtReportListDTO> pageReportData(TtReportListVO listVO);

    void exportReportData(TtReportListVO listVO, HttpServletResponse response);
} 