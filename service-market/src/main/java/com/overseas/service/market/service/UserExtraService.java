package com.overseas.service.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.service.market.dto.audit.master.AuditMasterGetDTO;
import com.overseas.service.market.entity.UserExtra;
import com.overseas.service.market.vo.auditMaster.AuditMasterSaveVO;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-09-14 16:09
 */
public interface UserExtraService extends IService<UserExtra> {

    Long saveQualification(AuditMasterSaveVO vo, Integer loginUserId, Integer userType);

    Long saveBankInfo(AuditMasterSaveVO vo, Integer loginUserId);

    Long validateMoney(AuditMasterSaveVO vo, Integer loginUserId);

    AuditMasterGetDTO getDetail(Integer loginUserId);
}
