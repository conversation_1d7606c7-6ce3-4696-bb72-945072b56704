package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.SelectDTO2;
import com.overseas.common.dto.TreeNodeDTO;
import com.overseas.common.dto.TreeNodeDTO2;
import com.overseas.common.dto.market.template.TemplateSelectDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.plan.direct.AdxSelectGetVO;
import com.overseas.common.vo.market.plan.direct.EpSelectGetVO;
import com.overseas.service.market.dto.adx.AdxGetDTO;
import com.overseas.service.market.dto.adx.AdxListDTO;
import com.overseas.service.market.entity.Adx;
import com.overseas.service.market.entity.Ep;
import com.overseas.service.market.entity.TemplateResource;
import com.overseas.service.market.enums.adx.AdxDockingEnum;
import com.overseas.service.market.enums.adx.AdxStatusEnum;
import com.overseas.service.market.enums.adx.AdxUploadEnum;
import com.overseas.service.market.mapper.*;
import com.overseas.service.market.service.AdxService;
import com.overseas.service.market.vo.adx.*;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class AdxServiceImpl extends ServiceImpl<AdxMapper, Adx> implements AdxService {

    private final EpMapper epMapper;

    private final TemplateConfigMapper templateConfigMapper;

    private final PlanMapper planMapper;

    private final TemplateResourceMapper templateResourceMapper;

    @Override
    public List<SelectDTO> selectWorkable() {
        List<SelectDTO> result = new ArrayList<>();
        List<Adx> list = listWorkable();
        list.forEach(adx -> result.add(new SelectDTO(adx.getId().longValue(), adx.getAdxName())));
        return result;
    }

    @Override
    public List<SelectDTO> getEpSelect(EpSelectGetVO getVO) {
        return this.epMapper.getEpSelect(new QueryWrapper<Ep>().lambda()
                .eq(Ep::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getAdxId()), Ep::getAdxId, getVO.getAdxId())
                .in(CollectionUtils.isNotEmpty(getVO.getAdxIds()), Ep::getAdxId, getVO.getAdxIds())
                .in(CollectionUtils.isNotEmpty(getVO.getEpIds()), Ep::getId, getVO.getEpIds())
                .orderByAsc(Ep::getId));
    }

    @Override
    public List<SelectDTO> getAdxSelect(AdxSelectGetVO getVO) {
        return baseMapper.selectAdx(new LambdaQueryWrapper<Adx>()
                .in(CollectionUtils.isNotEmpty(getVO.getAdxIds()), Adx::getId, getVO.getAdxIds())
                .eq(Adx::getAdxStatus, AdxStatusEnum.NORMAL.getId())
                .eq(Adx::getIsDel, IsDelEnum.NORMAL.getId())
        );
    }

    @Override
    public List<TreeNodeDTO> getAdxEpTree() {
        List<TreeNodeDTO2> treeNodeDTO2s = this.baseMapper.getAdxEpTree(new QueryWrapper<Adx>()
                .orderByAsc("de.id")
                .eq("de.is_del", IsDelEnum.NORMAL.getId())
                .eq("da.is_del", IsDelEnum.NORMAL.getId())
                .eq("da.adx_status", AdxStatusEnum.NORMAL.getId())
        );
        if (treeNodeDTO2s.isEmpty()) {
            return List.of();
        }
        List<TreeNodeDTO> result = new ArrayList<>();
        Map<Long, TreeNodeDTO> adxMap = new HashMap<>();
        treeNodeDTO2s.forEach(treeNodeDTO2 -> {
            result.add(new TreeNodeDTO(treeNodeDTO2.getId(), treeNodeDTO2.getName(), treeNodeDTO2.getPId()));
            adxMap.putIfAbsent(treeNodeDTO2.getPId(), new TreeNodeDTO(treeNodeDTO2.getPId(), treeNodeDTO2.getPName(), 0L));
        });
        result.addAll(adxMap.values());
        return result;
    }

    @Override
    public List<SelectDTO> getDockingAdxSelect() {
        List<Adx> adxList = this.baseMapper.selectList(new QueryWrapper<Adx>().lambda()
                .eq(Adx::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(Adx::getIsDocking, AdxDockingEnum.DOCKING.getId())
                .orderByAsc(Adx::getId));
        return adxList.stream().map(adx ->
                new SelectDTO(adx.getId().longValue(), adx.getAdxName().split("\\(")[0].concat(adx.getAdxUtc()))
        ).collect(Collectors.toList());
    }

    @Override
    public List<SelectDTO> select(AdxSelectVO selectVO) {
        return this.baseMapper.selectList(new LambdaQueryWrapper<Adx>()
                .and(CollectionUtils.isEmpty(selectVO.getIds()), q -> q
                        .eq(Adx::getIsDel, IsDelEnum.NORMAL.getId())
                        .eq(ObjectUtils.isNotNullOrZero(selectVO.getAdxStatus()), Adx::getAdxStatus, selectVO.getAdxStatus())
                )
                .in(CollectionUtils.isNotEmpty(selectVO.getIds()), Adx::getId, selectVO.getIds())
                .orderByDesc(Adx::getId)
        ).stream().map(u -> new SelectDTO(u.getId().longValue(), u.getAdxName())).collect(Collectors.toList());
    }

    @Override
    public List<SelectDTO2> protocolSelect() {
        return this.baseMapper.selectList(new LambdaQueryWrapper<Adx>()
                .eq(Adx::getIsDel, IsDelEnum.NORMAL.getId())
                .groupBy(Adx::getProtocol)
                .orderByDesc(Adx::getProtocol)
        ).stream().map(u -> new SelectDTO2(u.getProtocol(), u.getProtocol())).collect(Collectors.toList());
    }

    @Override
    public PageUtils<?> list(AdxListVO listVO) {
        IPage<AdxListDTO> iPage = new Page<>(listVO.getPage(), listVO.getPageNum());
        iPage = this.baseMapper.list(iPage, new QueryWrapper<>()
                .eq(ObjectUtils.isNotNullOrZero(listVO.getAdxStatus()), "adx_status", listVO.getAdxStatus())
                .eq(StringUtils.isNotBlank(listVO.getProtocol()), "protocol", listVO.getProtocol())
                .and(StringUtils.isNotBlank(listVO.getSearch()), q ->
                        q.like("adx_prefix", listVO.getSearch())
                                .or().like("adx_name", listVO.getSearch())
                ).eq("is_del", IsDelEnum.NORMAL.getId())
                .orderByDesc("id")
        );
        if (iPage.getRecords().isEmpty()) {
            return new PageUtils<>(iPage);
        }
        Map<Long, TemplateSelectDTO> templateSelectMap = templateConfigMapper.getTemplateSelect(new LambdaQueryWrapper<>())
                .stream().collect(Collectors.toMap(TemplateSelectDTO::getId, Function.identity()));
        Map<Long, List<TemplateResource>> templateResourceMap = templateResourceMapper.getByResourceAndType(
                iPage.getRecords().stream().map(AdxListDTO::getId).collect(Collectors.toList()), 1
        ).stream().collect(Collectors.groupingBy(TemplateResource::getResourceId));
        iPage.getRecords().forEach(adx -> {
            adx.setIsDockingName(ICommonEnum.getNameById(adx.getIsDocking(), AdxDockingEnum.class));
            adx.setAdvertUploadName(ICommonEnum.getNameById(adx.getAdvertUpload(), AdxUploadEnum.class));
            adx.setMasterUploadName(ICommonEnum.getNameById(adx.getMasterUpload(), AdxUploadEnum.class));
            adx.setAdxTemplates(templateResourceMap.getOrDefault(adx.getId(), new ArrayList<>())
                    .stream().map(u -> templateSelectMap.getOrDefault(u.getTemplateId(), null))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList())
            );
        });
        return new PageUtils<>(iPage);
    }

    @Override
    public void save(AdxSaveVO saveVO, Integer operateUid) {
        this.checkAdx(saveVO.getAdxName(), saveVO.getAdxPrefix(), null);
        Adx adx = new Adx();
        BeanUtils.copyProperties(saveVO, adx);
        adx.setDockingOn("");
        adx.setDefendOn("");
        adx.setPutOn("");
        adx.setCreateUid(operateUid);
        this.setAdxDealType(saveVO.getDealType(), adx);
        this.baseMapper.insert(adx);
    }

    @Override
    public AdxGetDTO get(AdxGetVO getVO) {
        Adx adx = this.baseMapper.selectById(getVO.getId());
        if (null == adx || IsDelEnum.DELETE.getId().intValue() == adx.getIsDel()) {
            throw new CustomException("ADX 不存在");
        }
        AdxGetDTO adxGetDTO = new AdxGetDTO();
        BeanUtils.copyProperties(adx, adxGetDTO);
        adxGetDTO.setDealType(new ArrayList<>());
        if (adx.getRtbStatus().equals(3)) {
            adxGetDTO.getDealType().add(1);
        }
        if (adx.getPdStatus().equals(3)) {
            adxGetDTO.getDealType().add(2);
        }
        if (adx.getPdbStatus().equals(3)) {
            adxGetDTO.getDealType().add(3);
        }
        return adxGetDTO;
    }

    @Override
    public void update(AdxUpdateVO updateVO, Integer operateUid) {
        this.checkAdx(updateVO.getAdxName(), "", updateVO.getId());
        Adx adx = this.baseMapper.selectById(updateVO.getId());
        if (null == adx) {
            throw new CustomException("ADX信息不存在");
        }
        Adx update = new Adx();
        BeanUtils.copyProperties(updateVO, update);
        update.setUpdateUid(operateUid);
        this.setAdxDealType(updateVO.getDealType(), update);
        this.baseMapper.update(update, new LambdaQueryWrapper<Adx>().eq(Adx::getId, updateVO.getId())
                .eq(Adx::getIsDel, IsDelEnum.NORMAL.getId()));
    }

    @Override
    public void bind(AdxBindVO bindVO, Integer operateUid) {
        bindVO.setAdxTemplates(bindVO.getAdxTemplates().stream().distinct().collect(Collectors.toList()));
        List<TemplateResource> templateResources = this.baseMapper.selectBatchIds(bindVO.getAdxIds())
                .stream().flatMap(adx ->
                        bindVO.getAdxTemplates().stream().map(templateId -> {
                            TemplateResource templateResource = new TemplateResource();
                            templateResource.setResourceId(adx.getId().longValue());
                            templateResource.setResourceType(1);
                            templateResource.setTemplateId(templateId);
                            templateResource.setCreateUid(operateUid);
                            return templateResource;
                        })
                ).collect(Collectors.toList());
        if (!templateResources.isEmpty()) {
            this.templateResourceMapper.batchInsert(templateResources);
        }
    }

    @Override
    public void unbind(AdxUnbindVO unbindVO, Integer operateUid) {
        Adx adx = this.baseMapper.selectById(unbindVO.getAdxId());
        if (null == adx) {
            throw new CustomException("ADX信息不存在");
        }
        String planIds = planMapper.listAdxDirect(new QueryWrapper<>()
                        .in("p.template_id", unbindVO.getAdxTemplates())
                        .eq("p.is_del", IsDelEnum.NORMAL.getId())
                        .eq("c.is_del", IsDelEnum.NORMAL.getId())
                        .like("direct.direct_value", unbindVO.getAdxId()), 1031)
                .stream().filter(direct -> {
                    List<Long> directValues = JSONObject.parseArray(direct.getDirectValue(), Long.class);
                    return directValues.contains(unbindVO.getAdxId());
                }).map(u -> u.getPlanId().toString()).collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(planIds)) {
            throw new CustomException("白名单减少的模板已被使用，相关计划ID：" + planIds);
        }
        TemplateResource templateResource = new TemplateResource();
        templateResource.setIsDel(IsDelEnum.DELETE.getId().intValue());
        templateResource.setUpdateUid(operateUid);
        this.templateResourceMapper.update(templateResource, new LambdaQueryWrapper<TemplateResource>()
                .eq(TemplateResource::getResourceId, unbindVO.getAdxId())
                .in(TemplateResource::getTemplateId, unbindVO.getAdxTemplates())
                .eq(TemplateResource::getResourceType, 1)
        );
    }

    @Override
    public void del(AdxDeleteVO deleteVO, Integer operateUid) {
        long count = this.epMapper.selectCount(new LambdaQueryWrapper<Ep>()
                .eq(Ep::getAdxId, deleteVO.getId())
                .eq(Ep::getIsDel, IsDelEnum.NORMAL.getId())
        );
        if (count > 0) {
            throw new CustomException("当前ADX下含有未删除EP，请删除EP后再删除ADX");
        }
        Adx del = new Adx();
        del.setIsDel(IsDelEnum.DELETE.getId());
        del.setUpdateUid(operateUid);
        this.baseMapper.update(del, new LambdaQueryWrapper<Adx>().eq(Adx::getId, deleteVO.getId()));
    }

    @Override
    public void status(AdxStatusVO statusVO, Integer operateUid) {
        Adx update = new Adx();
        update.setAdxStatus(statusVO.getAdxStatus());
        update.setUpdateUid(operateUid);
        this.baseMapper.update(update, new LambdaQueryWrapper<Adx>().eq(Adx::getId, statusVO.getId())
                .eq(Adx::getIsDel, IsDelEnum.NORMAL.getId()));

    }

    @Override
    public void checkAdxTemplate(List<Long> adxIds, List<Long> templateIds) {
//        if (CollectionUtils.isEmpty(adxIds) || CollectionUtils.isEmpty(templateIds)
//                || templateIds.stream().filter(ObjectUtils::isNotNullOrZero).count() > 1) {
//            return;
//        }
//        List<Adx> adxList = this.baseMapper.selectBatchIds(adxIds);
//        if (CollectionUtils.isEmpty(adxIds)) {
//            throw new CustomException("ADX信息不存在");
//        }
//        Map<Long, List<TemplateResource>> templateResourceMap = templateResourceMapper.getByResourceAndType(adxIds, 1)
//                .stream().collect(Collectors.groupingBy(TemplateResource::getResourceId));
//        String notAllowAdx = adxList.stream().map(adx -> {
//            List<Long> adxTemplateIds = templateResourceMap.getOrDefault(adx.getId().longValue(), new ArrayList<>())
//                    .stream().map(TemplateResource::getTemplateId).collect(Collectors.toList());
//            if (CollectionUtils.isEmpty(adxTemplateIds)) {
//                return null;
//            }
//            if (new HashSet<>(adxTemplateIds).containsAll(templateIds)) {
//                return null;
//            }
//            return adx.getAdxName();
//        }).filter(Objects::nonNull).collect(Collectors.joining(","));
//        if (StringUtils.isNotBlank(notAllowAdx)) {
//            throw new CustomException("ADX（" + notAllowAdx + "）不支持选中模板使用");
//        }
    }

    /**
     * 获取可工作的adx数据
     *
     * @return 返回数据
     */
    private List<Adx> listWorkable() {
        QueryWrapper<Adx> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("adx_status", AdxStatusEnum.NORMAL.getId())
                .eq("is_del", IsDelEnum.NORMAL.getId())
                .select("id", "adx_name")
                .orderByAsc("id");
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 校验ADX重复
     *
     * @param adxName   adx 名称
     * @param adxPrefix adx 前缀
     * @param id        原来adx id
     */
    private void checkAdx(String adxName, String adxPrefix, Long id) {
        Adx adx = this.baseMapper.selectOne(new LambdaQueryWrapper<Adx>()
                .and(q -> q.eq(Adx::getAdxName, adxName)
                        .or().eq(Adx::getAdxPrefix, adxPrefix)
                ).ne(ObjectUtils.isNotNullOrZero(id), Adx::getId, id)
                .eq(Adx::getIsDel, IsDelEnum.NORMAL.getId())
                .last("limit 1")
        );
        if (null != adx) {
            if (adx.getAdxName().equals(adxName)) {
                throw new CustomException("ADX名称已存在，请修改后再试");
            }
            if (adx.getAdxPrefix().equals(adxPrefix)) {
                throw new CustomException("ADX前缀已存在，请修改后再试");
            }
            throw new CustomException("ADX已存在，请修改后再试");
        }
    }

    /**
     * 设置 deal type
     *
     * @param dealType deal type
     * @param adx      返回数据
     */
    private void setAdxDealType(List<Integer> dealType, Adx adx) {
        if (dealType.contains(1)) {
            adx.setRtbStatus(3);
        } else {
            adx.setRtbStatus(1);
        }
        if (dealType.contains(2)) {
            adx.setPdStatus(3);
        } else {
            adx.setPdStatus(1);
        }
        if (dealType.contains(3)) {
            adx.setPdbStatus(3);
        } else {
            adx.setPdbStatus(1);
        }
    }
}
