package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.global.iop.api.IopClient;
import com.global.iop.api.IopClientImpl;
import com.global.iop.api.IopRequest;
import com.global.iop.api.IopResponse;
import com.global.iop.domain.Protocol;
import com.global.iop.util.ApiException;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.common.FFmpegDTO;
import com.overseas.common.enums.CpsProjectEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.enums.market.productLibrary.GenerateStatusEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.DateUtils;
import com.overseas.common.utils.FFmpegUtils;
import com.overseas.common.utils.Md5CalculateUtils;
import com.overseas.common.utils.UploadUtils;
import com.overseas.common.vo.monitor.put.NoticeDevelopVO;
import com.overseas.service.market.dto.ae.*;
import com.overseas.service.market.dto.trackingGroupUrl.TrackingGroupUrlDTO;
import com.overseas.service.market.entity.CpsOrder;
import com.overseas.service.market.entity.ae.AeApp;
import com.overseas.service.market.entity.ae.AeAuthorization;
import com.overseas.service.market.entity.ae.AeProduct;
import com.overseas.service.market.entity.cps.*;
import com.overseas.service.market.entity.productLibrary.ProductLibraryResource;
import com.overseas.service.market.entity.tracking.TrackingGroupProduct;
import com.overseas.service.market.entity.tracking.TrackingGroupUrl;
import com.overseas.service.market.enums.cps.*;
import com.overseas.service.market.enums.trackingGroup.TrackingProductStatusEnum;
import com.overseas.service.market.feign.FgMonitorService;
import com.overseas.service.market.mapper.CpsOrderMapper;
import com.overseas.service.market.mapper.ae.AeAppMapper;
import com.overseas.service.market.mapper.ae.AeAuthorizationMapper;
import com.overseas.service.market.mapper.ae.AeProductMapper;
import com.overseas.service.market.mapper.cps.*;
import com.overseas.service.market.mapper.productLibrary.ProductLibraryResourceMapper;
import com.overseas.service.market.mapper.tracking.TrackingGroupProductMapper;
import com.overseas.service.market.mapper.tracking.TrackingGroupUrlMapper;
import com.overseas.service.market.service.AeAuthorizationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class AeAuthorizationServiceImpl extends ServiceImpl<AeAuthorizationMapper, AeAuthorization>
        implements AeAuthorizationService {

    public final String url = "https://api-sg.aliexpress.com";

    private final Integer aeProjectId = 25;

    private final FgMonitorService fgMonitorService;

    private final AeAppMapper aeAppMapper;

    private final CpsDpaUrlMapper cpsDpaUrlMapper;

    private final AeProductMapper aeProductMapper;

    private final CpsOrderMapper cpsOrderMapper;

    private final CpsProductMapper cpsProductMapper;

    private final CpsProductAeMapper cpsProductAeMapper;

    private final CpsProductCategoryMapper cpsProductCategoryMapper;

    private final CpsProductMaterialMapper cpsProductMaterialMapper;

    private final ProductLibraryResourceMapper productLibraryResourceMapper;

    private final TrackingGroupUrlMapper trackingGroupUrlMapper;

    private final TrackingGroupProductMapper trackingGroupProductMapper;

    @Override
    public void saveAeAuthorization(String code, String appKey) {
        log.info("save authorization, code : {}, app key : {}", code, appKey);
        IopRequest request = new IopRequest();
        request.setApiName("/auth/token/create");
        request.addApiParameter("code", code);
        request.addApiParameter("uuid", "");
        AeApp aeApp = this.aeAppMapper.selectOne(new QueryWrapper<AeApp>().lambda().eq(AeApp::getAppKey, appKey));
        if (null == aeApp) {
            log.info("ae app key : {} is not exist", appKey);
            throw new CustomException("ae app key : " + appKey + " is not exist");
        } else {
            String result = this.getRequestResult(appKey, aeApp.getAppSecret(), request, Protocol.GOP);
            AeAuthorizationDTO aeAuthorizationDTO = this.formatAeResult(result, new TypeReference<>() {
            });
            if (null != aeAuthorizationDTO) {

                AeAuthorization aeAuthorizationInDb = this.baseMapper.selectOne(new QueryWrapper<AeAuthorization>()
                        .lambda().eq(AeAuthorization::getAppId, aeApp.getId()));
                if (null == aeAuthorizationInDb) {
                    AeAuthorization aeAuthorization = new AeAuthorization();
                    BeanUtils.copyProperties(aeAuthorizationDTO, aeAuthorization);
                    aeAuthorization.setAppId(aeApp.getId());
                    this.baseMapper.insert(aeAuthorization);
                    log.info("save authorization success, id : {}", aeAuthorization.getId());
                } else {
                    BeanUtils.copyProperties(aeAuthorizationDTO, aeAuthorizationInDb);
                    this.baseMapper.updateById(aeAuthorizationInDb);
                    log.info("save authorization success, id : {}", aeAuthorizationInDb.getId());
                }
            }
        }
    }

    @Override
    public void refreshToken() {
        List<AeAuthorization> aeAuthorizationList = this.baseMapper.selectList(new LambdaQueryWrapper<AeAuthorization>()
                .eq(AeAuthorization::getProjectId, CpsProjectEnum.AE.getId())
        );
        log.info("start refresh token : {}", aeAuthorizationList.size());
        aeAuthorizationList.forEach(aeAuthorization -> {
            log.info("authorization id : {}", aeAuthorization.getId());
            IopRequest request = new IopRequest();
            request.setApiName("/auth/token/refresh");
            request.addApiParameter("refresh_token", aeAuthorization.getRefreshToken());
            // token有效期不足一小时，刷新
            if (aeAuthorization.getExpireTime() <= (DateUtils.date2Long(new Date()) + 3600) * 1000) {
                AeApp aeApp = this.aeAppMapper.selectOne(new QueryWrapper<AeApp>().lambda()
                        .eq(AeApp::getId, aeAuthorization.getAppId())
                );
                AeAuthorizationDTO aeAuthorizationDTO = this.formatAeResult(
                        this.getRequestResult(aeApp.getAppKey(), aeApp.getAppSecret(), request, Protocol.GOP),
                        new TypeReference<>() {
                        }
                );
                if (null != aeAuthorizationDTO) {
                    BeanUtils.copyProperties(aeAuthorizationDTO, aeAuthorization);
                    this.baseMapper.updateById(aeAuthorization);
                }
            }
        });
    }

    @Override
    public List<SelectDTO> selectFirstCategory() {
        return this.cpsProductCategoryMapper.selectFirstCategory(new QueryWrapper<CpsProductCategory>()
                .lambda().eq(CpsProductCategory::getParentId, 0));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pullProduct(CpsProductTypeEnum typeEnum, String categoryId, String language, boolean addOnly) {
        this.pullProduct(typeEnum, categoryId, language, addOnly, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pullProduct(CpsProductTypeEnum typeEnum, String categoryId, String language, boolean addOnly,
                            List<String> searchProductIds) {
        List<AeAppAuthorizationDTO> appAuthorizations = this.baseMapper.listAppAuthorization(
                new QueryWrapper<AeAuthorization>()
                .eq("app.project_id", CpsProjectEnum.AE.getId())
        );
        log.info("pull ae product, app count : {}", appAuthorizations.size());
        appAuthorizations.forEach(appAuthorization -> {
            log.info("pull ae product, authorization id : {}", appAuthorization.getId());
            Integer pageNum = 1;
            Integer pageSize = 20;
            boolean flag = true;
            while (flag && (pageNum * pageSize) <= 1000) {
                log.info("pull ae product, type:{}, category id:{}, language:{}, page:{}", typeEnum.getName(),
                        categoryId, language, pageNum);
                // 如果是商品详情，先获取商品ID集合
                List<String> productIds = null;
                if (CpsProductTypeEnum.PRODUCT_DETAIL.equals(typeEnum)) {
                    if (CollectionUtils.isNotEmpty(searchProductIds)) {
                        productIds = searchProductIds;
                    } else {
                        productIds = this.getProductIdsFromOrder();
                    }
                    // 指定商品ID和订单商品时，直接只查询一次
                    flag = false;
                }
                AeProductPageResultDTO aeProductPageResultDTO = this.requestProductByPage(typeEnum, categoryId,
                        language, pageNum, pageSize, StringUtils.join(productIds, ","), appAuthorization);
                List<String> existProductIds = List.of();
                if (null != aeProductPageResultDTO) {
                    pageNum++;
                    if (aeProductPageResultDTO.getCurrentRecordCount() < pageSize) {
                        flag = false;
                    }
                    if (aeProductPageResultDTO.getCurrentRecordCount() != 0) {
                        log.info("pull ae product, type:{}, category id:{}, language:{}, page:{}, count:{}",
                                typeEnum.getName(), categoryId, language, pageNum,
                                aeProductPageResultDTO.getProducts().getProduct().size());
                        aeProductPageResultDTO.getProducts().getProduct().forEach(productDTO -> {
                            log.info("pull ae product, product info : {}", JSONObject.toJSONString(productDTO));
                            this.saveAeProduct(productDTO, typeEnum, language, addOnly);
                        });
                        existProductIds = aeProductPageResultDTO.getProducts().getProduct().stream()
                                .map(AeProductDTO::getProductSign).collect(Collectors.toList());
                    }
                } else {
                    flag = false;
                }
                if (CpsProductTypeEnum.PRODUCT_DETAIL.equals(typeEnum)) {
                    this.updateProductStatus(productIds, existProductIds);
                }
            }
        });
    }

    @Override
    public void downloadMaterial() {
        List<CpsProductMaterial> materials = this.cpsProductMaterialMapper.selectList(
                new QueryWrapper<CpsProductMaterial>().lambda()
                        .eq(CpsProductMaterial::getIsDownload, CpsMaterialDownloadStatusEnum.WAITING.getId())
                        .last("LIMIT 20")
        );
        materials.forEach(material -> {
            String materialPath = UploadUtils.download("cpsProductMaterial", material.getMaterialUrl(),
                    material.getMaterialMd5());
            if (null != materialPath) {
                material.setMaterialPath(materialPath);
                try {
                    FFmpegDTO fFmpegDTO = FFmpegUtils.info(UploadUtils.getUploadPath(materialPath), materialPath);
                    material.setMaterialHeight(fFmpegDTO.getHeight());
                    material.setMaterialWidth(fFmpegDTO.getWidth());
                    material.setMaterialDuration(fFmpegDTO.getDuration());
                    material.setMaterialSize(fFmpegDTO.getSize());
                    material.setIsDownload(CpsMaterialDownloadStatusEnum.FINISHED.getId());
                } catch (IOException e) {
                    log.info("get cps material info error : {}", e.getMessage());
                    material.setIsDownload(CpsMaterialDownloadStatusEnum.FAILED.getId());
                }
            } else {
                material.setIsDownload(CpsMaterialDownloadStatusEnum.FAILED.getId());
            }
            this.cpsProductMaterialMapper.updateById(material);
        });
    }

    @Override
    public void checkProduct() {
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            futures.add(CompletableFuture.runAsync(this::checkAeProduct));
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    public List<AeApp> getApps() {
        // 获取要生成的APP
        return this.aeAppMapper.selectList(new QueryWrapper<AeApp>()
                .lambda().in(AeApp::getId, List.of(6)));
    }

    /**
     * 生成dpa投放用的cps链接
     */
    @Override
    public void generateDpaCpsUrl(List<AeApp> aeApps) {
        List<CpsProduct> cpsProducts = this.listWaitGenerateDpaUrlProduct();
        List<List<CpsProduct>> cpsProductGroup = this.splitList(cpsProducts, 20);

        List<CompletableFuture<Void>> futures = new ArrayList<>();
        cpsProductGroup.forEach( list -> futures.add(
                CompletableFuture.runAsync(() -> this.generateDpaCpsUrl(aeApps, list))
        ));
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    @Override
    public void generateProductLibraryCpsUrl(List<AeApp> aeApps) {
        List<ProductLibraryResource> resources = this.productLibraryResourceMapper.selectList(
                new QueryWrapper<ProductLibraryResource>().lambda()
                        .in(ProductLibraryResource::getGenerateStatus,
                                List.of(GenerateStatusEnum.WAITING.getId(),GenerateStatusEnum.QUERY_PRODUCT.getId(), 11))
        );
        // 按照状态对资源进行分组
        Map<Integer, List<ProductLibraryResource>> resourceMap = resources.stream()
                .collect(Collectors.groupingBy(ProductLibraryResource::getGenerateStatus));
        this.insertAeProduct(resourceMap.getOrDefault(GenerateStatusEnum.WAITING.getId(), List.of()));
        this.generateProductLibraryResourceUrl(aeApps,
                resourceMap.getOrDefault(GenerateStatusEnum.QUERY_PRODUCT.getId(), List.of()));

        // 更新商品库可用商品
        this.productLibraryResourceMapper.updateLibraryAvailableNumber();
    }

    @Override
    public void generateTrackingGroupCpsUrl() {
        QueryWrapper<TrackingGroupUrl> queryWrapper = new QueryWrapper<>();
        // 生成时间戳为三天前
        long currentTime = System.currentTimeMillis() / 1000;
        Long time = currentTime - (3600 * 24 * 3);
        // 未删除状态的商品和链接，生成时间为0（新链接）或者生成时间超过三天
        queryWrapper.and(p->p.eq("mtgu.generate_time", 0)
                        .or().lt("mtgu.generate_time", time))
                .eq("mtgp.is_del", IsDelEnum.NORMAL.getId())
                .eq("mtgu.is_del", IsDelEnum.NORMAL.getId());
        Map<Long, Integer> trackingGroupProductStatus = new HashMap<>();
        // 获取需要生成链接的记录
        List<TrackingGroupUrlDTO> trackingGroupUrlS = this.trackingGroupUrlMapper.listTrackingGroupUrl(queryWrapper);
        trackingGroupUrlS.forEach(trackingGroupUrlDTO -> {
            // 调用接口生成cps链接
            String cpsUlr = this.getAeProductCpsUrl(trackingGroupUrlDTO.getAppKey(),
                    trackingGroupUrlDTO.getAppSecret(), trackingGroupUrlDTO.getTrackingIdName(),
                    trackingGroupUrlDTO.getProductUrl());
            TrackingGroupUrl trackingGroupUrl = new TrackingGroupUrl();
            trackingGroupUrl.setId(trackingGroupUrlDTO.getId());
            trackingGroupUrl.setCpsUrl(this.addMarco(cpsUlr));
            trackingGroupUrl.setUrlType(trackingGroupUrlDTO.getUrlType());
            trackingGroupUrl.setGenerateTime(currentTime);
            // 更新记录信息
            this.trackingGroupUrlMapper.updateById(trackingGroupUrl);
            trackingGroupProductStatus.put(trackingGroupUrlDTO.getGroupProductId(),
                    StringUtils.isNotBlank(cpsUlr)
                            ? TrackingProductStatusEnum.NORMAL.getId()
                            : TrackingProductStatusEnum.DISABLED.getId());
        });
        // 更新tracking组关联的商品状态
        trackingGroupProductStatus.forEach((groupProductId, status) -> {
            TrackingGroupProduct trackingGroupProduct = new TrackingGroupProduct();
            trackingGroupProduct.setId(groupProductId);
            trackingGroupProduct.setProductStatus(status);
            this.trackingGroupProductMapper.updateById(trackingGroupProduct);
        });
    }

    private void checkAeProduct() {
        List<AeProduct> aeProducts = this.aeProductMapper.selectList(new QueryWrapper<AeProduct>()
                .lambda().eq(AeProduct::getProductStatus, AeProductStatusEnum.WAITING.getId())
                .last("LIMIT 19")
        );
        if (aeProducts.isEmpty()) {
            return;
        }

        // 获取商品ID集合
        List<Long> productIds = aeProducts.stream().map(AeProduct::getProductId)
                .collect(Collectors.toList());

        // 设置为处理中
        AeProduct aeProduct = new AeProduct();
        aeProduct.setProductStatus(AeProductStatusEnum.RUNNING.getId());
        int update = this.aeProductMapper.update(aeProduct, new UpdateWrapper<AeProduct>().lambda()
                .in(AeProduct::getProductId, productIds));
        log.info("check ae product start : {}", update);
        if (update > 0) {
            this.pullProduct(CpsProductTypeEnum.PRODUCT_DETAIL, null, null, true,
                productIds.stream().map(Object::toString).collect(Collectors.toList()));
        }

        // 设置为已处理
        aeProduct.setProductStatus(AeProductStatusEnum.FINISHED.getId());
        int checkFinish = this.aeProductMapper.update(aeProduct, new UpdateWrapper<AeProduct>().lambda()
                .in(AeProduct::getProductId, productIds));
        log.info("check ae product finish : {}", checkFinish);
    }

    private List<CpsProduct> listWaitGenerateDpaUrlProduct() {
        List<AeProduct> aeProducts = this.aeProductMapper.selectList(new QueryWrapper<AeProduct>().lambda()
                .eq(AeProduct::getProductStatus, AeProductStatusEnum.FINISHED.getId())
                .last("LIMIT 400"));
        if (aeProducts.isEmpty()) {
            return List.of();
        }
        List<CpsProduct> products = this.cpsProductMapper.selectList(new QueryWrapper<CpsProduct>().lambda()
                .eq(CpsProduct::getProjectId, CpsProjectEnum.AE.getId())
                .in(CpsProduct::getProductSign, aeProducts.stream().map(AeProduct::getProductId)
                        .collect(Collectors.toList()))
                .last("LIMIT 200")
        );
        AeProduct aeProduct = new AeProduct();
        aeProduct.setProductStatus(AeProductStatusEnum.GENERATE.getId());
        int update = this.aeProductMapper.update(aeProduct, new UpdateWrapper<AeProduct>().lambda().in(AeProduct::getId, aeProducts
                .stream().map(AeProduct::getId).collect(Collectors.toList())));
        return update > 0 ? products : List.of();
    }

    private <T> List<List<T>> splitList(List<T> list, int groupSize){
        int length = list.size();
        // 计算可以分成多少组
        int num = ( length + groupSize - 1 )/groupSize ;
        List<List<T>> newList = new ArrayList<>(num);
        for (int i = 0; i < num; i++) {
            // 开始位置
            int fromIndex = i * groupSize;
            // 结束位置
            int toIndex = Math.min((i + 1) * groupSize, length);
            newList.add(list.subList(fromIndex,toIndex)) ;
        }
        return  newList ;
    }

    private void generateDpaCpsUrl(List<AeApp> aeApps, List<CpsProduct> cpsProducts) {
        cpsProducts.forEach( cpsProduct -> aeApps.forEach(aeApp -> {
            String cpsUrl = this.generateAeProductCpsUrl(aeApp, cpsProduct.getProductDetailUrl());
            CpsDpaUrl cpsDpaUrl = new CpsDpaUrl();
            cpsDpaUrl.setCpsAppId(aeApp.getId());
            cpsDpaUrl.setProductId(cpsProduct.getId());
            cpsDpaUrl.setGenerateStatus(StringUtils.isNotBlank(cpsUrl) ? 1 : 0);
            cpsDpaUrl.setCpsUrl(this.addMarco(cpsUrl));
            // 插入合成结果
            this.cpsDpaUrlMapper.insert(cpsDpaUrl);
        }));
    }

    private String generateAeProductCpsUrl(AeApp aeApp, String productUrl) {
        return this.getAeProductCpsUrl(aeApp.getAppKey(), aeApp.getAppSecret(), "default", productUrl);
    }

    private String getAeProductCpsUrl(String appKey, String appSecret, String trackingId, String productUrl) {
        IopRequest request = new IopRequest();
        request.setApiName("aliexpress.affiliate.link.generate");
        request.addApiParameter("promotion_link_type", "0");
        request.addApiParameter("source_values", productUrl);
        request.addApiParameter("tracking_id", trackingId);
        String result = this.getRequestResult(appKey, appSecret, request, Protocol.TOP);
        AeLinkGenerateBodyDTO response = this.formatAeResult(result, new TypeReference<>() {});
        if (null != response && response.getResponse().getRespResult().getRespCode() == 200) {
            return response.getResponse().getRespResult().getResult().getPromotionLinks()
                    .getPromotionLink().get(0).getPromotionLink();
        } else {
            return "";
        }
    }

    private String addMarco(String cpsUrl) {
        cpsUrl += cpsUrl.contains("?") ? "&" : "?";
        cpsUrl += "af=__SUB_CHANNEL__&cn=__OID__&cv=__ADID__&dp=__UNIQUEID__";
        return cpsUrl;
    }

    private AeProductPageResultDTO requestProductByPage(CpsProductTypeEnum typeEnum, String categoryId,
                                                        String language, Integer pageNum, Integer pageSize,
                                                        String productIds, AeAppAuthorizationDTO appAuthorization) {
        IopRequest request = new IopRequest();
        request.setApiName(typeEnum.getApiUrl());
        if (StringUtils.isNotBlank(categoryId)) {
            request.addApiParameter("category_id", categoryId);
        }
        if (StringUtils.isNotBlank(language)) {
            request.addApiParameter("target_language", language);
        }
        if (StringUtils.isNotBlank(productIds)) {
            request.addApiParameter("product_ids", productIds);
        }
        request.addApiParameter("page_no", pageNum.toString());
        request.addApiParameter("page_size", pageSize.toString());
        String productResult = this.getRequestResult(appAuthorization.getAppKey(), appAuthorization.getAppSecret(),
                request, Protocol.TOP);
        Map<String, Object> response = JSONObject.parseObject(productResult);
        Object respResult = response.get(typeEnum.getResponseKey());
        if (null != respResult) {
            try {
                AeResultDTO<AeRespResultDTO<AeProductPageResultDTO>> productPageData = JSONObject.parseObject(
                        JSONObject.toJSONString(respResult), new TypeReference<>() {
                        });
                if (null != productPageData.getRespResult().getRespCode()
                        && productPageData.getRespResult().getRespCode().equals(200)) {
                    return productPageData.getRespResult().getResult();
                }
            } catch (Exception e) {
                fgMonitorService.noticeDevelop(NoticeDevelopVO.builder()
                        .message("parse product page result error :" + e.getMessage()).build()
                );
                log.info("pull ae product, parse product page result error : {}", e.getMessage());
            }
        } else {
//            fgMonitorService.noticeDevelop(NoticeDevelopVO.builder().message("pull ae product error").build());
            log.info("pull ae product error, params : {}", JSONObject.toJSONString(request.getApiParams()));
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveAeProduct(AeProductDTO aeProductDTO, CpsProductTypeEnum typeEnum, String language,
                              boolean addOnly) {
        CpsProduct product = new CpsProduct();
        CpsProductAe productAe = new CpsProductAe();
        this.convertProductInfo(aeProductDTO, product, productAe);
        product.setProductType(typeEnum.getId());
        product.setProductLanguage(language);

        try {
            CpsProduct productInDb = this.cpsProductMapper.selectOne(new QueryWrapper<CpsProduct>().lambda()
                    .eq(CpsProduct::getProjectId, this.aeProjectId)
                    .eq(CpsProduct::getProductSign, aeProductDTO.getProductSign())
            );
            Long productId;
            // 插入或更新主表及特有信息
            if (null == productInDb) {
                // 插入
                this.cpsProductMapper.insert(product);
                productId = product.getId();

                // 插入ae信息表数据
                productAe.setProductId(productId);
                this.cpsProductAeMapper.insert(productAe);
                this.saveOrUpdateMaterial(productId, aeProductDTO);
            } else {
                if (!addOnly) {
                    // 更新
                    BeanUtils.copyProperties(product, productInDb, "id");
                    productId = productInDb.getId();
                    this.cpsProductMapper.updateById(productInDb);
                    // 更新ae信息表数据
                    this.cpsProductAeMapper.update(productAe,
                            new QueryWrapper<CpsProductAe>().lambda().eq(CpsProductAe::getProductId, productId));
                    this.saveOrUpdateMaterial(productId, aeProductDTO);
                }
            }
        } catch (Exception e) {
            log.info("add product error : {}", e.getMessage());
        }
    }

    private List<String> getProductIdsFromOrder() {
        return this.cpsOrderMapper.getProductIdsFromOrder();
    }

    private void updateProductStatus(List<String> productIds, List<String> existProductIds) {
        CpsOrder updateOrder = new CpsOrder();
        updateOrder.setProductStatus(CpsProductStatusEnum.NOT_EXIST.getId());
        if (!productIds.isEmpty()) {
            this.cpsOrderMapper.update(updateOrder,
                    new UpdateWrapper<CpsOrder>().lambda().in(CpsOrder::getProductId, productIds));
        }
        updateOrder.setProductStatus(CpsProductStatusEnum.EXIST.getId());
        if (!existProductIds.isEmpty()) {
            this.cpsOrderMapper.update(updateOrder,
                    new UpdateWrapper<CpsOrder>().lambda().in(CpsOrder::getProductId, existProductIds));
        }
    }

    private void saveOrUpdateMaterial(Long productId, AeProductDTO aeProductDTO) {
        List<CpsProductMaterial> materials = this.getAeProductMaterial(productId, aeProductDTO);
        this.cpsProductMaterialMapper.batchInsertMaterial(materials);
    }

    /**
     * 从ae商品信息中获取主图和小图素材信息
     *
     * @param productId    商品ID
     * @param aeProductDTO ae返回商品信息
     * @return 素材集合
     */
    private List<CpsProductMaterial> getAeProductMaterial(Long productId, AeProductDTO aeProductDTO) {
        Map<String, CpsProductMaterial> materials = new HashMap<>();
        List<String> smallImageUrls = aeProductDTO.getProductSmallImageUrls().containsKey("product_small_image_url")
                ? aeProductDTO.getProductSmallImageUrls().getOrDefault("product_small_image_url", List.of())
                : aeProductDTO.getProductSmallImageUrls().getOrDefault("product_small_image_urls", List.of());
        if (smallImageUrls.isEmpty() && aeProductDTO.getProductSmallImageUrls().containsKey("string")) {
            smallImageUrls = aeProductDTO.getProductSmallImageUrls().getOrDefault("string", List.of());
        }

        CpsProductMaterial smallImageMaterial = new CpsProductMaterial();
        for (int i = 0; i < smallImageUrls.size(); i++) {
            String smallImageUrl = smallImageUrls.get(i);
            smallImageMaterial.setProductId(productId);
            smallImageMaterial.setMaterialMd5(Md5CalculateUtils.getUrlMD5(smallImageUrl));
            smallImageMaterial.setMaterialUrl(smallImageUrl);
            smallImageMaterial.setMaterialType(i > 0
                    ? CpsMaterialTypeEnum.PRODUCT_SMALL.getId()
                    : CpsMaterialTypeEnum.PRODUCT_MAIN.getId());
            materials.put(smallImageMaterial.getMaterialMd5(), smallImageMaterial);
        }
        return new ArrayList<>(materials.values());
    }

    private void convertProductInfo(AeProductDTO aeProductDTO, CpsProduct product, CpsProductAe productAe) {
        BeanUtils.copyProperties(aeProductDTO, product);
        BeanUtils.copyProperties(aeProductDTO, productAe);
        product.setProjectId(this.aeProjectId);
        // discount commission_rate hot_product_commission_rate
        product.setProductDiscount(
                new BigDecimal(aeProductDTO.getProductDiscount().replace("%", "")));
        productAe.setCommissionRate(
                Double.valueOf(aeProductDTO.getCommissionRate().replace("%", "")));
        productAe.setHotProductCommissionRate(
                Double.valueOf(aeProductDTO.getHotProductCommissionRate().replace("%", "")));
    }

    /**
     * 基于appKey和appSecret请求ae接口
     *
     * @param appKey     appKey
     * @param appSecret  appSecret
     * @param iopRequest 请求对象内容
     * @param protocol   接口协议，新接口：Protocol.GOP，老接口：Protocol.TOP
     * @return 请求结果字符串
     */
    private String getRequestResult(String appKey, String appSecret, IopRequest iopRequest, Protocol protocol) {
        try {
            IopClient iopClient = new IopClientImpl(url, appKey, appSecret);
            IopResponse response = iopClient.execute(iopRequest, protocol);
            return response.getBody();
        } catch (ApiException apiException) {
            log.info("ae api result error : {}", apiException.getErrorMessage());
            throw new CustomException("接口调用异常");
        }
    }

    /**
     * 格式化返回数据
     *
     * @param result 结果对象json串
     * @param clazz  要格式化的对象类
     * @param <T>    对象类
     * @return 结果集
     */
    private <T> T formatAeResult(String result, TypeReference<T> clazz) {
        try {
            log.debug("format ae result info :{}", result);
            return JSONObject.parseObject(result, clazz);
        } catch (Exception e) {
            log.info("format ae result error : {}", result);
            return null;
        }
    }

    /**
     * 插入商品到AE product表中等待查询商品信息
     * @param resources 商品库资源信息
     */
    private void insertAeProduct(List<ProductLibraryResource> resources) {
        Map<String, AeProduct> aeProducts = new HashMap<>();
        List<Long> successIds = new ArrayList<>();
        List<Long> errorIds = new ArrayList<>();
        resources.forEach(resource -> {
            if (StringUtils.isNumeric(resource.getProductSign())) {
                AeProduct aeProduct = new AeProduct();
                aeProduct.setProductId(Long.valueOf(resource.getProductSign()));
                aeProducts.put(resource.getProductSign(), aeProduct);
                successIds.add(resource.getId());
            } else {
                errorIds.add(resource.getId());
            }
        });
        ProductLibraryResource temp = new ProductLibraryResource();
        if (!errorIds.isEmpty()) {  // productSign解析异常的更新状态
            temp.setGenerateStatus(GenerateStatusEnum.ERROR_SIGN.getId());
            this.productLibraryResourceMapper.update(temp, new UpdateWrapper<ProductLibraryResource>()
                    .lambda().in(ProductLibraryResource::getId, errorIds));
        }
        if (!successIds.isEmpty()) {
            this.aeProductMapper.batchSaveAeProduct(new ArrayList<>(aeProducts.values()));
            temp.setGenerateStatus(GenerateStatusEnum.QUERY_PRODUCT.getId());
            this.productLibraryResourceMapper.update(temp, new UpdateWrapper<ProductLibraryResource>()
                    .lambda().in(ProductLibraryResource::getId, successIds));
        }
    }

    /**
     * 生成商品库中商品cps链接
     * @param resources 商品库资源信息
     */
    private void generateProductLibraryResourceUrl(List<AeApp> aeApps, List<ProductLibraryResource> resources) {

        if (!resources.isEmpty()) {
            // 查找已处理和已生成的链接，都是可查询到信息的商品
            List<AeProduct> aeProducts = this.aeProductMapper.selectList(new QueryWrapper<AeProduct>().lambda()
                    .in(AeProduct::getProductId,
                            resources.stream().map(ProductLibraryResource::getProductSign).collect(Collectors.toList()))
                    .in(AeProduct::getProductStatus,
                            List.of(AeProductStatusEnum.FINISHED.getId(), AeProductStatusEnum.GENERATE.getId()))
            );
            if (!aeProducts.isEmpty()) {
                List<CpsProduct> cpsProducts = this.cpsProductMapper.selectList(new QueryWrapper<CpsProduct>().lambda()
                        .eq(CpsProduct::getProjectId, CpsProjectEnum.AE.getId())
                        .in(CpsProduct::getProductSign, aeProducts.stream().map(AeProduct::getProductId)
                                .collect(Collectors.toList()))
                );
                // 查找到商品信息
                if (!cpsProducts.isEmpty()) {
                    Map<String, CpsProduct> cpsProductMap = cpsProducts.stream()
                            .collect(Collectors.toMap(CpsProduct::getProductSign, Function.identity()));
                    resources.forEach( resource -> aeApps.forEach(aeApp -> {
                        CpsProduct tempProduct = cpsProductMap.get(resource.getProductSign());
                        if (null != tempProduct) {
                            String cpsUrl = this.generateAeProductCpsUrl(aeApp, tempProduct.getProductDetailUrl());
                            resource.setProductId(tempProduct.getId());
                            if (!StringUtils.isNotEmpty(cpsUrl)) {
                                resource.setGenerateStatus(GenerateStatusEnum.FAIL.getId());
                            } else {
                                resource.setCpsUrl(this.addMarco(cpsUrl));
                                resource.setGenerateStatus(GenerateStatusEnum.SUCCESS.getId());
                            }
                            this.productLibraryResourceMapper.updateById(resource);
                        }
                    }));
                }
            }
        }
    }
}
