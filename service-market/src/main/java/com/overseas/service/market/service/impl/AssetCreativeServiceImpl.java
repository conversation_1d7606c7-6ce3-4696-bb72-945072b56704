package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.overseas.common.dto.market.asset.AssetDTO;
import com.overseas.common.dto.market.assetCreative.AssetCreativeLogListDTO;
import com.overseas.common.dto.market.copyWriting.CopyWritingListDTO;
import com.overseas.common.dto.market.creativeUnit.CreativeUnitAssetDTO;
import com.overseas.common.dto.market.textLibrary.TextLibraryInfoListDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.enums.market.asset.AssetCreativeUpdateTypeEnum;
import com.overseas.common.enums.market.asset.AssetTypeEnum;
import com.overseas.common.enums.market.copyWriting.CopyWritingTypeEnum;
import com.overseas.common.enums.market.plan.PlanStatusEnum;
import com.overseas.common.enums.market.textLibrary.TextLibraryStatusEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.utils.redis.RedisUtils;
import com.overseas.common.vo.market.assetCreative.AssetCreativeLogListVO;
import com.overseas.common.vo.market.creative.CreativeUpdateAssetWaitCountVO;
import com.overseas.common.vo.market.material.MaterialByIdVO;
import com.overseas.common.vo.market.material.MaterialVO;
import com.overseas.service.market.dto.creative.CreativeGetDTO;
import com.overseas.service.market.entity.*;
import com.overseas.service.market.enums.creative.units.CreativeUnitStatusEnum;
import com.overseas.service.market.enums.material.MaterialAssetTypeEnum;
import com.overseas.service.market.mapper.*;
import com.overseas.service.market.mapper.assetTask.TaskProductAssetMapper;
import com.overseas.service.market.service.*;
import com.overseas.service.market.vo.creative.CreativeSaveVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@Slf4j
@RequiredArgsConstructor
@Service
public class AssetCreativeServiceImpl implements AssetCreativeService {

    private final RedisUtils redisUtils;

    private final PlanMapper planMapper;

    private final AssetService assetService;

    private final CreativeService creativeService;

    private final CreativeUnitMapper creativeUnitMapper;

    private final MaterialService materialService;

    private final PlanAssetUpdateLogMapper planAssetUpdateLogMapper;

    private final TaskProductAssetMapper taskProductAssetMapper;

    private final TextLibraryMapper textLibraryMapper;

    private final TextLibraryInfoMapper textLibraryInfoMapper;

    private final TextLibraryService textLibraryService;

    private final CopyWritingMapper copyWritingMapper;

    private int CREATIVE_MAX = 20;

    @Override
    public PageUtils<?> list(AssetCreativeLogListVO listVO) {
        IPage<AssetCreativeLogListDTO> iPage = new Page<>(listVO.getPage(), listVO.getPageNum());
        iPage = this.planAssetUpdateLogMapper.list(iPage, new QueryWrapper<>()
                .eq("mpaul.is_del", IsDelEnum.NORMAL.getId())
                .eq("mpaul.plan_id", listVO.getPlanId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getUpdateType()), "mpaul.update_type", listVO.getUpdateType())
                .between(StringUtils.isNotBlank(listVO.getStartDate()) && StringUtils.isNotBlank(listVO.getEndDate()),
                        "mpaul.create_time", listVO.getStartDate() + " 00:00:00", listVO.getEndDate() + " 23:59:59")
                .eq(StringUtils.isNotBlank(listVO.getSearch()), "mpaul.creative_unit_id", listVO.getSearch())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getAssetType()) && !AssetTypeEnum.TEXT.getId().equals(listVO.getAssetType()), "ma.asset_type", listVO.getAssetType())
                .isNull(ObjectUtils.isNotNullOrZero(listVO.getAssetType()) && AssetTypeEnum.TEXT.getId().equals(listVO.getAssetType()), "ma.asset_type")
                .orderByDesc("mpaul.id")
        );
        if (CollectionUtils.isEmpty(iPage.getRecords())) {
            return new PageUtils<>(iPage);
        }
        Set<Long> assetIds = new HashSet<>();
        iPage.getRecords().forEach(u -> {
            assetIds.add(u.getTitleId());
            assetIds.add(u.getDescId());
        });
        Map<String, CopyWritingListDTO> copyWritingMap = this.copyWritingMapper.listCopyWriting(new QueryWrapper<CopyWriting>()
                .in("writing.asset_id", assetIds)
                .eq("writing.master_id", listVO.getMasterId())
        ).stream().collect(Collectors.toMap(u -> String.format("%s-%s", u.getAssetId(), u.getCopyWritingType()), Function.identity()));
        iPage.getRecords().forEach(dto -> {
            if (ObjectUtils.isNotNullOrZero(dto.getAssetId())) {
                Asset assetTmp = new Asset();
                BeanUtils.copyProperties(dto, assetTmp);
                this.assetService.formatAsset(assetTmp);
                dto.setHttpUrl(assetTmp.getHttpUrl());
                dto.setCoverImgUrl(assetTmp.getCoverImgUrl());
                dto.setAssetTypeName(ICommonEnum.getNameById(dto.getAssetType(), AssetTypeEnum.class));
            } else {
                dto.setAssetTypeName("文案");
            }
            CopyWritingListDTO title = copyWritingMap.get(String.format("%s-%s", dto.getTitleId(), CopyWritingTypeEnum.TITLE.getId()));
            if (null != title) {
                dto.setTitleText(title.getCopyWriting());
                dto.setTitleTranslatedText(title.getTranslatedText());
                dto.setCountryName(title.getCountryName());
            }
            CopyWritingListDTO desc = copyWritingMap.get(String.format("%s-%s", dto.getDescId(), CopyWritingTypeEnum.DESC.getId()));
            if (null != desc) {
                dto.setDescText(desc.getCopyWriting());
                dto.setDescTranslatedText(desc.getTranslatedText());
            } else {
                dto.setDescText("");
                dto.setDescTranslatedText("");
            }
            dto.setUpdateTypeName(ICommonEnum.getNameById(dto.getUpdateType(), AssetCreativeUpdateTypeEnum.class));
        });
        return new PageUtils<>(iPage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addCreativeByAsset(Long planId, Long monitorId) {
        String key = this.getRedisKey(planId);
        this.redisUtils.del(key);
        if (StringUtils.isNotBlank(this.redisUtils.get(key))) {
            log.info("计划：{} 添加素材脚本执行中", planId);
            return 0;
        }
        this.redisUtils.set(key, "1", 60 * 60);
        try {
            return this.addPlanCreative(planId, monitorId);
        } catch (Exception e) {
            log.error("计划：{} 添加素材出现异常，{}", planId, e.getMessage(), e);
        }
        this.redisUtils.del(key);
        return 0;
    }

    /**
     * 添加计划创意
     *
     * @param planId 计划ID
     * @return 返回计划数据
     */
    private int addPlanCreative(Long planId, Long monitorId) {
        log.info("计划：{},素材添加开始", planId);
        Plan plan = this.planMapper.selectById(planId);
        if (null == plan) {
            throw new CustomException("计划不存在");
        }
        if (!PlanStatusEnum.MARKETING.getId().equals(plan.getPlanStatus())) {
            throw new CustomException("计划不在投放中");
        }
        CreativeGetDTO creativeGetDTO = this.creativeService.getCreativeDetailByPlanId(planId, plan.getMasterId());
        int max = 0;
        if (creativeGetDTO.getAssetUpdateCount() == -1) {
            max = creativeGetDTO.getUnits().size() + creativeGetDTO.getAssetWaitCount();
        } else if (creativeGetDTO.getAssetUpdateCount() > 0) {
            max = creativeGetDTO.getAssetUpdateCount();
        }
        max = Math.min(max, CREATIVE_MAX);
        if (creativeGetDTO.getUnits().size() >= max) {
            log.info("计划：{},素材添加结束，数量已满， 当前素材数：{}, 添加上限数：{}", planId, creativeGetDTO.getUnits().size(), max);
            return 0;
        }
        if (creativeGetDTO.getIsAssetUpdate().equals(0)) {
            log.info("计划：{},素材添加结束，未开启计划更新", planId);
            return 0;
        }
        List<CreativeUnitAssetDTO> unitAssets = this.creativeUnitMapper.listCreativeUnitMaterialAsset(new QueryWrapper<CreativeUnit>()
                .eq("ma.is_del", IsDelEnum.NORMAL.getId())
                .eq("mcu.master_id", plan.getMasterId())
                .eq("mcu.plan_id", planId)
                .in("mma.field_type", List.of(1, 3, 4))
        );
        if (unitAssets.isEmpty()) {
            log.info("计划：{},素材添加结束，计划内无创意素材数据", planId);
            return 0;
        }
        //获取计划中创意单元素材尺寸
        Map<String, CreativeUnitAssetDTO> sizeMap = unitAssets.stream()
                //获取主元素，且不为删除数据
                .filter(u -> u.getIsDel().equals(IsDelEnum.NORMAL.getId().intValue()) && MaterialAssetTypeEnum.PRIMARY_ASSET.getId().equals(u.getFieldType()))
                .collect(Collectors.toMap(u -> String.format("(%s,%s)-%s", u.getWidth(), u.getHeight(), u.getAssetType()), Function.identity(),
                                //取创意状态小的，如果相同，则取创意单元ID大的创意信息
                                (o, n) ->
                                        n.getCreativeUnitStatus() < o.getCreativeUnitStatus() ? n :
                                                !n.getCreativeUnitStatus().equals(o.getCreativeUnitStatus()) ?
                                                        o : n.getCreativeUnitId() > o.getCreativeUnitId() ? n : o
                        )
                );
        //获取素材
        List<AssetDTO> assets = this.getCanUseAsset(unitAssets, creativeGetDTO, plan, sizeMap);
        if (null == assets) {
            return 0;
        }
        List<TextLibraryInfoListDTO> texts = this.getCanUseText(unitAssets, creativeGetDTO, plan);
        if (null == texts) {
            return 0;
        }
        if (CollectionUtils.isEmpty(assets) && CollectionUtils.isEmpty(texts)) {
            log.info("计划：{},素材添加结束，无可上新素材或文案数据", planId);
            return 0;
        }
        //保存创意信息
        return this.saveCreative(plan, creativeGetDTO, assets, texts, sizeMap, max, monitorId);
    }

    /**
     * 创意信息
     *
     * @param plan           计划信息
     * @param creativeGetDTO 创意信息
     * @param assets         素材
     * @param texts          文案
     * @param sizeMap        尺寸信息
     * @param max            最大上新数
     * @param monitorId      监控ID
     * @return 返回监控数据
     */
    private int saveCreative(Plan plan, CreativeGetDTO creativeGetDTO,
                             List<AssetDTO> assets, List<TextLibraryInfoListDTO> texts,
                             Map<String, CreativeUnitAssetDTO> sizeMap, int max, Long monitorId) {
        //  拼接数据
        CreativeSaveVO saveVO = new CreativeSaveVO();
        BeanUtils.copyProperties(creativeGetDTO, saveVO);
        Map<String, MaterialVO> materialMap = new HashMap<>();
        Map<String, PlanAssetUpdateLog> updateLogs = new HashMap<>();
        int maxCycle = Math.min(texts.size(), assets.size());
        if (maxCycle == 0) {
            maxCycle = Math.max(texts.size(), assets.size());
        }
        for (int i = 0; i < maxCycle; i++) {
            if (saveVO.getUnits().size() >= max) {
                continue;
            }
            AssetDTO asset = i < assets.size() ? assets.get(i) : null;
            TextLibraryInfoListDTO text = i < texts.size() ? texts.get(i) : null;
            String size = "";
            String textName = "";
            if (null != text) {
                textName = String.format("%s-%s", text.getTitleId(), text.getDescId());
            }
            try {
                MaterialVO materialVO = null;
                if (null != asset) {
                    size = String.format("(%s,%s)-%s", asset.getWidth(), asset.getHeight(), asset.getAssetType());
                    materialVO = materialMap.get(size);
                    if (null == materialVO) {
                        if (!sizeMap.containsKey(size)) {
                            log.info("size {} 在计划 {} 中不存在", size, plan.getId());
                            continue;
                        }
                        MaterialByIdVO materialByIdVO = new MaterialByIdVO();
                        materialByIdVO.setCreativeUnitId(sizeMap.get(size).getCreativeUnitId());
                        materialByIdVO.setMasterId(plan.getMasterId());
                        materialMap.put(size, this.materialService.getMaterialByUnitId(materialByIdVO));
                        materialVO = materialMap.get(size);

                    }
                } else {
                    if (CollectionUtils.isEmpty(materialMap)) {
                        materialMap.put("0", this.materialService.getMaterialByUnitIds(
                                List.of(sizeMap.values().stream().map(CreativeUnitAssetDTO::getCreativeUnitId).max(Long::compareTo).get()),
                                plan.getMasterId().longValue()).get(0));
                    }
                    if (CollectionUtils.isNotEmpty(materialMap)) {
                        materialVO = materialMap.values().stream().findFirst().get();
                    }
                }
                if (null == materialVO) {
                    log.info("size {} 文案 {} 在计划 {} 中无法获取到素材信息", size, textName, plan.getId());
                    continue;
                }
                log.debug("size {} 在计划 {} 中选中复制创意 :{}, 素材 :{} 文案 {}", size, plan.getId(), materialVO.getCreativeUnitId(), null == asset ? "" : asset.getAssetId(), textName);
                materialVO = ObjectUtils.copyOf(materialVO, new TypeReference<>() {
                });
                materialVO.setId(null);
                materialVO.setCreativeUnitId(null);
                materialVO.setCreativeUnitStatus(CreativeUnitStatusEnum.MARKETING.getId());
                materialVO.setCreativeUnitName("");
                if (null != asset) {
                    materialVO.getAssets().getAsset().setAssetId(asset.getAssetId());
                    materialVO.getAssets().getAsset().setAssetType(asset.getAssetType());
                    materialVO.getAssets().getAsset().setAssetName(asset.getAssetName());
                    materialVO.getAssets().getAsset().setIsUpload(asset.getIsUpload());
                }
                materialVO.getAssets().getAsset().setMaterialId(null);
                if (null != text) {
                    //文案
                    materialVO.getAssets().getTitle().setAssetId(text.getTitleId());
                    materialVO.getAssets().getTitle().setAssetType(AssetTypeEnum.TEXT.getId());
                    materialVO.getAssets().getTitle().setContent(text.getTitleText());
                    materialVO.getAssets().getTitle().setTranslatedText(text.getTitleTranslatedText());
                    //描述
                    materialVO.getAssets().getDescription().setAssetId(text.getDescId());
                    materialVO.getAssets().getDescription().setAssetType(AssetTypeEnum.TEXT.getId());
                    materialVO.getAssets().getDescription().setContent(text.getDescText());
                    materialVO.getAssets().getDescription().setTranslatedText(text.getDescTranslatedText());
                }
                materialVO.getAssets().getTitle().setMaterialId(null);
                materialVO.getAssets().getDescription().setMaterialId(null);
                if (null != materialVO.getAssets().getEndcard()) {
                    materialVO.getAssets().getEndcard().setMaterialId(null);
                }
                if (null != materialVO.getAssets().getConcurrent()) {
                    materialVO.getAssets().getConcurrent().setMaterialId(null);
                }
                saveVO.getUnits().add(materialVO);
                //添加日志
                PlanAssetUpdateLog updateLog = new PlanAssetUpdateLog();
                updateLog.setPlanId(plan.getId());
                //素材修改
                if (null != asset) {
                    updateLog.setAssetId(asset.getAssetId());
                } else {
                    updateLog.setAssetId(0L);
                }
                //文案修改
                if (null != text) {
                    updateLog.setTitleId(text.getTitleId());
                    updateLog.setDescId(text.getDescId());
                    updateLog.setLibraryInfoId(text.getId());
                } else {
                    updateLog.setTitleId(0L);
                    updateLog.setDescId(0L);
                    updateLog.setLibraryInfoId(0L);
                }
                //监测上新
                if (ObjectUtils.isNotNullOrZero(monitorId)) {
                    updateLog.setMonitorId(monitorId);
                    updateLog.setUpdateType(AssetCreativeUpdateTypeEnum.MONITOR_REFRESH.getId());
                }
                updateLogs.put(String.format("%s-%s-%s", materialVO.getAssets().getAsset().getAssetId(), materialVO.getAssets().getTitle().getAssetId(),
                        materialVO.getAssets().getDescription().getAssetId()), updateLog);
            } catch (Exception e) {
                log.error("计划 {} 添加素材循环中出现错误 {}", plan.getId(), e.getMessage(), e);
            }
        }
        if (updateLogs.isEmpty()) {
            log.info("计划: {}, 素材自动上新结束，无内容需要补充", plan.getId());
            return 0;
        }
        try {
            this.creativeService.saveCreative(saveVO, plan.getCreateUid());
        } catch (Exception e) {
            log.error("计划创意保存失败 {}", e.getMessage(), e);
            throw new CustomException("计划创意保存失败 {}", e.getMessage());
        }
        //记录日志
        this.creativeService.getCreativeDetailByPlanId(plan.getId(), plan.getMasterId())
                .getUnits().forEach(unit -> {
                    String key = String.format("%s-%s-%s", unit.getAssets().getAsset().getAssetId(), unit.getAssets().getTitle().getAssetId(), unit.getAssets().getDescription().getAssetId());
                    if (updateLogs.containsKey(key)) {
                        updateLogs.get(key).setCreativeUnitId(unit.getCreativeUnitId());
                    }
                });
        updateLogs.forEach((key, update) -> planAssetUpdateLogMapper.insert(update));
        //修改待上新数量
        if (creativeGetDTO.getAssetUpdateCount() == -1) {
            this.creativeService.updateAssetWaitCount(CreativeUpdateAssetWaitCountVO.builder().planId(plan.getId()).count(-updateLogs.size()).build());
        }
        log.info("计划: {},补充结束，补充了 {} 条创意", plan.getId(), updateLogs.size());
        return updateLogs.size();
    }

    /**
     * 获取可用素材
     *
     * @param unitAssets     原素材内容
     * @param creativeGetDTO 创意单元数据
     * @param plan           计划ID
     * @return 返回数据
     */
    private List<AssetDTO> getCanUseAsset(List<CreativeUnitAssetDTO> unitAssets, CreativeGetDTO creativeGetDTO, Plan plan,
                                          Map<String, CreativeUnitAssetDTO> sizeMap) {
        if (CollectionUtils.isEmpty(creativeGetDTO.getAssetTaskIds())) {
            return List.of();
        }
        List<Long> excludeAssetIds = unitAssets.stream().map(CreativeUnitAssetDTO::getAssetId).collect(Collectors.toList());
        //获取素材
        List<AssetDTO> assets = this.getAssetInfoByAssetTaskIds(creativeGetDTO.getAssetTaskIds(),
                plan.getMasterId(), excludeAssetIds, sizeMap.keySet());
        if (null == assets) {
            return List.of();
        }
        //过滤类型和尺寸不符合的素材
        assets = assets.stream().filter(asset -> {
            String size = String.format("(%s,%s)-%s", asset.getWidth(), asset.getHeight(), asset.getAssetType());
            return sizeMap.containsKey(size);
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(assets)) {
            log.info("计划：{},素材添加结束，无符合条件素材上新", plan.getId());
            return null;
        }
        return assets;
    }

    /**
     * 获取可用素材
     *
     * @param unitAssets     原素材内容
     * @param creativeGetDTO 创意单元数据
     * @param plan           计划ID
     * @return 返回数据
     */
    private List<TextLibraryInfoListDTO> getCanUseText(List<CreativeUnitAssetDTO> unitAssets, CreativeGetDTO creativeGetDTO, Plan plan) {
        if (CollectionUtils.isEmpty(creativeGetDTO.getTextLibraryIds())) {
            return List.of();
        }
        List<Long> textLibraryIds = this.textLibraryMapper.selectList(new LambdaQueryWrapper<TextLibrary>()
                .in(TextLibrary::getId, creativeGetDTO.getTextLibraryIds())
                .eq(TextLibrary::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(TextLibrary::getLibraryStatus, TextLibraryStatusEnum.OPEN.getId())
        ).stream().map(TextLibrary::getId).collect(Collectors.toList());
        if (textLibraryIds.isEmpty()) {
            log.info("计划：{},素材添加结束，无符合条件文案库上新", plan.getId());
            return null;
        }
        //获取使用文案过滤数据
        Map<Long, List<CreativeUnitAssetDTO>> textGroupMap = unitAssets.stream()
                //获取主元素，且不为删除数据
                .filter(u -> !MaterialAssetTypeEnum.PRIMARY_ASSET.getId().equals(u.getFieldType()))
                .collect(Collectors.groupingBy(CreativeUnitAssetDTO::getCreativeUnitId));
        List<String> exculudeTextList = textGroupMap.values().stream()
                .map(val -> {
                    Long titleId = 0L, descId = 0L;
                    for (CreativeUnitAssetDTO u : val) {
                        if (MaterialAssetTypeEnum.TITLE.getId().equals(u.getFieldType())) {
                            titleId = u.getAssetId();
                        }
                        if (MaterialAssetTypeEnum.DESCRIPTION.getId().equals(u.getFieldType())) {
                            descId = u.getAssetId();
                        }
                    }
                    return String.format("(%s,%s)", titleId, descId);
                }).distinct().collect(Collectors.toList());
        //获取素材
        List<TextLibraryInfoListDTO> texts = this.textLibraryInfoMapper.listInfo(new QueryWrapper<>()
                .in("library_id", creativeGetDTO.getTextLibraryIds())
                .eq("is_del", IsDelEnum.NORMAL.getId())
                .notInSql("(title_id, desc_id)", String.join(",", exculudeTextList))
                .groupBy("title_id").groupBy("desc_id")
                .orderByAsc("id")
        );
        if (CollectionUtils.isEmpty(texts)) {
            log.info("计划：{},素材添加结束，无符合条件文案库中文案上新", plan.getId());
            return null;
        }
        return this.textLibraryService.completeInfos(texts, plan.getMasterId().longValue());
    }


    /**
     * 根据素材获取素材组
     *
     * @param assetTaskIds    素材组ID
     * @param masterId        账户ID
     * @param excludeAssetIds 排除素材iD
     * @param sizes           符合尺寸ID
     * @return 返回素材数据
     */
    private List<AssetDTO> getAssetInfoByAssetTaskIds(List<Long> assetTaskIds, Integer masterId, List<Long> excludeAssetIds, Set<String> sizes) {
        if (CollectionUtils.isEmpty(assetTaskIds)) {
            return null;
        }
        return this.taskProductAssetMapper.getAssetsByWrapper(new QueryWrapper<>()
                .in("mtpa.asset_task_id", assetTaskIds)
                .eq("mtpa.is_del", IsDelEnum.NORMAL.getId())
                .eq("matp.is_del", IsDelEnum.NORMAL.getId())
                .eq("ma.is_del", IsDelEnum.NORMAL.getId())
                .eq("mar.is_del", IsDelEnum.NORMAL.getId())
                .notIn("mtpa.asset_id", excludeAssetIds)
                .inSql("(width, height)", sizes.stream().map(u -> u.split("-")[0]).collect(Collectors.joining(",")))
                .isNotNull("ma.id")
                .orderByDesc("ma.id"), masterId
        );
    }

    /**
     * 获取 redis key
     *
     * @param planId 计划ID
     * @return 返回数据
     */
    private String getRedisKey(Long planId) {
        return "add_asset_by_plan_" + planId;
    }

}
