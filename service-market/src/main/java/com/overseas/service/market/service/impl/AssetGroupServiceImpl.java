package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.assetGroup.AssetGroupListDTO;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.assetGroup.*;
import com.overseas.service.market.entity.AssetGroup;
import com.overseas.service.market.entity.AssetGroupMaster;
import com.overseas.service.market.entity.AssetGroupResource;
import com.overseas.service.market.entity.AssetResource;
import com.overseas.service.market.mapper.AssetGroupMapper;
import com.overseas.service.market.mapper.AssetGroupMasterMapper;
import com.overseas.service.market.mapper.AssetGroupResourceMapper;
import com.overseas.service.market.service.AssetGroupService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@RequiredArgsConstructor
@Slf4j
@Service
public class AssetGroupServiceImpl implements AssetGroupService {

    private final AssetGroupMapper assetGroupMapper;

    private final AssetGroupMasterMapper assetGroupMasterMapper;

    private final AssetGroupResourceMapper assetGroupResourceMapper;

    @Override
    public PageUtils<?> list(AssetGroupListVO listVO, Integer userId) {
        IPage<AssetGroupListDTO> iPage = new Page<>(listVO.getPage(), listVO.getPageNum());
        QueryWrapper<?> queryWrapper = new QueryWrapper<>()
                .like(StringUtils.isNotBlank(listVO.getSearch()), "group_name", listVO.getSearch())
                .eq("is_del", IsDelEnum.NORMAL.getId())
                .orderByDesc("id");
        if (ObjectUtils.isNotNullOrZero(listVO.getMasterId())) {
            List<Long> assetGroupIds = assetGroupMasterMapper.selectList(new LambdaQueryWrapper<AssetGroupMaster>()
                    .eq(AssetGroupMaster::getMasterId, listVO.getMasterId())
                    .eq(AssetGroupMaster::getIsDel, IsDelEnum.NORMAL.getId())
            ).stream().map(AssetGroupMaster::getAssetGroupId).distinct().collect(Collectors.toList());
            if (assetGroupIds.isEmpty()) {
                return new PageUtils<>(iPage);
            }
            queryWrapper.in("id", assetGroupIds);
        }
        iPage = assetGroupMapper.list(iPage, queryWrapper);
        if (iPage.getRecords().isEmpty()) {
            return new PageUtils<>(iPage);
        }
        Map<Long, List<AssetGroupListDTO.MasterDTO>> masterMap = assetGroupMasterMapper.listMaster(new QueryWrapper<>()
                .in("asset_group_id", iPage.getRecords().stream().map(AssetGroupListDTO::getId).collect(Collectors.toList()))
                .eq("mgm.is_del", IsDelEnum.NORMAL.getId())
        ).stream().collect(Collectors.groupingBy(AssetGroupListDTO.MasterDTO::getAssetGroupId));
        iPage.getRecords().forEach(group -> group.setMasters(masterMap.getOrDefault(group.getId(), List.of())));
        return new PageUtils<>(iPage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(AssetGroupSaveVO saveVO, Integer userId) {
        checkGroupName(saveVO.getGroupName(), null);
        AssetGroup assetGroup = new AssetGroup();
        BeanUtils.copyProperties(saveVO, assetGroup);
        assetGroup.setCreateUid(userId);
        assetGroupMapper.insert(assetGroup);
        //新增推送账户
        this.batchAddMaster(List.of(assetGroup.getId()), saveVO.getMasterIds(), userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(AssetGroupUpdateVO updateVO, Integer userId) {
        checkGroupName(updateVO.getGroupName(), updateVO.getId());
        AssetGroup assetGroup = new AssetGroup();
        BeanUtils.copyProperties(updateVO, assetGroup);
        assetGroup.setUpdateUid(userId);
        assetGroupMapper.update(assetGroup, new LambdaQueryWrapper<AssetGroup>()
                .eq(AssetGroup::getId, updateVO.getId())
                .eq(AssetGroup::getIsDel, IsDelEnum.NORMAL.getId())
        );
        //修改推送账户
        AssetGroupMaster assetGroupMaster = new AssetGroupMaster();
        assetGroupMaster.setIsDel(IsDelEnum.DELETE.getId().intValue());
        assetGroupMaster.setUpdateUid(userId);
        this.assetGroupMasterMapper.update(assetGroupMaster, new LambdaQueryWrapper<AssetGroupMaster>()
                .eq(AssetGroupMaster::getAssetGroupId, updateVO.getId())
                .notIn(CollectionUtils.isNotEmpty(updateVO.getMasterIds()), AssetGroupMaster::getMasterId, updateVO.getMasterIds())
        );
        this.batchAddMaster(List.of(updateVO.getId()), updateVO.getMasterIds(), userId);
    }

    @Override
    public void del(AssetGroupDelVO delVO, Integer userId) {
        AssetGroup assetGroup = this.assetGroupMapper.selectById(delVO.getId());
        if (assetGroup.getMaterialNum() > 0) {
            throw new CustomException("素材组已设置素材，无法删除");
        }
        AssetGroup update = new AssetGroup();
        update.setIsDel(IsDelEnum.DELETE.getId().intValue());
        update.setUpdateUid(userId);
        assetGroupMapper.update(update, new LambdaQueryWrapper<AssetGroup>()
                .eq(AssetGroup::getId, assetGroup.getId())
                .eq(AssetGroup::getIsDel, IsDelEnum.NORMAL.getId())
        );
    }

    @Override
    public void batchDeleteGroupByAsset(List<Long> assetIds, Integer userId) {
        if (CollectionUtils.isEmpty(assetIds)) {
            return;
        }
        List<Long> assetGroupIds = this.getAssetGroupIdByAssetIds(assetIds);
        if (assetGroupIds.isEmpty()) {
            return;
        }
        //删除素材与素材组之间的关系
        AssetGroupResource assetGroupResource = new AssetGroupResource();
        assetGroupResource.setUpdateUid(userId);
        assetGroupResource.setIsDel(IsDelEnum.DELETE.getId().intValue());
        this.assetGroupResourceMapper.update(assetGroupResource, new LambdaQueryWrapper<AssetGroupResource>()
                .in(AssetGroupResource::getAssetId, assetIds)
                .eq(AssetGroupResource::getIsDel, IsDelEnum.NORMAL.getId())
        );
        this.refreshMaterialNum(assetGroupIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAddGroup(AssetGroupBatchSetVO setVO, Integer userId) {
        List<Long> assetGroupIds = this.getAssetGroupIdByAssetIds(setVO.getAssetIds());
        //清空关系
        AssetGroupResource update = new AssetGroupResource();
        update.setIsDel(IsDelEnum.DELETE.getId().intValue());
        update.setCreateUid(userId);
        assetGroupResourceMapper.update(update, new LambdaQueryWrapper<AssetGroupResource>()
                .in(AssetGroupResource::getAssetId, setVO.getAssetIds()));
        //设置,添加素材组与素材关系
        if (CollectionUtils.isNotEmpty(setVO.getAssetGroupIds())) {
            List<AssetGroupResource> assetGroupResources = new ArrayList<>();
            setVO.getAssetIds().forEach(assetId ->
                    setVO.getAssetGroupIds().forEach(assetGroupId -> {
                        AssetGroupResource assetGroupResource = new AssetGroupResource();
                        assetGroupResource.setAssetGroupId(assetGroupId);
                        assetGroupResource.setAssetId(assetId);
                        assetGroupResource.setCreateUid(userId);
                        assetGroupResources.add(assetGroupResource);
                    })
            );
            assetGroupResourceMapper.insertByUk(assetGroupResources);
        }
        //刷新素材组使用数量
        List<Long> refreshAssetGroupIds = new ArrayList<>();
        refreshAssetGroupIds.addAll(assetGroupIds);
        refreshAssetGroupIds.addAll(setVO.getAssetGroupIds());
        this.refreshMaterialNum(refreshAssetGroupIds);
        //插入素材组账户关系
        if (CollectionUtils.isNotEmpty(setVO.getMasterIds())) {
            this.batchAddMaster(setVO.getAssetGroupIds(), setVO.getMasterIds(), userId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAddMasterByAssetIds(List<Long> assetIds, List<Integer> masterIds, Integer userId) {
        List<Long> assetGroupIds = this.getAssetGroupIdByAssetIds(assetIds);
        this.batchAddMaster(assetGroupIds, masterIds, userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAddMaster(List<Long> assetGroupIds, List<Integer> masterIds, Integer userId) {
        if (CollectionUtils.isEmpty(assetGroupIds) || CollectionUtils.isEmpty(masterIds)) {
            return;
        }
        List<AssetGroupMaster> assetGroupMasters = new ArrayList<>();
        assetGroupIds.forEach(assetGroupId ->
                masterIds.forEach(masterId -> {
                    AssetGroupMaster assetGroupMaster = new AssetGroupMaster();
                    assetGroupMaster.setMasterId(masterId);
                    assetGroupMaster.setAssetGroupId(assetGroupId);
                    assetGroupMaster.setCreateUid(userId);
                    assetGroupMasters.add(assetGroupMaster);
                })
        );
        assetGroupMasterMapper.insertByUk(assetGroupMasters);
    }

    @Override
    public List<SelectDTO> selectAll(AssetGroupSelectAllVO selectVO) {
        List<SelectDTO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(selectVO.getAppointIds())) {
            result.addAll(assetGroupMapper.selectList(new LambdaQueryWrapper<AssetGroup>()
                    .in(AssetGroup::getId, selectVO.getAppointIds())
                    .orderByDesc(AssetGroup::getId)
            ).stream().map(u -> new SelectDTO(u.getId(), u.getGroupName())).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(selectVO.getMasterIds())) {
            result.addAll(assetGroupMapper.selectByMaster(new QueryWrapper<>()
                    .in("magm.master_id", selectVO.getMasterIds())
                    .eq("magm.is_del", IsDelEnum.NORMAL.getId())
                    .eq("mag.is_del", IsDelEnum.NORMAL.getId())
                    .groupBy("mag.id")
                    .orderByDesc("num")
            ));
        }
        result.addAll(assetGroupMapper.selectList(new LambdaQueryWrapper<AssetGroup>()
                .eq(AssetGroup::getIsDel, IsDelEnum.NORMAL.getId())
                .notIn(CollectionUtils.isNotEmpty(result), AssetGroup::getId, result.stream().map(SelectDTO::getId).collect(Collectors.toList()))
                .orderByDesc(AssetGroup::getId)
        ).stream().map(u -> new SelectDTO(u.getId(), u.getGroupName())).collect(Collectors.toList()));
        return result;
    }

    @Override
    public List<SelectDTO> select(AssetGroupSelectVO selectVO) {
        List<SelectDTO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(selectVO.getAppointIds())) {
            result.addAll(assetGroupMapper.selectList(new LambdaQueryWrapper<AssetGroup>()
                    .in(AssetGroup::getId, selectVO.getAppointIds())
                    .orderByDesc(AssetGroup::getId)
            ).stream().map(u -> new SelectDTO(u.getId(), u.getGroupName())).collect(Collectors.toList()));
        }
        result.addAll(assetGroupMapper.selectByMaster(new QueryWrapper<>()
                .eq("magm.master_id", selectVO.getMasterId())
                .eq("magm.is_del", IsDelEnum.NORMAL.getId())
                .eq("mag.is_del", IsDelEnum.NORMAL.getId())
                .notIn(CollectionUtils.isNotEmpty(result), "mag.id", result.stream().map(SelectDTO::getId).collect(Collectors.toList()))
                .groupBy("mag.id")
                .orderByDesc("mag.id")
        ));
        return result;
    }

    /**
     * 检查分组名称
     *
     * @param groupName 分组名称
     * @param id        素材组ID
     */
    private void checkGroupName(String groupName, Long id) {
        long count = assetGroupMapper.selectCount(new LambdaQueryWrapper<AssetGroup>()
                .eq(AssetGroup::getGroupName, groupName)
                .eq(AssetGroup::getIsDel, IsDelEnum.NORMAL.getId())
                .ne(ObjectUtils.isNotNullOrZero(id), AssetGroup::getId, id)
        );
        if (count > 0) {
            throw new CustomException("素材组名称已被使用，请修改后再试");
        }
    }

    /**
     * 获取素材组ID
     *
     * @param assetIds 素材ID
     * @return 返回素材组ID
     */
    private List<Long> getAssetGroupIdByAssetIds(List<Long> assetIds) {
        if (CollectionUtils.isEmpty(assetIds)) {
            return List.of();
        }
        return this.assetGroupResourceMapper.selectList(new LambdaQueryWrapper<AssetGroupResource>()
                .in(AssetGroupResource::getAssetId, assetIds)
                .eq(AssetGroupResource::getIsDel, IsDelEnum.NORMAL.getId())
                .select(AssetGroupResource::getAssetGroupId)
        ).stream().map(AssetGroupResource::getAssetGroupId).distinct().collect(Collectors.toList());
    }

    /**
     * 刷新素材数量
     *
     * @param assetGroupIds 素材组ID
     */
    private void refreshMaterialNum(List<Long> assetGroupIds) {
        if (CollectionUtils.isEmpty(assetGroupIds)) {
            return;
        }
        AssetGroup assetGroup = new AssetGroup();
        assetGroup.setMaterialNum(0);
        this.assetGroupMapper.update(assetGroup, new LambdaQueryWrapper<AssetGroup>()
                .in(AssetGroup::getId, assetGroupIds));
        this.assetGroupMapper.refreshMaterialNum(assetGroupIds);
    }
}
