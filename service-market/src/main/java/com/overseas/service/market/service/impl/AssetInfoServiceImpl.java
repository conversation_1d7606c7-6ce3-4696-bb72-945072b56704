package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.market.assetInfo.AssetInfoDTO;
import com.overseas.common.enums.MachineRoomEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.UploadUtils;
import com.overseas.common.vo.market.assetInfo.AssetInfoGetVO;
import com.overseas.service.market.entity.*;
import com.overseas.service.market.enums.assets.AssetTypeEnum;
import com.overseas.service.market.mapper.*;
import com.overseas.service.market.service.AssetInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

@Service
@RequiredArgsConstructor
@Slf4j
public class AssetInfoServiceImpl extends ServiceImpl<AssetInfoMapper, AssetInfo> implements AssetInfoService {

    private final AssetMapper assetMapper;

    @Value("${local.machine-room}")
    private String machineRoom;

    @Override
    public AssetInfoDTO getAssetInfo(AssetInfoGetVO getVO) {
        AssetInfoDTO result = new AssetInfoDTO();
        AssetInfo assetInfo = this.getOne(new LambdaQueryWrapper<AssetInfo>()
                .eq(AssetInfo::getAssetId, getVO.getAssetId()));
        if (assetInfo == null) {
            // 获取素材信息，调用本地脚本
            Asset asset = this.assetMapper.selectById(getVO.getAssetId());
            if (ObjectUtils.isNotNull(asset)) {
                String path = UploadUtils.getUploadPath(asset.getContent());
                String extension = UploadUtils.getExtension(asset.getContent()).toLowerCase();
                String type = "";
                if (AssetTypeEnum.IMG.getFormat().contains(extension)) {
                    type = AssetTypeEnum.IMG.getType();
                } else if (AssetTypeEnum.VIDEO.getFormat().contains(extension)) {
                    type = AssetTypeEnum.VIDEO.getType();
                }
                
                log.info("素材信息 : asset id : {}, path : {}, type : {}, machine room : {}", getVO.getAssetId(), path,
                        type, machineRoom);
                this.excPython(getVO.getAssetId(), path, type, machineRoom.toLowerCase());
            }

            assetInfo = this.getOne(new LambdaQueryWrapper<AssetInfo>()
                    .eq(AssetInfo::getAssetId, getVO.getAssetId()));
            if (ObjectUtils.isNull(assetInfo)) {
                throw new CustomException(50404, "素材解构服务异常，稍后再试");
            }
        }
        BeanUtils.copyProperties(assetInfo, result);
        return result;
    }

    private String excPython(Long assetId, String path, String type, String origin) {
        String pythonVersion;
        if (MachineRoomEnum.DE.getMachineRoom().equals(origin)) {
            pythonVersion = "python3.9";
        } else {
            pythonVersion = "python3";
        }
        String pythonCommand = pythonVersion + " /home/<USER>/ai_dsp_overseas/service_projects/asset/extract_arg.py "
                + assetId + " " + path + " " + type + " " + origin;
        log.info("pythonCommand : {}", pythonCommand);
        StringBuilder result = new StringBuilder();
        try {
            // 指定Python解释器的路径和Python脚本的路径
            Process process = Runtime.getRuntime().exec(pythonCommand);

            // 读取命令的输出
            BufferedReader in = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line = null;
            while ((line = in.readLine()) != null) {
                System.out.println(line);
                result.append(line);
            }
            in.close();

            // 等待进程结束
            int code = process.waitFor();
            log.info("exec result : {}", code);
        } catch (IOException e) {
            log.info("exec python io error : {}", e.getMessage());
            throw new CustomException("素材解析IO异常，{}", e.getMessage());
        } catch (InterruptedException e) {
            log.info("exec python interrupt error : {}", e.getMessage());
            throw new CustomException("素材解析异常，{}", e.getMessage());
        }
        return result.toString();
    }
}
