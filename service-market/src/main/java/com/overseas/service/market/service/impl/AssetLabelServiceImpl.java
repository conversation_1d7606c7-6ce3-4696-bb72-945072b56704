package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.CascaderDTO2;
import com.overseas.common.dto.market.asset.assetLabel.AssetLabelSelectDTO;
import com.overseas.common.dto.market.assetLabel.AssetLabelGetDTO;
import com.overseas.common.dto.market.assetLabel.AssetLabelListDTO;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.assetLabel.*;
import com.overseas.service.market.entity.AssetLabel;
import com.overseas.service.market.entity.AssetLabelResource;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.vo.market.asset.assetLabel.AssetLabelSaveVO;
import com.overseas.common.vo.market.asset.assetLabel.AssetLabelSelectGetVO;
import com.overseas.service.market.entity.AssetResource;
import com.overseas.service.market.mapper.AssetLabelMapper;
import com.overseas.service.market.mapper.AssetLabelResourceMapper;
import com.overseas.service.market.mapper.AssetResourceMapper;
import com.overseas.service.market.service.AssetLabelService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class AssetLabelServiceImpl extends ServiceImpl<AssetLabelMapper, AssetLabel> implements AssetLabelService {

    private final AssetLabelResourceMapper assetLabelResourceMapper;

    private final AssetResourceMapper assetResourceMapper;
    
    private final Integer labelLevel = 4;

    @Override
    public List<AssetLabelSelectDTO> getAssetLabelSelect(AssetLabelSelectGetVO getVO) {
        return this.baseMapper.getAssetLabelSelect(new QueryWrapper<AssetLabel>()
                .eq("mal.is_del", IsDelEnum.NORMAL.getId())
                .eq("mal.level", this.labelLevel)
                .and(StringUtils.isNotBlank(getVO.getSearch())
                        || CollectionUtils.isNotEmpty(getVO.getIds()),
                        q -> q.like(StringUtils.isNotBlank(getVO.getSearch()), "mal.label_name", getVO.getSearch())
                        .or().in(CollectionUtils.isNotEmpty(getVO.getIds()), "mal.id", getVO.getIds()))
                .orderByDesc("mal.material_num"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAssetLabel(AssetLabelSaveVO saveVO, Integer userId) {
        if (saveVO.getAssetIds().isEmpty()) {
            return;
        }
        // 清除历史素材标签关系记录
        AssetLabelResource assetLabelResource = new AssetLabelResource();
        assetLabelResource.setUpdateUid(userId);
        assetLabelResource.setIsDel(IsDelEnum.DELETE.getId().intValue());
        this.assetLabelResourceMapper.update(assetLabelResource, new QueryWrapper<AssetLabelResource>().lambda()
                .eq(AssetLabelResource::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(AssetLabelResource::getMasterId, saveVO.getMasterId())
                .in(AssetLabelResource::getAssetId, saveVO.getAssetIds()));
        // 保存标签
        if (!saveVO.getLabelNameList().isEmpty()) {
            // 新增素材与标签关系
            Map<String, Long> labelMap = this.baseMapper.selectList(new QueryWrapper<AssetLabel>().lambda()
                    .eq(AssetLabel::getIsDel, IsDelEnum.NORMAL.getId())
                    .in(AssetLabel::getLabelName, saveVO.getLabelNameList())).stream()
                        .collect(Collectors.toMap(AssetLabel::getLabelName, AssetLabel::getId)
                    );
            if (labelMap.isEmpty()) {
                throw new CustomException("标签不存在");
            }
            List<AssetLabelResource> assetLabelResources = new ArrayList<>() {{
                saveVO.getAssetIds().forEach(assetId -> saveVO.getLabelNameList().forEach(labelName -> {
                    AssetLabelResource assetLabelResource = new AssetLabelResource();
                    assetLabelResource.setAssetId(assetId);
                    assetLabelResource.setLabelId(labelMap.get(labelName));
                    assetLabelResource.setMasterId(saveVO.getMasterId());
                    assetLabelResource.setIsDel(IsDelEnum.NORMAL.getId().intValue());
                    add(assetLabelResource);
                }));
            }};
            this.assetLabelResourceMapper.saveAssetLabelResource(assetLabelResources, userId);
        }
        //刷新标签素材数量
        this.assetLabelResourceMapper.selectList(new QueryWrapper<AssetLabelResource>().lambda()
                .eq(AssetLabelResource::getMasterId, saveVO.getMasterId())
                .in(AssetLabelResource::getAssetId, saveVO.getAssetIds())
                .groupBy(AssetLabelResource::getLabelId)
        ).forEach(u -> this.refreshLabelMaterialNum(u.getLabelId()));
    }

    @Override
    public List<SelectDTO> categorySelect(AssetLabelCategoryVO categoryVO) {
        return this.baseMapper.selectList(new LambdaQueryWrapper<AssetLabel>()
                        .eq(AssetLabel::getLevel, categoryVO.getLevel())
                        .eq(AssetLabel::getIsDel, IsDelEnum.NORMAL.getId())
                        .eq(ObjectUtils.isNotNullOrZero(categoryVO.getPid()),
                                AssetLabel::getPid, categoryVO.getPid())
                        .like(StringUtils.isNotBlank(categoryVO.getSearch()),
                                AssetLabel::getLabelName, categoryVO.getSearch())
                ).stream().map(assetLabel -> new SelectDTO(assetLabel.getId(), assetLabel.getLabelName()))
                .collect(Collectors.toList());
    }

    @Override
    public PageUtils<?> list(AssetLabelListVO listVO) {
        IPage<AssetLabelListDTO> iPage = new Page<>(listVO.getPage(), listVO.getPageNum());
        this.baseMapper.list(iPage, new QueryWrapper<>()
                .eq("m1.level", this.labelLevel)
                .eq("m1.is_del", IsDelEnum.NORMAL.getId())
                .eq("le1.is_del", IsDelEnum.NORMAL.getId())
                .eq("le2.is_del", IsDelEnum.NORMAL.getId())
                .eq("le3.is_del", IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getLevel1()), "le1.id", listVO.getLevel1())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getLevel2()), "le2.id", listVO.getLevel2())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getLevel3()), "le3.id", listVO.getLevel3())
                .and(StringUtils.isNotBlank(listVO.getSearch()), q ->
                        q.like("m1.label_name", listVO.getSearch())
                                .or().eq("m1.id", listVO.getSearch())
                ).orderByDesc("m1.id")
        );
        return new PageUtils<>(iPage);
    }

    @Override
    public void save(com.overseas.common.vo.market.assetLabel.AssetLabelSaveVO saveVO, Integer userId) {
        this.checkNameRepeat(saveVO.getLabelName(), null);
        AssetLabel assetLabel = new AssetLabel();
        BeanUtils.copyProperties(saveVO, assetLabel);
        assetLabel.setCreateUid(userId);
        assetLabel.setLevel(this.labelLevel);
        assetLabel.setIsDel(IsDelEnum.NORMAL.getId().intValue());
        this.baseMapper.saveAssetLabelByUk(List.of(assetLabel), userId);
    }

    @Override
    public void update(AssetLabelUpdateVO updateVO, Integer userId) {
        this.checkNameRepeat(updateVO.getLabelName(), updateVO.getId());
        AssetLabel assetLabel = new AssetLabel();
        BeanUtils.copyProperties(updateVO, assetLabel);
        assetLabel.setCreateUid(userId);
        this.baseMapper.update(assetLabel, new LambdaQueryWrapper<AssetLabel>().eq(AssetLabel::getId, updateVO.getId()));
    }

    @Override
    public AssetLabelGetDTO get(AssetLabelGetVO getVO) {
        AssetLabelGetDTO assetLabel = this.baseMapper.get(new QueryWrapper<>()
                .eq("m1.level", this.labelLevel)
                .eq("m1.is_del", IsDelEnum.NORMAL.getId())
                .eq("le1.is_del", IsDelEnum.NORMAL.getId())
                .eq("le2.is_del", IsDelEnum.NORMAL.getId())
                .eq("le3.is_del", IsDelEnum.NORMAL.getId())
                .eq("m1.id", getVO.getId())
        );
        if (null == assetLabel) {
            throw new CustomException("标签内容不存在");
        }
        return assetLabel;
    }

    @Override
    public void del(AssetLabelDelVO delVO, Integer userId) {
        long count = this.assetLabelResourceMapper.selectCount(new LambdaQueryWrapper<AssetLabelResource>()
                .eq(AssetLabelResource::getLabelId, delVO.getId())
                .eq(AssetLabelResource::getIsDel, IsDelEnum.NORMAL.getId())
        );
        if (count > 0) {
            throw new CustomException("当前标签在素材中已被使用，无法删除");
        }
        this.baseMapper.delete(new LambdaQueryWrapper<AssetLabel>()
                .eq(AssetLabel::getId, delVO.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAdd(AssetLabelBatchAddVO addVO, Integer userId) {
        //获取广告主数据
        Map<Long, List<AssetResource>> assetMasterMap = assetResourceMapper.selectList(new LambdaQueryWrapper<AssetResource>()
                .in(AssetResource::getAssetId, addVO.getAssetIds())
                .eq(AssetResource::getIsDel, IsDelEnum.NORMAL.getId())
                .select(AssetResource::getAssetId, AssetResource::getMasterId)
        ).stream().collect(Collectors.groupingBy(AssetResource::getAssetId));
        //插入数据
        List<AssetLabelResource> assetLabelResources = new ArrayList<>();
        for (Long assetId : addVO.getAssetIds()) {
            assetMasterMap.get(assetId).forEach(master -> {
                for (Long labelId : addVO.getLabelIds()) {
                    AssetLabelResource assetLabelResource = new AssetLabelResource();
                    assetLabelResource.setLabelId(labelId);
                    assetLabelResource.setAssetId(assetId);
                    assetLabelResource.setMasterId(master.getMasterId());
                    assetLabelResource.setIsDel(IsDelEnum.NORMAL.getId().intValue());
                    assetLabelResources.add(assetLabelResource);
                }
            });
        }
        if (assetLabelResources.isEmpty()) {
            return;
        }
        //插入数据
        assetLabelResourceMapper.saveAssetLabelResource(assetLabelResources, userId);
        //刷新标签数量
        addVO.getLabelIds().forEach(this::refreshLabelMaterialNum);
    }

    @Override
    public void batchDel(AssetLabelBatchDelVO delVO, Integer userId) {
        AssetLabelResource assetLabelResource = new AssetLabelResource();
        assetLabelResource.setIsDel(IsDelEnum.DELETE.getId().intValue());
        assetLabelResource.setUpdateUid(userId);
        assetLabelResourceMapper.update(assetLabelResource, new LambdaQueryWrapper<AssetLabelResource>()
                .in(AssetLabelResource::getLabelId, delVO.getLabelIds())
                .in(AssetLabelResource::getAssetId, delVO.getAssetIds())
                .eq(AssetLabelResource::getIsDel, IsDelEnum.NORMAL.getId())
        );
        //刷新素材数量
        delVO.getLabelIds().forEach(this::refreshLabelMaterialNum);
    }

    @Override
    public List<CascaderDTO2> cascader(AssetLabelCascaderVO cascaderVO) {
        List<AssetLabel> children = this.baseMapper.selectList(new LambdaQueryWrapper<AssetLabel>()
                .in(CollectionUtils.isNotEmpty(cascaderVO.getPid()), AssetLabel::getPid, cascaderVO.getPid())
                .eq(AssetLabel::getLevel, cascaderVO.getLevel())
                .eq(AssetLabel::getIsDel, IsDelEnum.NORMAL.getId())
                .orderByDesc(AssetLabel::getId)
        );
        if (children.isEmpty()) {
            return List.of();
        }
        Map<Long, CascaderDTO2> result = this.baseMapper.selectBatchIds(children.stream().map(AssetLabel::getPid)
                        .distinct().collect(Collectors.toList()))
                .stream().filter(u -> u.getIsDel().equals(IsDelEnum.NORMAL.getId().intValue()))
                .map(u -> new CascaderDTO2(u.getId().toString(), u.getLabelName(), new ArrayList<>()))
                .collect(Collectors.toMap(u -> Long.parseLong(u.getValue()), Function.identity()));
        children.forEach(child -> {
            if (result.containsKey(child.getPid())) {
                result.get(child.getPid()).getChildren().add(new CascaderDTO2(child.getId().toString(), child.getLabelName(), null));
            }
        });
        return new ArrayList<>(result.values());
    }


    /**
     * 确认标签名称是否重复
     *
     * @param labelName 标签名称
     * @param id        排除标签ID
     */
    private void checkNameRepeat(String labelName, Long id) {
        List<AssetLabel> assetLabels = this.baseMapper.selectList(new LambdaQueryWrapper<AssetLabel>()
                .eq(AssetLabel::getLabelName, labelName)
                .ne(ObjectUtils.isNotNullOrZero(id), AssetLabel::getId, id)
                .eq(AssetLabel::getIsDel, IsDelEnum.NORMAL.getId())
        );
        if (!assetLabels.isEmpty()) {
            throw new CustomException(String.format("标签名称（ID: %s）已存在，请确认后再试", assetLabels.get(0).getId()));
        }
    }

    /**
     * 更新标签数量
     *
     * @param labelId 标签ID
     */
    private void refreshLabelMaterialNum(Long labelId) {
        Long count = assetLabelResourceMapper.getLabelMaterialCount(new LambdaQueryWrapper<AssetLabelResource>()
                .eq(AssetLabelResource::getLabelId, labelId)
                .eq(AssetLabelResource::getIsDel, IsDelEnum.NORMAL.getId())
        );
        this.baseMapper.updateMaterialNum(count, labelId);
    }
}
