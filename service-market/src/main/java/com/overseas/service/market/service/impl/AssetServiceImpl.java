package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.configuration.SheinConfiguration;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.SelectDTO2;
import com.overseas.common.dto.SelectDTO3;
import com.overseas.common.dto.common.FFmpegDTO;
import com.overseas.common.dto.market.asset.*;
import com.overseas.common.dto.market.asset.assetLabel.AssetLabelNameDTO;
import com.overseas.common.dto.report.AssetReportListDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.enums.ResultStatusEnum;
import com.overseas.common.enums.market.PutEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.*;
import com.overseas.common.vo.market.asset.*;
import com.overseas.common.vo.monitor.put.NoticeDevelopVO;
import com.overseas.common.vo.report.AssetReportListVO;
import com.overseas.service.market.dto.assetTask.AssetTaskNameDTO;
import com.overseas.service.market.entity.*;
import com.overseas.service.market.entity.assetTask.TaskProductAsset;
import com.overseas.service.market.enums.assets.AssetIsUsedEnum;
import com.overseas.service.market.enums.assets.AssetSourceTypeEnum;
import com.overseas.service.market.enums.assets.AssetTypeEnum;
import com.overseas.common.enums.market.campaign.CampaignStatusEnum;
import com.overseas.service.market.enums.creative.CreativeAuditStatusEnum;
import com.overseas.service.market.enums.creative.units.CreativeUnitStatusEnum;
import com.overseas.service.market.enums.material.MaterialAssetTypeEnum;
import com.overseas.service.market.enums.plan.PlanModeEnum;
import com.overseas.common.enums.market.plan.PlanStatusEnum;
import com.overseas.service.market.enums.user.UserTypeEnum;
import com.overseas.service.market.feign.FgMonitorService;
import com.overseas.service.market.feign.FgReportService;
import com.overseas.service.market.mapper.*;
import com.overseas.service.market.mapper.assetTask.TaskProductAssetMapper;
import com.overseas.service.market.service.AssetGroupService;
import com.overseas.service.market.service.AssetService;
import com.overseas.service.market.vo.assetTask.AssetSizeSelectVO;
import com.overseas.service.market.vo.other.AssertCheckVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-24 9:26
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AssetServiceImpl extends ServiceImpl<AssetMapper, Asset> implements AssetService {

    private final AssetResourceMapper assetResourceMapper;

    private final AssetLabelResourceMapper assetLabelResourceMapper;

    private final FgReportService fgReportService;

    private final AssetGroupService assetGroupService;

    private final CreativeUnitMapper creativeUnitMapper;

    private final SheinConfiguration sheinConfiguration;

    private final AssetSizeMapper assetSizeMapper;

    private final TaskProductAssetMapper taskProductAssetMapper;

    private final FgMonitorService fgMonitorService;

    // Lazada账号列表
    private final List<Integer> lazadaMasterIds = List.of(10007, 10014, 10015, 10016, 10017, 10018, 20024);

    private String compressAssetSh;

    @Value("${com-upload.compress-asset-sh}")
    public void setCompressAssetSh(String compressAssetSh) {
        this.compressAssetSh = compressAssetSh;
    }

    @Override
    public Asset getByMd5(String md5) {
        Asset assertInDb = getOne(new LambdaQueryWrapper<Asset>().eq(Asset::getMd5, md5));
        if (null == assertInDb) {
            throw new CustomException(ResultStatusEnum.INVALID_REQUEST.getCode(), "Asset文件不存在，请检查后重试");
        }
        formatAsset(assertInDb);
        return assertInDb;
    }

    @Override
    public Asset checkAsset(AssertCheckVO checkVO, Integer userId) {
        Asset asset = this.getOne(new QueryWrapper<Asset>().lambda().eq(Asset::getMd5, checkVO.getMd5())
                .eq(Asset::getIsDel, IsDelEnum.NORMAL.getId()));
        // 如果素材不存在，返回前端异常
        if (asset == null) {
            throw new CustomException(ResultStatusEnum.INVALID_REQUEST.getCode(), "Asset文件不存在，请检查后重试");
        }
        this.formatAsset(asset);
        return asset;
    }

    @Override
    public Asset getAsset(Long id) {
        Asset assertInDb = getById(id);
        if (null == assertInDb) {
            throw new CustomException(ResultStatusEnum.INVALID_REQUEST.getCode(), "Asset文件不存在，请检查后重试");
        }
        formatAsset(assertInDb);
        return assertInDb;
    }

    @Override
    public void formatAsset(Asset assertInDb) {
        // 媒体素材需要增加httpUrl地址
        if (!AssetTypeEnum.TEXT.getId().equals(assertInDb.getAssetType())) {
            assertInDb.setHttpUrl(UploadUtils.getNetworkUrl(assertInDb.getContent(), assertInDb.getIsUpload()));
        }
        assertInDb.setAssetId(assertInDb.getId());
        // 视频素材需要增加缩略图地址
        if (AssetTypeEnum.VIDEO.getId().equals(assertInDb.getAssetType()) && !assertInDb.getCoverImgId().equals(0L)) {
            Asset coverImg = getAsset(assertInDb.getCoverImgId());
            assertInDb.setCoverImgUrl(coverImg.getHttpUrl());
        } else {
            assertInDb.setCoverImgUrl(ConstantUtils.EMPTY);
        }
    }

    @Override
    public Asset saveAsset(Integer assetType, String basePath, String fileName, Integer userId) throws IOException {

        // 生成MD5
        String md5 = Md5CalculateUtils.getFileMD5(UploadUtils.getUploadPath(basePath));
        // 获取素材宽高、时长、大小
        FFmpegDTO fFmpegDTO = FFmpegUtils.info(UploadUtils.getUploadPath(basePath), fileName);
        Asset asset = this.getOne(new LambdaQueryWrapper<Asset>().eq(Asset::getMd5, md5));
        if (asset == null) {
            // 新增
            asset = new Asset();
            BeanUtils.copyProperties(fFmpegDTO, asset);
            asset.setMd5(md5);
            asset.setAssetType(assetType);
            asset.setContent(basePath);
            asset.setFormat(UploadUtils.getExtension(basePath));
            asset.setCreateUid(userId);
            if (AssetTypeEnum.VIDEO.getId().equals(assetType)) {
                asset.setCoverImgId(this.createCoverImg(asset.getContent(), userId));
            }
            this.save(asset);
        } else {
            BeanUtils.copyProperties(fFmpegDTO, asset);
            asset.setAssetType(assetType);
            asset.setFormat(UploadUtils.getExtension(basePath));
            if (AssetTypeEnum.VIDEO.getId().equals(assetType) && asset.getCoverImgId().equals(0L)) {
                asset.setCoverImgId(createCoverImg(asset.getContent(), userId));
            }
            asset.setIsDel(IsDelEnum.NORMAL.getId().intValue());
            this.updateById(asset);
        }
        this.formatAsset(asset);
        return asset;
    }

    @Override
    public Asset saveTextAsset(String text, Integer loginUserId) {
        if (StringUtils.isBlank(text)) {
            throw new CustomException("文本内容为空，请检查后重试");
        }
        text = text.trim();
        String md5 = DigestUtils.md5DigestAsHex(text.getBytes());
        Asset assetInDb;
        try {
            return this.getByMd5(md5);
        } catch (CustomException e) {
            assetInDb = new Asset();
            assetInDb.setAssetType(AssetTypeEnum.TEXT.getId());
            assetInDb.setCreateUid(loginUserId);
            assetInDb.setMd5(md5);
            assetInDb.setContent(text);
            this.save(assetInDb);
            return assetInDb;
        }
    }

    @Override
    public List<SelectDTO3> getAssetSizeSelect(AssetSizeSelectGetVO getVO) {

        QueryWrapper<Asset> queryWrapper = this.getAssetSelectQueryWrapper(getVO);

        if (queryWrapper == null) {
            return List.of();
        }

        return this.baseMapper.getAssetSizeSelect(queryWrapper);
    }

    @Override
    public List<SelectDTO> selectAssetSize(AssetSizeSelectGetVO getVO) {

        QueryWrapper<Asset> queryWrapper = this.getAssetSelectQueryWrapper(getVO);

        if (queryWrapper == null) {
            return List.of();
        }

        queryWrapper.ne(ObjectUtils.isNotNullOrZero(getVO.getIsDpa()), "ma.fill_width", 0);
        if (CollectionUtils.isNotEmpty(getVO.getSizes())) {
            queryWrapper.and(q -> getVO.getSizes().forEach(size -> {
                String[] sizeStr = size.split("\\*");
                q.or().and(q1 -> q1
                        .eq("ma.width", sizeStr[0])
                        .eq("ma.height", sizeStr[1]));
            }));
        }

        return this.baseMapper.selectAssetSize(queryWrapper);
    }

    private QueryWrapper<Asset> getAssetSelectQueryWrapper(AssetSizeSelectGetVO getVO) {

        QueryWrapper<Asset> queryWrapper = new QueryWrapper<Asset>()
                .eq(ObjectUtils.isNotNullOrZero(getVO.getAssetType()), "ma.asset_type", getVO.getAssetType())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getMasterId()), "mar.master_id", getVO.getMasterId())
                .groupBy("CONCAT(ma.width,'*',ma.height)")
                .orderByDesc("COUNT(CONCAT(ma.width,'*',ma.height))");


        // 如果是报表模块，则查询需要的素材ID；如果是列表模块，则过滤已删除的素材
        if (getVO.getIsReport()) {
            List<Long> assetIds = this.fgReportService.getAssetIds().getData();
            if (assetIds.isEmpty()) {
                return null;
            }
            queryWrapper.in("ma.id", assetIds);
        } else {
            queryWrapper.eq("ma.is_del", IsDelEnum.NORMAL.getId())
                    .eq("mar.is_del", IsDelEnum.NORMAL.getId());
        }
        return queryWrapper;
    }

    @Override
    public PageUtils<AssetListDTO> getAssetPage(AssetListVO listVO, Integer userId, User user) {
        listVO.setIsPut(this.sheinConfiguration.isPut(listVO.getIsPut(), user.getRoleId()));
        // 1.获取筛选Wrapper
        QueryWrapper<Asset> queryWrapper = new QueryWrapper<Asset>()
                .in(CollectionUtils.isNotEmpty(listVO.getAssetIds()), "ma.id", listVO.getAssetIds())
                .eq("ma.is_del", IsDelEnum.NORMAL.getId())
                .eq("mar.is_del", IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotAll(listVO.getIsMasterUsed()), "mar.is_master_used", listVO.getIsMasterUsed())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getMasterId()),
                        "mar.master_id", listVO.getMasterId())
                .eq("ma.asset_type", listVO.getAssetType())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getSourceType()),
                        "mar.source_type", listVO.getSourceType())
                .gt(StringUtils.isNotBlank(listVO.getCreateStartDate()),
                        "ma.create_time", listVO.getCreateStartDate() + " 00:00:00")
                .lt(StringUtils.isNotBlank(listVO.getCreateEndDate()),
                        "ma.create_time", listVO.getCreateEndDate() + " 23:59:59")
                // 过滤创意标签
                .eq(ObjectUtils.isNotNullOrZero(listVO.getVideoType()),
                        "ma.video_type", listVO.getVideoType())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getCreativeDirection()),
                        "ma.creative_direction", listVO.getCreativeDirection())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getGoodsCategory()),
                        "ma.goods_category", listVO.getGoodsCategory())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getAssetCategory()),
                        "ma.asset_category", listVO.getAssetCategory())
                .and(StringUtils.isNotBlank(listVO.getSearch()),
                        q -> q.or().like("ma.id", listVO.getSearch())
                                .or().like("mar.asset_name", listVO.getSearch()))
                .eq(listVO.getIsUsed() != null && !listVO.getIsUsed().equals(-1),
                        "ma.is_used", listVO.getIsUsed())
                .eq(PutEnum.NOT_PUT.getId().equals(listVO.getIsPut()),
                        "ma.create_uid", user.getId())
                .groupBy("ma.id")
                .orderBy("uploadTime".equals(listVO.getSortField()),
                        "asc".equals(listVO.getSortType()), "ma.create_time")
                .orderByDesc("ma.id");

        List<Long> assetIdResult = new ArrayList<>();
        // 是否查询标签
        if (CollectionUtils.isNotEmpty(listVO.getLabelIds())) {
            List<Long> assetIds = this.assetLabelResourceMapper.selectList(new QueryWrapper<AssetLabelResource>()
                            .lambda().eq(AssetLabelResource::getIsDel, IsDelEnum.NORMAL.getId())
                            .eq(AssetLabelResource::getMasterId, listVO.getMasterId())
                            .in(AssetLabelResource::getLabelId, listVO.getLabelIds()))
                    .stream().map(AssetLabelResource::getAssetId).distinct().collect(Collectors.toList());
            if (assetIds.isEmpty()) {
                return new PageUtils<>(List.of(), 0L);
            }
            assetIdResult = assetIds;
        }
        //是否查询素材组
        if (CollectionUtils.isNotEmpty(listVO.getAssetTaskIds())) {
            assetIdResult = this.taskProductAssetMapper.selectList(new QueryWrapper<TaskProductAsset>().lambda()
                    .eq(TaskProductAsset::getIsDel, IsDelEnum.NORMAL.getId())
                    .in(TaskProductAsset::getAssetTaskId, listVO.getAssetTaskIds())
                    .in(CollectionUtils.isNotEmpty(assetIdResult), TaskProductAsset::getAssetId, assetIdResult)
            ).stream().map(TaskProductAsset::getAssetId).distinct().collect(Collectors.toList());
            if (assetIdResult.isEmpty()) {
                return new PageUtils<>(List.of(), 0L);
            }
        }

        queryWrapper.in(CollectionUtils.isNotEmpty(assetIdResult), "ma.id", assetIdResult);

        // 是否查询尺寸
        this.fillSizeToWrapper(queryWrapper, listVO.getSizes());

        IPage<AssetListDTO> page = new Page<>(listVO.getPage(), listVO.getPageNum());
        // 如果是设计师模块，不需要查报表数据
        if (listVO.getIsDesigner()) {
            queryWrapper.eq("mar.source_type", AssetSourceTypeEnum.LOCAL_UPLOAD.getId())
                    .and(CollectionUtils.isNotEmpty(listVO.getMasterIds()), q -> {
                        if (listVO.getMasterIds().size() == 1) {
                            if (listVO.getMasterIds().contains(0L)) {
                                q.isNull("mar2.master_id");
                            } else {
                                q.in("mar2.master_id", listVO.getMasterIds());
                            }
                        } else {
                            q.in("mar2.master_id", listVO.getMasterIds());
                            if (listVO.getMasterIds().contains(0L)) {
                                q.or().isNull("mar2.master_id");
                            }
                        }
                    });
        }
        IPage<AssetListDTO> pageData = listVO.getIsDesigner()
                ? this.baseMapper.listAssetByPage(page, queryWrapper) : this.getMarketAssetList(listVO, queryWrapper);
        List<Long> currentAssetIds;
        if (pageData.getRecords().isEmpty()) {
            return new PageUtils<>(List.of(), 0L);
        }
        currentAssetIds = pageData.getRecords().stream().map(AssetListDTO::getId).collect(Collectors.toList());
        // 查询素材关联触点单元数量；设计师不需要查询关联创意单元数量，运营模块查询
        Map<Long, Integer> assetNumMap = listVO.getIsDesigner()
                ? new HashMap<>() : this.getUnitCountOfAsset(listVO.getMasterId(), currentAssetIds, MaterialAssetTypeEnum.PRIMARY_ASSET.getId(), user);
        // 查询素材关联触点单元数量；设计师不需要查询关联创意单元数量，运营模块查询
        Map<Long, Integer> assetPutNumMap = listVO.getIsDesigner()
                ? new HashMap<>() : this.getUnitPutCountOfAsset(listVO.getMasterId(), currentAssetIds, MaterialAssetTypeEnum.PRIMARY_ASSET.getId(), user);
        // 查询素材对应的标签
        Map<Long, List<AssetLabelNameDTO>> assetLabelNameMap = this.getLabelOfAsset(
                listVO.getMasterId(), currentAssetIds);
        // 查询素材对应的标签
        Map<Long, List<AssetTaskNameDTO>> assetTaskNameMap = this.getAssetTaskOfAsset(
                UserTypeEnum.DESIGNER.getId().equals(user.getUserType()) ? 0 : listVO.getMasterId(), currentAssetIds);
        // 查询素材相关的投放账号
        Map<Long, List<AssetResourceDTO>> assetMasterMap = listVO.getIsDesigner()
                ? this.getMasterOfAsset(currentAssetIds) : new HashMap<>();
        pageData.getRecords().forEach(entity -> {
            entity.setAssetSize(Double.parseDouble(String.format("%.2f", entity.getAssetSize() / 1024)));
            entity.setAssetPath(UploadUtils.getNetworkUrl(entity.getAssetPath(), entity.getIsUpload()));
            entity.setSourceTypeName(ICommonEnum.getNameById(entity.getSourceType(), AssetSourceTypeEnum.class));
            entity.setCreativeUnitNum(assetNumMap.getOrDefault(entity.getId(), 0));
            entity.setCreativeUnitPutNum(assetPutNumMap.getOrDefault(entity.getId(), 0));
            entity.setLabelList(assetLabelNameMap.getOrDefault(entity.getId(), List.of())
                    .stream().map(AssetLabelNameDTO::getLabelName).collect(Collectors.toList())
            );
            entity.setLabelInfos(assetLabelNameMap.getOrDefault(entity.getId(), List.of()));
            //素材组信息
            entity.setTaskInfos(assetTaskNameMap.getOrDefault(entity.getId(), List.of()));
            entity.setLabels(entity.getLabelList().isEmpty()
                    ? ConstantUtils.PLACEHOLDER_2 : String.join(",", entity.getLabelList())
            );
            entity.setMasterList(assetMasterMap.getOrDefault(entity.getId(), List.of()));
            String masterNames = entity.getMasterList().stream().map(AssetResourceDTO::getMasterName)
                    .collect(Collectors.joining(","));
            entity.setMasterNames(StringUtils.isNotBlank(masterNames) ? masterNames : ConstantUtils.PLACEHOLDER_2);
            entity.setIsUsedName(listVO.getIsDesigner()
                    ? ICommonEnum.getNameById(entity.getIsUsed(), AssetIsUsedEnum.class)
                    : ConstantUtils.PLACEHOLDER_2
            );
            entity.setIsMasterUsedName(null != entity.getIsMasterUsed() ?
                    ICommonEnum.getNameById(entity.getIsMasterUsed(), AssetIsUsedEnum.class)
                    : ConstantUtils.PLACEHOLDER_2
            );
        });
        return new PageUtils<>(pageData);
    }

    /**
     * 获取素材列表及其投放数据
     *
     * @param listVO       查询参数
     * @param queryWrapper SQL参数
     * @return 返回结果
     */
    private IPage<AssetListDTO> getMarketAssetList(AssetListVO listVO, QueryWrapper<Asset> queryWrapper) {

        IPage<AssetListDTO> page = new Page<>(listVO.getPage(), listVO.getPageNum());
        IPage<AssetListDTO> pageData = new Page<>();
        // 报表数据查询参数
        AssetReportListVO reportListVO = new AssetReportListVO();
        BeanUtils.copyProperties(listVO, reportListVO);
        // 如果查询了活动、计划
        if (CollectionUtils.isNotEmpty(listVO.getCampaignIds()) || CollectionUtils.isNotEmpty(listVO.getPlanIds())) {
            queryWrapper.eq("mcu.is_del", IsDelEnum.NORMAL.getId())
                    .in(CollectionUtils.isNotEmpty(listVO.getCampaignIds()),
                            "mcu.campaign_id", listVO.getCampaignIds())
                    .in(CollectionUtils.isNotEmpty(listVO.getPlanIds()),
                            "mcu.plan_id", listVO.getPlanIds());

        }

        // 默认以ID排序，则先查询素材信息，在查询报表数据
        if (List.of("", "id", "uploadTime").contains(listVO.getSortField())
                || listVO.getSortType().equals("normal")) {
            // 查询素材数据
            pageData = (CollectionUtils.isNotEmpty(listVO.getCampaignIds())
                    || CollectionUtils.isNotEmpty(listVO.getPlanIds()))
                    ? this.baseMapper.getAssetPageByUnit(page, queryWrapper)
                    : this.baseMapper.getAssetPage(page, queryWrapper);
            if (pageData.getRecords().isEmpty()) {
                return pageData;
            }
            reportListVO.setAssetIds(pageData.getRecords().stream().map(AssetListDTO::getId)
                    .collect(Collectors.toList()));
            reportListVO.setPage(1L);
            reportListVO.setPageNum((long) pageData.getRecords().size());
            reportListVO.setSortField("");
            reportListVO.setSearch("");
            // 将查询报表的创意标签参数置为0，避免重复查询
            reportListVO.setVideoType(0L);
            reportListVO.setCreativeDirection(0L);
            reportListVO.setGoodsCategory(0L);
            reportListVO.setAssetCategory(0L);
            reportListVO.setTimeZone(listVO.getTimeZone());
            reportListVO.setMasterIds(List.of(listVO.getMasterId()));
            // 获取素材的投放数据
            Map<Long, AssetReportListDTO> assetReportMap = this.fgReportService
                    .getAssetReportList(reportListVO).getData()
                    .stream().collect(Collectors.toMap(AssetReportListDTO::getAssetId, Function.identity()));
            // 填充投放数据
            pageData.getRecords().forEach(entity -> {
                if (assetReportMap.get(entity.getId()) != null) {
                    BeanUtils.copyProperties(assetReportMap.get(entity.getId()),
                            entity, "assetName", "assetPath");
                }
            });
        } else {
            // 1. 先查询筛选后的素材ID
            List<AssetListDTO> assetListDTOList = (CollectionUtils.isNotEmpty(listVO.getCampaignIds())
                    || CollectionUtils.isNotEmpty(listVO.getPlanIds()))
                    ? this.baseMapper.getAssetListByUnit(queryWrapper) : this.baseMapper.getAssetList(queryWrapper);
            if (assetListDTOList.isEmpty()) {
                return pageData;
            }
            reportListVO.setAssetIds(assetListDTOList.stream().map(AssetListDTO::getId).collect(Collectors.toList()));
            reportListVO.setSearch("");
            // 将查询报表的创意标签参数置为0，避免重复查询
            reportListVO.setVideoType(0L);
            reportListVO.setCreativeDirection(0L);
            reportListVO.setGoodsCategory(0L);
            reportListVO.setAssetCategory(0L);
            // 2. 再查询这些ID的报表数据
            FeignR<List<AssetReportListDTO>> feignR = this.fgReportService.getAssetReportList(reportListVO);
            List<AssetListDTO> reportList = feignR.getData().stream().map(u -> {
                AssetListDTO assetListDTO = new AssetListDTO();
                BeanUtils.copyProperties(u, assetListDTO);
                assetListDTO.setId(u.getAssetId());
                return assetListDTO;
            }).collect(Collectors.toList());
            if (reportList.isEmpty()) {
                return pageData;
            }
            // 获取素材的信息Map
            Map<Long, AssetListDTO> assetMap = assetListDTOList.stream().collect(
                    Collectors.toMap(AssetListDTO::getId, Function.identity()));
            // 填充数据
            reportList.forEach(entity -> {
                AssetListDTO assetListDTO = assetMap.get(entity.getId());
                entity.setAssetName(assetListDTO.getAssetName());
                entity.setAssetPath(assetListDTO.getAssetPath());
                entity.setFormat(assetListDTO.getFormat());
                entity.setHeight(assetListDTO.getHeight());
                entity.setWidth(assetListDTO.getWidth());
                entity.setSize(assetListDTO.getSize());
                entity.setAssetSize(assetListDTO.getAssetSize());
                entity.setDuration(assetListDTO.getDuration());
                entity.setSourceType(assetListDTO.getSourceType());
                entity.setUploadTime(assetListDTO.getUploadTime());
                entity.setCreateUid(assetListDTO.getCreateUid());
                entity.setCreateName(assetListDTO.getCreateName());
                entity.setCoverImgId(assetListDTO.getCoverImgId());
                entity.setAssetType(assetListDTO.getAssetType());
                entity.setIsMasterUsed(assetListDTO.getIsMasterUsed());
            });
            pageData.setRecords(reportList);
            pageData.setTotal(feignR.getTotal());
        }
        pageData.getRecords().forEach(u -> {
            // 视频素材需要增加缩略图地址
            if (AssetTypeEnum.VIDEO.getId().equals(u.getAssetType()) && !u.getCoverImgId().equals(0)) {
                Asset coverImg = getAsset(u.getCoverImgId().longValue());
                u.setCoverImgUrl(coverImg.getHttpUrl());
            } else {
                u.setCoverImgUrl(ConstantUtils.EMPTY);
            }
        });
        return pageData;
    }

    /**
     * 查询素材关联触点单元数量
     *
     * @param masterId 账号ID
     * @param assetIds 素材ID
     * @return 返回结果
     */
    @Override
    public Map<Long, Integer> getUnitCountOfAsset(Long masterId, List<Long> assetIds, Integer fieldType, User user) {
        return this.baseMapper.getAssetCreativeUnitNumList(new QueryWrapper<Asset>()
                        .eq("mc.is_del", IsDelEnum.NORMAL.getId())
                        .eq("mp.is_del", IsDelEnum.NORMAL.getId())
                        .eq("mp.plan_mode", PlanModeEnum.NORMAL.getId())
                        .eq("mp.is_plan_put", sheinConfiguration.isPut(null, user.getRoleId()))
                        .eq("ma.is_del", IsDelEnum.NORMAL.getId())
                        .eq("mcu.is_del", IsDelEnum.NORMAL.getId())
                        .eq("mcu.master_id", masterId)
                        .eq("mcu.audit_status", CreativeAuditStatusEnum.PASS.getId())
                        .eq("mma.field_type", fieldType)
                        .in("ma.id", assetIds)
                        .groupBy("ma.id")).stream()
                .collect(Collectors.toMap(AssetCreativeUnitNumDTO::getAssetId,
                        AssetCreativeUnitNumDTO::getCreativeUnitNum)
                );
    }

    /**
     * 查询素材关联触点单元数量
     *
     * @param masterId 账号ID
     * @param assetIds 素材ID
     * @return 返回结果
     */
    @Override
    public Map<Long, Integer> getUnitPutCountOfAsset(Long masterId, List<Long> assetIds, Integer fieldType, User user) {
        return this.baseMapper.getAssetCreativeUnitNumList(new QueryWrapper<Asset>()
                        .eq("mc.is_del", IsDelEnum.NORMAL.getId())
                        .eq("mc.campaign_status", CampaignStatusEnum.OPEN.getId())
                        .eq("mp.is_del", IsDelEnum.NORMAL.getId())
                        .eq("mp.plan_status", PlanStatusEnum.MARKETING.getId())
                        .eq("mp.plan_mode", PlanModeEnum.NORMAL.getId())
                        .eq("mp.is_plan_put", sheinConfiguration.isPut(null, user.getRoleId()))
                        .eq("ma.is_del", IsDelEnum.NORMAL.getId())
                        .eq("mcu.is_del", IsDelEnum.NORMAL.getId())
                        .eq("mcu.creative_unit_status", CreativeUnitStatusEnum.MARKETING.getId())
                        .eq("mcu.master_id", masterId)
                        .eq("mcu.audit_status", CreativeAuditStatusEnum.PASS.getId())
                        .in("ma.id", assetIds)
                        .eq("mma.field_type", fieldType)
                        .groupBy("ma.id")).stream()
                .collect(Collectors.toMap(AssetCreativeUnitNumDTO::getAssetId,
                        AssetCreativeUnitNumDTO::getCreativeUnitNum)
                );
    }

    /**
     * 查询素材对应的素材组
     *
     * @param masterId 账户
     * @param assetIds 素材组
     * @return 返回数据
     */
    private Map<Long, List<AssetTaskNameDTO>> getAssetTaskOfAsset(Long masterId, List<Long> assetIds) {
        return this.taskProductAssetMapper.listAssetTaskName(new QueryWrapper<>()
                .eq("mtpa.is_del", IsDelEnum.NORMAL.getId())
                .in("mtpa.asset_id", assetIds)
                .eq("mat.is_del", IsDelEnum.NORMAL.getId())
                .eq("matp.is_del", IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(masterId), "mat.master_id", masterId)
                .groupBy("mtpa.asset_id")
                .groupBy("mtpa.asset_task_id")
                .orderByDesc("mtpa.asset_id")
                .orderByDesc("mtpa.asset_task_id")
        ).stream().collect(Collectors.groupingBy(AssetTaskNameDTO::getAssetId));
    }

    /**
     * 查询素材对应的标签
     *
     * @param masterId 账号ID
     * @param assetIds 素材ID
     * @return 返回结果
     */
    private Map<Long, List<AssetLabelNameDTO>> getLabelOfAsset(Long masterId, List<Long> assetIds) {

        return this.assetLabelResourceMapper.getAssetLabelResource(new QueryWrapper<AssetLabelResource>()
                .eq("malr.is_del", IsDelEnum.NORMAL.getId())
                .eq("mal.is_del", IsDelEnum.NORMAL.getId())
                .eq("malr.master_id", masterId)
                .in("malr.asset_id", assetIds)
                .isNotNull("mal.id")
        ).stream().collect(Collectors.groupingBy(AssetLabelNameDTO::getAssetId));
    }

    /**
     * 查询素材相关的投放账号
     *
     * @param assetIds 素材ID
     * @return 返回结果
     */
    private Map<Long, List<AssetResourceDTO>> getMasterOfAsset(List<Long> assetIds) {
        return this.assetResourceMapper.getMasterOfAsset(new QueryWrapper<AssetResource>()
                .eq("mar.is_del", IsDelEnum.NORMAL.getId())
                .eq("mar.source_type", AssetSourceTypeEnum.ACCOUNT_SHARING.getId())
                .in("mar.asset_id", assetIds)
        ).stream().collect(Collectors.groupingBy(AssetResourceDTO::getId));
    }

    @Override
    public void saveAssetResource(AssetResourceSaveVO saveVO, Integer userId) {
        // 1.获取需要新增的记录
        List<AssetResource> assetResourceList = new ArrayList<>() {{
            saveVO.getAssetList().forEach(assetVO ->
                    saveVO.getMasterIds().forEach(masterId -> {
                        AssetResource assetResource = new AssetResource();
                        assetResource.setAssetId(assetVO.getAssetId());
                        assetResource.setAssetName(assetVO.getAssetName());
                        assetResource.setMasterId(masterId);
                        assetResource.setSourceType(saveVO.getSourceType());
                        assetResource.setIsDel(IsDelEnum.NORMAL.getId().intValue());
                        add(assetResource);
                    }));
        }};

        // 2.save
        this.assetResourceMapper.saveAssetResource(assetResourceList, userId);

        // 3.如果是共享，则共享相关标签
        if (saveVO.getSourceType().equals(AssetSourceTypeEnum.ACCOUNT_SHARING.getId())) {
            List<Long> assetIds = saveVO.getAssetList().stream().map(AssetVO::getAssetId).collect(Collectors.toList());
            Map<Long, List<AssetLabelResource>> assetLabelResourceMap = this.assetLabelResourceMapper
                    .selectList(new QueryWrapper<AssetLabelResource>().lambda()
                            .eq(AssetLabelResource::getIsDel, IsDelEnum.NORMAL.getId())
                            .eq(AssetLabelResource::getMasterId, userId)
                            .in(AssetLabelResource::getAssetId, assetIds)
                    ).stream().collect(Collectors.groupingBy(AssetLabelResource::getAssetId));
            if (assetLabelResourceMap.isEmpty()) {
                return;
            }
            List<AssetLabelResource> assetLabelResources = new ArrayList<>() {{
                assetLabelResourceMap.forEach(
                        (assetId, labelResources) -> labelResources.forEach(
                                labelResource -> saveVO.getMasterIds().forEach(masterId -> {
                                    AssetLabelResource assetLabelResource = new AssetLabelResource();
                                    assetLabelResource.setAssetId(assetId);
                                    assetLabelResource.setLabelId(labelResource.getLabelId());
                                    assetLabelResource.setMasterId(masterId);
                                    assetLabelResource.setIsDel(IsDelEnum.NORMAL.getId().intValue());
                                    add(assetLabelResource);
                                })
                        )
                );
            }};
            this.assetLabelResourceMapper.saveAssetLabelResource(assetLabelResources, userId);
            //下发账户素材组
            this.assetGroupService.batchAddMasterByAssetIds(assetIds,
                    saveVO.getMasterIds().stream().map(Long::intValue).collect(Collectors.toList()), userId);
        }

        // 4.如果是上传，则更新素材表中position_x,position_y,fill_width、及标签信息
        if (saveVO.getSourceType().equals(AssetSourceTypeEnum.LOCAL_UPLOAD.getId())) {

            saveVO.getAssetList().forEach(assetVO -> {
                Asset asset = new Asset();
                asset.setId(asset.getAssetId());
                // 设置DPA模版信息
                this.setAssetDpaPosition(asset, assetVO.getAssetName());
                // 判断是否需要设置素材的创意标签信息
                if (ObjectUtils.isNotNullOrZero(saveVO.getIsUpdateLabel())) {
                    asset.setVideoType(saveVO.getVideoType());
                    asset.setCreativeDirection(saveVO.getCreativeDirection());
                    asset.setGoodsCategory(saveVO.getGoodsCategory());
                    asset.setAssetCategory(saveVO.getAssetCategory());
                }
                this.baseMapper.update(asset, new QueryWrapper<Asset>().lambda()
                        .eq(Asset::getId, assetVO.getAssetId())
                );
            });
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAssetResourceV2(AssetResourceSaveV2VO saveVO, Integer userId) {
        //本地上传
        AssetResourceSaveVO assetResourceSaveVO = new AssetResourceSaveVO();
        assetResourceSaveVO.setAssetList(saveVO.getAssetList());
        assetResourceSaveVO.setIsUpdateLabel(0);
        assetResourceSaveVO.setMasterIds(List.of(saveVO.getUserId()));
        assetResourceSaveVO.setSourceType(AssetSourceTypeEnum.LOCAL_UPLOAD.getId());
        this.saveAssetResource(assetResourceSaveVO, userId);
        //分享数据
        if (CollectionUtils.isNotEmpty(saveVO.getMasterIds())) {
            assetResourceSaveVO.setMasterIds(saveVO.getMasterIds());
            assetResourceSaveVO.setSourceType(AssetSourceTypeEnum.ACCOUNT_SHARING.getId());
            this.saveAssetResource(assetResourceSaveVO, userId);
        }
    }

    @Override
    public List<Long> checkExistUnit(AssetResourceDeleteGetVO getVO) {

        if (getVO.getAssetIds().isEmpty() || getVO.getMasterIds().isEmpty()) {
            return List.of();
        }
        // 先查有没有关联的创意单元
        return this.creativeUnitMapper.listUnitByAssetId(new QueryWrapper<CreativeUnit>()
                        .in("mar.asset_id", getVO.getAssetIds())
                        .in("mar.master_id", getVO.getMasterIds())
                        .eq("mar.is_del", IsDelEnum.NORMAL.getId())
                        .eq("mcu.is_del", IsDelEnum.NORMAL.getId())
                        .ne("mcu.creative_unit_status", CreativeUnitStatusEnum.FILE.getId()))
                .stream().map(CreativeUnit::getId).collect(Collectors.toList());
    }

    @Override
    public void unitFile(AssetUnitFileVO fileVO, User user) {
        // 先查有没有关联的创意单元
        List<Long> creativeUnitIds = this.creativeUnitMapper.listUnitByAssetId(new QueryWrapper<CreativeUnit>()
                        .eq("mp.is_plan_put", sheinConfiguration.isPut(null, user.getRoleId()))
                        .in("mar.asset_id", fileVO.getAssetIds())
                        .in("mar.master_id", fileVO.getMasterIds())
                        .eq("mar.is_del", IsDelEnum.NORMAL.getId())
                        .eq("mcu.is_del", IsDelEnum.NORMAL.getId())
                        .eq("mma.field_type", 1)
                        .ne("mcu.creative_unit_status", CreativeUnitStatusEnum.FILE.getId()))
                .stream().map(CreativeUnit::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(creativeUnitIds)) {
            return;
        }
        CreativeUnit creativeUnit = new CreativeUnit();
        creativeUnit.setUpdateUid(user.getId());
        creativeUnit.setCreativeUnitStatus(CreativeUnitStatusEnum.FILE.getId());
        this.creativeUnitMapper.update(creativeUnit, new QueryWrapper<CreativeUnit>().lambda()
                .in(CreativeUnit::getId, creativeUnitIds));
    }

    @Override
    public void deleteAssetResource(AssetResourceDeleteGetVO getVO, Integer userId) {

        if (getVO.getAssetIds().isEmpty() || getVO.getMasterIds().isEmpty()) {
            return;
        }

        // 然后在删除素材绑定关系
        AssetResource assetResource = new AssetResource();
        assetResource.setIsDel(IsDelEnum.DELETE.getId().intValue());
        assetResource.setUpdateUid(userId);
        this.assetResourceMapper.update(assetResource, new QueryWrapper<AssetResource>().lambda()
                .eq(AssetResource::getIsDel, IsDelEnum.NORMAL.getId())
                .and(q -> getVO.getMasterIds().forEach(masterId ->
                        q.or(q1 -> q1.eq(AssetResource::getMasterId, masterId)
                                .in(AssetResource::getAssetId, getVO.getAssetIds())))));

        // 如果存在绑定的创意单元，则将创意单元状态设置为归档
        if (CollectionUtils.isNotEmpty(getVO.getCreativeUnitIds())) {
            CreativeUnit creativeUnit = new CreativeUnit();
            creativeUnit.setUpdateUid(userId);
            creativeUnit.setCreativeUnitStatus(CreativeUnitStatusEnum.FILE.getId());
            this.creativeUnitMapper.update(creativeUnit, new QueryWrapper<CreativeUnit>().lambda()
                    .in(CreativeUnit::getId, getVO.getCreativeUnitIds()));
        }
    }

    @Override
    public void formatAssetInfo() {
        // 每十分钟扫描一小时内需要解析的素材名称
        Date createTime = DateUtils.formatHour(new Date(), -3);
        List<AssetResource> assetResources = this.assetResourceMapper.selectList(new QueryWrapper<AssetResource>()
                .lambda()
                .in(AssetResource::getMasterId, this.lazadaMasterIds)
                .like(AssetResource::getAssetName, "lzd_")
                .eq(AssetResource::getCId, "")
                .gt(AssetResource::getUpdateTime, createTime)
        );
        assetResources.forEach(assetResource -> {
            setLazadaAssetInfo(assetResource, assetResource.getAssetName());
            this.assetResourceMapper.updateById(assetResource);
        });
    }

    @Override
    public void checkLazadaCidAsset() {
        // 检查是否有素材不包含lazada cid或解析异常
        Date today = DateUtils.getTodayDate();
        List<AssetResource> assetResources = this.assetResourceMapper.selectList(new QueryWrapper<AssetResource>()
                .lambda()
                .in(AssetResource::getMasterId, this.lazadaMasterIds)
                .gt(AssetResource::getUpdateTime, today)
                .and(i -> i.notLike(AssetResource::getAssetName, "lzd_")
                        .or(p -> p.like(AssetResource::getAssetName, "lzd_").eq(AssetResource::getCId, "")))
        );
        if (!assetResources.isEmpty()) {
            this.fgMonitorService.noticeDevelop(NoticeDevelopVO.builder()
                    .message(assetResources.size() + "素材不包含lazada cid或解析异常！").build());
        }

//        检查分国家素材cid前缀是否符合规则
//        分国家cid规律：
//        SG:lzd_im_20001/lzd_vi_20001
//        MY:lzd_im_20002/lzd_vi_20002
//        PH:lzd_im_20003/lzd_vi_20003
//        ID: lzd_im_20004/lzd_vi_20004
//        TH:lzd_im_20005/lzd_vi_20005
//        VN:lzd_im_20006/lzd_vi_20006
        Map<String, Integer> keyMap = new HashMap<>() {{
            put("20001", 10016);
            put("20002", 10014);
            put("20003", 10015);
            put("20004", 10007);
            put("20005", 10017);
            put("20006", 10018);
        }};
        List<String> cidKeys = List.of("lzd_im_", "lzd_vi_");
        List<AssetResource> errors = new ArrayList<>();
        keyMap.forEach((key, masterId) -> cidKeys.forEach(cidKey -> {
            List<AssetResource> masterAssets = this.assetResourceMapper.selectList(new QueryWrapper<AssetResource>()
                    .lambda().eq(AssetResource::getMasterId, masterId)
                    .gt(AssetResource::getCreateTime, today)
                    .like(AssetResource::getCId, cidKey)
                    .notLike(AssetResource::getCId, cidKey + key)
            );
            errors.addAll(masterAssets);
        }));
        if (!errors.isEmpty()) {
            log.info("{}素材cid前缀解析异常", errors.size());
            List<Long> masterIds = errors.stream().map(AssetResource::getMasterId)
                    .distinct().collect(Collectors.toList());

            this.fgMonitorService.noticeDevelop(NoticeDevelopVO.builder()
                    .message("账号：" + StringUtils.join(masterIds, ",") + "下cid异常！").build());
        }
    }

    private void setLazadaAssetInfo(AssetResource assetResource, String name) {
        String assetName = name.replace("-", "_");
        if (assetName.contains("lzd_")) {
            List<String> assetNames = Arrays.stream(assetName.split("\\.")[0].split("_"))
                    .collect(Collectors.toList());
            // 校验lzd_开头的第三个
            if (assetNames.size() >= 3 && NumberUtils.isNumeric(assetNames.get(2))) {
                List<String> cidNames = assetNames.subList(0, 3);
                String cId = String.join("_", cidNames);
                assetResource.setCId(cId);
            }
            if (assetNames.size() >= 5) {
                String triggerItemId = assetNames.get(3).equals("0") ? "" : assetNames.get(3);
                String subClusterId = assetNames.get(4).equals("0") ? "" : assetNames.get(4);
                if (NumberUtils.isNumeric(triggerItemId)) {
                    assetResource.setTriggerItemId(triggerItemId);
                }
                if (NumberUtils.isNumeric(subClusterId)) {
                    assetResource.setSubClusterId(subClusterId);
                }
            }
        }
    }

    private void setAssetDpaPosition(Asset asset, String assetName) {
        String name = assetName.split("\\.")[0];
        if (name.contains("DPA_")) {
            name = name.replaceAll("-", "_");
            String positionStr = name.split("DPA_")[1];
            String[] assetNames = positionStr.split("_");
            try {
                asset.setPositionX(Long.parseLong(assetNames[0].trim()));
                asset.setPositionY(Long.parseLong(assetNames[1].trim()));
                asset.setFillWidth(Long.parseLong(assetNames[2].trim()));
            } catch (Exception exception) {
                asset.setPositionX(0L);
                asset.setPositionY(0L);
                asset.setFillWidth(0L);
            }
        } else {
            asset.setPositionX(0L);
            asset.setPositionY(0L);
            asset.setFillWidth(0L);
        }
    }

    @Override
    public void deleteAsset(AssetDeleteGetVO getVO, Integer userId) {

        if (getVO.getIds().isEmpty()) {
            return;
        }
        // 删除素材——账号之间关联关系
        AssetResource assetResource = new AssetResource();
        assetResource.setUpdateUid(userId);
        assetResource.setIsDel(IsDelEnum.DELETE.getId().intValue());
        this.assetResourceMapper.update(assetResource, new QueryWrapper<AssetResource>().lambda()
                .eq(AssetResource::getMasterId, getVO.getMasterId())
                .in(AssetResource::getAssetId, getVO.getIds()));
        //删除素材-素材组之间的关联关系
        this.assetGroupService.batchDeleteGroupByAsset(getVO.getIds(), userId);
        // 删除素材——标签——账号之间关联关系
        this.deleteAssetLabelResource(getVO.getIds(), getVO.getMasterId(), userId);
    }

    @Override
    public void batchDownload(AssetBatchDownloadGetVO getVO, Integer userId, HttpServletRequest request,
                              HttpServletResponse response) {
        if (getVO.getIds().isEmpty()) {
            return;
        }
        Map<String, String> assetMap = this.baseMapper.getAssetPathList(new QueryWrapper<Asset>()
                        .eq("ma.is_del", IsDelEnum.NORMAL.getId())
                        .eq("mar.is_del", IsDelEnum.NORMAL.getId())
                        .eq("ma.asset_type", getVO.getAssetType())
                        .eq("mar.master_id", getVO.getMasterId())
                        .in("ma.id", getVO.getIds()))
                .stream().collect(Collectors.toMap(AssetNamePathDTO::getAssetName,
                        u -> UploadUtils.getUploadPath(u.getAssetPath()))
                );
        if (assetMap.isEmpty()) {
            throw new CustomException("素材不能为空");
        }
        String assetTypeName = ICommonEnum.getNameById(getVO.getAssetType(), AssetTypeEnum.class);
        String zipName = assetTypeName + "_" + DateUtils.getTodayStringDate() + "_"
                + Md5CalculateUtils.getStringMd5(userId + "_" + StringUtils.join(getVO.getIds(), ","))
                + ".zip";
        try {
            ZipUtils.zipAndDownload(zipName, request, response, assetMap);
        } catch (IOException e) {
            log.error("下载素材库文件失败，类型：{}, 参数：{}, 错误：{}", assetTypeName, getVO.getIds(), e.getMessage());
            throw new CustomException("素材文件下载失败，请联系研发人员");
        }
    }

    @Override
    public void shareAsset(AssetShareGetVO getVO, Integer userId) {

        if (getVO.getResourceIds().isEmpty()) {
            throw new CustomException("请设置共享账户");
        }
        List<Long> existMasterIds = this.assetResourceMapper.selectList(new QueryWrapper<AssetResource>().lambda()
                        .eq(AssetResource::getIsDel, IsDelEnum.NORMAL.getId())
                        .eq(AssetResource::getAssetId, getVO.getAssetId())
                        .in(AssetResource::getMasterId, getVO.getResourceIds()))
                .stream().map(AssetResource::getMasterId).collect(Collectors.toList());
        getVO.setResourceIds(getVO.getResourceIds().stream().filter(u -> !existMasterIds.contains(u))
                .collect(Collectors.toList()));

        if (getVO.getResourceIds().isEmpty()) {
            return;
        }

        // 保存素材给指定用户
        List<AssetResource> assetResourceList = getVO.getResourceIds().stream().map(resourceId -> {
            AssetResource assetResource = new AssetResource();
            assetResource.setAssetId(getVO.getAssetId());
            assetResource.setAssetName(getVO.getAssetName());
            assetResource.setMasterId(resourceId);
            assetResource.setSourceType(AssetSourceTypeEnum.ACCOUNT_SHARING.getId());
            assetResource.setIsDel(IsDelEnum.NORMAL.getId().intValue());
            return assetResource;
        }).collect(Collectors.toList());
        this.assetResourceMapper.saveAssetResource(assetResourceList, userId);

        // 保存素材与标签的关系给指定用户
        List<AssetLabelResource> assetLabelResources = this.assetLabelResourceMapper.selectList(
                new QueryWrapper<AssetLabelResource>().lambda()
                        .eq(AssetLabelResource::getIsDel, IsDelEnum.NORMAL.getId())
                        .eq(AssetLabelResource::getAssetId, getVO.getAssetId())
                        .eq(AssetLabelResource::getMasterId, getVO.getMasterId())
        );

        if (assetLabelResources.isEmpty()) {
            return;
        }
        List<AssetLabelResource> assetLabelList = new ArrayList<>() {{
            getVO.getResourceIds().forEach(resourceId -> assetLabelResources.forEach(assetLabelResource -> {
                AssetLabelResource assetLabel = new AssetLabelResource();
                BeanUtils.copyProperties(assetLabelResource, assetLabel);
                assetLabel.setMasterId(resourceId);
                assetLabel.setCreateUid(userId);
                add(assetLabel);
            }));
        }};
        this.assetLabelResourceMapper.saveAssetLabelResource(assetLabelList, userId);
    }

    @Override
    public List<AssetSizeDTO> getAlgorithmUrlList(AssetAlgorithmVO assetAlgorithmVO, Integer userId) {
        // 调用算法接口，获取衍生素材
        return List.of(new AssetSizeDTO(
                UploadUtils.getHttpUrl("/assets/2022/09/22/7ebf42abf8da46e7df668b14c4f093b3.jpg"),
                "4*3",
                1143L)
        );
    }

    @Override
    public List<SelectDTO2> getAlgorithmScaleSelect(AssetAlgorithmVO assetAlgorithmVO) {

        return new ArrayList<>() {{
            add(new SelectDTO2("2*3", "2*3"));
            add(new SelectDTO2("3*4", "3*4"));
            add(new SelectDTO2("1*1", "1*1"));
            add(new SelectDTO2("16*9", "16*9"));
            add(new SelectDTO2("16*10", "16*10"));
        }};
    }

    @Override
    public AssetSizeDTO getAlgorithmByScale(AssetAlgorithmGetVO getVO, Integer userId) {
        return new AssetSizeDTO(
                UploadUtils.getHttpUrl("/assets/2022/09/22/7ebf42abf8da46e7df668b14c4f093b3.jpg"),
                getVO.getScale(),
                1143L
        );
    }

    @Override
    public List<SelectDTO> listAsset(AssetGetVO getVO) {
        return this.baseMapper.listAsset(new QueryWrapper<Asset>().lambda().in(Asset::getId, getVO.getAssetIds()));
    }

    @Override
    public AssetCountDTO getAssetCount(AssetCountGetVO getVO) {

        AssetCountDTO assetCountDTO = new AssetCountDTO();
        List<AssetCountDTO> assetCountDTOS = this.baseMapper.getAssetCount(new QueryWrapper<Asset>()
                .eq("mar.master_id", getVO.getMasterId())
                .eq("ma.is_del", IsDelEnum.NORMAL.getId())
                .eq("mar.is_del", IsDelEnum.NORMAL.getId())
                .groupBy("ma.id"));
        if (assetCountDTOS.isEmpty()) {
            return assetCountDTO;
        }
        Map<Integer, List<AssetCountDTO>> assetCountMap = assetCountDTOS.stream()
                .collect(Collectors.groupingBy(AssetCountDTO::getAssetType));
        assetCountDTO.setImageCount(assetCountMap.getOrDefault(AssetTypeEnum.IMG.getId(), List.of()).size());
        assetCountDTO.setVideoCount(assetCountMap.getOrDefault(AssetTypeEnum.VIDEO.getId(), List.of()).size());
        assetCountDTO.setAssetCount(assetCountDTO.getImageCount() + assetCountDTO.getVideoCount());
        return assetCountDTO;
    }

    @Override
    public List<Long> getAssetIdsBySearch(AssetSearchGetVO getVO) {

        QueryWrapper<Asset> queryWrapper = new QueryWrapper<Asset>()
                .eq(ObjectUtils.isNotNullOrZero(getVO.getMasterId()),
                        "mar.master_id", getVO.getMasterId())
                .in(!getVO.getMasterIds().isEmpty(),
                        "mar.master_id", getVO.getMasterIds())
                // 过滤创意标签
                .eq(ObjectUtils.isNotNullOrZero(getVO.getVideoType()),
                        "ma.video_type", getVO.getVideoType())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getCreativeDirection()),
                        "ma.creative_direction", getVO.getCreativeDirection())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getGoodsCategory()),
                        "ma.goods_category", getVO.getGoodsCategory())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getAssetCategory()),
                        "ma.asset_category", getVO.getAssetCategory())
                .and(q -> q.like("ma.id", getVO.getSearch())
                        .or().like("mar.asset_name", getVO.getSearch()));
        // 是否查询尺寸
        this.fillSizeToWrapper(queryWrapper, getVO.getSizes());
        return this.baseMapper.getAssetIdsBySearch(queryWrapper);
    }

    @Override
    public Map<Long, AssetBaseDTO> getAssetInfoByIds(AssetGetVO getVO) {
        return this.baseMapper.getAssetInfoByIds(new QueryWrapper<Asset>()
                        .in(!getVO.getMasterIds().isEmpty(), "mar.master_id", getVO.getMasterIds())
                        .in("ma.id", getVO.getAssetIds())
                        .groupBy("ma.id")).stream()
                .collect(Collectors.toMap(AssetBaseDTO::getId, Function.identity()));
    }

    @Override
    public List<String> listAssetCIdByMasterId(AssetCIdGetVO getVO) {
        return this.baseMapper.listAssetCId(new QueryWrapper<Asset>()
                .eq("ma.is_del", IsDelEnum.NORMAL.getId())
                .eq("mar.is_del", IsDelEnum.NORMAL.getId())
                .eq("mar.master_id", getVO.getMasterId())
                .ne("mar.c_id", "")
                .eq(ObjectUtils.isNotNullOrZero(getVO.getAssetType()), "ma.asset_type", getVO.getAssetType())
                .orderByDesc("ma.id")
                .last(ObjectUtils.isNotNullOrZero(getVO.getCount()), " LIMIT " + getVO.getCount()));
    }

    @Override
    public void updateAssetName(AssetNameUpdateVO updateVO, Integer userId) {
        Asset asset = this.getAsset(updateVO.getAssetId());
        String format = UploadUtils.getExtension(updateVO.getAssetName());
        if (StringUtils.isNotBlank(asset.getFormat()) && !asset.getFormat().equals(format)) {
            throw new CustomException("不允许修改素材名称中格式后缀");
        }
        AssetResource assetResource = new AssetResource();
        assetResource.setAssetName(updateVO.getAssetName());
        this.assetResourceMapper.update(assetResource, new QueryWrapper<AssetResource>().lambda()
                .eq(AssetResource::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(AssetResource::getSourceType, AssetSourceTypeEnum.ACCOUNT_SHARING.getId())
                .eq(AssetResource::getAssetId, updateVO.getAssetId())
                .eq(AssetResource::getMasterId, updateVO.getMasterId()));
    }

    @Override
    public void compressAsset(Integer codeRate) {
        List<Asset> assets = this.baseMapper.selectList(new QueryWrapper<Asset>().lambda()
                .eq(Asset::getAssetType, AssetTypeEnum.VIDEO.getId())
                .eq(Asset::getIsCompress, 0)
                .last("LIMIT 20")
        );
        assets.forEach(asset -> {
            String filePath = UploadUtils.getUploadPath(asset.getContent());
            String execStr = this.compressAssetSh;
            String compressFilePath = filePath.replace(".mp4", "_" + codeRate + ".mp4");
            execStr = execStr.replace("__CODE_RATE__", codeRate.toString());
            execStr = execStr.replace("__FILE_PATH__", filePath);
            execStr = execStr.replace("__COMPRESS_FILE_PATH__", compressFilePath);
            log.info("compress video, asset id : {}, command : {}", asset.getId(), execStr);
            int isCompress = 2;
            Asset assetUpdate = new Asset();
            try {
                Process process = Runtime.getRuntime().exec(execStr);
                int exitCode = process.waitFor();
                if (exitCode == 0) {
                    isCompress = 1;
                    FFmpegDTO fFmpegDTO = FFmpegUtils.info(compressFilePath, asset.getContent());
                    assetUpdate.setCompressSize(fFmpegDTO.getSize());
                    assetUpdate.setCodeRate(codeRate);
                    log.info("compress video success, asset id : {}", asset.getId());
                } else {
                    log.info("compress video fail, asset id : {}", asset.getId());
                }
            } catch (IOException e) {
                log.info("compress video io error, asset id : {}", asset.getId());
            } catch (InterruptedException e) {
                log.info("compress video interrupted error, asset id : {}", asset.getId());
            }
            assetUpdate.setIsCompress(isCompress);
            this.baseMapper.update(assetUpdate, new UpdateWrapper<Asset>().lambda()
                    .eq(Asset::getId, asset.getId())
            );
        });
    }

    @Override
    public List<Long> newAssetIds(AssetNewIdsVO newIdsVO) {
        return this.baseMapper.selectList(new LambdaQueryWrapper<Asset>()
                .between(Asset::getCreateTime,
                        String.format("%s 00:00:00", newIdsVO.getNewStartDate()),
                        String.format("%s 23:59:59", newIdsVO.getNewEndDate()))
                .eq(Asset::getIsDel, IsDelEnum.NORMAL.getId())
                .select(Asset::getId)
        ).stream().map(Asset::getId).collect(Collectors.toList());
    }

    @Override
    public List<SelectDTO> selectAssetSize(AssetSizeSelectVO selectVO) {
        return this.assetSizeMapper.selectAssetSize(new QueryWrapper<AssetSize>().lambda()
                .like(StringUtils.isNotBlank(selectVO.getSearch()),
                        AssetSize::getSizeName, selectVO.getSearch()).orderByDesc(AssetSize::getId));
    }

    @Override
    public PageUtils<DesignAssetReportListDTO> listReport(AssetReportListVO listVO, Long userId) {
        FeignR<List<AssetReportListDTO>> feignR = fgReportService.listAssetPage(listVO);
        if (feignR.getTotal() == 0) {
            return new PageUtils<>(listVO.getPage(), listVO.getPageNum());
        }
        // 查询素材对应的标签
        Map<Long, List<AssetLabelNameDTO>> assetLabelNameMap = this.getLabelOfAsset(
                userId, feignR.getData().stream().map(AssetReportListDTO::getAssetId).collect(Collectors.toList()));
        List<DesignAssetReportListDTO> list = new ArrayList<>();
        feignR.getData().forEach(p -> {
            DesignAssetReportListDTO dto = new DesignAssetReportListDTO();
            BeanUtils.copyProperties(p, dto);
            dto.setLabelInfos(assetLabelNameMap.getOrDefault(p.getAssetId(), List.of()));
            list.add(dto);
        });
        return new PageUtils<>(list, feignR.getTotal());
    }

    /**
     * 添加尺寸至数据中
     *
     * @param queryWrapper 条件
     * @param sizes        尺寸
     */
    private void fillSizeToWrapper(QueryWrapper<Asset> queryWrapper, List<String> sizes) {
        if (sizes.isEmpty()) {
            return;
        }
        List<String> heights = new ArrayList<>();
        List<String> widths = new ArrayList<>();
        sizes.forEach(size -> {
            widths.add(size.split("[*]")[0]);
            heights.add(size.split("[*]")[1]);
        });
        queryWrapper.and(q -> q.in("ma.width", widths)
                .in("ma.height", heights));
    }

    /**
     * 删除素材——标签——广告主之间关联关系
     *
     * @param assetIds 素材ID列表
     * @param masterId 广告主ID
     * @param userId   用户ID
     */
    private void deleteAssetLabelResource(List<Long> assetIds, Long masterId, Integer userId) {
        AssetLabelResource assetLabelResource = new AssetLabelResource();
        assetLabelResource.setIsDel(IsDelEnum.DELETE.getId().intValue());
        assetLabelResource.setUpdateUid(userId);
        this.assetLabelResourceMapper.update(assetLabelResource, new QueryWrapper<AssetLabelResource>().lambda()
                .eq(AssetLabelResource::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(AssetLabelResource::getMasterId, masterId)
                .in(AssetLabelResource::getAssetId, assetIds));
    }

    /**
     * 生成缩略图
     *
     * @param basePath    路径
     * @param loginUserId 用户ID
     * @return 返回数据
     * @throws IOException 异常
     */
    private Long createCoverImg(String basePath, Integer loginUserId) throws IOException {
        String videoPath = UploadUtils.getUploadPath(basePath);
        // 获取视频素材地址
        String fileName = Md5CalculateUtils.getStringMd5(
                basePath + (new Date()).getTime() + Math.random() * 10000)
                + ".jpg";
        String destBasePath = basePath.substring(0, basePath.lastIndexOf("/") + 1) + fileName;
        String destPath = UploadUtils.getUploadPath(destBasePath);
        if (!FFmpegUtils.coverImg(videoPath, 1, destPath)) {
            throw new CustomException("生成视频封面图失败，请检查后重试");
        }
        if (!new File(destPath).exists()) {
            throw new CustomException("生成视频封面图失败，请检查后重试");
        }
        Asset asset = this.saveAsset(AssetTypeEnum.IMG.getId(), destBasePath, fileName, loginUserId);
        return asset.getId();
    }
}
