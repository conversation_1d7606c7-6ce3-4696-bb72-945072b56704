package com.overseas.service.market.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.enums.CpsProjectEnum;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.service.market.dto.assetTask.AssetTaskProductDTO;
import com.overseas.service.market.dto.assetTask.AssetTaskProductSyncDTO;
import com.overseas.service.market.entity.assetTask.AssetTaskProduct;
import com.overseas.service.market.entity.cps.CpsProduct;
import com.overseas.service.market.entity.cps.CpsProductMaterial;
import com.overseas.service.market.enums.assetTask.AssetProductStatusEnum;
import com.overseas.service.market.enums.assetTask.AssetProductTypeEnum;
import com.overseas.service.market.enums.cps.CpsProductTypeEnum;
import com.overseas.service.market.mapper.assetTask.AssetTaskProductMapper;
import com.overseas.service.market.mapper.cps.CpsProductMapper;
import com.overseas.service.market.mapper.cps.CpsProductMaterialMapper;
import com.overseas.service.market.service.AeAuthorizationService;
import com.overseas.service.market.service.AssetTaskProductService;
import com.overseas.service.market.service.LazadaService;
import com.overseas.service.market.vo.assetTask.CpsUrlGenerateVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class AssetTaskProductServiceImpl extends ServiceImpl<AssetTaskProductMapper, AssetTaskProduct>
        implements AssetTaskProductService {

    private final CpsProductMapper cpsProductMapper;

    private final CpsProductMaterialMapper cpsProductMaterialMapper;

    private final AeAuthorizationService aeAuthorizationService;

    private final LazadaService lazadaService;

    @Override
    public List<AssetTaskProductDTO> generateCpsUrl(CpsUrlGenerateVO generateVO) {
        List<AssetTaskProductDTO> result = new ArrayList<>();
        CpsProjectEnum projectEnum = ICommonEnum.get(generateVO.getProjectId(), CpsProjectEnum.class);
        generateVO.getAssetTaskProducts().forEach(assetTaskProductVO -> {
            AssetTaskProductDTO productDTO = new AssetTaskProductDTO();
            BeanUtils.copyProperties(assetTaskProductVO, productDTO);
            this.setProductInfo(productDTO, projectEnum);
            result.add(productDTO);
        });
        return result;
    }

    @Override
    public void syncProductSchedule() {
        List<AssetTaskProductSyncDTO> syncs = this.baseMapper.listAssetSync(new QueryWrapper<>()
                .and(q -> q.eq("matp.product_id", 0).or().eq("matp.product_cps_url", ""))
                .eq("matp.is_del", IsDelEnum.NORMAL.getId())
                .eq("mat.is_del", IsDelEnum.NORMAL.getId())
                .lt("matp.sync_count", 3)
        );
        if (syncs.isEmpty()) {
            log.info("同步结束，无商品信息需要同步");
        }
        Map<Integer, List<AssetTaskProductSyncDTO>> syncMap = syncs.stream()
                .collect(Collectors.groupingBy(AssetTaskProductSyncDTO::getProjectId));
        syncMap.forEach((key, list) -> {
            try {
                CpsProjectEnum cpsProjectEnum = ICommonEnum.get(key, CpsProjectEnum.class);
                List<String> productIds = list.stream()
                        .peek(u -> {
                            String productId = null;
                            try {
                                productId = this.getProductIdByUrl(cpsProjectEnum, u.getProductUrl());
                            } catch (Exception e) {
                                log.error(e.getMessage(), e);
                            }
                            u.setProductSign(productId);
                        }).map(AssetTaskProductSyncDTO::getProductSign)
                        .filter(StringUtils::isNotBlank).collect(Collectors.toList());
                Map<String, CpsProduct> cpsProductMap;
                if (CollectionUtils.isNotEmpty(productIds)) {
                    try {
                        switch (cpsProjectEnum) {
                            case AE:
                                aeAuthorizationService.pullProduct(CpsProductTypeEnum.PRODUCT_DETAIL,
                                        "", "", false, productIds);
                                break;
                            case LAZADA:
                                lazadaService.pullProductByIds(productIds);
                                break;
                            default:
                        }
                    } catch (Exception e) {
                        log.error("拉取数据问题:{}", e.getMessage(), e);
                    }
                    cpsProductMap = cpsProductMapper.selectList(new LambdaQueryWrapper<CpsProduct>()
                            .eq(CpsProduct::getProjectId, cpsProjectEnum.getId())
                            .in(CpsProduct::getProductSign, productIds)
                    ).stream().collect(Collectors.toMap(CpsProduct::getProductSign, Function.identity()));

                } else {
                    cpsProductMap = new HashMap<>();
                }
                list.forEach(sync -> {
                    try {
                        CpsProduct cpsProduct = cpsProductMap.get(sync.getProductSign());
                        if (null != cpsProduct) {
                            sync.setProductId(cpsProduct.getId());
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                    this.baseMapper.updateAssetSync(sync.getProductId(), sync.getId());
                });
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
    }

    private void setProductInfo(AssetTaskProductDTO productDTO, CpsProjectEnum projectEnum) {
        CpsProduct cpsProduct;
        productDTO.setAssetProductStatusName(ICommonEnum.getNameById(
                productDTO.getAssetProductStatus(), AssetProductStatusEnum.class));
        productDTO.setAssetProductTypeName(ICommonEnum.getNameById(
                productDTO.getAssetProductType(), AssetProductTypeEnum.class));
        if (ObjectUtils.isNotNullOrZero(productDTO.getProductId())) {
            cpsProduct = this.cpsProductMapper.selectById(productDTO.getProductId());
        } else {
            String productId = this.getProductIdByUrl(projectEnum, productDTO.getProductUrl());
            cpsProduct = this.cpsProductMapper.selectOne(new QueryWrapper<CpsProduct>().lambda()
                    .eq(CpsProduct::getProductSign, productId)
                    .eq(CpsProduct::getProjectId, projectEnum.getId())
                    .last("limit 1")
            );
        }
        if (null != cpsProduct) {
            productDTO.setProductId(cpsProduct.getId());
            productDTO.setProductTitle(cpsProduct.getProductTitle());
            if (StringUtils.isNotBlank(productDTO.getProductUrl())) {
                productDTO.setProductUrl(cpsProduct.getProductDetailUrl());
            }
            productDTO.setProductMaterials(this.cpsProductMaterialMapper.selectList(
                    new QueryWrapper<CpsProductMaterial>().lambda()
                            .eq(CpsProductMaterial::getProductId, cpsProduct.getId())
                            .eq(CpsProductMaterial::getIsDel, IsDelEnum.NORMAL.getId())));
        }
    }

    @Override
    public String getProductIdByUrl(CpsProjectEnum projectEnum, String productUrl) {
        String productId = "";
        switch (projectEnum) {
            case AE:
                if (StringUtils.isNotBlank(productUrl)) {
                    productId = StringUtils.substringBetween(productUrl, "item/", ".html");
                }
                break;
            case AMAZON:
                if (StringUtils.isNotBlank(productUrl)) {
                    productId = StringUtils.substringBetween(productUrl, "dp/", "?");
                }
                if (StringUtils.isBlank(productId)) {
                    return productId;
                }
                if (productId.contains("/")) {
                    productId = StringUtils.substringBefore(productId, "/");
                }
                break;
            case LAZADA:
                break;
            default:
        }
        return productId;
    }
}
