package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.SelectDTO2;
import com.overseas.common.enums.CpsProjectEnum;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ExcelUtils;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.utils.UploadUtils;
import com.overseas.service.market.dto.assetTask.*;
import com.overseas.service.market.entity.AssetLabel;
import com.overseas.service.market.entity.AssetSize;
import com.overseas.service.market.entity.User;
import com.overseas.service.market.entity.assetTask.*;
import com.overseas.service.market.entity.cps.CpsProductMaterial;
import com.overseas.service.market.enums.assetTask.AssetProductStatusEnum;
import com.overseas.service.market.enums.assetTask.AssetProductTypeEnum;
import com.overseas.service.market.enums.assetTask.AssetTaskStatusEnum;
import com.overseas.service.market.enums.assetTask.AssetTaskTypeEnum;
import com.overseas.service.market.enums.user.UserTypeEnum;
import com.overseas.service.market.mapper.AssetLabelMapper;
import com.overseas.service.market.mapper.AssetSizeMapper;
import com.overseas.service.market.mapper.assetTask.*;
import com.overseas.service.market.mapper.cps.CpsProductMaterialMapper;
import com.overseas.service.market.service.AssetTaskService;
import com.overseas.service.market.service.TaskProductAssetService;
import com.overseas.service.market.vo.assetTask.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AssetTaskServiceImpl extends ServiceImpl<AssetTaskMapper, AssetTask> implements AssetTaskService {

    private final AssetTaskProductMapper assetTaskProductMapper;

    private final AssetTaskFeedbackMapper assetTaskFeedbackMapper;

    private final TaskProductAssetMapper taskProductAssetMapper;

    private final CpsProductMaterialMapper cpsProductMaterialMapper;

    private final TaskProductAssetService taskProductAssetService;

    private final AssetLabelMapper assetLabelMapper;

    private final AssetSizeMapper assetSizeMapper;

    @Override
    public AssetTask saveAssetTask(AssetTaskSaveVO assetTaskSaveVO, Integer loginUserId) {
        this.checkAssetTaskName(assetTaskSaveVO.getAssetTaskName(), 0L);
        AssetTask assetTask = new AssetTask();
        BeanUtils.copyProperties(assetTaskSaveVO, assetTask);
        assetTask.setCreateUid(loginUserId);
        assetTask.setAssetSizeId(JSONObject.toJSONString(assetTaskSaveVO.getAssetSizeIds()));
        this.baseMapper.insert(assetTask);
        this.saveAssetTaskProducts(assetTaskSaveVO.getAssetTaskProducts(), assetTask.getId(), loginUserId);
        this.saveAssetTaskFeedbacks(assetTaskSaveVO.getAssetTaskFeedbacks(), assetTask.getId(), loginUserId);
        return assetTask;
    }

    @Override
    public AssetTask updateAssetTask(AssetTaskUpdateVO assetTaskUpdateVO, User user) {
        AssetTask origin = this.baseMapper.selectById(assetTaskUpdateVO.getId());
        this.checkAssetTaskName(assetTaskUpdateVO.getAssetTaskName(), assetTaskUpdateVO.getId());
        AssetTask assetTask = new AssetTask();
        BeanUtils.copyProperties(assetTaskUpdateVO, assetTask);
        assetTask.setUpdateUid(user.getId());
        //如果将状态设置未执行中， 且设置用户是设计师，且负责设计师不存在，则将此操作设计师设置为负责设计师
        if (List.of(AssetTaskStatusEnum.ING.getId(), AssetTaskStatusEnum.FEEDBACK.getId()).contains(assetTask.getAssetTaskStatus())) {
            if (ObjectUtils.isNullOrZero(assetTask.getDesignerId()) && UserTypeEnum.DESIGNER.getId().equals(user.getUserType())) {
                assetTask.setDesignerId(user.getId());
            }
        }
        if (!assetTaskUpdateVO.getAssetTaskStatus().equals(origin.getAssetTaskStatus())) {
            assetTask.setPrevStatus(origin.getAssetTaskStatus());
        }
        assetTask.setAssetSizeId(JSONObject.toJSONString(assetTaskUpdateVO.getAssetSizeIds()));
        this.baseMapper.updateById(assetTask);
        this.saveAssetTaskProducts(assetTaskUpdateVO.getAssetTaskProducts(), assetTask.getId(), user.getId());
        this.saveAssetTaskFeedbacks(assetTaskUpdateVO.getAssetTaskFeedbacks(), assetTask.getId(), user.getId());
        return assetTask;
    }

    @Override
    public List<SelectDTO> assetTaskUserSelect(AssetTaskUserSelectVO selectVO) {
        return this.baseMapper.selectAssetTaskUser(new QueryWrapper<>()
                .eq("mat.master_id", selectVO.getMasterId())
                .eq("mat.is_del", IsDelEnum.NORMAL.getId())
                .groupBy("mat.create_uid")
        );
    }

    @Override
    public PageUtils<AssetTaskListDTO> listAssetTask(AssetTaskListVO listVO, User loginUser) {
        QueryWrapper<AssetTask> queryWrapper = new QueryWrapper<>();
        // 非设计师只能查看自己创建的
        if (!UserTypeEnum.DESIGNER.getId().equals(loginUser.getUserType())) {
            queryWrapper.eq(ObjectUtils.isNotNullOrZero(listVO.getCreateUid()), "mat.create_uid", listVO.getCreateUid());
        }
        queryWrapper.eq("mat.is_del", IsDelEnum.NORMAL.getId())
                .and(CollectionUtils.isNotEmpty(listVO.getAssetSizeIds()),
                        q -> listVO.getAssetSizeIds().forEach(
                                assetSizeId -> q.like("mat.asset_size_id", assetSizeId)))
                .eq(ObjectUtils.isNotNullOrZero(listVO.getProjectId()),
                        "mat.project_id", listVO.getProjectId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getMasterId()),
                        "mat.master_id", listVO.getMasterId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getAssetTaskType()),
                        "mat.asset_task_type", listVO.getAssetTaskType())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getAssetTaskStatus()),
                        "mat.asset_task_status", listVO.getAssetTaskStatus())
//                .eq("matp.is_del", IsDelEnum.NORMAL.getId())
//                .eq("matp.asset_product_status", AssetProductStatusEnum.OPEN.getId())
                .and(StringUtils.isNotBlank(listVO.getSearch()),
                        i -> i.eq(StringUtils.isNumeric(listVO.getSearch()),
                                        "mat.id", listVO.getSearch())
                                .or().like("mat.asset_task_name", listVO.getSearch())
                ).groupBy("mat.id").orderByDesc("mat.id");
        IPage<AssetTaskListDTO> pageData = this.baseMapper.listAssetTask(
                new Page<>(listVO.getPage(), listVO.getPageNum()), queryWrapper);
        if (pageData.getRecords().isEmpty()) {
            return new PageUtils<>(pageData);
        }
        Map<Long, String> assetSizeMap = assetSizeMapper.selectBatchIds(pageData.getRecords().stream()
                        .flatMap(u -> JSONObject.parseArray(u.getAssetSizeId(), Long.class).stream()).distinct().collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(AssetSize::getId, AssetSize::getSizeName, (o, n) -> o));
        pageData.getRecords().forEach(assetTaskListDTO -> {
            assetTaskListDTO.setAssetTaskStatusName(
                    ICommonEnum.getNameById(assetTaskListDTO.getAssetTaskStatus(), AssetTaskStatusEnum.class));
            assetTaskListDTO.setAssetTaskTypeName(
                    ICommonEnum.getNameById(assetTaskListDTO.getAssetTaskType(), AssetTaskTypeEnum.class));
            assetTaskListDTO.setAssetSizeIds(JSONObject.parseArray(assetTaskListDTO.getAssetSizeId(), Long.class)
                    .stream().map(u -> {
                        String name = assetSizeMap.get(u);
                        if (null == name) {
                            return null;
                        } else {
                            return new SelectDTO(u, name);
                        }
                    }).filter(Objects::nonNull).collect(Collectors.toList()));
        });
        return new PageUtils<>(pageData);
    }

    @Override
    public AssetTaskGetDTO getAssetTask(AssetTaskGetVO getVO) {
        AssetTask assetTask = this.baseMapper.selectById(getVO.getAssetTaskId());
        if (ObjectUtils.isNullOrZero(assetTask)) {
            throw new CustomException("素材制作任务不存在");
        }
        AssetTaskGetDTO getDTO = new AssetTaskGetDTO();
        BeanUtils.copyProperties(assetTask, getDTO);
        getDTO.setAssetSizeIds(JSONObject.parseArray(getDTO.getAssetSizeId(), Integer.class));
        // 设置关联商品信息
        getDTO.setAssetTaskProducts(this.getAssetTaskProduct(getDTO.getId()));
        // 根据任务类型获取关联的素材信息
        getDTO.setMaterials(this.getAssetTaskMaterials(getDTO.getId()));
        // 设置关联反馈信息
        getDTO.setAssetTaskFeedbacks(this.getAssetTaskFeedback(getDTO.getId()));
        return getDTO;
    }

    @Override
    public List<SelectDTO> selectAssetTask(AssetTaskSelectVO selectVO) {
        return this.baseMapper.selectAssetTask(
                new QueryWrapper<AssetTask>().lambda()
                        .eq(AssetTask::getIsDel, IsDelEnum.NORMAL.getId())
                        .eq(ObjectUtils.isNotNullOrZero(selectVO.getAssetTaskType()),
                                AssetTask::getAssetTaskType, selectVO.getAssetTaskType())
                        .eq(ObjectUtils.isNotNullOrZero(selectVO.getProjectId()),
                                AssetTask::getProjectId, selectVO.getProjectId())
                        .eq(ObjectUtils.isNotNullOrZero(selectVO.getMasterId()),
                                AssetTask::getMasterId, selectVO.getMasterId())
                        // 状态数组不为空切不包含0时，查询指定状态
                        .in(!selectVO.getAssetTaskStatus().isEmpty()
                                        && !selectVO.getAssetTaskStatus().contains(0),
                                AssetTask::getAssetTaskStatus, selectVO.getAssetTaskStatus())
                        .orderByDesc(AssetTask::getId));
    }

    @Override
    public void bindProduct(AssetTaskBindVO bindVO, Integer loginUserId) {
        AssetTask assetTaskInDb = this.baseMapper.selectById(bindVO.getAssetTaskId());
        if (null == assetTaskInDb) {
            throw new CustomException("绑定素材制作任务不存在！");
        }
        if (!assetTaskInDb.getAssetTaskStatus().equals(bindVO.getAssetTaskStatus())) {
            assetTaskInDb.setAssetTaskStatus(bindVO.getAssetTaskStatus());
            this.baseMapper.updateById(assetTaskInDb);
        }
        // 设置绑定的商品
        this.saveAssetTaskProducts(bindVO.getAssetTaskProducts(), bindVO.getAssetTaskId(), loginUserId);
        // 设置反馈信息
        this.saveAssetTaskFeedbacks(bindVO.getAssetTaskFeedbacks(), assetTaskInDb.getId(), loginUserId);
    }

    @Override
    public List<AssetTaskImportExcelDTO> importExcel(AssetTaskImportExcelVO excelVO) {
        List<AssetTaskImportExcelDTO> list = ExcelUtils.read(UploadUtils.getUploadPath(excelVO.getFilePath()), AssetTaskImportExcelDTO.class);
        for (int i = 0; i < list.size(); i++) {
            if (null == list.get(i) || StringUtils.isBlank(list.get(i).getProductUrl())) {
                throw new CustomException("Excel中第" + (i + 2) + "行数据中商品链接为空");
            }
        }

        return list;
    }

    @Override
    public void exportProduct(AssetTaskGetVO getVO, HttpServletResponse response) throws IOException {
        AssetTask assetTask = this.baseMapper.selectById(getVO.getAssetTaskId());
        if (null == assetTask || assetTask.getIsDel() == IsDelEnum.DELETE.getId().intValue()) {
            throw new CustomException("素材制作任务不存在");
        }
        List<AssetTaskProductDTO> products = this.assetTaskProductMapper.listAssetTaskProduct(
                new QueryWrapper<AssetTaskProduct>().eq("matp.asset_task_id", assetTask.getId())
                        .eq("matp.is_del", IsDelEnum.NORMAL.getId()));
        List<SelectDTO2> header = new ArrayList<>() {{
            add(new SelectDTO2("productId", "商品ID"));
            add(new SelectDTO2("productTitle", "商品名称"));
            add(new SelectDTO2("trackingIdVal", "Tracking ID"));
            add(new SelectDTO2("productUrl", "商品链接"));
            add(new SelectDTO2("productCpsUrl", "投放链接"));
        }};
        if (!assetTask.getProjectId().equals(CpsProjectEnum.AMAZON.getId())) {
            header = header.stream().filter(u -> "trackingIdVal".equals(u.getKey())).collect(Collectors.toList());
        }
        ExcelUtils.download(response, String.format("%s-任务商品信息", assetTask.getAssetTaskName()), "Sheet", products, header);
    }

    @Override
    public void checkData(AssetTaskCheckDataVO dataVO) {
        //判断素材是否使用
        taskProductAssetService.checkAssetIsUse(dataVO.getMasterId(), dataVO.getAssetIds());
        if (CollectionUtils.isEmpty(dataVO.getLabelIds())) {
            throw new CustomException("无任何标签");
        }
        List<Long> fourth = this.assetLabelMapper.selectBatchIds(dataVO.getLabelIds())
                .stream().filter(u -> u.getLevel().equals(4)).map(AssetLabel::getPid).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(fourth)
                && this.assetLabelMapper.selectBatchIds(fourth).stream().anyMatch(u -> u.getPid().equals(103L))) {
            return;
        }
        throw new CustomException("必须选中商品分类标签");
    }

    @Override
    public void unbindProductAsset(AssetTaskAssetUnbindVO unbindVO, Integer loginUserId) {
        TaskProductAsset taskProductAsset = new TaskProductAsset();
        taskProductAsset.setIsDel(IsDelEnum.DELETE.getId());
        taskProductAsset.setUpdateUid(loginUserId);
        QueryWrapper<TaskProductAsset> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TaskProductAsset::getTaskProductId, unbindVO.getTaskProductId())
                .in(TaskProductAsset::getAssetId, unbindVO.getAssetIds());
        this.taskProductAssetMapper.update(taskProductAsset, queryWrapper);
    }

    @Override
    public void unbindTaskAsset(AssetBatchUnbindVO unbindVO, Integer loginUserId) {
        TaskProductAsset taskProductAsset = new TaskProductAsset();
        taskProductAsset.setIsDel(IsDelEnum.DELETE.getId());
        taskProductAsset.setUpdateUid(loginUserId);
        QueryWrapper<TaskProductAsset> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(TaskProductAsset::getAssetTaskId, unbindVO.getAssetTaskIds())
                .eq(TaskProductAsset::getAssetId, unbindVO.getAssetId());
        this.taskProductAssetMapper.update(taskProductAsset, queryWrapper);
    }

    private void checkAssetTaskName(String assetTaskName, Long assetTaskId) {
        AssetTask assetTask = this.baseMapper.selectOne(new QueryWrapper<AssetTask>().lambda()
                .ne(ObjectUtils.isNotNullOrZero(assetTaskId), AssetTask::getId, assetTaskId)
                .eq(AssetTask::getAssetTaskName, assetTaskName)
                .eq(AssetTask::getIsDel, IsDelEnum.NORMAL.getId()));
        if (null != assetTask) {
            throw new CustomException("素材制作任务名称重复，请检查！");
        }
    }

    private void saveAssetTaskProducts(List<AssetTaskProductVO> assetTaskProductVOS, Long assetTaskId,
                                       Integer loginUserId) {
        assetTaskProductVOS.forEach(assetTaskProductVO -> {
            AssetTaskProduct assetTaskProduct = new AssetTaskProduct();
            BeanUtils.copyProperties(assetTaskProductVO, assetTaskProduct);
            assetTaskProduct.setAssetTaskId(assetTaskId);
            if (ObjectUtils.isNotNullOrZero(assetTaskProductVO.getId())) {
                assetTaskProduct.setUpdateUid(loginUserId);
                this.assetTaskProductMapper.updateById(assetTaskProduct);
            } else {
                assetTaskProduct.setCreateUid(loginUserId);
                this.assetTaskProductMapper.insert(assetTaskProduct);
            }
        });
    }

    private void saveAssetTaskFeedbacks(List<AssetTaskFeedbackVO> assetTaskFeedbackVOS, Long assetTaskId,
                                        Integer loginUserId) {
        assetTaskFeedbackVOS.forEach(assetTaskFeedbackVO -> {
            AssetTaskFeedback assetTaskFeedback = new AssetTaskFeedback();
            assetTaskFeedback.setFeedbackContent(assetTaskFeedbackVO.getFeedbackContent());
            assetTaskFeedback.setAssetTaskId(assetTaskId);
            if (ObjectUtils.isNullOrZero(assetTaskFeedbackVO.getId())) {
                assetTaskFeedback.setCreateUid(loginUserId);
                this.assetTaskFeedbackMapper.insert(assetTaskFeedback);
            }
        });
    }

    private List<AssetTaskProductDTO> getAssetTaskProduct(Long assetTaskId) {
        List<AssetTaskProductDTO> products = this.assetTaskProductMapper.listAssetTaskProduct(
                new QueryWrapper<AssetTaskProduct>().eq("matp.asset_task_id", assetTaskId)
                        .eq("matp.is_del", IsDelEnum.NORMAL.getId()));
        List<Long> productIds = products.stream().map(AssetTaskProductDTO::getProductId).collect(Collectors.toList());
        List<CpsProductMaterial> productMaterials = productIds.isEmpty() ? List.of()
                : this.cpsProductMaterialMapper.selectList(new QueryWrapper<CpsProductMaterial>()
                .lambda().in(CpsProductMaterial::getProductId, productIds));
        Map<Long, List<CpsProductMaterial>> productMaterialsMap = productMaterials.stream()
                .collect(Collectors.groupingBy(CpsProductMaterial::getProductId));
        products.forEach(product -> {
            // 按照素材类型升序排序
            List<CpsProductMaterial> materials = productMaterialsMap.getOrDefault(product.getProductId(), List.of())
                    .stream().sorted(Comparator.comparing(CpsProductMaterial::getMaterialType))
                    .collect(Collectors.toList());
            product.setProductMaterials(materials);
            product.setAssetProductTypeName(
                    ICommonEnum.getNameById(product.getAssetProductType(), AssetProductTypeEnum.class));
            product.setAssetProductStatusName(
                    ICommonEnum.getNameById(product.getAssetProductStatus(), AssetProductStatusEnum.class));
        });
        return products;
    }

    private List<AssetTaskFeedbackDTO> getAssetTaskFeedback(Long assetTaskId) {
        return this.assetTaskFeedbackMapper.getAssetTaskFeedbacks(new QueryWrapper<AssetTaskFeedback>().lambda()
                .eq(AssetTaskFeedback::getAssetTaskId, assetTaskId)
                .eq(AssetTaskFeedback::getIsDel, IsDelEnum.NORMAL.getId())
                .orderByAsc(AssetTaskFeedback::getId)
        );
    }

    private List<AssetTaskMaterialsDTO> getAssetTaskMaterials(Long assetTaskId) {
        List<AssetTaskMaterialDTO> taskMaterials = this.taskProductAssetMapper.getAssetTaskMaterials(
                new QueryWrapper<TaskProductAsset>().eq("mtpa.asset_task_id", assetTaskId)
                        .eq("mtpa.is_del", IsDelEnum.NORMAL.getId()));
        Map<Long, List<AssetTaskMaterialDTO>> assetTaskMaterialsMap = taskMaterials.stream()
                .collect(Collectors.groupingBy(AssetTaskMaterialDTO::getTaskProductId));
        List<AssetTaskMaterialsDTO> result = new ArrayList<>();
        assetTaskMaterialsMap.forEach((taskProductId, assetTaskMaterials) -> {
            AssetTaskMaterialsDTO assetTaskMaterialsDTO = new AssetTaskMaterialsDTO();
            assetTaskMaterialsDTO.setAssetTaskId(assetTaskId);
            assetTaskMaterialsDTO.setTaskProductId(taskProductId);
            assetTaskMaterials.forEach(assetTaskMaterialDTO ->
                    assetTaskMaterialDTO.setAssetUrl(UploadUtils.getHttpUrl(assetTaskMaterialDTO.getContent())));
            assetTaskMaterialsDTO.setMaterials(assetTaskMaterials);
            result.add(assetTaskMaterialsDTO);
        });
        return result;
    }
}
