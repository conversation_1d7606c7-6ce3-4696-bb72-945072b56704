package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.service.market.dto.audit.master.AuditMasterGetDTO;
import com.overseas.service.market.entity.AuditAgentMaster;
import com.overseas.service.market.entity.AuditMaster;
import com.overseas.service.market.events.qualificationExpire.QualificationExpireEvent;
import com.overseas.service.market.vo.auditMaster.AuditMasterSaveVO;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.service.market.enums.master.MasterAuditMasterStatusEnum;
import com.overseas.service.market.mapper.AuditAgentMasterMapper;
import com.overseas.service.market.service.AssetService;
import com.overseas.service.market.service.AuditAgentMasterService;
import com.overseas.service.market.service.AuditMasterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-09-14 16:09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuditAgentMasterServiceImpl extends ServiceImpl<AuditAgentMasterMapper, AuditAgentMaster> implements AuditAgentMasterService {

    private final AuditMasterService auditMasterService;

    private final AssetService assetService;

    public final ApplicationContext applicationContext;

    @Override
    @Transactional
    public Long saveQualification(AuditMasterSaveVO vo, Integer loginUserId) {
        // 保存资质信息，保存时关联auditMaster表
        AuditMaster auditMasterSave = new AuditMaster();
        BeanUtils.copyProperties(vo, auditMasterSave);
        Long auditMasterId = auditMasterService.saveMasterMainInfo(auditMasterSave);
        // 保存主表信息
        // 先取出历史数据
        AuditAgentMaster auditAgentMasterSave = null;
        try {
            auditAgentMasterSave = getAgentMaster(auditMasterId, loginUserId);
        } catch (CustomException e) {
            log.info(e.getMessage());
        }
        // 保存时，如果主体ID更改了，需要对当前的auditMasterId进行数据判重，如果存在就提示不合法
        if (ObjectUtils.isNullOrZero(vo.getId()) || !auditMasterId.equals(vo.getId())) {
            if (null != auditAgentMasterSave) {
                throw new CustomException("当前已经存在该资质，请检查后重试");
            }
        }
        if (null == auditAgentMasterSave) {
            auditAgentMasterSave = new AuditAgentMaster();
            BeanUtils.copyProperties(vo, auditAgentMasterSave, "id", "bankName", "subBankName", "bankAccount", "validateMoney");
            auditAgentMasterSave.setAuditMasterId(auditMasterId);
            auditAgentMasterSave.setUserId(loginUserId);
            auditAgentMasterSave.setCreateUid(loginUserId);
            auditAgentMasterSave.setAuditStatus(MasterAuditMasterStatusEnum.AUDIT_WAIT.getId());
            auditAgentMasterSave.setStep(2);
            save(auditAgentMasterSave);
        } else {
            BeanUtils.copyProperties(vo, auditAgentMasterSave, "id", "bankName", "subBankName", "bankAccount", "validateMoney");
            auditAgentMasterSave.setAuditStatus(MasterAuditMasterStatusEnum.AUDIT_WAIT.getId());
            auditAgentMasterSave.setUpdateUid(loginUserId);
            auditAgentMasterSave.setStep(2);
            updateById(auditAgentMasterSave);
        }
        return auditAgentMasterSave.getAuditMasterId();
    }

    @Override
    public Long saveBankInfo(AuditMasterSaveVO vo, Integer loginUserId) {
        AuditAgentMaster auditAgentMasterInDb = getAgentMaster(vo.getId(), loginUserId);
        auditAgentMasterInDb.setBankAccount(vo.getBankAccount());
        auditAgentMasterInDb.setBankName(vo.getBankName());
        auditAgentMasterInDb.setSubBankName(vo.getSubBankName());
        // 如果当前状态在验证之后，那么提交后需要将状态重置到待对公验证状态
        if (auditAgentMasterInDb.getAuditStatus().compareTo(MasterAuditMasterStatusEnum.IDENTIFY_WAIT.getId()) > 0) {
            auditAgentMasterInDb.setAuditStatus(MasterAuditMasterStatusEnum.IDENTIFY_WAIT.getId());
        }
        auditAgentMasterInDb.setStep(3);
        updateById(auditAgentMasterInDb);
        return vo.getId();
    }

    @Override
    public Long validateMoney(AuditMasterSaveVO vo, Integer loginUserId) {
        AuditAgentMaster auditAgentMasterInDb = getAgentMaster(vo.getId(), loginUserId);
        // 打款需要检验当前的状态
        if (!MasterAuditMasterStatusEnum.IDENTIFY_WAIT.getId().equals(auditAgentMasterInDb.getAuditStatus())) {
            throw new CustomException("资质状态不合法，无法进行打款验证");
        }
        if (auditAgentMasterInDb.getValidateMoney().compareTo(vo.getValidateMoney()) != 0) {
            log.info("资质打款验证不通过，代理商ID：{}，资质ID：{}，输入金额：{}，打款金额：{}", loginUserId, vo.getId(), vo.getValidateMoney(), auditAgentMasterInDb.getValidateMoney());
            auditAgentMasterInDb.setAuditStatus(MasterAuditMasterStatusEnum.IDENTIFY_DENY.getId());
        } else {
            auditAgentMasterInDb.setAuditStatus(MasterAuditMasterStatusEnum.AUDIT_PASS.getId());
        }
        auditAgentMasterInDb.setStep(4);
        updateById(auditAgentMasterInDb);
        applicationContext.publishEvent(new QualificationExpireEvent(this, loginUserId, vo.getId(), auditAgentMasterInDb.getAuditStatus()));

        return vo.getId();
    }

    @Override
    public AuditMasterGetDTO getDetail(Long id, Integer loginUserId) {
        AuditAgentMaster auditAgentMasterInDb = getAgentMaster(id, loginUserId);
        AuditMasterGetDTO result = new AuditMasterGetDTO();
        BeanUtils.copyProperties(auditAgentMasterInDb, result, "id", "validateMoney");
        AuditMaster auditMasterInDb = auditMasterService.getMaster(auditAgentMasterInDb.getAuditMasterId());
        result.setBusinessLicence(auditMasterInDb.getBusinessLicence());
        result.setCreditCode(auditMasterInDb.getCreditCode());
        result.setId(auditAgentMasterInDb.getAuditMasterId());
        result.setBusinessLicenceAssetUrl(assetService.getAsset(auditAgentMasterInDb.getBusinessLicenceAssetId()).getHttpUrl());
        return result;
    }

    /**
     * 获取代理下的资质信息
     *
     * @param auditMasterId 审核广告主ID
     * @param userId 用户ID
     * @return 资质信息
     */
    @Override
    public AuditAgentMaster getAgentMaster(Long auditMasterId, Integer userId) {
        AuditAgentMaster one = getOne(new LambdaQueryWrapper<AuditAgentMaster>()
                .eq(AuditAgentMaster::getUserId, userId)
                .eq(AuditAgentMaster::getAuditMasterId, auditMasterId));
        if (null == one) {
            throw new CustomException("资质不存在，请检查后重试");
        }
        return one;
    }
}
