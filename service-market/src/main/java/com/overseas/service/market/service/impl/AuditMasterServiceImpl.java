package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.service.market.entity.AuditMaster;
import com.overseas.service.market.entity.Country;
import com.overseas.service.market.entity.User;
import com.overseas.service.market.feign.FgSystemService;
import com.overseas.service.market.mapper.AuditMasterMapper;
import com.overseas.service.market.vo.auditMaster.AuditMasterListVO;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.auditMaster.AuditMasterListDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.FeignR;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.sys.area.AreaCityListVO;
import com.overseas.service.market.enums.master.MasterAuditMasterStatusEnum;
import com.overseas.service.market.enums.user.UserTypeEnum;
import com.overseas.service.market.service.AuditMasterService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-16 20:13
 */
@Service
@RequiredArgsConstructor
public class AuditMasterServiceImpl extends ServiceImpl<AuditMasterMapper, AuditMaster> implements AuditMasterService {

    private final FgSystemService fgSystemService;

    @Override
    public List<SelectDTO> selectAuditMaster(User loginUser, List<Integer> permissionMasterIds) {
        // 代理则联表audit_agent_master查询符合条件的资质主体
        if (UserTypeEnum.AGENT.getId().equals(loginUser.getUserType())) {
            return this.baseMapper.selectPermissionAuditMasterByAgent(new QueryWrapper<AuditMaster>()
                    .select("aam.audit_master_id as id, am.business_licence AS `name`")
                    .eq("aam.user_id", loginUser.getId())
                    .orderByDesc("aam.audit_master_id"));
            // 管理则通过权限下的投放账号获取主体资质信息
        } else {
            return this.baseMapper.selectPermissionAuditMasterByManager(new QueryWrapper<User>()
                    .in("u.id", permissionMasterIds).select("ua.audit_master_id as id,am.business_licence as `name`")
                    .groupBy("ua.audit_master_id")
                    .orderByDesc("ua.audit_master_id"));
        }
    }

    @Override
    public PageUtils<AuditMasterListDTO> pageAuditMaster(AuditMasterListVO auditMasterListVO, Integer loginUserId) {
        QueryWrapper<AuditMaster> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("maa.user_id", loginUserId)
                .eq(ObjectUtils.isNotNullOrZero(auditMasterListVO.getAuditMasterStatus()), "maa.audit_status", auditMasterListVO.getAuditMasterStatus())
                .and(StringUtils.isNotEmpty(auditMasterListVO.getSearch()), i -> i.like("am.id", auditMasterListVO.getSearch())
                        .or().like("am.business_licence", auditMasterListVO.getSearch()))
                .orderByDesc("maa.audit_master_id");
        IPage<AuditMasterListDTO> iPage = new Page<>(auditMasterListVO.getPage(), auditMasterListVO.getPageNum());
        iPage = this.baseMapper.pageAuditMaster(iPage, queryWrapper);
        Map<String, Country> cityMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(iPage.getRecords())) {
            AreaCityListVO areaCityListVO = new AreaCityListVO();
            areaCityListVO.setCityIds(iPage.getRecords().stream().map(AuditMasterListDTO::getCityId).collect(Collectors.toList()));
            FeignR<List<Country>> feignR = fgSystemService.listCity(areaCityListVO);
            if (!feignR.getCode().equals(0)) {
                throw new CustomException(feignR.getCode(), feignR.getMsg());
            }
            cityMap = feignR.getData().stream().collect(Collectors.toMap(Country::getCityId, Function.identity()));
        }
        for (AuditMasterListDTO auditMaster : iPage.getRecords()) {
            auditMaster.setAuditStatusName(ICommonEnum.getNameById(auditMaster.getAuditStatus(), MasterAuditMasterStatusEnum.class));
            if (cityMap.containsKey(auditMaster.getCityId())) {
                Country country = cityMap.get(auditMaster.getCityId());
                auditMaster.setCompanyAddress(country.getCountryName() + "-" + country.getProvinceName() + "-" + country.getCityName());
            }
            if (StringUtils.isEmpty(auditMaster.getExpireDate())) {
                auditMaster.setExpireDate("无期限");
            }
        }
        return new PageUtils<>(iPage);
    }

    @Override
    public Long saveMasterMainInfo(AuditMaster auditMaster) {
        String creditCode = format(auditMaster.getCreditCode());
        String businessLicence = format(auditMaster.getBusinessLicence());
        if (StringUtils.isEmpty(creditCode)) {
            throw new CustomException("统一社会信用码不能为空");
        }
        if (StringUtils.isEmpty(businessLicence)) {
            throw new CustomException("营业执照名称不能为空");
        }
        AuditMaster one = getOne(new LambdaQueryWrapper<AuditMaster>().eq(AuditMaster::getCreditCode, creditCode));
        if (null == one) {
            one = new AuditMaster();
            one.setBusinessLicence(businessLicence);
            one.setCreditCode(creditCode);
            save(one);
        }
        return one.getId();
    }

    @Override
    public AuditMaster getMaster(Long id) {
        AuditMaster auditMaster = getById(id);
        if (null == auditMaster) {
            throw new CustomException("审核主体不存在，请检查后重试");
        }
        return auditMaster;
    }

    /**
     * 公共方法对统一社会信用码进行处理
     */
    private String format(String creditCode) {
        String replace = creditCode.replaceAll("（", "(")
                .replaceAll("）", ")");
        return replace.trim();
    }
}
