package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.vo.market.behaviorApp.*;
import com.overseas.common.configuration.SheinConfiguration;
import com.overseas.service.market.entity.*;
import com.overseas.service.market.events.notifyControl.ControlContants;
import com.overseas.service.market.events.notifyControl.ControlPlanEvent;
import com.overseas.service.market.feign.FgSystemService;
import com.overseas.service.market.mapper.PlanMapper;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.behaviorApp.BehaviorAppGetDTO;
import com.overseas.common.dto.market.behaviorApp.BehaviorAppIndustrySelectDTO;
import com.overseas.common.dto.market.behaviorApp.BehaviorAppListDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.FeignR;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.sys.industry.IndustryListGetVO;
import com.overseas.service.market.dto.behaviorApp.BehaviorAppSelectDTO;
import com.overseas.common.enums.market.bahaviorApp.BehaviorAppFieldTypeEnum;
import com.overseas.common.enums.market.bahaviorApp.BehaviorAppStatusEnum;
import com.overseas.common.enums.market.bahaviorApp.BehaviorAppTypeEnum;
import com.overseas.service.market.enums.monitor.MonitorStatusEnum;
import com.overseas.common.enums.market.plan.PlanStatusEnum;
import com.overseas.service.market.mapper.BehaviorAppFieldMapper;
import com.overseas.service.market.mapper.BehaviorAppMapper;
import com.overseas.service.market.mapper.MonitorMapper;
import com.overseas.service.market.service.BehaviorAppService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BehaviorAppServiceImpl extends ServiceImpl<BehaviorAppMapper, BehaviorApp> implements BehaviorAppService {

    private final BehaviorAppFieldMapper behaviorAppFieldMapper;

    private final PlanMapper planMapper;

    private final MonitorMapper monitorMapper;

    private final ApplicationContext applicationContext;

    private final FgSystemService fgSystemService;

    private final SheinConfiguration sheinConfiguration;

    private void checkBehaviorApp(Long id, String name, String packageName) {

        BehaviorApp behaviorApp = this.baseMapper.selectOne(new QueryWrapper<BehaviorApp>().lambda()
                .eq(BehaviorApp::getIsDel, IsDelEnum.NORMAL.getId())
                .ne(ObjectUtils.isNotNullOrZero(id), BehaviorApp::getId, id)
                .eq(StringUtils.isNotBlank(name), BehaviorApp::getAppName, name));
        if (null != behaviorApp) {
            throw new CustomException("应用名称已存在，请确认后再试");
        }
        List<BehaviorAppListDTO> behaviorAppGetDTOS = this.baseMapper.getBehaviorAppByPackageName(
                new QueryWrapper<BehaviorApp>().ne(ObjectUtils.isNotNullOrZero(id), "mba.id", id)
                        .eq(StringUtils.isNotBlank(packageName), "mbaf.content", packageName)
                        .eq("mba.is_del", IsDelEnum.NORMAL.getId())
                        .eq("mbaf.field_name", "package_name")
        );
        if (!behaviorAppGetDTOS.isEmpty()) {
            throw new CustomException("应用包名已存在，请确认后再试");
        }
    }

    /**
     * 增加哈希值
     *
     * @return 返回数据
     */
    private String getHashString(Map<String, Object> params) {

        TreeMap<String, Object> sortedMap = new TreeMap<>(params);
        StringBuilder keyString = new StringBuilder();
        for (Map.Entry<String, Object> entry : sortedMap.entrySet()) {
            keyString.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        String hashString = DigestUtils.md5Hex(String.valueOf(keyString));
        log.info("Hash base string {} , md5 {}", keyString, hashString);

        return hashString;
    }

    private void saveBehaviorAppField(BehaviorApp behaviorApp, BehaviorAppSaveVO saveVO) {

        List<String> fieldNameList = List.of("app_name", "app_type", "package_name", "industry_id",
                "app_store_url", "app_download_url", "app_domain", "app_version", "privacy_policy",
                "privacy_url", "permission_url"
        );

        List<BehaviorAppField> behaviorAppFieldList = fieldNameList.stream().map(fieldName -> {
            BehaviorAppField behaviorAppField = new BehaviorAppField();
            behaviorAppField.setBehaviorAppId(behaviorApp.getId());
            behaviorAppField.setInputType(BehaviorAppFieldTypeEnum.TXT.getId());
            behaviorAppField.setFieldName(fieldName);
            Object object = ObjectUtils.getObjectValue(saveVO, ObjectUtils.underlineToCamel(fieldName));
            if (ObjectUtils.underlineToCamel(fieldName).equals("industryId")) {
                behaviorAppField.setContent(JSONObject.toJSONString(object));
            } else {
                behaviorAppField.setContent(object == null ? "" : object.toString());
            }
            return behaviorAppField;
        }).collect(Collectors.toList());

        this.behaviorAppFieldMapper.saveBehaviorAppField(behaviorAppFieldList);
    }

    @Override
    public List<BehaviorAppSelectDTO> selectBehaviorApp(BehaviorAppSelectGetVO getVO, User user) {
        List<BehaviorAppSelectDTO> apps = this.baseMapper.selectBehaviorApp(new QueryWrapper<BehaviorApp>().lambda()
                .eq(ObjectUtils.isNotNullOrZero(getVO.getAppStatus()), BehaviorApp::getAppStatus, getVO.getAppStatus())
                .eq(BehaviorApp::getIsDel, IsDelEnum.NORMAL.getId())
                //TODO shein特殊修改
                .eq(sheinConfiguration.isRole(user.getRoleId()), BehaviorApp::getCreateUid, user.getId())
                .orderByDesc(BehaviorApp::getId));
        if (apps.isEmpty()) {
            return List.of();
        }
        List<Long> appIds = apps.stream().map(SelectDTO::getId).collect(Collectors.toList());
        List<Monitor> monitors = this.monitorMapper.selectList(new QueryWrapper<Monitor>().lambda()
                .in(Monitor::getAppId, appIds)
        );
        // 按照状态分组
        Map<Long, List<Monitor>> map = monitors.stream().collect(Collectors.groupingBy(Monitor::getAppId));
        Map<Integer, List<BehaviorAppSelectDTO>> appMap = apps.stream()
                .sorted(Comparator.comparing(BehaviorAppSelectDTO::getAppStatus))
                .collect(Collectors.groupingBy(BehaviorAppSelectDTO::getAppStatus));
        // 重新汇总数据
        List<BehaviorAppSelectDTO> result = new ArrayList<>();
        appMap.forEach((key, values) -> {
            result.addAll(values);
        });
        // 组装监测站点数据
        result.forEach(app -> {
            List<Monitor> ms = map.getOrDefault(app.getId(), List.of());
            app.setMonitorIds(ms.stream().map(Monitor::getId).collect(Collectors.toList()));
        });
        return result;
    }

    @Override
    public List<BehaviorAppIndustrySelectDTO> selectBehaviorAppAndIndustry(BehaviorAppSelectGetVO getVO, User user) {
        return this.baseMapper.selectBehaviorAppAndIndustry(new QueryWrapper<BehaviorApp>().lambda()
                .eq(ObjectUtils.isNotNullOrZero(getVO.getAppStatus()), BehaviorApp::getAppStatus, getVO.getAppStatus())
                //TODO shein特殊修改
                .eq(sheinConfiguration.isRole(user.getRoleId()), BehaviorApp::getCreateUid, user.getId())
                .eq(BehaviorApp::getIsDel, IsDelEnum.NORMAL.getId())
                .orderByAsc(BehaviorApp::getAppStatus)
        ).stream().map(map -> {
            BehaviorAppIndustrySelectDTO selectDTO = new BehaviorAppIndustrySelectDTO();
            selectDTO.setId((Long) map.get("id"));
            selectDTO.setName((String) map.get("name"));
            selectDTO.setAppStatus((Integer) map.get("app_status"));
            selectDTO.setIndustryId(
                    (StringUtils.isEmpty((String) map.get("industry_id")) || map.get("industry_id").equals("[]"))
                            ? List.of() : JSONObject.parseArray((String) map.get("industry_id"), Long.class)
            );
            return selectDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public PageUtils<BehaviorAppListDTO> getBehaviorPage(BehaviorAppListVO listVO, User user) {
        IPage<BehaviorAppListDTO> page = new Page<>(listVO.getPage(), listVO.getPageNum());
        IPage<BehaviorAppListDTO> pageData = this.baseMapper.getBehaviorAppPage(page, new QueryWrapper<BehaviorApp>()
                .eq(ObjectUtils.isNotNullOrZero(listVO.getAppType()), "mba.app_type", listVO.getAppType())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getAppStatus()), "mba.app_status", listVO.getAppStatus())
                .eq("mba.is_del", IsDelEnum.NORMAL.getId())
                .eq("mbaf.field_name", "package_name")
                //TODO shein特殊修改
                .eq(sheinConfiguration.isRole(user.getRoleId()), "mba.create_uid", user.getId())
                .and(StringUtils.isNotBlank(listVO.getSearch()), q -> q
                        .or().like("mba.id", listVO.getSearch())
                        .or().like("mba.app_name", listVO.getSearch()))
                .orderByDesc("mba.id"));

        pageData.getRecords().forEach(entity -> {
            entity.setAppTypeName(ICommonEnum.getNameById(entity.getAppType(), BehaviorAppTypeEnum.class));
            entity.setAppStatusName(ICommonEnum.getNameById(entity.getAppStatus(), BehaviorAppStatusEnum.class));
        });
        return new PageUtils<>(pageData);
    }

    @Override
    public BehaviorAppGetDTO getBehaviorApp(BehaviorAppGetVO getVO) {
        BehaviorAppGetDTO behaviorAppGetDTO = new BehaviorAppGetDTO();
        BehaviorApp behaviorAppInDb = this.getBehaviorAppById(getVO.getId());
        BeanUtils.copyProperties(behaviorAppInDb, behaviorAppGetDTO);

        List<BehaviorAppField> behaviorAppFieldList = this.behaviorAppFieldMapper.selectList(
                new QueryWrapper<BehaviorAppField>().lambda()
                        .eq(BehaviorAppField::getBehaviorAppId, behaviorAppGetDTO.getId()));

        behaviorAppFieldList.forEach(behaviorAppField -> {
            String fieldName = ObjectUtils.underlineToCamel(behaviorAppField.getFieldName());
            if (List.of("privacyPolicy", "appType").contains(fieldName)) {
                ObjectUtils.setObjectValue(behaviorAppGetDTO, fieldName, Integer.parseInt(behaviorAppField.getContent()));
            } else if (fieldName.equals("industryId")) {
                behaviorAppGetDTO.setIndustryId(JSONObject.parseArray(behaviorAppField.getContent(), Long.class));
                if (CollectionUtils.isNotEmpty(behaviorAppGetDTO.getIndustryId())) {
                    FeignR<String> feignR = this.fgSystemService.getIndustryName(new IndustryListGetVO(behaviorAppGetDTO.getIndustryId()));
                    if (!feignR.getCode().equals(0)) {
                        throw new CustomException(feignR.getCode(), feignR.getMsg());
                    }
                    behaviorAppGetDTO.setIndustryName(feignR.getData());
                }
            } else {
                ObjectUtils.setObjectValue(behaviorAppGetDTO, fieldName, behaviorAppField.getContent());
            }
        });

        behaviorAppGetDTO.setAppTypeName(ICommonEnum.getNameById(behaviorAppGetDTO.getAppType(),
                BehaviorAppTypeEnum.class));
        behaviorAppGetDTO.setAppStatusName(ICommonEnum.getNameById(behaviorAppGetDTO.getAppStatus(),
                BehaviorAppStatusEnum.class));
        return behaviorAppGetDTO;
    }

    @Override
    public void saveBehaviorApp(BehaviorAppSaveVO saveVO, Integer userId) {

        this.checkBehaviorApp(null, saveVO.getAppName(), saveVO.getPackageName());
        BehaviorApp behaviorApp = new BehaviorApp();
        behaviorApp.setAppName(saveVO.getAppName());
        behaviorApp.setAppType(saveVO.getAppType());
        behaviorApp.setMd5Id(this.getHashString(ObjectUtils.toMap(saveVO)));
        behaviorApp.setCreateUid(userId);
        this.baseMapper.insert(behaviorApp);

        // 插入App内容
        this.saveBehaviorAppField(behaviorApp, saveVO);
    }

    @Override
    public void updateBehaviorApp(BehaviorAppUpdateVO updateVO, Integer userId) {
        this.checkBehaviorApp(updateVO.getId(), updateVO.getAppName(), updateVO.getPackageName());

        BehaviorApp behaviorApp = this.getBehaviorAppById(updateVO.getId());
        BeanUtils.copyProperties(updateVO, behaviorApp);
        String originMd5 = behaviorApp.getMd5Id();
        behaviorApp.setMd5Id(this.getHashString(ObjectUtils.toMap(updateVO)));
        behaviorApp.setUpdateUid(userId);
        this.baseMapper.update(behaviorApp, new QueryWrapper<BehaviorApp>().lambda()
                .eq(BehaviorApp::getId, behaviorApp.getId())
        );

        this.behaviorAppFieldMapper.delete(new QueryWrapper<BehaviorAppField>().lambda()
                .eq(BehaviorAppField::getBehaviorAppId, updateVO.getId())
        );
        this.saveBehaviorAppField(behaviorApp, updateVO);

        log.info("Behavior origin md5 {}, new md5 {}", originMd5, behaviorApp.getMd5Id());
        // 通知中控
        if (!originMd5.equals(behaviorApp.getMd5Id())) {
            List<Long> planIds = this.planMapper.selectList(new QueryWrapper<Plan>().lambda()
                            .ne(Plan::getPlanStatus, PlanStatusEnum.FINISH.getId())
                            .eq(Plan::getIsDel, IsDelEnum.NORMAL.getId())
                            .eq(Plan::getBehaviorAppId, behaviorApp.getId()))
                    .stream().map(Plan::getId).collect(Collectors.toList());
            if (planIds.isEmpty()) {
                return;
            }
            // 同步修改关联计划中的行业ID
            Plan plan = new Plan();
            plan.setIndustryId(JSONObject.toJSONString(updateVO.getIndustryId()));
            plan.setCreateUid(userId);
            this.planMapper.update(plan, new QueryWrapper<Plan>().lambda().in(Plan::getId, planIds));

            planIds.forEach(
                    planId -> this.applicationContext.publishEvent(
                            new ControlPlanEvent(this, ControlContants.METHOD_UPDATE, planId)
                    )
            );
        }
    }

    @Override
    public void changeBehaviorAppStatus(BehaviorAppStatusGetVO getVO, Integer userId) {
        BehaviorApp behaviorApp = this.getBehaviorAppById(getVO.getId());
        behaviorApp.setAppStatus(getVO.getAppStatus());
        behaviorApp.setUpdateUid(userId);
        this.baseMapper.update(behaviorApp,
                new UpdateWrapper<BehaviorApp>().lambda().eq(BehaviorApp::getId, getVO.getId()));
    }

    @Override
    public void deleteBehaviorApp(BehaviorAppGetVO getVO, Integer userId) {

        BehaviorApp behaviorApp = this.getBehaviorAppById(getVO.getId());

        // 获取该行为应用已关联的所有计划ID
        List<Plan> planList = this.planMapper.selectList(new QueryWrapper<Plan>().lambda()
                .ne(Plan::getPlanStatus, PlanStatusEnum.FINISH.getId())
                .eq(Plan::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(Plan::getBehaviorAppId, getVO.getId()));
        if (!planList.isEmpty()) {
            throw new CustomException("当前应用已绑定计划，请确认后再试");
        }

        // 获取该行为应用已关联的所有监测站点ID
        List<Monitor> monitorList = this.monitorMapper.selectList(new QueryWrapper<Monitor>().lambda()
                .eq(Monitor::getMonitorStatus, MonitorStatusEnum.OPEN.getId())
                .eq(Monitor::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(Monitor::getAppId, getVO.getId()));
        if (!monitorList.isEmpty()) {
            throw new CustomException("当前应用已绑定监测站点，请确认后再试");
        }

        behaviorApp.setIsDel(IsDelEnum.DELETE.getId().intValue());
        behaviorApp.setUpdateUid(userId);
        this.baseMapper.update(behaviorApp,
                new QueryWrapper<BehaviorApp>().lambda().eq(BehaviorApp::getId, getVO.getId()));
    }

    /**
     * 根据ID获取应用信息
     *
     * @param id 应用ID
     * @return 应用信息
     */
    private BehaviorApp getBehaviorAppById(Long id) {
        BehaviorApp behaviorApp = this.baseMapper.selectById(id);
        if (null == behaviorApp) {
            throw new CustomException("应用不存在");
        }
        return behaviorApp;
    }
}
