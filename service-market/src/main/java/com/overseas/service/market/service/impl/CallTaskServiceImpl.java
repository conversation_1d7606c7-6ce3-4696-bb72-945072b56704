package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.aliyun.sdk.service.dyvmsapi20170525.models.QueryCallDetailByCallIdResponseBody;
import com.aliyun.sdk.service.dyvmsapi20170525.models.SingleCallByTtsResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.entity.BaseServer;
import com.overseas.common.enums.AliCallTtsCodeEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.utils.AliCallUtils;
import com.overseas.service.market.entity.call.CallNotice;
import com.overseas.service.market.entity.call.CallRecord;
import com.overseas.service.market.entity.call.CallTask;
import com.overseas.service.market.enums.call.CallRecordStatusEnum;
import com.overseas.service.market.mapper.BaseServerMapper;
import com.overseas.service.market.mapper.call.CallNoticeMapper;
import com.overseas.service.market.mapper.call.CallRecordMapper;
import com.overseas.service.market.mapper.call.CallTaskMapper;
import com.overseas.service.market.service.CallTaskService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CallTaskServiceImpl extends ServiceImpl<CallTaskMapper, CallTask> implements CallTaskService {

    private final BaseServerMapper baseServerMapper;

    private final CallNoticeMapper callNoticeMapper;

    private final CallRecordMapper callRecordMapper;

    @Value("${local.machine-room}")
    private String machineRoom;

    @Override
    public void saveSheinCallTask() {
        // 查询是否有新的变动未通知
        List<CallNotice> callNotices = this.callNoticeMapper.selectList(new QueryWrapper<CallNotice>()
                .lambda().eq(CallNotice::getCallTaskId, 0)
        );
        if (callNotices.isEmpty()) {
            return;
        }
        // 插入外呼任务记录
        CallTask callTask = new CallTask();
        callTask.setCallMessage((int) callNotices.stream().map(CallNotice::getSourceId).distinct().count() + "个计划");
        callTask.setIsDel(IsDelEnum.NORMAL.getId().intValue());
        this.baseMapper.insert(callTask);
        CallNotice callNotice = new CallNotice();
        callNotice.setCallTaskId(callTask.getId());
        // 任务ID更新到变更记录中
        int update = this.callNoticeMapper.update(callNotice, new QueryWrapper<CallNotice>().lambda()
                .in(CallNotice::getId, callNotices.stream().map(CallNotice::getId).collect(Collectors.toList())));
        // 任务下插入多个待执行外呼记录
        if (update > 0) {
            AliCallTtsCodeEnum.SHEIN.getCallPhones().forEach(phone -> {
                CallRecord callRecord = new CallRecord();
                callRecord.setCallTaskId(callTask.getId());
                callRecord.setPhoneNumber(phone);
                callRecord.setTtsCode(AliCallTtsCodeEnum.SHEIN.getTtsCode());
                this.callRecordMapper.insert(callRecord);
            });
        }
    }

    @Override
    public void execSheinCall() {
        List<CallRecord> callRecords = this.callRecordMapper.selectList(new QueryWrapper<CallRecord>().lambda()
                // 待处理，呼叫失败，未接听的重新拨打电话
                .in(CallRecord::getCallStatus, List.of(CallRecordStatusEnum.WAITING.getId(),
                        CallRecordStatusEnum.CALL_FAILED, CallRecordStatusEnum.NO_ANSWER))
                .eq(CallRecord::getIsDel, IsDelEnum.NORMAL.getId())
        );
        BaseServer baseServer = this.baseServerMapper.selectOne(new QueryWrapper<BaseServer>().lambda()
                .eq(BaseServer::getServerKey, this.machineRoom));
        if (null != baseServer) {
            callRecords.forEach(callRecord -> {
                Map<String, String> ttsParam = new HashMap<>();
                String planInfo = baseServer.getServerName() + "机房计划";
                ttsParam.put("planInfo", planInfo);
                SingleCallByTtsResponseBody singleCallByTtsResponseBody = AliCallUtils.call(callRecord.getPhoneNumber(),
                        AliCallTtsCodeEnum.SHEIN.getTtsCode(), ttsParam);
                if (null != singleCallByTtsResponseBody) {
                    callRecord.setCallId(singleCallByTtsResponseBody.getCallId());
                    callRecord.setCallStatus(CallRecordStatusEnum.CALLED.getId());
                    callRecord.setCallDate(System.currentTimeMillis());
                } else {
                    callRecord.setCallStatus(CallRecordStatusEnum.CALL_FAILED.getId());
                }
                this.callRecordMapper.updateById(callRecord);
            });
        }
    }

    @Override
    public void checkCallStatus() {
        // 查询呼叫成功的，检查是否接通
        List<CallRecord> callRecords = this.callRecordMapper.selectList(new QueryWrapper<CallRecord>().lambda()
                .eq(CallRecord::getCallStatus, CallRecordStatusEnum.CALLED.getId())
                .eq(CallRecord::getIsDel, IsDelEnum.NORMAL.getId())
        );
        callRecords.forEach(callRecord -> {
            QueryCallDetailByCallIdResponseBody callDetail = AliCallUtils.getCallDetail(callRecord.getCallId(),
                    callRecord.getCallDate());
            if (null != callDetail) {
                Map<String, String> dataMap = JSONObject.parseObject(callDetail.getData(), new TypeReference<>() {});
                if (List.of("200000", "200001").contains(dataMap.getOrDefault("state", ""))) {
                    callRecord.setCallStatus(CallRecordStatusEnum.ANSWERED.getId());
                } else {
                    callRecord.setCallStatus(CallRecordStatusEnum.NO_ANSWER.getId());
                }
            } else {
                callRecord.setCallStatus(CallRecordStatusEnum.CALL_FAILED.getId());
            }
            this.callRecordMapper.updateById(callRecord);
        });
    }
}
