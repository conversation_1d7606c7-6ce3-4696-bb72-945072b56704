package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.configuration.SheinConfiguration;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.report.ReportListDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.enums.SortTypeEnum;
import com.overseas.common.enums.TimeZoneEnum;
import com.overseas.common.enums.market.PutEnum;
import com.overseas.common.enums.market.campaign.CampaignTypeEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.DateUtils;
import com.overseas.common.utils.FeignR;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.campaign.*;
import com.overseas.common.vo.market.market.RecordBatchDeleteVO;
import com.overseas.common.vo.market.market.RecordBatchSwitchVO;
import com.overseas.common.vo.market.master.MasterPageFirstVO;
import com.overseas.common.vo.report.ReportHasCostGetVO;
import com.overseas.common.vo.report.SelectSortByCostVO;
import com.overseas.service.market.common.utils.BudgetUtils;
import com.overseas.service.market.dto.campaign.CampaignMasterSelectDTO;
import com.overseas.service.market.dto.market.MasterPageFirstDTO;
import com.overseas.service.market.dto.plan.WebPlanStatusContainerDTO;
import com.overseas.service.market.entity.*;
import com.overseas.service.market.enums.BatchUpdateEnum;
import com.overseas.common.enums.market.campaign.CampaignModeEnum;
import com.overseas.common.enums.market.campaign.CampaignWebStatusEnum;
import com.overseas.service.market.enums.plan.PlanGenerateMapTypeEnum;
import com.overseas.common.enums.market.plan.PlanStatusEnum;
import com.overseas.service.market.feign.FgReportService;
import com.overseas.service.market.feign.FgSystemService;
import com.overseas.service.market.mapper.*;
import com.overseas.service.market.service.CampaignService;
import com.overseas.service.market.service.CampaignUpdateRecordService;
import com.overseas.service.market.service.MarketPageListService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class CampaignServiceImpl extends ServiceImpl<CampaignMapper, Campaign> implements
        CampaignService, MarketPageListService {

    private final FgReportService fgReportService;

    private final PlanMapper planMapper;

    private final CreativeMapper creativeMapper;

    private final CreativeUnitMapper creativeUnitMapper;

    private final FgSystemService fgSystemService;

    private final SheinConfiguration sheinConfiguration;

    private final PlanGenerateMapMapper planGenerateMapMapper;

    private final CampaignUpdateRecordService campaignUpdateRecordService;

    @Override
    public List<SelectDTO> selectCampaign(CampaignSelectGetVO getVO, List<Integer> permissionMasterIds, User user) {
        List<Long> masterIds = permissionMasterIds.stream().map(Long::valueOf).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(getVO.getMasterIds()) && ObjectUtils.isNullOrZero(getVO.getMasterId())) {
            getVO.setMasterIds(masterIds);
        }
        if (CollectionUtils.isEmpty(getVO.getCampaignIds())) {
            //如果没有指定mode ，且非指定活动ID的情况下，则仅展示正常活动
            if (CollectionUtils.isEmpty(getVO.getCampaignMode())) {
                getVO.setCampaignMode(List.of(CampaignModeEnum.NORMAL.getId()));
            }
        }
        List<SelectDTO> campaignSelects = this.selectCampaign(getVO, user);
        if (campaignSelects.isEmpty()) {
            return campaignSelects;
        }
        if (ObjectUtils.isNotNullOrZero(getVO.getSortByCost())) {
            SelectSortByCostVO sortByCostVO = new SelectSortByCostVO();
            BeanUtils.copyProperties(getVO, sortByCostVO);
            sortByCostVO.setSelects(campaignSelects);
            sortByCostVO.setType(1);
            return fgReportService.selectSorByCost(sortByCostVO).getData();
        }
        return campaignSelects;
    }

    @Override
    public List<SelectDTO> selectCopyCampaign(CampaignSelectGetVO getVO, List<Integer> permissionMasterIds, User user) {
        if (ObjectUtils.isNullOrZero(getVO.getMasterId()) && CollectionUtils.isEmpty(getVO.getMasterIds())) {
            getVO.setMasterIds(permissionMasterIds.stream().map(Long::valueOf).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(getVO.getCampaignIds())) {
            //如果没有指定mode ，且非指定活动ID的情况下，则仅展示正常活动
            if (CollectionUtils.isEmpty(getVO.getCampaignMode())) {
                getVO.setCampaignMode(List.of(CampaignModeEnum.NORMAL.getId()));
            }
        }
        return this.baseMapper.selectCampaign(new QueryWrapper<Campaign>().lambda()
                .in(CollectionUtils.isNotEmpty(getVO.getMasterIds()), Campaign::getMasterId, getVO.getMasterIds())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getMasterId()), Campaign::getMasterId, getVO.getMasterId())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getExcludeDel()), Campaign::getIsDel, IsDelEnum.NORMAL.getId())
                .in(CollectionUtils.isNotEmpty(getVO.getCampaignIds()), Campaign::getId, getVO.getCampaignIds())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getMarketTarget()), Campaign::getMarketTarget, getVO.getMarketTarget())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getPutOnTarget()), Campaign::getPutOnTarget, getVO.getPutOnTarget())
                .in(CollectionUtils.isNotEmpty(getVO.getCampaignMode()), Campaign::getCampaignMode, getVO.getCampaignMode())
                .in(CollectionUtils.isEmpty(getVO.getCampaignIds()), Campaign::getCampaignMode, CampaignModeEnum.normal())
                .orderByDesc(Campaign::getId));
    }

    @Override
    public List<SelectDTO> selectCampaign(CampaignSelectGetVO getVO, User user) {
        return this.baseMapper.selectCampaign(new QueryWrapper<Campaign>().lambda()
                .eq(ObjectUtils.isNotNullOrZero(getVO.getMasterId()), Campaign::getMasterId, getVO.getMasterId())
                .in(CollectionUtils.isNotEmpty(getVO.getMasterIds()), Campaign::getMasterId, getVO.getMasterIds())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getExcludeDel()), Campaign::getIsDel, IsDelEnum.NORMAL.getId())
                .in(CollectionUtils.isNotEmpty(getVO.getCampaignIds()), Campaign::getId, getVO.getCampaignIds())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getMarketTarget()), Campaign::getMarketTarget, getVO.getMarketTarget())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getPutOnTarget()), Campaign::getPutOnTarget, getVO.getPutOnTarget())
                .in(CollectionUtils.isNotEmpty(getVO.getCampaignMode()), Campaign::getCampaignMode, getVO.getCampaignMode())
                .in(CollectionUtils.isEmpty(getVO.getCampaignIds()), Campaign::getCampaignMode, CampaignModeEnum.normal())
                .eq(Campaign::getIsCampaignPut, this.sheinConfiguration.isPut(getVO.getIsPut(), user.getRoleId()))
                .orderByDesc(Campaign::getId));
    }

    @Override
    public List<SelectDTO> selectPutCampaign(List<Long> masterIds) {
        return this.baseMapper.selectCampaign(new QueryWrapper<Campaign>().lambda()
                .in(CollectionUtils.isNotEmpty(masterIds), Campaign::getMasterId, masterIds)
                .eq(Campaign::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(Campaign::getCampaignStatus, PlanStatusEnum.MARKETING.getId())
                .eq(Campaign::getIsCampaignPut, PutEnum.IS_PUT)
                .orderByDesc(Campaign::getId));
    }

    @Override
    public PageUtils<CampaignMasterSelectDTO> selectPageCampaignAndMaster(CampaignMasterSelectVO selectVO,
                                                                          List<Integer> permissionMasterIds,
                                                                          User user) {
        IPage<CampaignMasterSelectDTO> iPage = new Page<>(selectVO.getPage(), selectVO.getPageNum());
        iPage = this.baseMapper.selectPageCampaignAndMaster(iPage, new QueryWrapper<Campaign>()
                .in("c.master_id", permissionMasterIds)
                .in(CollectionUtils.isNotEmpty(selectVO.getMasterIds()), "c.master_id", selectVO.getMasterIds())
                .and(q ->
                        //如果是指定活动ID，则不需要过滤状态，直接展示
                        q.in(CollectionUtils.isNotEmpty(selectVO.getCampaignIds()),
                                        "c.id", selectVO.getCampaignIds())
                                //如果是非指定活动ID，则需要过滤删除状态
                                .or(CollectionUtils.isEmpty(selectVO.getCampaignIds()),
                                        q1 -> q1.eq("c.is_del", IsDelEnum.NORMAL.getId())
                                )
                )
                .eq("c.campaign_mode", CampaignModeEnum.NORMAL.getId())
                .eq("c.is_campaign_put", PutEnum.IS_PUT.getId())
                .eq("c.is_campaign_put", sheinConfiguration.isPut(selectVO.getIsPut(), user.getRoleId()))
                .like(StringUtils.isNotBlank(selectVO.getSearch()), "c.campaign_name", selectVO.getSearch())
                .orderByDesc("c.id")
        );
        return new PageUtils<>(iPage);
    }

    @Override
    public List<SelectDTO> selectCampaign(List<Integer> masterIds) {
        return this.baseMapper.selectCampaign(new QueryWrapper<Campaign>().lambda()
                .in(Campaign::getMasterId, masterIds)
                .orderByDesc(Campaign::getId));
    }

    @Override
    public List<SelectDTO> selectCampaignHasCost(CampaignSelectGetVO getVO, User user) {
        ReportHasCostGetVO idsGetVO = new ReportHasCostGetVO();
        idsGetVO.setMasterId(getVO.getMasterId());
        idsGetVO.setType("campaign");
        List<Long> ids = this.fgReportService.getHasCostIds(idsGetVO).getData();
        if (ids.isEmpty()) {
            return List.of();
        }
        return this.baseMapper.selectCampaign(new QueryWrapper<Campaign>().lambda()
                .eq(Campaign::getIsDel, IsDelEnum.NORMAL.getId())
                .in(Campaign::getId, ids)
                .eq(Campaign::getIsCampaignPut, sheinConfiguration.isPut(getVO.getIsPut(), user.getRoleId()))
                .orderByDesc(Campaign::getId));
    }

    @Override
    public boolean checkRepeatName(String name, Integer masterId, Long campaignId) {
        return !this.lambdaQuery()
                .eq(Campaign::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(Campaign::getCampaignName, name)
                .ne(ObjectUtils.isNotNullOrZero(campaignId), Campaign::getId, campaignId)
                .eq(Campaign::getMasterId, masterId)
                .select(Campaign::getId).list().isEmpty();
    }

    @Override
    public Campaign getCampaign(Long id, Integer masterId) {
        Campaign campaign = this.getOne(new LambdaQueryWrapper<Campaign>()
                .eq(Campaign::getId, id).eq(Campaign::getMasterId, masterId)
        );
        if (null == campaign) {
            throw new CustomException("活动ID不存在，请检查后重试");
        }
        campaign.setBudgetDay(BudgetUtils.format(campaign.getBudgetDay(), campaign.getBudgetType()));
        return campaign;
    }

    @Override
    public Campaign getCopyCampaign(Long id, Integer masterId) {
        Campaign campaign = this.getOne(new LambdaQueryWrapper<Campaign>()
                .eq(Campaign::getId, id).eq(Campaign::getMasterId, masterId)
        );
        if (null == campaign) {
            throw new CustomException("活动ID不存在，请检查后重试");
        }
        if (isSheinCampaign(campaign)) {
            PlanGenerateMap planGenerateMap = planGenerateMapMapper.selectOne(new LambdaQueryWrapper<PlanGenerateMap>()
                    .eq(PlanGenerateMap::getSheinId, campaign.getId())
                    .eq(PlanGenerateMap::getMapType, PlanGenerateMapTypeEnum.CAMPAIGN.getId())
                    .eq(PlanGenerateMap::getIsDel, IsDelEnum.NORMAL.getId())
                    .last("limit 1")
            );
            campaign = this.getOne(new LambdaQueryWrapper<Campaign>()
                    .eq(Campaign::getId, planGenerateMap.getMarketId())
                    .eq(Campaign::getMasterId, masterId));
        }
        campaign.setBudgetDay(BudgetUtils.format(campaign.getBudgetDay(), campaign.getBudgetType()));
        return campaign;
    }

    @Override
    public Long saveCampaign(CampaignSaveVO saveVO, User user) {
        this.checkCampaign(null, saveVO.getCampaignName(), saveVO.getMasterId());
        Campaign campaign = new Campaign();
        BeanUtils.copyProperties(saveVO, campaign);
        campaign.setBudgetDay(BudgetUtils.inDb(campaign.getBudgetDay(), campaign.getBudgetType()));
        campaign.setCreateUid(user.getId());
        if (sheinConfiguration.isRole(user.getRoleId())) {
            campaign.setIsCampaignPut(PutEnum.NOT_PUT.getId());
        } else {
            campaign.setIsCampaignPut(PutEnum.IS_PUT.getId());
            //如果是非shein 则尝试读取defaultCampaignStatus字段
            if (ObjectUtils.isNotNullOrZero(saveVO.getDefaultCampaignStatus())) {
                campaign.setCampaignStatus(saveVO.getDefaultCampaignStatus());
            }
        }
        save(campaign);
        //如果是客户活动，创建投放活动
        if (sheinConfiguration.isRole(user.getRoleId())) {
            saveSheinCampaign(campaign);
        }
        //记录日志
        saveCampaignUpdateRecordBySaveVO(saveVO, campaign, user.getId());
        return campaign.getId();
    }

    @Override
    public Long updateCampaign(CampaignUpdateVO updateVO, Integer userId) {
        Campaign campaign = this.checkCampaign(updateVO.getId(), null, updateVO.getMasterId());
        //模式,删除状态不能变化
        updateVO.setCampaignMode(campaign.getCampaignMode());
        updateVO.setIsDel(campaign.getIsDel());
        BeanUtils.copyProperties(updateVO, campaign);
        campaign.setBudgetDay(BudgetUtils.inDb(campaign.getBudgetDay(), campaign.getBudgetType()));
        campaign.setUpdateUid(userId);
        if (null != updateVO.getNextBudgetTypeVal()) {
            campaign.setNextBudgetType(updateVO.getNextBudgetTypeVal());
        }
        if (null != updateVO.getNextBudgetDayVal()) {
            campaign.setNextBudgetDay(updateVO.getNextBudgetDayVal());
        }
        this.baseMapper.update(campaign, new QueryWrapper<Campaign>().lambda().eq(Campaign::getId, campaign.getId()));
        //记录日志
        this.saveCampaignUpdateRecordBySaveVO(updateVO, campaign, userId);
        return campaign.getId();
    }

    @Override
    public IPage<MasterPageFirstDTO> pageMarket(MasterPageFirstVO listVO, List<Integer> permissionMasterIds,
                                                List<Long> ids, WebPlanStatusContainerDTO webPlanStatusContainerDTO) {
        IPage<MasterPageFirstDTO> iPage = new Page<>(listVO.getPage(), listVO.getPageNum());
        return this.baseMapper.pageMarket(iPage, getQueryWrapper(listVO, permissionMasterIds, ids));
    }

    @Override
    public List<Long> listAllIdMarket(MasterPageFirstVO listVO, List<Integer> permissionMasterIds,
                                      WebPlanStatusContainerDTO webPlanStatusContainerDTO) {
        return this.baseMapper.listCampaignId(this.getQueryWrapper(listVO, permissionMasterIds, null));
    }

    @Override
    public List<Long> listAllMasterIdMarket(MasterPageFirstVO listVO, List<Integer> permissionMasterIds,
                                            WebPlanStatusContainerDTO webPlanStatusContainerDTO) {
        return this.baseMapper.listIdsByCampaign(this.getQueryWrapper(listVO, permissionMasterIds, null)
                .select(" DISTINCT c.master_id"));
    }

    /**
     * 公共方法获取查询条件（适用于推广页面）
     *
     * @param listVO              传入参数
     * @param permissionMasterIds 广告主ID
     * @param ids                 活动ID
     * @return 返回参数
     */
    private QueryWrapper<Campaign> getQueryWrapper(MasterPageFirstVO listVO, List<Integer> permissionMasterIds,
                                                   List<Long> ids) {
        QueryWrapper<Campaign> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("c.is_del", IsDelEnum.NORMAL.getId())
                //添加投放活动展示过滤
                .eq("campaign_mode", CampaignModeEnum.NORMAL.getId())
                //Shein投放过滤
                .eq("is_campaign_put", listVO.getIsPut());
        if (CollectionUtils.isEmpty(ids)) {
            queryWrapper
                    .in(CollectionUtils.isNotEmpty(listVO.getSheinCampaignIds()), "c.id", listVO.getSheinCampaignIds())
                    .in("c.master_id", CollectionUtils.isNotEmpty(listVO.getMasterIds()) ? listVO.getMasterIds() : permissionMasterIds)
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getCampaignStatus()), "c.campaign_status", listVO.getCampaignStatus())
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getCampaignType()), "c.campaign_type", listVO.getCampaignType())
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getMasterId()), "c.master_id", listVO.getMasterId())
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getMarketTarget()), "c.market_target", listVO.getMarketTarget())
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getPutOnTarget()), "c.put_on_target", listVO.getPutOnTarget())
                    .and(StringUtils.isNotBlank(listVO.getSearch()),
                            i -> i.like("c.id", listVO.getSearch())
                                    .or().like("c.campaign_name", listVO.getSearch())
                    )
                    .and(CollectionUtils.isNotEmpty(listVO.getSearches()), q -> {
                        if (CollectionUtils.isNotEmpty(listVO.getSearches())) {
                            listVO.getSearches().forEach(u -> q.like("c.campaign_name", u));
                        }
                    })
                    .ge(StringUtils.isNotEmpty(listVO.getCreateStartDate()),
                            "c.create_time", listVO.getCreateStartDate() + " 00:00:00")
                    .le(StringUtils.isNotEmpty(listVO.getCreateEndDate()),
                            "c.create_time", listVO.getCreateEndDate() + " 23:59:59");
        } else {
            queryWrapper.in("c.id", ids);
        }
        queryWrapper.orderBy(true, SortTypeEnum.ASC.getSortType().equals(listVO.getSortType()), "c.id")
                .groupBy("c.id");
        return queryWrapper;
    }

    @Override
    public Map<Long, ReportListDTO> getReportMapMarket(List<ReportListDTO> reportList) {
        return reportList.stream().collect(Collectors.toMap(ReportListDTO::getCampaignId, Function.identity()));
    }

    @Override
    public void formatListMarket(List<MasterPageFirstDTO> list, WebPlanStatusContainerDTO webPlanStatusContainerDTO, Long masterId) {
        for (MasterPageFirstDTO record : list) {
            record.setBudgetDay(BudgetUtils.format(record.getBudgetDay(), record.getBudgetType()));
            record.setNextBudgetDay(BudgetUtils.format(record.getNextBudgetDay(), record.getNextBudgetType()));
            record.setCampaignStatusName(ICommonEnum.getNameById(record.getCampaignStatus(), CampaignWebStatusEnum.class));
            record.setCampaignTypeName(ICommonEnum.getNameById(record.getCampaignType(), CampaignTypeEnum.class));
            record.setCampaignTimeZoneName(TimeZoneEnum.getNameById(record.getCampaignTimeZone()));
        }
    }

    @Override
    public List<Long> listReportIdMarket(List<ReportListDTO> reportList) {
        return reportList.stream().map(ReportListDTO::getCampaignId).collect(Collectors.toList());
    }

    @Override
    public Long getValueById(ReportListDTO reportDTO) {
        return reportDTO.getCampaignId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDelete(RecordBatchDeleteVO batchDeleteVO) {
        // 检查是否有要删除的ID
        if (!batchDeleteVO.getIds().isEmpty()) {
            UpdateWrapper<Campaign> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda()
                    .eq(Campaign::getMasterId, batchDeleteVO.getMasterId())
                    .in(Campaign::getId, batchDeleteVO.getIds())
                    .eq(Campaign::getIsDel, IsDelEnum.NORMAL.getId());
            Campaign campaign = new Campaign();
            campaign.setUpdateUid(batchDeleteVO.getUserId());
            campaign.setIsDel(IsDelEnum.DELETE.getId().intValue());
            try {
                int deleteCampaign = this.baseMapper.update(campaign, updateWrapper);
                if (deleteCampaign >= 0) {
                    // 删除成功后，要删除当前计划下所有的计划、创意、创意单元
                    CreativeUnit creativeUnit = new CreativeUnit();
                    creativeUnit.setIsDel(IsDelEnum.DELETE.getId().intValue());
                    creativeUnit.setUpdateUid(batchDeleteVO.getUserId());
                    creativeUnitMapper.update(creativeUnit, new LambdaQueryWrapper<CreativeUnit>()
                            .in(CreativeUnit::getCampaignId, batchDeleteVO.getIds()));

                    Plan plan = new Plan();
                    plan.setIsDel(IsDelEnum.DELETE.getId().intValue());
                    plan.setUpdateUid(batchDeleteVO.getUserId());
                    planMapper.update(plan, new LambdaQueryWrapper<Plan>()
                            .in(Plan::getCampaignId, batchDeleteVO.getIds()));

                    Creative creative = new Creative();
                    creative.setIsDel(IsDelEnum.DELETE.getId().intValue());
                    creative.setUpdateUid(batchDeleteVO.getUserId());
                    creativeMapper.update(creative, new LambdaQueryWrapper<Creative>()
                            .in(Creative::getCampaignId, batchDeleteVO.getIds()));
                }
                return true;
            } catch (Exception e) {
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean batchSwitch(RecordBatchSwitchVO batchSwitchVO) {
        List<Campaign> campaigns = this.baseMapper.selectList(new LambdaQueryWrapper<Campaign>()
                .in(Campaign::getId, batchSwitchVO.getIds())
                .eq(Campaign::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(batchSwitchVO.getMasterId()), Campaign::getMasterId, batchSwitchVO.getMasterId())
                .in(CollectionUtils.isNotEmpty(batchSwitchVO.getMasterIds()), Campaign::getMasterId, batchSwitchVO.getMasterIds())
        );
        if (campaigns.isEmpty()) {
            return true;
        }
        Campaign campaign = new Campaign();
        campaign.setCampaignStatus(batchSwitchVO.getPutStatus());
        campaign.setUpdateUid(batchSwitchVO.getUserId());
        this.baseMapper.update(campaign, new UpdateWrapper<Campaign>().lambda()
                .in(Campaign::getId, batchSwitchVO.getIds())
                .eq(Campaign::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(batchSwitchVO.getMasterId()), Campaign::getMasterId, batchSwitchVO.getMasterId())
                .in(CollectionUtils.isNotEmpty(batchSwitchVO.getMasterIds()), Campaign::getMasterId, batchSwitchVO.getMasterIds())
        );
        Map<String, Object> map = new HashMap<>() {{
            put("campaign_status", batchSwitchVO.getPutStatus());
        }};
        //记录日志
        List<CampaignUpdateRecord> campaignUpdateRecords = campaigns.stream().map(camp -> {
            CampaignUpdateRecord campaignUpdateRecord = new CampaignUpdateRecord();
            campaignUpdateRecord.setCampaignId(camp.getId());
            campaignUpdateRecord.setContent(JSONObject.toJSONString(map));
            return campaignUpdateRecord;
        }).collect(Collectors.toList());
        this.campaignUpdateRecordService.saveUpdateRecord(campaignUpdateRecords, batchSwitchVO.getUserId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateCampaign(CampaignBatchUpdateVO batchUpdateVO) {
        Campaign campaign = new Campaign();
        campaign.setUpdateUid(batchUpdateVO.getUserId());
        Map<String, Object> map = new HashMap<>();
        // 设置活动名称
        if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateCampaignName())) {
            campaign.setCampaignName(batchUpdateVO.getCampaignName());
            map.put("campaign_name", batchUpdateVO.getCampaignName());
        }
        // 设置日预算类型
        if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateBudgetType())) {
            campaign.setBudgetType(batchUpdateVO.getBudgetType());
            campaign.setBudgetDay(BudgetUtils.inDb(batchUpdateVO.getBudgetDay(), campaign.getBudgetType()));
            map.put("budget_type", batchUpdateVO.getBudgetType());
            map.put("budget_day", batchUpdateVO.getBudgetDay());
        }
        // 设置次日预算类型
        if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateNextBudgetType())) {
            campaign.setNextBudgetType(batchUpdateVO.getNextBudgetType());
            campaign.setNextBudgetDay(BudgetUtils.inDb(batchUpdateVO.getNextBudgetDay(), campaign.getNextBudgetType()));
            map.put("next_budget_type", batchUpdateVO.getNextBudgetType());
            map.put("next_budget_day", batchUpdateVO.getNextBudgetDay());
        }
        //设置活动时区
        if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateCampaignTimeZone())) {
            campaign.setCampaignTimeZone(batchUpdateVO.getCampaignTimeZone());
            map.put("campaign_time_zone", batchUpdateVO.getCampaignTimeZone());
        }
        //设置活动曝光频次
        if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateFrequencyView())) {
            campaign.setFrequencyCycleView(batchUpdateVO.getFrequencyCycleView());
            campaign.setFrequencyNumView(batchUpdateVO.getFrequencyNumView());
            map.put("frequency_cycle_view", batchUpdateVO.getFrequencyCycleView());
            map.put("frequency_num_view", batchUpdateVO.getFrequencyNumView());
        }
        //设置活动点击频次
        if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateFrequencyClick())) {
            campaign.setFrequencyCycleClick(batchUpdateVO.getFrequencyCycleClick());
            campaign.setFrequencyNumClick(batchUpdateVO.getFrequencyNumClick());
            map.put("frequency_cycle_click", batchUpdateVO.getFrequencyCycleClick());
            map.put("frequency_num_click", batchUpdateVO.getFrequencyNumClick());
        }
        // 获取当前登录账号下的所有广告主
        List<Integer> masterIds = new ArrayList<>();
        if (ObjectUtils.isNullOrZero(batchUpdateVO.getMasterId())) {
            FeignR<List<Integer>> feignR = this.fgSystemService.listMasterId();
            if (!feignR.getCode().equals(0)) {
                throw new CustomException("内部服务异常");
            }
            masterIds = feignR.getData();
            if (masterIds.isEmpty()) {
                throw new CustomException("账户信息异常");
            }
        }
        try {
            List<Campaign> campaigns = this.baseMapper.selectList(new LambdaQueryWrapper<Campaign>()
                    .eq(ObjectUtils.isNotNullOrZero(batchUpdateVO.getMasterId()), Campaign::getMasterId, batchUpdateVO.getMasterId())
                    .in(CollectionUtils.isNotEmpty(masterIds), Campaign::getMasterId, masterIds)
                    .in(Campaign::getId, batchUpdateVO.getCampaignIds()));
            if (campaigns.isEmpty()) {
                return true;
            }
            //修改数据
            this.baseMapper.update(campaign, new UpdateWrapper<Campaign>().lambda()
                    .eq(ObjectUtils.isNotNullOrZero(batchUpdateVO.getMasterId()), Campaign::getMasterId, batchUpdateVO.getMasterId())
                    .in(CollectionUtils.isNotEmpty(masterIds), Campaign::getMasterId, masterIds)
                    .in(Campaign::getId, batchUpdateVO.getCampaignIds()));
            //记录日志
            List<CampaignUpdateRecord> campaignUpdateRecords = campaigns.stream().map(camp -> {
                CampaignUpdateRecord campaignUpdateRecord = new CampaignUpdateRecord();
                campaignUpdateRecord.setContent(JSONObject.toJSONString(map));
                campaignUpdateRecord.setCampaignId(camp.getId());
                return campaignUpdateRecord;
            }).collect(Collectors.toList());
            this.campaignUpdateRecordService.saveUpdateRecord(campaignUpdateRecords, batchUpdateVO.getUserId());
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object syncSheinCampaign(Long campaignId, User user) {
        Campaign campaign = this.getOne(new LambdaQueryWrapper<Campaign>().eq(Campaign::getId, campaignId));
        if (null == campaign) {
            log.error("活动ID {} 不存在，请检查后重试", campaignId);
            return null;
        }
        //更新shein活动信息
        if (this.isSheinCampaign(campaign)) {
            campaign.setBudgetDay(BudgetUtils.format(campaign.getBudgetDay(), campaign.getBudgetType()));
            return this.syncSheinCampaigns(campaign, user);
        }
        return null;
    }

    @Override
    public void updateCampaignBudgetByNextDay(Date date) {
        TimeZoneEnum timeZoneEnum = TimeZoneEnum.getTimeZoneByCurrentHour(Integer.parseInt(DateUtils.format(date, "HH")));
        if (timeZoneEnum == null) {
            return;
        }
        // 获取需要更新的活动列表：次日预算不为0
        List<Campaign> campaigns = this.baseMapper.listCampaignByTimeZone(new QueryWrapper<Campaign>()
                .eq("mc.is_del", IsDelEnum.NORMAL.getId())
                .eq("mm.time_zone", timeZoneEnum.getId())
                .ne("mc.next_budget_type", 0));

        if (campaigns.isEmpty()) {
            return;
        }
        // 更新
        campaigns.forEach(campaign -> {
            campaign.setBudgetType(campaign.getNextBudgetType());
            campaign.setBudgetDay(campaign.getNextBudgetDay());
            campaign.setNextBudgetType(0);
            campaign.setNextBudgetDay(new BigDecimal(0));
        });
        this.baseMapper.batchSaveCampaign(campaigns);
    }

    private Campaign checkCampaign(Long id, String name, Long masterId) {
        Campaign campaign = this.baseMapper.selectOne(new QueryWrapper<Campaign>().lambda()
                .eq(Campaign::getMasterId, masterId)
                .eq(ObjectUtils.isNotNullOrZero(id), Campaign::getId, id)
                .eq(StringUtils.isNotBlank(name), Campaign::getCampaignName, name)
                .eq(Campaign::getIsDel, IsDelEnum.NORMAL.getId())
        );
        if (id == null) {
            if (campaign != null) {
                throw new CustomException("活动名称已存在，请确认后再试");
            }
        } else if (name == null) {
            if (campaign == null) {
                throw new CustomException("活动不存在，请确认后再试");
            }
        }
        return campaign;
    }

    /**
     * 同步shein活动信息
     *
     * @param campaign 新活动
     * @param user     用户
     */
    public Object syncSheinCampaigns(Campaign campaign, User user) {
        //判断是否shein活动
        if (!isSheinCampaign(campaign)) {
            return null;
        }
        //获取shein活动关系
        List<PlanGenerateMap> planGenerateMaps = planGenerateMapMapper.selectList(new LambdaQueryWrapper<PlanGenerateMap>()
                .eq(PlanGenerateMap::getMapType, PlanGenerateMapTypeEnum.CAMPAIGN.getId())
                .eq(PlanGenerateMap::getSheinId, campaign.getId())
        );
        if (planGenerateMaps.isEmpty()) {
            return null;
        }
        //如果是删除
        if (campaign.getIsDel().equals(IsDelEnum.DELETE.getId().intValue())) {
            RecordBatchDeleteVO recordBatchDeleteVO = new RecordBatchDeleteVO();
            recordBatchDeleteVO.setIds(planGenerateMaps.stream().map(PlanGenerateMap::getMarketId).collect(Collectors.toList()));
            recordBatchDeleteVO.setListType("campaign");
            recordBatchDeleteVO.setMasterId(campaign.getMasterId());
            recordBatchDeleteVO.setUserId(user.getId());
            return recordBatchDeleteVO;
        }
        planGenerateMaps.forEach(planGenerateMap -> {
            try {
                this.syncSheinCampaign(planGenerateMap, campaign, user);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
        //修改状态
        RecordBatchSwitchVO switchVO = new RecordBatchSwitchVO();
        switchVO.setIds(planGenerateMaps.stream().map(PlanGenerateMap::getMarketId).collect(Collectors.toList()));
        switchVO.setListType("campaign");
        switchVO.setUserId(user.getId());
        switchVO.setPutStatus(campaign.getCampaignStatus());
        switchVO.setMasterId(campaign.getMasterId());
        return switchVO;
    }

    /**
     * 处理单个活动数据
     *
     * @param planGenerateMap 活动关系
     * @param campaign        原活动信息
     * @param user            用户
     */
    public void syncSheinCampaign(PlanGenerateMap planGenerateMap, Campaign campaign, User user) {
        //修改活动内容
        Campaign generateCampaign = this.getCampaign(planGenerateMap.getMarketId(), campaign.getMasterId().intValue());
        CampaignUpdateVO updateVO = JSONObject.parseObject(JSONObject.toJSONString(generateCampaign), CampaignUpdateVO.class);
        updateVO.setCampaignTimeZone(campaign.getCampaignTimeZone());
        updateVO.setBudgetDay(campaign.getBudgetDay());
        updateVO.setBudgetType(campaign.getBudgetType());
        updateVO.setFrequencyCycleClick(campaign.getFrequencyCycleClick());
        updateVO.setFrequencyNumClick(campaign.getFrequencyNumClick());
        updateVO.setFrequencyCycleView(campaign.getFrequencyCycleView());
        updateVO.setFrequencyNumView(campaign.getFrequencyNumView());
        updateVO.setNextBudgetDayVal(campaign.getNextBudgetDay());
        updateVO.setNextBudgetTypeVal(campaign.getNextBudgetType());
        updateVO.setCampaignName(String.format("投放-%s", campaign.getCampaignName()));
        this.updateCampaign(updateVO, user.getId());
    }

    /**
     * 是否是shein活动
     *
     * @param campaign 活动
     * @return 返回数据
     */
    private Boolean isSheinCampaign(Campaign campaign) {
        return PutEnum.NOT_PUT.getId().equals(campaign.getIsCampaignPut());
    }

    /**
     * 保存信息
     *
     * @param campaignSaveVO 条件
     * @param campaignInDb   活动
     * @param userId         用户ID
     */
    private void saveCampaignUpdateRecordBySaveVO(CampaignSaveVO campaignSaveVO, Campaign campaignInDb, Integer userId) {
        CampaignUpdateRecord campaignUpdateRecord = new CampaignUpdateRecord();
        campaignUpdateRecord.setCampaignId(campaignInDb.getId());
        Map<String, Object> map = new HashMap<>() {{
            put("campaign_name", campaignSaveVO.getCampaignName());
            put("budget_type", campaignSaveVO.getBudgetType());
            put("budget_day", campaignSaveVO.getBudgetDay());
            put("campaign_time_zone", campaignSaveVO.getCampaignTimeZone());
            put("frequency_cycle_view", campaignSaveVO.getFrequencyCycleView());
            put("frequency_num_view", campaignSaveVO.getFrequencyNumView());
            put("frequency_cycle_click", campaignSaveVO.getFrequencyCycleClick());
            put("frequency_num_click", campaignSaveVO.getFrequencyNumClick());
            put("next_budget_type", campaignSaveVO.getNextBudgetTypeVal());
            put("next_budget_day", campaignSaveVO.getNextBudgetTypeVal());
        }};
        campaignUpdateRecord.setContent(JSONObject.toJSONString(map));
        this.campaignUpdateRecordService.saveUpdateRecord(List.of(campaignUpdateRecord), userId);
    }


    /**
     * 保存 shein 计划
     *
     * @param campaign 活动信息
     */
    private void saveSheinCampaign(Campaign campaign) {
        User user = new User();
        // 设置用户信息，写死yaoyu2
        user.setId(10010);
        user.setRoleId(3);
        Long oldCampaignId = campaign.getId();
        campaign.setCampaignName(String.format("%s-%s", "投放", campaign.getCampaignName()));
        Long campaignId = this.saveCampaign(JSONObject.parseObject(JSONObject.toJSONString(campaign), CampaignSaveVO.class), user);
        //插入shein map
        planGenerateMapMapper.insertMap(PlanGenerateMapTypeEnum.CAMPAIGN.getId(), oldCampaignId, campaignId, user.getId());
    }

}
