package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.overseas.common.configuration.SheinConfiguration;
import com.overseas.common.dto.market.campaign.CampaignUpdateRecordListDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.TimeZoneEnum;
import com.overseas.common.enums.market.FrequencyCycleEnum;
import com.overseas.common.utils.DateUtils;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.campaign.CampaignUpdateRecordListVO;
import com.overseas.service.market.entity.CampaignUpdateRecord;
import com.overseas.service.market.entity.User;
import com.overseas.service.market.enums.BudgetTypeEnum;
import com.overseas.common.enums.market.campaign.CampaignStatusEnum;
import com.overseas.service.market.mapper.CampaignUpdateRecordMapper;
import com.overseas.service.market.service.CampaignUpdateRecordService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class CampaignUpdateRecordServiceImpl implements CampaignUpdateRecordService {

    private final CampaignUpdateRecordMapper campaignUpdateRecordMapper;

    private final SheinConfiguration sheinConfiguration;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<CampaignUpdateRecord> saveUpdateRecord(List<CampaignUpdateRecord> campaignUpdateRecords, Integer userId) {
        if (campaignUpdateRecords.isEmpty()) {
            return List.of();
        }
        // 处理入库字段
        List<Long> campaignIds = new ArrayList<>();
        campaignUpdateRecords.forEach(planUpdateRecord -> {
            Map<String, Object> map = new HashMap<>() {{
                put("campaignId", planUpdateRecord.getCampaignId());
                put("content", planUpdateRecord.getContent());
            }};
            planUpdateRecord.setMd5(DigestUtils.md5Hex(JSONObject.toJSONString(map)));
            campaignIds.add(planUpdateRecord.getCampaignId());
        });

        // 获取记录表中最新的记录
        List<CampaignUpdateRecord> existCampaignUpdateRecords = this.campaignUpdateRecordMapper.selectList(
                new LambdaQueryWrapper<CampaignUpdateRecord>()
                        .eq(CampaignUpdateRecord::getUpdateDate, 0)
                        .in(CampaignUpdateRecord::getCampaignId, campaignIds)
                        .orderByAsc(CampaignUpdateRecord::getId)
        );
        // 根据已有最新记录与即将入库的记录做对比；
        Map<Long, CampaignUpdateRecord> existPlanUpdateRecordMd5Map = existCampaignUpdateRecords.stream()
                .collect(Collectors.toMap(CampaignUpdateRecord::getCampaignId, Function.identity(), (o, n) -> n));
        List<CampaignUpdateRecord> recordList = new ArrayList<>();
        List<Long> updateIds = new ArrayList<>();
        Map<String, List<String>> fieldMap = this.fieldMap();
        List<String> fields = this.fields(fieldMap);
        campaignUpdateRecords.forEach(campaignUpdateRecord -> {
            // 1.如果当前计划下未有记录
            if (existPlanUpdateRecordMd5Map.get(campaignUpdateRecord.getCampaignId()) == null) {
                campaignUpdateRecord.setRecordType(1);
                recordList.add(campaignUpdateRecord);
                return;
            }
            CampaignUpdateRecord existCampaignUpdateRecord = existPlanUpdateRecordMd5Map.get(campaignUpdateRecord.getCampaignId());
            // 2.如果已有记录与即将入库记录md5不相同，则新增，并把原记录update_date置为当前时间戳，标为旧纪录；并新增新纪录；
            // 如果已有记录与即将入库记录md5相同，则不新增；
            if (!existCampaignUpdateRecord.getMd5().equals(campaignUpdateRecord.getMd5())) {
                Map<String, Object> oldMap = JSONObject.parseObject(existCampaignUpdateRecord.getContent()),
                        newMap = JSONObject.parseObject(campaignUpdateRecord.getContent());
                this.fillValueFromOldToNew(oldMap, newMap, fields);
                // 因为编辑计划、批量编辑、快捷编辑存储的content内容不同，为了避免重复添加记录，进一步校验是否修改相关内容
                AtomicBoolean isChange = new AtomicBoolean(false);
                fieldMap.forEach((key, value) -> {
                    for (String field : value) {
                        if (newMap.get(field) != null) {
                            if (newMap.get(key).equals(newMap.get("old_" + key)) && newMap.get(field).equals(newMap.get("old_" + field))) {
                                newMap.remove(field);
                            } else {
                                isChange.set(true);
                            }
                        }
                    }
                    // 如果类型下所有节点都没有，则去除类型
                    if (!this.isContainsAnyKeys(new ArrayList<>(newMap.keySet()), value)) {
                        newMap.remove(key);
                    }
                });
                // 如果未修改，则return掉
                if (!isChange.get()) {
                    return;
                }
                campaignUpdateRecord.setContent(JSONObject.toJSONString(newMap));
                campaignUpdateRecord.setRecordType(2);
                updateIds.add(existCampaignUpdateRecord.getId());
                recordList.add(campaignUpdateRecord);
            }
        });
        // 新增
        if (CollectionUtils.isNotEmpty(recordList)) {
            this.campaignUpdateRecordMapper.saveUpdateRecord(recordList, userId);
        }
        // 编辑
        if (CollectionUtils.isNotEmpty(updateIds)) {
            CampaignUpdateRecord campaignUpdateRecord = new CampaignUpdateRecord();
            campaignUpdateRecord.setUpdateDate(DateUtils.string2Long(DateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss")));
            this.campaignUpdateRecordMapper.update(campaignUpdateRecord, new QueryWrapper<CampaignUpdateRecord>().lambda()
                    .in(CampaignUpdateRecord::getId, updateIds));
        }
        return recordList;
    }

    /**
     * 是否包含任何key
     *
     * @param list list
     * @param keys key
     * @return 返回数据
     */
    private Boolean isContainsAnyKeys(List<String> list, List<String> keys) {
        for (String key : keys) {
            if (list.contains(key)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 填补旧数据
     *
     * @param oldMap 旧数据
     * @param newMap 新数据
     * @param fields 字段数组
     */
    private void fillValueFromOldToNew(Map<String, Object> oldMap, Map<String, Object> newMap, List<String> fields) {
        fields.forEach(field -> newMap.put("old_" + field, oldMap.get((oldMap.containsKey(field) ? "" : "old_") + field)));
    }

    @Override
    public PageUtils<CampaignUpdateRecordListDTO> listUpdateRecord(CampaignUpdateRecordListVO listVO, User user) {
        IPage<CampaignUpdateRecordListDTO> pageData = this.campaignUpdateRecordMapper.listUpdateRecord(
                new Page<>(listVO.getPage(), listVO.getPageNum()),
                new QueryWrapper<CampaignUpdateRecord>()
                        .eq("mc.master_id", listVO.getMasterId())
                        .eq(ObjectUtils.isNotNullOrZero(listVO.getIsPut()),
                                "mc.is_campaign_put", sheinConfiguration.isPut(listVO.getIsPut(), user.getRoleId()))
                        .eq(ObjectUtils.isNotNullOrZero(listVO.getCampaignId()),
                                "record.campaign_id", listVO.getCampaignId())
                        .between(StringUtils.isNotBlank(listVO.getStartDate()) && StringUtils.isNotBlank(listVO.getEndDate()),
                                "record.create_time", listVO.getStartDate() + " 00:00:00", listVO.getEndDate() + " 23:59:59")
                        .eq(ObjectUtils.isNotNullOrZero(listVO.getCreateUid()), "record.create_uid", listVO.getCreateUid())
                        .orderByDesc("record.id")
        );
        Map<String, String> showMap = new HashMap<>(32) {
            {
                put("campaign_name", "活动名称");
                put("campaign_status", "活动状态");
                put("campaign_time_zone", "活动时区");
            }
        };
        for (CampaignUpdateRecordListDTO record : pageData.getRecords()) {
            if (1 == record.getRecordType()) {
                record.setContent("活动创建");
            } else {
                record.setContent(this.contentDeal(record.getContent(), showMap));
            }
            record.setDay(DateUtils.date2Long(DateUtils.string2Date(record.getDate(), "yyyy-MM-dd HH")));
        }
        return new PageUtils<>(pageData);
    }

    /**
     * 内容处理
     *
     * @param content 内容
     * @param showMap 展示map
     * @return 返回数据
     */
    private String contentDeal(String content, Map<String, String> showMap) {
        Map<String, Object> contentMap = JSONObject.parseObject(content);
        List<String> contentList = new ArrayList<>();
        // 日预算
        if (contentMap.get("budget_day") != null) {
            contentList.add(this.getContentStr("budget_type", "日预算", contentMap.get("old_budget_type"), contentMap.get("budget_type"),
                    contentMap.get("old_budget_day"), contentMap.get("budget_day")));
        }
        // 小时预算
        if (contentMap.get("budget_hour") != null) {
            contentList.add(this.getContentStr("budget_type", "小时预算", contentMap.get("old_budget_type"), contentMap.get("budget_type"),
                    contentMap.get("old_budget_hour"), contentMap.get("budget_hour")));
        }
        // 次日预算
        if (contentMap.get("next_budget_day") != null) {
            contentList.add(this.getContentStr("next_budget_type", "次日预算", contentMap.get("old_next_budget_type"), contentMap.get("next_budget_type"),
                    contentMap.get("old_next_budget_day"), contentMap.get("next_budget_day")));
        }
        // 曝光频次
        if (contentMap.get("frequency_cycle_view") != null) {
            contentList.add(this.getContentStr("frequency_cycle_view", "曝光频次", contentMap.get("old_frequency_cycle_view"), contentMap.get("frequency_cycle_view"),
                    contentMap.get("old_frequency_num_view"), contentMap.get("frequency_num_view")));
        }
        // 点击频次
        if (contentMap.get("frequency_cycle_click") != null) {
            contentList.add(this.getContentStr("frequency_cycle_click", "点击频次", contentMap.get("old_frequency_cycle_click"), contentMap.get("frequency_cycle_click"),
                    contentMap.get("old_frequency_num_click"), contentMap.get("frequency_num_click")));
        }
        showMap.forEach((key, name) -> {
            if (contentMap.containsKey(key)) {
                String oldKey = String.format("old_%s", key);
                contentList.add(this.getContentStr(key, name, contentMap.get(oldKey), contentMap.get(key), contentMap.get(oldKey), contentMap.get(key)));
            }
        });
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < contentList.size(); i++) {
            stringBuilder.append(i + 1).append("、").append(contentList.get(i)).append("</br>");
        }
        return stringBuilder.toString();
    }

    /**
     * 获取内容
     *
     * @param type     类型
     * @param typeName 类型名称
     * @param oldType  老类型
     * @param newType  新类型
     * @param oldValue 老数据
     * @param newValue 新数据
     * @return 返回字段
     */
    private String getContentStr(String type, String typeName, Object oldType, Object newType, Object oldValue, Object newValue) {
        switch (type) {
            case "budget_type":
            case "next_budget_type":
                BudgetTypeEnum newBudgetTypeEnum = ICommonEnum.get(Integer.parseInt(newType.toString()), BudgetTypeEnum.class);
                if (oldValue == null) {
                    return typeName + "设置为 " + (newValue.equals(0) ? "不限" : (newBudgetTypeEnum.getName() + newValue + newBudgetTypeEnum.getUnit()));
                }
                BudgetTypeEnum oldBudgetTypeEnum = ICommonEnum.get(Integer.parseInt(oldType.toString()), BudgetTypeEnum.class);
                return typeName + "由原 " + (oldValue.equals(0) ? "不限" : (oldBudgetTypeEnum.getName() + oldValue + oldBudgetTypeEnum.getUnit())) + " 调整至 " +
                        (newValue.equals(0) ? "不限" : (newBudgetTypeEnum.getName() + newValue + newBudgetTypeEnum.getUnit()));
            case "campaign_status":
                CampaignStatusEnum campaignStatusEnum = ICommonEnum.get(Integer.parseInt(newType.toString()), CampaignStatusEnum.class);
                if (null == oldValue) {
                    return typeName + "设置为 " + campaignStatusEnum.getName();
                }
                CampaignStatusEnum oldStatusEnum = ICommonEnum.get(Integer.parseInt(oldValue.toString()), CampaignStatusEnum.class);
                return typeName + "由原 " + oldStatusEnum.getName() + " 修改为 " + campaignStatusEnum.getName();
            case "frequency_cycle_view":
            case "frequency_cycle_click":
                FrequencyCycleEnum newFrequencyCycleEnum = ICommonEnum.get(Integer.parseInt(newType.toString()), FrequencyCycleEnum.class);
                if (oldValue == null) {
                    return typeName + "设置为 " + (newValue.equals(0) ? "不限" : (newValue + "/" + newFrequencyCycleEnum.getName()));
                }
                FrequencyCycleEnum oldFrequencyCycleEnum = ICommonEnum.get(Integer.parseInt(oldType.toString()), FrequencyCycleEnum.class);
                return typeName + "由原 " + (oldValue.equals(0) ? "不限" : (oldValue + "/" + oldFrequencyCycleEnum.getName())) + " 修改为 " +
                        (newValue.equals(0) ? "不限" : (newValue + "/" + newFrequencyCycleEnum.getName()));
            case "campaign_name":
                if (null == oldValue) {
                    return typeName + "设置为 " + newValue;
                }
                return typeName + "由原 " + oldValue + " 修改为 " + newValue;
            case "campaign_time_zone":
                TimeZoneEnum newEnum = TimeZoneEnum.get(Integer.parseInt(newValue.toString()));
                if (null == oldValue) {
                    return typeName + "设置为 " + newEnum.getName();
                }
                TimeZoneEnum oldEnum = TimeZoneEnum.get(Integer.parseInt(oldValue.toString()));
                return typeName + "由原 " + oldEnum.getName() + " 修改为 " + newEnum.getName();
            default:
                return "";
        }
    }


    /**
     * 获取key val list
     *
     * @return 数据
     */
    private List<String> fields(Map<String, List<String>> fieldMap) {
        List<String> values = fieldMap.values().stream().flatMap(Collection::stream).distinct().collect(Collectors.toList());
        values.addAll(fieldMap.keySet());
        return values;
    }

    /**
     * 获取 fieldMap
     *
     * @return 返回数据
     */
    private Map<String, List<String>> fieldMap() {
        Map<String, List<String>> map = new HashMap<>(64);
        map.put("budget_type", List.of("budget_day", "budget_hour"));
        map.put("campaign_status", List.of("campaign_status"));
        map.put("frequency_cycle_view", List.of("frequency_num_view"));
        map.put("frequency_cycle_click", List.of("frequency_num_click"));
        map.put("campaign_name", List.of("campaign_name"));
        map.put("campaign_time_zone", List.of("campaign_time_zone"));
        map.put("next_budget_type", List.of("next_budget_day"));
        return map;
    }
}
