package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.overseas.common.dto.SelectDTO3;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.vo.market.channel.ChannelSelectVO;
import com.overseas.service.market.entity.ChannelInfo;
import com.overseas.service.market.mapper.ChannelInfoMapper;
import com.overseas.service.market.service.ChannelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class ChannelServiceImpl implements ChannelService {

    private final ChannelInfoMapper channelInfoMapper;


    @Override
    public List<SelectDTO3> channelSelect(ChannelSelectVO selectVO) {
        return list(selectVO).stream()
                .map(u -> new SelectDTO3(u.getChannelInfoId(), u.getChannelInfoName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<SelectDTO3> channelSelectByKey(ChannelSelectVO selectVO) {
        return list(selectVO).stream()
                .map(u -> new SelectDTO3(String.format("%s-%s", u.getChannelType(), u.getChannelInfoId()), u.getChannelInfoName()))
                .collect(Collectors.toList());
    }

    private List<ChannelInfo> list(ChannelSelectVO selectVO) {
        return channelInfoMapper.selectList(new LambdaQueryWrapper<ChannelInfo>()
                .eq(ChannelInfo::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(selectVO.getChannelId()), ChannelInfo::getChannelId, selectVO.getChannelId())
                .like(StringUtils.isNotBlank(selectVO.getName()), ChannelInfo::getChannelInfoName, selectVO.getName())
                .eq(ObjectUtils.isNotNullOrZero(selectVO.getChannelType()), ChannelInfo::getChannelType, selectVO.getChannelType())
        );
    }
}
