package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.configuration.SheinConfiguration;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.copyWriting.CopyWritingCascaderDTO;
import com.overseas.common.dto.market.copyWriting.CopyWritingDTO;
import com.overseas.common.dto.market.copyWriting.CopyWritingListDTO;
import com.overseas.common.dto.market.copyWriting.CopyWritingUploadDTO;
import com.overseas.common.dto.market.textCombine.TextCombineListDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.enums.market.copyWriting.CopyWritingStatusEnum;
import com.overseas.common.enums.market.copyWriting.CopyWritingTypeEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.*;
import com.overseas.common.vo.market.copyWriting.*;
import com.overseas.common.vo.report.asset.AssetFieldListVO;
import com.overseas.service.market.entity.Asset;
import com.overseas.service.market.entity.CopyWriting;
import com.overseas.service.market.entity.Country;
import com.overseas.service.market.entity.User;
import com.overseas.service.market.enums.assets.AssetTypeEnum;
import com.overseas.service.market.feign.FgReportService;
import com.overseas.service.market.mapper.AssetMapper;
import com.overseas.service.market.mapper.CopyWritingMapper;
import com.overseas.service.market.mapper.CountryMapper;
import com.overseas.service.market.service.AssetService;
import com.overseas.service.market.service.CopyWritingService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CopyWritingServiceImpl extends ServiceImpl<CopyWritingMapper, CopyWriting> implements CopyWritingService {

    private final AssetMapper assetMapper;

    private final AssetService assetService;

    private final CountryMapper countryMapper;

    private final FgReportService fgReportService;

    private final SheinConfiguration sheinConfiguration;

    @Override
    public void saveCopyWriting(CopyWritingSaveVO saveVO, Integer userId) {

        // 1.先校验当前账号下是否已经存在该文案
        CopyWriting existCopyWriting = this.baseMapper.selectOneByContent(new QueryWrapper<CopyWriting>()
                .eq("writing.is_del", IsDelEnum.NORMAL.getId())
                .eq("writing.master_id", saveVO.getMasterId())
                .eq("writing.copy_writing_type", saveVO.getCopyWritingType())
                .eq("asset.content", saveVO.getCopyWriting()));
        if (existCopyWriting != null) {
            throw new CustomException("该文案已存在，请确认后再试");
        }
        // 2.再根据文案获取素材
        Asset asset = this.getOrSaveCopyWritingAsset(saveVO.getCopyWriting(), userId);
        // 3.保存文案与素材信息
        CopyWriting copyWriting = new CopyWriting();
        BeanUtils.copyProperties(saveVO, copyWriting);
        copyWriting.setAssetId(asset.getId());
        copyWriting.setCreateUid(userId);
        this.baseMapper.insert(copyWriting);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveCopyWriting(CopyWritingBatchSaveVO saveVO, Integer userId) {
        this.completeCountryId(saveVO.getCopyWritingList());
        Map<String, CopyWritingBatchSaveItemVO> notExistAsset = saveVO.getCopyWritingList().stream()
                .filter(u -> ObjectUtils.isNullOrZero(u.getAssetId()))
                .collect(Collectors.toMap(u -> Md5CalculateUtils.getStringMd5(u.getCopyWriting()), Function.identity()));
        if (!notExistAsset.isEmpty()) {
            Map<String, Long> assetMap = this.assetMapper.selectList(new LambdaQueryWrapper<Asset>()
                            .in(Asset::getMd5, notExistAsset.keySet())).stream()
                    .collect(Collectors.toMap(Asset::getMd5, Asset::getId));
            notExistAsset.forEach((k, v) -> {
                if (assetMap.containsKey(k)) {
                    v.setAssetId(assetMap.get(k));
                } else {
                    Asset asset = assetService.saveTextAsset(v.getCopyWriting(), userId);
                    v.setAssetId(asset.getId());
                }
            });
        }
        // 1.获取需要存储的copyWriting
        List<CopyWriting> copyWritingList = saveVO.getCopyWritingList().stream().map(copyWritingVO -> {
            CopyWriting copyWriting = new CopyWriting();
            copyWriting.setAssetId(copyWritingVO.getAssetId());
            copyWriting.setCountryId(copyWritingVO.getCountryId());
            copyWriting.setMasterId(saveVO.getMasterId());
            copyWriting.setTranslatedText(StringUtils.isNotEmpty(copyWritingVO.getTranslatedText()) ? copyWritingVO.getTranslatedText() : "");
            copyWriting.setCopyWritingType(copyWritingVO.getCopyWritingType());
            copyWriting.setIsDel(IsDelEnum.NORMAL.getId().intValue());
            copyWriting.setCreateUid(userId);
            return copyWriting;
        }).collect(Collectors.toList());
        // 5.存储
        this.baseMapper.batchSaveCopyWriting(copyWritingList, userId);
    }

    @Override
    public void batchCreativeSaveCopyWriting(CopyWritingBatchSaveVO saveVO, Integer userId) {
        //获取素材ID
        List<Long> assetIds = saveVO.getCopyWritingList().stream().map(CopyWritingBatchSaveItemVO::getAssetId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(assetIds)) {
            return;
        }
        Map<String, CopyWriting> copyWritingMap = this.baseMapper.selectList(
                new LambdaQueryWrapper<CopyWriting>()
                        .in(CopyWriting::getAssetId, assetIds)
                        .eq(CopyWriting::getMasterId, saveVO.getMasterId())
                        .eq(CopyWriting::getIsDel, IsDelEnum.NORMAL.getId())
        ).stream().collect(Collectors.toMap(u -> String.format("%s-%s", u.getAssetId(), u.getCopyWritingType()), Function.identity()));
        //获取数据
        List<CopyWriting> copyWritingList = saveVO.getCopyWritingList().stream().map(copyWritingVO -> {
            CopyWriting old = copyWritingMap.get(String.format("%s-%s",
                    copyWritingVO.getAssetId(), copyWritingVO.getCopyWritingType()));
            CopyWriting copyWriting = new CopyWriting();
            copyWriting.setAssetId(copyWritingVO.getAssetId());
            //记录翻译文案, 如果为空，使用原文案
            copyWriting.setCountryId(ObjectUtils.isNullOrZero(copyWritingVO.getCountryId()) ? (null == old ? 0L : old.getCountryId()) : copyWritingVO.getCountryId());
            copyWriting.setMasterId(saveVO.getMasterId());
            //记录翻译文案, 如果为空，使用原文案
            copyWriting.setTranslatedText(StringUtils.isNotEmpty(copyWritingVO.getTranslatedText()) ?
                    copyWritingVO.getTranslatedText() :
                    null == old ? "" : old.getTranslatedText());
            copyWriting.setCopyWritingType(copyWritingVO.getCopyWritingType());
            copyWriting.setIsDel(IsDelEnum.NORMAL.getId().intValue());
            copyWriting.setCreateUid(userId);
            return copyWriting;
        }).collect(Collectors.toList());
        // 5.存储
        this.baseMapper.batchSaveCopyWriting(copyWritingList, userId);
    }

    @Override
    public void updateCopyWriting(CopyWritingUpdateVO updateVO, Integer userId) {

        // 1.先文案获取素材
        Asset asset = this.getOrSaveCopyWritingAsset(updateVO.getCopyWriting(), userId);
        // 2.校验文案是否重复
        CopyWriting copyWriting = this.baseMapper.selectOne(new QueryWrapper<CopyWriting>().lambda()
                .eq(CopyWriting::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(CopyWriting::getMasterId, updateVO.getMasterId())
                .eq(CopyWriting::getAssetId, asset.getId())
                .eq(CopyWriting::getCopyWritingType, updateVO.getCopyWritingType())
                .ne(CopyWriting::getId, updateVO.getId()));
        if (copyWriting != null) {
            throw new CustomException("该文案已存在，请确认后再试");
        }
        // 3.获取文案与账号关系
        copyWriting = new CopyWriting();
        // 4.更新
        copyWriting.setAssetId(asset.getId());
        copyWriting.setCountryId(updateVO.getCountryId());
        copyWriting.setTranslatedText(updateVO.getTranslatedText());
        copyWriting.setUpdateUid(userId);
        this.baseMapper.update(copyWriting, new QueryWrapper<CopyWriting>().lambda()
                .eq(CopyWriting::getId, updateVO.getId()));
    }

    private Asset getOrSaveCopyWritingAsset(String copyWriting, Integer userId) {

        // 1.先md5获取素材
        String md5 = DigestUtils.md5DigestAsHex(copyWriting.getBytes());
        Asset asset = this.assetMapper.selectOne(new QueryWrapper<Asset>().lambda()
                .eq(Asset::getMd5, md5));
        // 2.如果asset不存在，则新增
        if (asset == null) {
            asset = new Asset();
            asset.setMd5(md5);
            asset.setAssetType(AssetTypeEnum.TEXT.getId());
            asset.setContent(copyWriting);
            asset.setCreateUid(userId);
            this.assetMapper.insert(asset);
        }
        return asset;
    }

    @Override
    public void deleteCopyWriting(CopyWritingGetVO getVO, Integer userId) {
        this.baseMapper.delete(new QueryWrapper<CopyWriting>().lambda()
                .eq(CopyWriting::getId, getVO.getId()));
//        CopyWriting copyWriting = new CopyWriting();
//        copyWriting.setIsDel(IsDelEnum.DELETE.getId().intValue());
//        copyWriting.setUpdateUid(userId);
//        this.baseMapper.update(copyWriting, new QueryWrapper<CopyWriting>().lambda()
//                .eq(CopyWriting::getId, getVO.getId()));
    }

    @Override
    public CopyWritingDTO getCopyWriting(CopyWritingGetVO getVO) {
        return this.baseMapper.getCopyWriting(new QueryWrapper<CopyWriting>()
                .eq("writing.is_del", IsDelEnum.NORMAL.getId())
                .eq("writing.id", getVO.getId()));
    }

    @Override
    public PageUtils<CopyWritingListDTO> listCopyWriting(CopyWritingListVO listVO, User user) {
        QueryWrapper<CopyWriting> copyQuery = new QueryWrapper<CopyWriting>()
                .eq("writing.is_del", IsDelEnum.NORMAL.getId())
                .eq("writing.master_id", listVO.getMasterId())
                .eq(StringUtils.isNotBlank(listVO.getAssetId()), "writing.asset_id", listVO.getAssetId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getCopyWritingStatus()), "writing.copy_writing_status", listVO.getCopyWritingStatus())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getCopyWritingType()), "writing.copy_writing_type", listVO.getCopyWritingType())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getCountryId()), "writing.country_id", listVO.getCountryId())
                .eq(sheinConfiguration.isRole(user.getRoleId()), "writing.create_uid", user.getId())
                .and(StringUtils.isNotEmpty(listVO.getSearch()), q -> q
                        .like("writing.translated_text", listVO.getSearch())
                        .or().like("asset.content", listVO.getSearch()));
        if (StringUtils.isBlank(listVO.getSortField()) || "id".equals(listVO.getSortField())) {
            IPage<CopyWritingListDTO> iPage = this.baseMapper.listCopyWritingPage(new Page<>(listVO.getPage(), listVO.getPageNum()),
                    copyQuery.orderBy(true, "asc".equalsIgnoreCase(listVO.getSortType()), "writing.id")
            );
            if (iPage.getRecords().isEmpty()) {
                return new PageUtils<>(iPage);
            }
            AssetFieldListVO assetFieldListVO = new AssetFieldListVO();
            BeanUtils.copyProperties(listVO, assetFieldListVO);
            assetFieldListVO.setFieldType(listVO.getCopyWritingType());
            assetFieldListVO.setAssetIds(iPage.getRecords().stream().map(CopyWritingListDTO::getAssetId).collect(Collectors.toList()));
            assetFieldListVO.setSortField("");
            Map<Long, TextCombineListDTO> resultMap = fgReportService.assetFieldList(assetFieldListVO).getData()
                    .stream().collect(Collectors.toMap(u -> CopyWritingTypeEnum.TITLE.getId().equals(listVO.getCopyWritingType()) ? u.getTitleId() : u.getDescId(),
                            Function.identity()));
            //创意总数
            Map<Long, Integer> assetNumMap = assetService.getUnitCountOfAsset(listVO.getMasterId(), assetFieldListVO.getAssetIds(), listVO.getCopyWritingType(), user);
            //投放创意
            Map<Long, Integer> assetPutNumMap = assetService.getUnitPutCountOfAsset(listVO.getMasterId(), assetFieldListVO.getAssetIds(), listVO.getCopyWritingType(), user);
            iPage.getRecords().forEach(u -> {
                TextCombineListDTO textCombineListDTO = resultMap.get(u.getAssetId());
                if (null != textCombineListDTO) {
                    BeanUtils.copyProperties(textCombineListDTO, u,
                            "id", "assetId", "copyWriting", "countryId", "countryName", "translatedText", "copyWritingStatus", "copyWritingType", "createTime");
                }
                u.setCopyWritingTypeName(ICommonEnum.getNameById(u.getCopyWritingType(), CopyWritingTypeEnum.class));
                u.setCreativeUnitNum(String.valueOf(assetNumMap.getOrDefault(u.getAssetId(), 0)));
                u.setCreativeUnitPutNum(assetPutNumMap.getOrDefault(u.getAssetId(), 0));
            });
            return new PageUtils<>(iPage);
        } else {
            Map<Long, CopyWritingListDTO> assetMap = this.baseMapper.listCopyWritingId(copyQuery)
                    .stream().collect(Collectors.toMap(CopyWritingListDTO::getAssetId, Function.identity()));
            AssetFieldListVO assetFieldListVO = new AssetFieldListVO();
            BeanUtils.copyProperties(listVO, assetFieldListVO);
            assetFieldListVO.setFieldType(listVO.getCopyWritingType());
            assetFieldListVO.setAssetIds(new ArrayList<>(assetMap.keySet()));
            FeignR<List<TextCombineListDTO>> feign = fgReportService.assetFieldList(assetFieldListVO);
            if (CollectionUtils.isEmpty(feign.getData())) {
                return new PageUtils<>(List.of(), 0L);
            }
            List<Long> assetIds = feign.getData().stream().map(u -> CopyWritingTypeEnum.TITLE.getId().equals(listVO.getCopyWritingType()) ? u.getTitleId() : u.getDescId()).collect(Collectors.toList());
            //创意总数
            Map<Long, Integer> assetNumMap = assetService.getUnitCountOfAsset(listVO.getMasterId(), assetIds, listVO.getCopyWritingType(), user);
            //投放创意
            Map<Long, Integer> assetPutNumMap = assetService.getUnitPutCountOfAsset(listVO.getMasterId(), assetIds, listVO.getCopyWritingType(), user);
            List<CopyWritingListDTO> result = new ArrayList<>();
            feign.getData().forEach(u -> {
                CopyWritingListDTO copyWritingListDTO = assetMap.get(
                        CopyWritingTypeEnum.TITLE.getId().equals(listVO.getCopyWritingType()) ? u.getTitleId() : u.getDescId()
                );
                BeanUtils.copyProperties(u, copyWritingListDTO,
                        "id", "assetId", "copyWriting", "countryId", "countryName", "translatedText", "copyWritingStatus", "copyWritingType", "createTime");
                copyWritingListDTO.setCopyWritingTypeName(ICommonEnum.getNameById(copyWritingListDTO.getCopyWritingType(), CopyWritingTypeEnum.class));
                copyWritingListDTO.setCreativeUnitNum(String.valueOf(assetNumMap.getOrDefault(copyWritingListDTO.getAssetId(), 0)));
                copyWritingListDTO.setCreativeUnitPutNum(assetPutNumMap.getOrDefault(copyWritingListDTO.getAssetId(), 0));
                result.add(copyWritingListDTO);

            });
            return new PageUtils<>(result, feign.getTotal());
        }
    }

    @Override
    public void exportListCopyWriting(CopyWritingListVO listVO, User user, HttpServletResponse response) throws IOException {
        listVO.setPage(1L);
        listVO.setPageNum(1000000L);
        PageUtils<CopyWritingListDTO> iPage = this.listCopyWriting(listVO, user);
        iPage.getData().forEach(u ->
                u.setCreativeUnitNum(ObjectUtils.isNotNullOrZero(u.getCreativeUnitNum()) ? String.format("%s/%s", u.getCreativeUnitPutNum(), u.getCreativeUnitNum()) : u.getCreativeUnitNum())
        );
        ExcelUtils.download(response, "文案列表", "文案", iPage.getData(), listVO.getCustoms());
    }

    @Override
    public List<CopyWritingCascaderDTO> listCopyWritingCascader(CopyWritingCascaderGetVO getVO) {
        List<CopyWritingListDTO> copyWritingListDTOS = this.baseMapper.listCopyWriting(new QueryWrapper<CopyWriting>()
                .eq("writing.is_del", IsDelEnum.NORMAL.getId())
                .eq("writing.master_id", getVO.getMasterId())
                .eq(ObjectUtils.isNotAll(getVO.getCopyWritingType()), "writing.copy_writing_type", getVO.getCopyWritingType())
                .orderByAsc("writing.copy_writing_status")
                .orderByDesc("writing.id"));

        if (CollectionUtils.isEmpty(copyWritingListDTOS)) {
            return List.of();
        }
        Map<Long, List<CopyWritingListDTO>> copyWritingListDTOMap = copyWritingListDTOS.stream()
                .collect(Collectors.groupingBy(CopyWritingListDTO::getCountryId));
        List<CopyWritingCascaderDTO> result = new ArrayList<>();
        copyWritingListDTOMap.forEach((countryId, copyWritingListDTOList) -> {
            CopyWritingCascaderDTO cascaderDTO = new CopyWritingCascaderDTO();
            cascaderDTO.setValue(countryId.toString());
            cascaderDTO.setLabel(copyWritingListDTOList.get(0).getCountryName());
            cascaderDTO.setChildren(copyWritingListDTOList.stream().map(copyWritingListDTO -> {
                CopyWritingCascaderDTO cascader = new CopyWritingCascaderDTO();
                cascader.setValue(copyWritingListDTO.getCopyWriting());
                if (CopyWritingStatusEnum.NORMAL.getId().equals(copyWritingListDTO.getCopyWritingStatus())) {
                    cascader.setLabel(copyWritingListDTO.getCopyWriting());
                } else {
                    cascader.setLabel(String.format("（暂停）%s", copyWritingListDTO.getCopyWriting()));
                }
                cascader.setStatus(copyWritingListDTO.getCopyWritingStatus());
                cascader.setTranslatedText(copyWritingListDTO.getTranslatedText());
                return cascader;
            }).collect(Collectors.toList()));
            result.add(cascaderDTO);
        });
        return result;
    }

    @Override
    public List<SelectDTO> selectCountry(CopyWritingCountrySelectGetVO getVO) {
        return this.baseMapper.selectCopyWritingCountry(new QueryWrapper<CopyWriting>()
                .eq("writing.is_del", IsDelEnum.NORMAL.getId())
                .ne("writing.country_id", 0)
                .orderByAsc("writing.country_id"));
    }

    @Override
    public List<String> checkCopyWriting(CopyWritingCheckGetVO getVO) {

        List<String> copyWritingList = getVO.getCopyWritingList().stream().distinct().collect(Collectors.toList());

        // 校验Md5是否存在
        List<Asset> assets = this.baseMapper.listCopyWritingByMd5(new QueryWrapper<CopyWriting>()
                .eq("writing.is_del", IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getMasterId()), "writing.master_id", getVO.getMasterId())
                .in("asset.md5", copyWritingList.stream().map(Md5CalculateUtils::getStringMd5).collect(Collectors.toList())));
        // 如果已经存在的md5，排除
        if (CollectionUtils.isNotEmpty(assets)) {
            copyWritingList.removeAll(assets.stream().map(Asset::getContent).collect(Collectors.toList()));
        }
        // 返回需要添加的文案列表
        return copyWritingList;
    }

    @Override
    public void shareCopyWriting(CopyWritingShareSaveVO saveVO, Integer userId) {
        List<CopyWriting> copyWritings = this.baseMapper.selectList(new QueryWrapper<CopyWriting>().lambda()
                .in(CopyWriting::getId, saveVO.getIds()));
        List<CopyWriting> copyWritingList = new ArrayList<>() {{
            copyWritings.forEach(copyWritingDTO -> saveVO.getMasterIds().forEach(masterId -> {
                CopyWriting copyWriting = new CopyWriting();
                BeanUtils.copyProperties(copyWritingDTO, copyWriting);
                copyWriting.setMasterId(masterId);
                copyWriting.setCreateUid(userId);
                add(copyWriting);
            }));
        }};
        this.baseMapper.batchSaveCopyWriting(copyWritingList, userId);
    }

    @Override
    public void changeStatus(CopyWritingStatusVO statusVO, Integer userId) {
        CopyWriting update = new CopyWriting();
        update.setCopyWritingStatus(statusVO.getCopyWritingStatus());
        update.setUpdateUid(userId);
        this.baseMapper.update(update, new LambdaQueryWrapper<CopyWriting>()
                .in(CopyWriting::getId, statusVO.getIds())
                .eq(CopyWriting::getIsDel, IsDelEnum.NORMAL.getId())
        );
    }

    @Override
    public List<CopyWritingUploadDTO> uploadCopyWriting(CopyWritingUploadSaveVO saveVO, Integer userId) {
        List<CopyWritingUploadDTO> list = ExcelUtils.read(saveVO.getFilePath(), CopyWritingUploadDTO.class);
        if (list.isEmpty()) {
            return list;
        }
        ValidatorUtils.validateEntities(list);
        return completeCountryId(list);
    }

    /**
     * 补全地域
     *
     * @param list 数据
     * @return 返回数据
     */
    private <T extends CopyWritingUploadDTO> List<T> completeCountryId(List<T> list) {
        List<String> countryNames = list.stream().map(CopyWritingUploadDTO::getCountryName).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(countryNames)) {
            return list;
        }
        Map<String, Country> countryMap = this.countryMapper.selectList(new LambdaQueryWrapper<Country>().in(Country::getCountryLanguage, countryNames))
                .stream().collect(Collectors.toMap(Country::getCountryLanguage, Function.identity()));
        if (countryMap.size() != countryNames.size()) {
            countryNames.removeAll(countryMap.keySet());
            throw new CustomException("文案中语言不存在");
        }
        list.forEach(u -> {
            u.setCountryId(Long.parseLong(countryMap.get(u.getCountryName()).getCountryId()));
            u.setCopyWritingType(ICommonEnum.getIdByName(u.getCopyWritingTypeName(), CopyWritingTypeEnum.class));
        });
        return list;
    }
}
