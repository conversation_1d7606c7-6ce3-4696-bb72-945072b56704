package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.overseas.common.dto.market.cps.amazon.CpsAmazon2OrderDTO;
import com.overseas.common.dto.market.cps.amazon.CpsAmazonEarningsExcelDTO;
import com.overseas.common.dto.market.cps.amazon.CpsAmazonOrderExcelDTO;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.enums.TimeZoneEnum;
import com.overseas.common.enums.market.cps.CpsSyncStatusEnum;
import com.overseas.common.utils.*;
import com.overseas.common.vo.market.cps.CpsSaveVO;
import com.overseas.service.market.entity.*;
import com.overseas.service.market.enums.cps.CpsOrderStatusEnum;
import com.overseas.service.market.enums.creative.units.CreativeUnitStatusEnum;
import com.overseas.common.enums.market.plan.PlanStatusEnum;
import com.overseas.service.market.mapper.CpsOrderMapper;
import com.overseas.service.market.mapper.CreativeUnitMapper;
import com.overseas.service.market.mapper.PlanMapper;
import com.overseas.service.market.mapper.cps.CpsFeeEarningMapper;
import com.overseas.service.market.mapper.cps.CpsFeeOrderMapper;
import com.overseas.service.market.service.CpsAmazonOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLDecoder;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@RequiredArgsConstructor
@Slf4j
@Service
public class CpsAmazonOrderServiceImpl implements CpsAmazonOrderService {

    private final CurrencyUtils currencyUtils;

    private final CpsFeeOrderMapper cpsFeeOrderMapper;

    private final CpsFeeEarningMapper cpsFeeEarningMapper;

    private final PlanMapper planMapper;

    private final CreativeUnitMapper creativeUnitMapper;

    private final CpsOrderMapper cpsOrderMapper;

    private final Integer MASTER_ID = 20012;

    private final Long PROJECT_ID = 26L;

    @Override
    public void saveOrderInfo(CpsSaveVO cpsSaveVO, Integer userId) {
        cpsSaveVO.setFilePath(UploadUtils.getUploadPath(cpsSaveVO.getFilePath()));
        this.exportFeeOrder(cpsSaveVO, userId);
        this.exportFeeEarning(cpsSaveVO, userId);
    }

    @Override
    public void mateOrderAndEarning() {
        List<CpsFeeEarning> feeEarnings = this.cpsFeeEarningMapper.selectList(new LambdaQueryWrapper<CpsFeeEarning>()
                .eq(CpsFeeEarning::getFeeOrderId, 0)
                .orderByAsc(CpsFeeEarning::getSettlementTime)
                .orderByDesc(CpsFeeEarning::getId)
        );
        if (CollectionUtils.isEmpty(feeEarnings)) {
            log.info("无未归因回传订单数据");
            return;
        }
        AtomicInteger ind = new AtomicInteger(1);
        long total = feeEarnings.size();
        feeEarnings.forEach(u -> {
            log.info("开始执行第{}条，总共:{}", ind.getAndIncrement(), total);
            try {
                LambdaQueryWrapper<CpsFeeOrder> lambdaQueryWrapper = new LambdaQueryWrapper<CpsFeeOrder>()
                        .eq(CpsFeeOrder::getTrackingId, u.getTrackingId())
                        .eq(CpsFeeOrder::getProductId, u.getProductId())
                        .ge(CpsFeeOrder::getProductCount, ObjectUtils.isNotNullOrZero(u.getProductCount()) ? u.getProductCount() : u.getReturnCount())
                        .lt(CpsFeeOrder::getOrderTime, u.getSettlementTime());
                if (u.getReturnCount() > 0) {
                    lambdaQueryWrapper.orderByDesc(CpsFeeOrder::getOrderTime);
                } else {
                    lambdaQueryWrapper.orderByAsc(CpsFeeOrder::getOrderTime);
                }
                List<CpsFeeOrder> cpsFeeOrders = cpsFeeOrderMapper.selectList(lambdaQueryWrapper);
                Optional<CpsFeeOrder> optionalCpsOrder;
                if (ObjectUtils.isNullOrZero(u.getReturnCount())) {
                    cpsFeeOrders = cpsFeeOrders.stream().filter(v -> !v.getProductCount().equals(v.getEarningProduct())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(cpsFeeOrders)) {
                        return;
                    }
                    optionalCpsOrder = cpsFeeOrders.stream().filter(v ->
                            v.getProductCount().equals(u.getProductCount()) && v.getEarningProduct() == 0
                    ).findFirst();
                    if (optionalCpsOrder.isEmpty()) {
                        optionalCpsOrder = cpsFeeOrders.stream().filter(v -> v.getProductCount() - v.getEarningProduct() >= u.getProductCount()).findFirst();
                    }
                } else {
                    cpsFeeOrders = cpsFeeOrders.stream().filter(v -> !v.getProductCount().equals(v.getEarningReturn())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(cpsFeeOrders)) {
                        return;
                    }
                    optionalCpsOrder = cpsFeeOrders.stream().filter(v -> v.getProductCount() - v.getEarningReturn() >= u.getReturnCount()).findFirst();
                }
                if (optionalCpsOrder.isEmpty()) {
                    return;
                }
                CpsFeeOrder current = optionalCpsOrder.get();
                //更新数据
                this.cpsFeeOrderMapper.updateFeeEarningProduct(u.getProductCount(), u.getReturnCount(), current.getId());
                //更新数据表
                this.cpsFeeEarningMapper.updateFeeOrderId(current.getId(), u.getId());
            } catch (Exception e) {
                log.error("归因订单出现错误:{}", e.getMessage(), e);
            }
        });
    }

    @Override
    public void transformCpsOrder() {
        Map<String, CreativeUnit> creativeUnitMap = this.getCreativeUnitTrackingMap();
        Map<String, Plan> planMap = this.getPlanTrackingMap();
        Map<String, BigDecimal> transRateMap = new HashMap<>();
        List<CpsAmazon2OrderDTO> orderList = this.cpsFeeOrderMapper.listOrderAndEarning(new QueryWrapper<>()
                .eq("cfo.is_sync", CpsSyncStatusEnum.NOT_SYNC.getId())
                .ne("cfe.fee_order_id", 0)
                .groupBy("cfe.fee_order_id")
                .groupBy("cfe.order_status")
        );
        List<CpsAmazon2OrderDTO> orderNoEarnings = this.cpsFeeOrderMapper.listOrderNoEarning(new QueryWrapper<>()
                .eq("cfo.is_sync", CpsSyncStatusEnum.NOT_SYNC.getId())
                .eq("cfo.earning_product", 0)
                .eq("cfo.earning_return", 0)
        );
        orderList.addAll(orderNoEarnings);
        if (CollectionUtils.isEmpty(orderList)) {
            log.info("无数据需要执行");
            return;
        }
        //数据处理数据
        List<CpsOrder> cpsOrders = orderList.stream().map(fee -> {
            try {
                BigDecimal transRate = transRateMap.get(fee.getCountry());
                if (null == transRate) {
                    transRate = currencyUtils.transRate(fee.getCountry());
                    transRateMap.put(fee.getCountry(), transRate);
                }
                //获取货币缩写
                String currency = currencyUtils.transCurrency(fee.getCountry());
                CpsOrder cpsOrder = new CpsOrder();
                BeanUtils.copyProperties(fee, cpsOrder);
                cpsOrder.setProjectId(PROJECT_ID);
                cpsOrder.setProductTitle(fee.getOrderName());
                cpsOrder.setCountry(cpsOrder.getCountry().toUpperCase());
                cpsOrder.setCurrency(currency);
                cpsOrder.setIsSync(CpsSyncStatusEnum.NOT_SYNC.getId());
                cpsOrder.setOrderTime(DateUtils.date2Long(
                        TimeZoneEnum.getUTC_8Date(DateUtils.long2Date(fee.getOrderTime())
                                , fee.getTimezone(), 0))
                );
                if ("Y".equals(fee.getConversionType())) {
                    cpsOrder.setConversionType(1);
                }
                if ("N".equals(fee.getConversionType())) {
                    cpsOrder.setConversionType(2);
                }
                if (null == cpsOrder.getConversionType()) {
                    cpsOrder.setConversionType(0);
                }
                cpsOrder.setOrderDate(DateUtils.string2Long(DateUtils.format(DateUtils.long2Date(cpsOrder.getOrderTime()))));
                cpsOrder.setOrderId(String.format("%s-%s", cpsOrder.getOrderTime(), cpsOrder.getOrderStatus()));
                switch (cpsOrder.getOrderStatus()) {
                    case 4:
                        cpsOrder.setProductCount(fee.getEarningReturn());
                        cpsOrder.setEstimateAmount(fee.getRevenue().negate().multiply(transRate));
                        cpsOrder.setEstimateCommission(fee.getAdFee().negate().multiply(transRate));
                        cpsOrder.setActualCommission(BigDecimal.ZERO);
                        cpsOrder.setActualAmount(BigDecimal.ZERO);
                        break;
                    case 3:
                        cpsOrder.setProductCount(fee.getEarningProduct() - fee.getEarningReturn());
                        if (cpsOrder.getProductCount() == 0) {
                            return null;
                        }
                        if (fee.getEarningReturn() == 0) {
                            cpsOrder.setActualAmount(fee.getRevenue().multiply(transRate));
                            cpsOrder.setActualCommission(fee.getAdFee().multiply(transRate));
                        } else {
                            cpsOrder.setActualAmount(fee.getEarningPrice().multiply(BigDecimal.valueOf(cpsOrder.getProductCount())).multiply(transRate));
                            cpsOrder.setActualCommission(fee.getAdFee().multiply(BigDecimal.valueOf(cpsOrder.getProductCount())).multiply(transRate)
                                    .divide(BigDecimal.valueOf(fee.getEarningProduct()), 2, RoundingMode.HALF_UP));
                        }
                        cpsOrder.setEstimateAmount(cpsOrder.getActualAmount());
                        cpsOrder.setEstimateCommission(cpsOrder.getActualCommission());
                        break;
                    case 2:
                        cpsOrder.setProductCount(fee.getEarningProduct());
                        cpsOrder.setEstimateAmount(fee.getProductPrice().multiply(BigDecimal.valueOf(cpsOrder.getProductCount())).multiply(transRate));
                        cpsOrder.setEstimateCommission(cpsOrder.getEstimateAmount().multiply(BigDecimal.valueOf(0.01d)));
                        cpsOrder.setActualCommission(BigDecimal.ZERO);
                        cpsOrder.setActualAmount(BigDecimal.ZERO);
                        break;
                    default:
                        return null;
                }
                if (creativeUnitMap.containsKey(fee.getTrackingId())) {
                    CreativeUnit unit = creativeUnitMap.get(fee.getTrackingId());
                    cpsOrder.setPlanId(unit.getPlanId());
                    cpsOrder.setMasterId(unit.getMasterId());
                    cpsOrder.setCreativeUnitId(unit.getId());
                } else if (planMap.containsKey(fee.getTrackingId())) {
                    Plan plan = planMap.get(fee.getTrackingId());
                    cpsOrder.setPlanId(plan.getId());
                    cpsOrder.setMasterId(plan.getMasterId());
                    cpsOrder.setCreativeUnitId(0L);
                } else {
                    CpsOrder last = this.cpsOrderMapper.selectOne(new LambdaQueryWrapper<CpsOrder>()
                            .eq(CpsOrder::getTrackingId, fee.getTrackingId())
                            .ne(CpsOrder::getPlanId, 0)
                            .orderByDesc(CpsOrder::getId)
                            .orderByDesc(CpsOrder::getOrderTime).last("limit 1")
                    );
                    if (null == last) {
                        log.error("fee order id : {} 无法获取到订单信息", fee.getFeeOrderId());
                        cpsOrder.setPlanId(0L);
                        cpsOrder.setMasterId(0);
                        cpsOrder.setCreativeUnitId(0L);
                    } else {
                        cpsOrder.setMasterId(last.getMasterId());
                        cpsOrder.setPlanId(last.getPlanId());
                        cpsOrder.setCreativeUnitId(last.getCreativeUnitId());
                    }
                }
                return cpsOrder;
            } catch (Exception e) {
                log.error("转化订单整体数据出现错误:{}", e.getMessage(), e);
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        //无数据需要执行
        if (CollectionUtils.isEmpty(cpsOrders)) {
            log.info("无转化订单整体数据需要录入");
            return;
        }
        log.info("转化订单整体数据录入:{}条", cpsOrders.size());
        this.cpsOrderMapper.batchSaveCpsAmazon(cpsOrders);
        //更新同步状态
        CpsFeeOrder update = new CpsFeeOrder();
        update.setIsSync(CpsSyncStatusEnum.SYNC.getId());
        this.cpsFeeOrderMapper.update(update,
                new LambdaQueryWrapper<CpsFeeOrder>().eq(CpsFeeOrder::getIsSync, CpsSyncStatusEnum.NOT_SYNC.getId())
        );
    }


    /**
     * 导入 fee order
     *
     * @param cpsSaveVO 条件
     */
    private void exportFeeOrder(CpsSaveVO cpsSaveVO, Integer userId) {
        // 1.录入excel中数据
        List<CpsAmazonOrderExcelDTO> orderList = ExcelUtils.read(cpsSaveVO.getFilePath(), 2, "Fee-Orders", CpsAmazonOrderExcelDTO.class);
        log.info("本次订单总共读取 {} 条数据", orderList.size());
        if (CollectionUtils.isNotEmpty(orderList)) {
            Map<String, Integer> repeatMap = new HashMap<>();
            orderList.forEach(u -> {
                u.setOrderTime(DateUtils.string2Long(u.getReportDate()));
                String key = String.format("%s-%s-%s-%s", u.getTrackingId(), u.getProductId(), u.getOrderTime(), u.getProductCount());
                if (repeatMap.containsKey(key)) {
                    Integer idx = repeatMap.get(key);
                    u.setIdx(idx + 1);
                } else {
                    u.setIdx(0);
                }
                repeatMap.put(key, u.getIdx());

            });
            cpsFeeOrderMapper.insertByUk(orderList, cpsSaveVO, userId);
        }
    }

    /**
     * 导入 fee earning
     *
     * @param cpsSaveVO 条件
     */
    private void exportFeeEarning(CpsSaveVO cpsSaveVO, Integer userId) {
        // 2.录入excel中的佣金
        List<CpsAmazonEarningsExcelDTO> earningList = ExcelUtils.read(cpsSaveVO.getFilePath(), 2, "Fee-Earnings", CpsAmazonEarningsExcelDTO.class);
        log.info("本次佣金总共读取 {} 条数据", earningList.size());
        if (CollectionUtils.isNotEmpty(earningList)) {
            //为了解决同一个excel相同问题
            Map<String, Integer> repeatMap = new HashMap<>();
            earningList.forEach(u -> {
                u.setSettlementTime(DateUtils.string2Long(u.getReportDate()));
                if (u.getReturnCount() > 0) {
                    u.setOrderStatus(CpsOrderStatusEnum.REFUND.getId());
                } else {
                    u.setOrderStatus(CpsOrderStatusEnum.SETTLED.getId());
                }
                String key = String.format("%s-%s-%s-%s-%s", u.getTrackingId(), u.getProductId(), u.getSettlementTime(), u.getProductCount(), u.getReturnCount());
                if (repeatMap.containsKey(key)) {
                    Integer idx = repeatMap.get(key);
                    u.setIdx(idx + 1);
                } else {
                    u.setIdx(0);
                }
                repeatMap.put(key, u.getIdx());
            });
            cpsFeeEarningMapper.insertByUk(earningList, userId);
        }
    }

    /**
     * 获取 计划 tracking map
     *
     * @return 计划map
     */
    private Map<String, Plan> getPlanTrackingMap() {
        // 4.获取计划中的落地页
        return this.planMapper.selectList(new QueryWrapper<Plan>().lambda()
                .eq(Plan::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(Plan::getPlanStatus, PlanStatusEnum.MARKETING.getId())
                .eq(Plan::getMasterId, MASTER_ID)
        ).stream().collect(Collectors.toMap(
                plan -> this.getTagByLanding(plan.getLandingUrl()), Function.identity(),
                (n, o) -> o.getId() > n.getId() ? o : n));
    }

    /**
     * 获取 创意 tracking map
     *
     * @return 返回数据
     */
    private Map<String, CreativeUnit> getCreativeUnitTrackingMap() {
        // 4.获取计划中的落地页
        return this.creativeUnitMapper.selectList(new QueryWrapper<CreativeUnit>().lambda()
                .eq(CreativeUnit::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(CreativeUnit::getCreativeUnitStatus, CreativeUnitStatusEnum.MARKETING.getId())
                .eq(CreativeUnit::getMasterId, MASTER_ID)
                .like(CreativeUnit::getLandingUrl, "tag=")
        ).stream().collect(Collectors.toMap(
                creativeUnit -> this.getTagByLanding(creativeUnit.getLandingUrl()),
                Function.identity(), (n, o) -> o.getId() > n.getId() ? o : n));
    }

    /**
     * 获取 tracking
     *
     * @param landing 落地页
     * @return 返回tracking
     */
    private String getTagByLanding(String landing) {
        try {
            landing = URLDecoder.decode(landing, "GBK");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        Pattern pattern = Pattern.compile("tag=[A-Za-z0-9-_]*");
        Matcher matcher = pattern.matcher(landing);
        String res = "";
        while (matcher.find()) {
            res = matcher.group().split("=")[1];
        }
        return res;
    }
}
