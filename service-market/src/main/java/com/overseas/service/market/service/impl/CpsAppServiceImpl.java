package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.service.market.dto.cps.app.CpsAppListDTO;
import com.overseas.service.market.entity.ae.AeApp;
import com.overseas.service.market.mapper.ae.AeAppMapper;
import com.overseas.service.market.service.CpsAppService;
import com.overseas.service.market.vo.cps.app.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class CpsAppServiceImpl extends ServiceImpl<AeAppMapper, AeApp> implements CpsAppService {

    @Override
    public void saveCpsApp(CpsAppSaveVO saveVO, Integer loginUserId) {
        this.checkCpsAppName(null, saveVO.getProjectId(), saveVO.getAppName());
        AeApp aeApp = new AeApp();
        aeApp.setCreateUid(loginUserId);
        BeanUtils.copyProperties(saveVO, aeApp);
        int insert = this.baseMapper.insert(aeApp);
        if (insert == 0) {
            throw new CustomException("添加失败");
        }
    }

    @Override
    public void updateCpsApp(CpsAppUpdateVO updateVO, Integer loginUserId) {
        this.checkCpsAppName(updateVO.getId(), updateVO.getProjectId(), updateVO.getAppName());
        AeApp aeApp = new AeApp();
        aeApp.setCreateUid(loginUserId);
        BeanUtils.copyProperties(updateVO, aeApp);
        int update = this.baseMapper.updateById(aeApp);
        if (update == 0) {
            throw new CustomException("添加失败");
        }
    }

    @Override
    public PageUtils<CpsAppListDTO> listCpsApp(CpsAppListVO listVO) {
        QueryWrapper<AeApp> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(ObjectUtils.isNotNullOrZero(listVO.getProjectId()),
                        "aa.project_id", listVO.getProjectId())
                .eq("aa.is_del", IsDelEnum.NORMAL.getId())
                .and(StringUtils.isNotBlank(listVO.getSearch()),
                        p -> p.eq("aa.id", listVO.getSearch())
                                .or().like("aa.app_name", listVO.getSearch())
                ).orderByDesc("aa.id");
        IPage<CpsAppListDTO> pageData = this.baseMapper.listCpsApp(
                new Page<>(listVO.getPage(), listVO.getPageNum()), queryWrapper);
        return new PageUtils<>(pageData);
    }

    @Override
    public void deleteCspApp(CpsAppDeleteVO deleteVO, Integer loginUserId) {
        AeApp aeApp = new AeApp();
        aeApp.setId(deleteVO.getCpsAppId());
        aeApp.setIsDel(IsDelEnum.DELETE.getId());
        aeApp.setUpdateUid(loginUserId);
        this.baseMapper.updateById(aeApp);
    }

    @Override
    public List<SelectDTO> selectCpsApp(CpsAppSelectVO selectVO) {
        return this.baseMapper.selectCpsApp(new QueryWrapper<AeApp>().lambda()
                .eq(AeApp::getProjectId, selectVO.getProjectId())
                .eq(AeApp::getIsDel, IsDelEnum.NORMAL.getId())
                .orderByDesc(AeApp::getId)
        );
    }

    private void checkCpsAppName(Long appId, Long projectId, String appName) {
        AeApp aeApp = this.baseMapper.selectOne(new QueryWrapper<AeApp>().lambda()
                .eq(AeApp::getProjectId, projectId)
                .eq(AeApp::getAppName, appName)
                .ne(ObjectUtils.isNotNullOrZero(appId), AeApp::getId, appId)
                .eq(AeApp::getIsDel, IsDelEnum.NORMAL.getId())
        );
        if (null != aeApp) {
            throw new CustomException("账户名称已存在，请修改后再试");
        }
    }
}
