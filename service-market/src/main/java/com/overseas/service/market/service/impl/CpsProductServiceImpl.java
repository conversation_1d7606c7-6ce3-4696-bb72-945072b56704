package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.report.ReportListDTO;
import com.overseas.common.enums.CpsProjectEnum;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.SortTypeEnum;
import com.overseas.common.enums.TimeZoneEnum;
import com.overseas.common.enums.market.PutEnum;
import com.overseas.common.enums.market.cps.CpsAnalysisLevelEnum;
import com.overseas.common.enums.report.ReportTypeEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.*;
import com.overseas.common.vo.report.ReportListVO;
import com.overseas.service.market.dto.cps.CpsProductAnalysisChartDTO;
import com.overseas.service.market.dto.cps.CpsProductAnalysisListDTO;
import com.overseas.service.market.dto.cps.CpsProductExportDTO;
import com.overseas.service.market.dto.cps.CpsProductListDTO;
import com.overseas.service.market.entity.CountryAll;
import com.overseas.service.market.entity.CpsOrder;
import com.overseas.service.market.entity.cps.CpsProduct;
import com.overseas.service.market.entity.cps.CpsProductCategory;
import com.overseas.service.market.entity.cps.CpsProductMaterial;
import com.overseas.service.market.enums.cps.CpsConversionTypeEnum;
import com.overseas.service.market.enums.cps.CpsMaterialTypeEnum;
import com.overseas.service.market.enums.cps.CpsOrderStatusEnum;
import com.overseas.service.market.enums.cps.CpsProductTypeEnum;
import com.overseas.service.market.feign.FgReportService;
import com.overseas.service.market.mapper.CountryAllMapper;
import com.overseas.service.market.mapper.cps.CpsProductCategoryMapper;
import com.overseas.service.market.mapper.cps.CpsProductMapper;
import com.overseas.service.market.mapper.cps.CpsProductMaterialMapper;
import com.overseas.service.market.service.CpsProductService;
import com.overseas.service.market.vo.cps.*;
import com.overseas.service.market.vo.cps.lazada.CpsProductCategorySaveVO;
import com.overseas.service.market.vo.cps.lazada.CpsProductListVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class CpsProductServiceImpl extends ServiceImpl<CpsProductMapper, CpsProduct> implements CpsProductService {
    private final CpsProductMaterialMapper cpsProductMaterialMapper;

    private final CpsProductCategoryMapper cpsProductCategoryMapper;

    private final CountryAllMapper countryAllMapper;

    private final FgReportService fgReportService;

    // 订单商品分析自定义维度字段
    private final Map<String, String> productAnalysisGroupFields = new HashMap<>() {{
        put("orderDate", "co.order_date");
        put("firstCategory", "cp.first_category_id");
        put("secondCategory", "cp.second_category_id");
        put("thirdCategory", "cp.third_category_id");
        put("fourthCategory", "cp.fourth_category_id");
        put("leafCategory", "cp.leaf_category_id");
        put("campaign", "mp.campaign_id");
        put("plan", "co.plan_id");
        put("trackingId", "co.tracking_id");
        put("orderStatus", "co.order_status");
        put("country", "co.country");
        put("creativeUnitId", "co.creative_unit_id");
        put("product", "co.product_id");
        put("conversionType", "co.conversion_type");
    }};

    // 选品库排序字段
    private final Map<String, String> sortFieldMap = new HashMap<>() {{
        put("id", "cp.id");
        put("productPrice", "cp.product_price");
        put("productDiscount", "cp.product_discount");
        put("discountedPrice", "cp.discounted_price");
        put("feedbackCount", "cpi.merchant_feedback_count");
        put("salesVolume", "cpi.lastest_volume");
    }};

    @Override
    public PageUtils<CpsProductListDTO> listCpsProduct(com.overseas.service.market.vo.cps.CpsProductListVO listVO) {
        IPage<CpsProductListDTO> page = new Page<>(listVO.getPage(), listVO.getPageNum());
        IPage<CpsProductListDTO> pageData;
        QueryWrapper<CpsProduct> queryWrapper = new QueryWrapper<>();

        queryWrapper.eq("cp.project_id", listVO.getProjectId());
        if (null != listVO.getMinPrice() && null != listVO.getMaxPrice()) {
            queryWrapper.between("cp.product_price", listVO.getMinPrice(), listVO.getMaxPrice());
        }
        if (StringUtils.isNotBlank(listVO.getLanguage())) {
            queryWrapper.eq("cp.product_language", listVO.getLanguage());
        }
        if (StringUtils.isNotBlank(listVO.getSortField()) && StringUtils.isNotBlank(listVO.getSortType())) {
            String sortField = this.sortFieldMap.getOrDefault(listVO.getSortField(), "cp.id");
            // 亚马逊支持查询评价数
            if (listVO.getProjectId() == CpsProjectEnum.AMAZON.getId().longValue()
                    && listVO.getSortField().equals("feedbackCount")) {
                sortField = this.sortFieldMap.get(listVO.getSortField());
            }
            // AE支持查询销量
            if (listVO.getProjectId() == CpsProjectEnum.AE.getId().longValue()
                    && listVO.getSortField().equals("salesVolume")) {
                sortField = this.sortFieldMap.get(listVO.getSortField());
            }
            if (listVO.getSortType().equals("desc")) {
                queryWrapper.orderByDesc(sortField);
            } else {
                queryWrapper.orderByAsc(sortField);
            }
        }
        queryWrapper.eq(ObjectUtils.isNotNullOrZero(listVO.getFirstCategoryId()),
                "cp.first_category_id", listVO.getFirstCategoryId());
        queryWrapper.eq(ObjectUtils.isNotNullOrZero(listVO.getSecondCategoryId()),
                "cp.second_category_id", listVO.getSecondCategoryId());
        queryWrapper.eq(ObjectUtils.isNotNullOrZero(listVO.getThirdCategoryId()),
                "cp.third_category_id", listVO.getThirdCategoryId());
        queryWrapper.eq(ObjectUtils.isNotNullOrZero(listVO.getFourthCategoryId()),
                "cp.fourth_category_id", listVO.getFourthCategoryId());
        queryWrapper.like(StringUtils.isNotBlank(listVO.getSearch()),
                "cp.product_sign", listVO.getSearch());
        // AE
        if (listVO.getProjectId() == 25L) {
            if (null != listVO.getMinSalesVolume() && null != listVO.getMaxSalesVolume()) {
                queryWrapper.between("cpi.lastest_volume", listVO.getMinSalesVolume(), listVO.getMaxSalesVolume());
            }
            pageData = this.baseMapper.listAeProduct(page, queryWrapper);
            //Amazon
        } else if (listVO.getProjectId() == 26L) {
            pageData = this.baseMapper.listAmazonProduct(page, queryWrapper);
            //Lazada
        } else if (listVO.getProjectId() == 18L) {
            pageData = this.baseMapper.listLazadaProduct(page, queryWrapper);
        } else {
            return new PageUtils<>(List.of(), 0L);
        }
        Map<Long, List<CpsProductMaterial>> productMaterials = this.getProductMaterials(
                pageData.getRecords().stream().map(CpsProductListDTO::getId).collect(Collectors.toList()));
        List<Long> firstCategoryIds = pageData.getRecords().stream().map(CpsProductListDTO::getFirstCategoryId)
                .collect(Collectors.toList());
        List<Long> secondCategoryIds = pageData.getRecords().stream().map(CpsProductListDTO::getSecondCategoryId)
                .collect(Collectors.toList());
        List<Long> thirdCategoryIds = pageData.getRecords().stream().map(CpsProductListDTO::getThirdCategoryId)
                .collect(Collectors.toList());
        List<Long> fourthCategoryIds = pageData.getRecords().stream().map(CpsProductListDTO::getFourthCategoryId)
                .collect(Collectors.toList());
        firstCategoryIds.addAll(secondCategoryIds);
        firstCategoryIds.addAll(thirdCategoryIds);
        firstCategoryIds.addAll(fourthCategoryIds);
        Map<Long, String> categoryMap = this.getProductCategory(listVO.getProjectId(), firstCategoryIds);
        // 商品素材
        pageData.getRecords().forEach(product -> {
            product.setCreateDate(DateUtils.format(product.getCreateTime(), DateUtils.DATE_TIME_PATTERN));
            product.setProductMaterials(
                    productMaterials.getOrDefault(product.getId(), List.of()));
            product.setProductTypeName(
                    ICommonEnum.getNameById(product.getProductType(), CpsProductTypeEnum.class));
            product.setFirstCategoryName(
                    categoryMap.getOrDefault(product.getFirstCategoryId(), ConstantUtils.PLACEHOLDER));
            product.setSecondCategoryName(
                    categoryMap.getOrDefault(product.getSecondCategoryId(), ConstantUtils.PLACEHOLDER));
            product.setThirdCategoryName(
                    categoryMap.getOrDefault(product.getThirdCategoryId(), ConstantUtils.PLACEHOLDER));
            product.setFourthCategoryName(
                    categoryMap.getOrDefault(product.getFourthCategoryId(), ConstantUtils.PLACEHOLDER));
        });
        return new PageUtils<>(pageData);
    }

    @Override
    public List<SelectDTO> selectCpsProductCategory(CpsProductCategorySelectVO selectVO) {
        List<CpsProductCategory> categories = this.cpsProductCategoryMapper.selectList(
                new QueryWrapper<CpsProductCategory>().lambda()
                        .eq(CpsProductCategory::getProjectId, selectVO.getProjectId())
                        .eq(CpsProductCategory::getParentId, selectVO.getParentId())
        );
        return dealCategoryName(selectVO.getProjectId().intValue(), categories).stream()
                .map(u -> new SelectDTO(u.getCategoryId(), u.getCategoryName())).collect(Collectors.toList());
    }

    @Override
    public void downloadMaterial(CpsMaterialDownloadVO downloadVO, HttpServletRequest request,
                                 HttpServletResponse response) {
        List<CpsProductMaterial> materials = this.cpsProductMaterialMapper.selectList(
                new QueryWrapper<CpsProductMaterial>().lambda()
                        .in(CpsProductMaterial::getProductId, downloadVO.getProductIds()));
        if (materials.isEmpty()) {
            throw new CustomException("已选商品无可下载素材");
        }
        Map<String, String> materialMap = new HashMap<>();
        materials.forEach(material -> {
            String name = material.getProductId() + "_"
                    + ICommonEnum.getNameById(material.getMaterialType(), CpsMaterialTypeEnum.class)
                    + "_" + material.getId() + "." + UploadUtils.getExtension(material.getMaterialPath());
            String path = UploadUtils.getUploadPath(material.getMaterialPath());
            materialMap.put(name, path);
        });
        String zipName = "商品素材_" + DateUtils.getTodayStringDate() + "_" +
                Md5CalculateUtils.getStringMd5(JSONObject.toJSONString(downloadVO.getProductIds())) + ".zip";
        try {
            ZipUtils.zipAndDownload(zipName, request, response, materialMap);
        } catch (IOException e) {
            log.error("下载素材库文件失败，类型：{}, 参数：{}, 错误：{}", zipName, downloadVO.getProductIds(), e.getMessage());
            throw new CustomException("素材文件下载失败，请联系研发人员");
        }
    }

    @Override
    public List<CpsProductAnalysisChartDTO> chartProductAnalysis(CpsProductAnalysisChartVO analysisListVO) {
        analysisListVO.setPage(1L);
        analysisListVO.setPageNum(100000L);
        analysisListVO.setSecondCategoryId(0L);
        PageUtils<CpsProductAnalysisListDTO> pageUtils = this.listProductAnalysis(analysisListVO);
        if (pageUtils.getTotal() == 0) {
            return List.of();
        }
        List<CpsProductAnalysisChartDTO> list = pageUtils.getData().subList(1, pageUtils.getData().size()).stream()
                .map(u -> CpsProductAnalysisChartDTO.builder()
                        .target(u.getFirstCategoryName())
                        .val(Double.parseDouble(ObjectUtils.getObjectValue(u, analysisListVO.getField()).toString())).build())
                .filter(u -> ObjectUtils.isNotNullOrZero(u.getVal()))
                .collect(Collectors.toList());
        double total = list.stream().mapToDouble(CpsProductAnalysisChartDTO::getVal).sum();
        return list.stream().peek(u -> {
            if (ObjectUtils.isNullOrZero(total)) {
                u.setRatio(BigDecimal.ZERO);
            } else {
                u.setRatio(BigDecimal.valueOf(u.getVal()).multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(total), 2, RoundingMode.HALF_UP));
            }
        }).collect(Collectors.toList());

    }

    @Override
    public PageUtils<CpsProductAnalysisListDTO> listProductAnalysis(CpsProductAnalysisListVO analysisListVO) {
        IPage<CpsProductAnalysisListDTO> iPage = this.listAnalysis(analysisListVO);
        if (iPage.getTotal() == 0) {
            return new PageUtils<>(iPage);
        }
        if (CpsAnalysisLevelEnum.COST_ALL.getId().equals(analysisListVO.getAnalysisLevel())) {
            String field = getDimensionType(analysisListVO.getDimensions());
            ReportListVO reportListVO = new ReportListVO();
            reportListVO.setIsPut(PutEnum.IS_PUT.getId());
            reportListVO.setMasterId(analysisListVO.getMasterId());
            reportListVO.setStartDate(analysisListVO.getStartDate());
            reportListVO.setEndDate(analysisListVO.getEndDate());
            reportListVO.setTimeZone(TimeZoneEnum.UTC_8.getId());
            reportListVO.setIdentify("");
            reportListVO.setSortField("");
            reportListVO.setSortType("asc");
            reportListVO.setPage(1L);
            reportListVO.setPageNum(1000L);
            switch (field) {
                default:
                    throw new CustomException("维度出现异常");
                case "orderDate":
                    reportListVO.setReportType(ReportTypeEnum.TIME.getId());
                    break;
                case "campaignId":
                    reportListVO.setCampaignIds(iPage.getRecords().stream().map((u -> Long.parseLong(ObjectUtils.getObjectValue(u, field).toString()))).collect(Collectors.toList()));
                    reportListVO.setReportType(ReportTypeEnum.CAMPAIGN.getId());
                    break;
                case "planId":
                    reportListVO.setPlanIds(iPage.getRecords().stream().map((u -> Long.parseLong(ObjectUtils.getObjectValue(u, field).toString()))).collect(Collectors.toList()));
                    reportListVO.setReportType(ReportTypeEnum.PLAN.getId());
                    break;
                case "creativeUnitId":
                    reportListVO.setCreativeUnitIds(iPage.getRecords().stream().map((u -> Long.parseLong(ObjectUtils.getObjectValue(u, field).toString()))).collect(Collectors.toList()));
                    reportListVO.setReportType(ReportTypeEnum.CREATIVE.getId());
                    break;
            }
            Map<String, ReportListDTO> resultMap = fgReportService.listReport(reportListVO)
                    .getData().stream().collect(Collectors.toMap(
                            u -> {
                                if ("orderDate".equals(field)) {
                                    return u.getReportDate();
                                } else {
                                    return ObjectUtils.getObjectValue(u, field).toString();
                                }
                            }, Function.identity()
                            , (o, v) -> o)
                    );
            iPage.getRecords().forEach(v -> {
                String val = ObjectUtils.getObjectValue(v, field).toString();
                ReportListDTO result = resultMap.get(val);
                if (null != result) {
                    BeanUtils.copyProperties(result, v);
                }
                v.setOrderRate(DoubleUtils.getRate(BigDecimal.valueOf(v.getOrderCount()), BigDecimal.valueOf(v.getClick())));
                if (ObjectUtils.isNotNullOrZero(v.getMasterCost())) {
                    v.setActualRoi(v.getActualCommission().divide(BigDecimal.valueOf(v.getMasterCost()), 2, RoundingMode.HALF_UP));
                    v.setEstimateRoi(v.getEstimateCommission().divide(BigDecimal.valueOf(v.getMasterCost()), 2, RoundingMode.HALF_UP));
                } else {
                    v.setActualRoi(BigDecimal.ZERO);
                    v.setEstimateRoi(BigDecimal.ZERO);
                }
                v.setCommissionRate(DoubleUtils.getRate(v.getActualCommission(), v.getActualAmount()));
            });
        }
        return new PageUtils<>(iPage);
    }

    @Override
    public void exportProductAnalysis(CpsProductAnalysisExportVO analysisListVO,
                                      HttpServletResponse response) throws IOException {
        analysisListVO.setPage(1L);
        analysisListVO.setPageNum(100000L);
        PageUtils<?> pageUtils = this.listProductAnalysis(analysisListVO);
        String fileName;
        switch (analysisListVO.getAnalysisLevel()) {
            case 1:
                fileName = "CPS数据分析-投放数据";
                break;
            case 2:
                fileName = "CPS数据分析-成单数据";
                break;
            case 3:
                fileName = "CPS数据分析-商品数据";
                break;
            default:
                throw new CustomException("无法下载数据");
        }

        ExcelUtils.download(response, fileName, "汇总", pageUtils.getData(), analysisListVO.getFields());
    }

    /**
     * 分析数据
     *
     * @param analysisListVO 筛选条件
     * @return 返回数据
     */
    public IPage<CpsProductAnalysisListDTO> listAnalysis(CpsProductAnalysisListVO analysisListVO) {
        IPage<CpsProductAnalysisListDTO> page = new Page<>(analysisListVO.getPage(), analysisListVO.getPageNum());
        IPage<CpsProductAnalysisListDTO> pageData;
        QueryWrapper<CpsOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("co.project_id", analysisListVO.getProjectId())
                .eq("co.master_id", analysisListVO.getMasterId())
                .in(CollectionUtils.isNotEmpty(analysisListVO.getCampaignIds()),
                        "mp.campaign_id", analysisListVO.getCampaignIds())
                .in(CollectionUtils.isNotEmpty(analysisListVO.getPlanIds()),
                        "co.plan_id", analysisListVO.getPlanIds())
                .in(CollectionUtils.isNotEmpty(analysisListVO.getOrderStatus()),
                        "co.order_status", analysisListVO.getOrderStatus())
                .eq(ObjectUtils.isNotNullOrZero(analysisListVO.getConversionType()),
                        "co.conversion_type", analysisListVO.getConversionType());
        if (StringUtils.isNotBlank(analysisListVO.getLanguage())) {
            queryWrapper.eq("cp.product_language", analysisListVO.getLanguage());
        }
        if (ObjectUtils.isNotNullOrZero(analysisListVO.getFirstCategoryId())) {
            queryWrapper.eq("cp.first_category_id", analysisListVO.getFirstCategoryId());
        }
        if (ObjectUtils.isNotNullOrZero(analysisListVO.getSecondCategoryId())) {
            queryWrapper.eq("cp.second_category_id", analysisListVO.getSecondCategoryId());
        }
        if (ObjectUtils.isNotNullOrZero(analysisListVO.getThirdCategoryId())) {
            queryWrapper.eq("cp.third_category_id", analysisListVO.getThirdCategoryId());
        }
        if (ObjectUtils.isNotNullOrZero(analysisListVO.getFourthCategoryId())) {
            queryWrapper.eq("cp.fourth_category_id", analysisListVO.getFourthCategoryId());
        }
        if (StringUtils.isNotBlank(analysisListVO.getStartDate())
                && StringUtils.isNotBlank(analysisListVO.getEndDate())) {
            queryWrapper.between("co.order_time",
                    DateUtils.string2Long(String.format("%s 00:00:00", analysisListVO.getStartDate())),
                    DateUtils.string2Long(String.format("%s 23:59:59", analysisListVO.getEndDate()))
            );
        }
        if (StringUtils.isNotBlank(analysisListVO.getSearch())) {
            queryWrapper.eq("co.creative_unit_id", analysisListVO.getSearch());
        }
        //总计
        CpsProductAnalysisListDTO total = this.baseMapper.totalProductAnalysis(queryWrapper);
        // 处理维度
        pageData = this.baseMapper.listProductAnalysis(page,
                queryWrapper.groupBy(analysisListVO.getDimensions().stream().map(this.productAnalysisGroupFields::get).collect(Collectors.toList())),
                new QueryWrapper<>().orderBy(StringUtils.isNotBlank(analysisListVO.getSortField()), SortTypeEnum.ASC.getSortType().equals(analysisListVO.getSortType()),
                        HumpLineUtils.humpToLine2(analysisListVO.getSortField()))
        );
        if (page.getTotal() == 0) {
            return page;
        }
        total.setPlanName(ConstantUtils.PLACEHOLDER);
        total.setCampaignName(ConstantUtils.PLACEHOLDER);
        total.setConversionTypeName(ConstantUtils.PLACEHOLDER);
        total.setCountryName(ConstantUtils.PLACEHOLDER);
        total.setOrderStatusName(ConstantUtils.PLACEHOLDER);
        total.setLeafCategoryName(ConstantUtils.PLACEHOLDER);
        total.setFirstCategoryName(ConstantUtils.PLACEHOLDER);
        total.setSecondCategoryName(ConstantUtils.PLACEHOLDER);
        total.setThirdCategoryName(ConstantUtils.PLACEHOLDER);
        total.setFourthCategoryName(ConstantUtils.PLACEHOLDER);
        total.setProductName(ConstantUtils.PLACEHOLDER);

        List<Long> categoryIds = new ArrayList<>();
        List<String> languages = new ArrayList<>();
        pageData.getRecords().forEach(cpsProductAnalysisListDTO -> {
            categoryIds.add(cpsProductAnalysisListDTO.getFirstCategoryId());
            categoryIds.add(cpsProductAnalysisListDTO.getSecondCategoryId());
            categoryIds.add(cpsProductAnalysisListDTO.getLeafCategoryId());
            categoryIds.add(cpsProductAnalysisListDTO.getThirdCategoryId());
            categoryIds.add(cpsProductAnalysisListDTO.getFourthCategoryId());
            languages.add(cpsProductAnalysisListDTO.getCountry());
        });
        // 获取国家信息
        List<CountryAll> countries = languages.isEmpty()
                ? List.of()
                : this.countryAllMapper.selectList(
                new QueryWrapper<CountryAll>().lambda().in(CountryAll::getCountryAlias, languages)
                        .ne(CountryAll::getCountryAlias, ""));
        // 获取一级、二级、叶子品类信息
        Map<Long, String> categoriesMap = this.getProductCategory(analysisListVO.getProjectId(), categoryIds);
        Map<String, String> countriesMap = countries.stream()
                .collect(Collectors.toMap(CountryAll::getCountryAlias, CountryAll::getCountryName));
        pageData.getRecords().forEach(cpsProductAnalysisListDTO -> {
            cpsProductAnalysisListDTO.setFirstCategoryName(categoriesMap.getOrDefault(
                    cpsProductAnalysisListDTO.getFirstCategoryId(), ConstantUtils.PLACEHOLDER));
            cpsProductAnalysisListDTO.setSecondCategoryName(categoriesMap.getOrDefault(
                    cpsProductAnalysisListDTO.getSecondCategoryId(), ConstantUtils.PLACEHOLDER));
            cpsProductAnalysisListDTO.setThirdCategoryName(categoriesMap.getOrDefault(
                    cpsProductAnalysisListDTO.getThirdCategoryId(), ConstantUtils.PLACEHOLDER));
            cpsProductAnalysisListDTO.setFourthCategoryName(categoriesMap.getOrDefault(
                    cpsProductAnalysisListDTO.getFourthCategoryId(), ConstantUtils.PLACEHOLDER));
            cpsProductAnalysisListDTO.setLeafCategoryName(categoriesMap.getOrDefault(
                    cpsProductAnalysisListDTO.getLeafCategoryId(), ConstantUtils.PLACEHOLDER));
            cpsProductAnalysisListDTO.setCountryName(countriesMap.getOrDefault(
                    cpsProductAnalysisListDTO.getCountry(), ConstantUtils.PLACEHOLDER));
            cpsProductAnalysisListDTO.setConversionTypeName(
                    ICommonEnum.getNameById(cpsProductAnalysisListDTO.getConversionType(), CpsConversionTypeEnum.class));
            cpsProductAnalysisListDTO.setOrderStatusName(
                    ICommonEnum.getNameById(cpsProductAnalysisListDTO.getOrderStatus(), CpsOrderStatusEnum.class));
        });
        pageData.setRecords(new ArrayList<>() {{
            add(total);
            addAll(pageData.getRecords());
        }});
        return pageData;
    }

    @Override
    public void updateCpsOrder() {
//        this.baseMapper.updateAeCpsOrder();
//        this.baseMapper.updateAmazonCpsOrder();
    }

    @Override
    public void export(CpsProductExportVO exportVO, HttpServletResponse response) throws IOException {
        List<CpsProductExportDTO> cpsProducts = this.baseMapper.selectList(new LambdaQueryWrapper<CpsProduct>()
                .eq(CpsProduct::getProjectId, exportVO.getProjectId())
                .in(CpsProduct::getId, exportVO.getProductIds())
        ).stream().map(u -> CpsProductExportDTO.builder()
                .productId(u.getId()).productTitle(u.getProductTitle())
                .productUrl(u.getProductDetailUrl()).build()
        ).collect(Collectors.toList());
        ExcelUtils.download(response, "选品数据表", CpsProductExportDTO.class, cpsProducts, List.of());
    }

    @Override
    public List<Long> labelByProductIds(CpsProductLabelVO labelVO) {
        List<Long> productIds = labelVO.getProductIds().stream().distinct().filter(ObjectUtils::isNotNullOrZero).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productIds)) {
            return List.of();
        }
        List<CpsProduct> cpsProducts = this.baseMapper.selectBatchIds(productIds);
        if (CollectionUtils.isEmpty(cpsProducts)) {
            return List.of();
        }
        List<Long> ids = new ArrayList<>();
        for (String key : List.of("fifthCategoryId", "fourthCategoryId", "thirdCategoryId", "secondCategoryId", "firstCategoryId")) {
            List<Long> categories = cpsProducts.stream().map(val -> {
                Object obj = ObjectUtils.getObjectValue(val, key);
                if (null != obj) {
                    return Long.parseLong(obj.toString());
                }
                return null;
            }).filter(ObjectUtils::isNotNullOrZero).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(categories)) {
                ids = this.cpsProductCategoryMapper.selectCategoryLabel(new QueryWrapper<>()
                        .eq("project_id", cpsProducts.get(0).getProjectId())
                        .eq("each_category_id", categories));
                if (CollectionUtils.isNotEmpty(ids)) {
                    return ids;
                }
            }
        }
        return ids;
    }

    @Override
    public CpsProduct listLazadaProductByCountry(CpsProductListVO listVO) {
        List<CpsProduct> products = this.baseMapper.selectList(new QueryWrapper<CpsProduct>().lambda()
                .eq(CpsProduct::getProjectId, 18)
                .eq(CpsProduct::getProductLanguage, listVO.getCountry())
                .eq(CpsProduct::getSecondCategoryId, 0)
                .last("LIMIT 1")
        );
        List<Long> productIds = products.stream().map(CpsProduct::getId).collect(Collectors.toList());
        CpsProduct cpsProductUpdate = new CpsProduct();
        cpsProductUpdate.setSecondCategoryId(-1L);
        if (productIds.isEmpty()) {
            return null;
        }
        int update = this.baseMapper.update(cpsProductUpdate, new QueryWrapper<CpsProduct>().lambda()
                .in(CpsProduct::getId, productIds));
        return update > 0 ? products.get(0) : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLazadaCategory(CpsProductCategorySaveVO saveVO) {
        CpsProduct cpsProduct = this.baseMapper.selectById(saveVO.getProductId());
        if (null == cpsProduct) {
            throw new CustomException("无此商品");
        }
        List<CountryAll> countries = this.countryAllMapper.selectList(new QueryWrapper<CountryAll>().lambda()
                .eq(CountryAll::getCountryAlias, cpsProduct.getProductLanguage()));
        Map<String, CountryAll> countryAllMap = countries.stream()
                .collect(Collectors.toMap(CountryAll::getCountryAlias, Function.identity()));
        if (null != countryAllMap.get(cpsProduct.getProductLanguage())) {
            Long countryId = countryAllMap.get(cpsProduct.getProductLanguage()).getCountryId();

            CpsProductCategory cat1 = this.cpsProductCategoryMapper.selectOne(new QueryWrapper<CpsProductCategory>()
                    .lambda().eq(CpsProductCategory::getProjectId, cpsProduct.getProjectId())
                    .eq(CpsProductCategory::getCategoryLevel, 1)
                    .eq(CpsProductCategory::getParentId, 0)
                    .eq(CpsProductCategory::getCategoryCountry, countryId)
                    .eq(CpsProductCategory::getCategoryDisplayName, saveVO.getFirstCategory())
            );
            CpsProductCategory cat2, cat3, cat4;

            Random random = new Random();
            if (null == cat1) {
                cat1 = new CpsProductCategory();
                cat1.setProjectId(cpsProduct.getProjectId());
                cat1.setParentId(0L);
                cat1.setCategoryId(System.currentTimeMillis() + random.nextInt(10000));
                cat1.setCategoryDisplayName(saveVO.getFirstCategory());
                cat1.setCategoryLevel(1);
                cat1.setCategoryCountry(countryId);
                this.cpsProductCategoryMapper.insert(cat1);
                // 新插入节点，清空下级，防止关系错乱
                cat2 = null;
            } else {
                cat2 = this.cpsProductCategoryMapper.selectOne(new QueryWrapper<CpsProductCategory>()
                        .lambda().eq(CpsProductCategory::getProjectId, cpsProduct.getProjectId())
                        .eq(CpsProductCategory::getCategoryLevel, 2)
                        .eq(CpsProductCategory::getParentId, cat1.getId())
                        .eq(CpsProductCategory::getCategoryCountry, countryId)
                        .eq(CpsProductCategory::getCategoryDisplayName, saveVO.getSecondCategory())
                );
            }
            if (null == cat2) {
                cat2 = new CpsProductCategory();
                cat2.setProjectId(cpsProduct.getProjectId());
                cat2.setParentId(cat1.getId());
                cat2.setCategoryId(System.currentTimeMillis() + random.nextInt(10000));
                cat2.setCategoryDisplayName(saveVO.getSecondCategory());
                cat2.setCategoryLevel(2);
                cat2.setCategoryCountry(countryId);
                this.cpsProductCategoryMapper.insert(cat2);
                cat3 = null;
            } else {
                cat3 = this.cpsProductCategoryMapper.selectOne(new QueryWrapper<CpsProductCategory>()
                        .lambda().eq(CpsProductCategory::getProjectId, cpsProduct.getProjectId())
                        .eq(CpsProductCategory::getCategoryLevel, 3)
                        .eq(CpsProductCategory::getParentId, cat2.getId())
                        .eq(CpsProductCategory::getCategoryCountry, countryId)
                        .eq(CpsProductCategory::getCategoryDisplayName, saveVO.getThirdCategory())
                );
            }
            if (null == cat3) {
                cat3 = new CpsProductCategory();
                cat3.setProjectId(cpsProduct.getProjectId());
                cat3.setParentId(cat2.getId());
                cat3.setCategoryId(System.currentTimeMillis() + random.nextInt(10000));
                cat3.setCategoryDisplayName(saveVO.getThirdCategory());
                cat3.setCategoryLevel(3);
                cat3.setCategoryCountry(countryId);
                this.cpsProductCategoryMapper.insert(cat3);
                cat4 = null;
            } else {
                cat4 = this.cpsProductCategoryMapper.selectOne(new QueryWrapper<CpsProductCategory>()
                        .lambda().eq(CpsProductCategory::getProjectId, cpsProduct.getProjectId())
                        .eq(CpsProductCategory::getCategoryLevel, 4)
                        .eq(CpsProductCategory::getParentId, cat3.getId())
                        .eq(CpsProductCategory::getCategoryCountry, countryId)
                        .eq(CpsProductCategory::getCategoryDisplayName, saveVO.getFourthCategory())
                );
            }
            if (null == cat4) {
                cat4 = new CpsProductCategory();
                cat4.setProjectId(cpsProduct.getProjectId());
                cat4.setParentId(cat3.getId());
                cat4.setCategoryId(System.currentTimeMillis() + random.nextInt(10000));
                cat4.setCategoryDisplayName(saveVO.getFourthCategory());
                cat4.setCategoryLevel(4);
                cat4.setCategoryCountry(countryId);
                this.cpsProductCategoryMapper.insert(cat4);
            }
            this.cpsProductCategoryMapper.updateLazadaCategory();
            cpsProduct.setFirstCategoryId(cat1.getId());
            cpsProduct.setSecondCategoryId(cat2.getId());
            cpsProduct.setThirdCategoryId(cat3.getId());
            cpsProduct.setFourthCategoryId(cat4.getId());
            this.baseMapper.updateById(cpsProduct);
        }
    }

    /**
     * 根据项目ID和分类ID获取分类信息map
     *
     * @param projectId   项目ID
     * @param categoryIds 分类id集合
     * @return 分类id和分类名称map
     */
    private Map<Long, String> getProductCategory(Long projectId, List<Long> categoryIds) {
        if (categoryIds.isEmpty()) {
            return new HashMap<>();
        }
        List<CpsProductCategory> categories = this.cpsProductCategoryMapper.selectList(
                new QueryWrapper<CpsProductCategory>().lambda()
                        .eq(ObjectUtils.isNotNullOrZero(projectId), CpsProductCategory::getProjectId, projectId)
                        .in(CpsProductCategory::getCategoryId, categoryIds));
        return dealCategoryName(projectId.intValue(), categories).stream().collect(Collectors.toMap(
                CpsProductCategory::getCategoryId,
                CpsProductCategory::getCategoryDisplayName
                , (o, v) -> v));
    }

    private Map<Long, List<CpsProductMaterial>> getProductMaterials(List<Long> productIds) {
        if (!productIds.isEmpty()) {
            List<CpsProductMaterial> productMaterials = this.cpsProductMaterialMapper.selectList(
                    new QueryWrapper<CpsProductMaterial>().lambda().in(CpsProductMaterial::getProductId, productIds));
            productMaterials.forEach(cpsProductMaterial -> cpsProductMaterial.setMaterialPath(
                    UploadUtils.getHttpUrl(cpsProductMaterial.getMaterialPath()))
            );
            return productMaterials.stream().collect(Collectors.groupingBy(CpsProductMaterial::getProductId));
        } else {
            return new HashMap<>();
        }
    }

    /**
     * 获取维度字段
     *
     * @param dimensions 维度数据
     * @return 字段
     */
    private String getDimensionType(List<String> dimensions) {
        for (String dim : List.of("creativeUnitId", "plan", "campaign")) {
            if (dimensions.contains(dim)) {
                if (dim.endsWith("Id")) {
                    return dim;
                }
                return String.format("%sId", dim);
            }
        }
        return dimensions.get(0);
    }

    /**
     * 处理分类名称
     *
     * @param projectId  项目ID
     * @param categories 分类数据
     */
    private List<CpsProductCategory> dealCategoryName(Integer projectId, List<CpsProductCategory> categories) {
        Map<Integer, String> country = this.categoryMap(projectId, "name");
        Map<Integer, String> valMap = this.categoryMap(projectId, "val");
        return categories.stream().peek(cpsProductCategory -> {
            String name = cpsProductCategory.getCategoryDisplayName();
            if (country.containsKey(cpsProductCategory.getCategoryCountry())) {
                name += "-" + country.get(cpsProductCategory.getCategoryCountry());
            }
            cpsProductCategory.setCategoryDisplayName(name);
            String categoryId = cpsProductCategory.getCategoryId().toString();
            if (valMap.containsKey(cpsProductCategory.getCategoryCountry())) {
                categoryId = valMap.get(cpsProductCategory.getCategoryCountry()) + "-" + categoryId;
            }
            cpsProductCategory.setCategoryDisplayId(categoryId);
        }).collect(Collectors.toList());
    }

    /**
     * 返回数据
     *
     * @param projectId 项目ID
     * @param type      返回类型
     * @return 分类
     */
    private Map<Integer, String> categoryMap(Integer projectId, String type) {
        Map<Integer, List<Map<String, String>>> map = new HashMap<>() {{
            put(CpsProjectEnum.AMAZON.getId(), new ArrayList<>() {{
                add(Map.of("name", "英国", "val", "EN", "id", "1"));
                add(Map.of("name", "德国", "val", "DE", "id", "2"));
            }});
            put(CpsProjectEnum.LAZADA.getId(), new ArrayList<>() {{
                add(Map.of("name", "越南", "val", "VN", "id", "1"));
                add(Map.of("name", "泰国", "val", "TH", "id", "2"));
                add(Map.of("name", "菲律宾", "val", "PH", "id", "3"));
                add(Map.of("name", "印度尼西亚", "val", "ID", "id", "4"));
            }});
        }};
        if (map.containsKey(projectId)) {
            return map.get(projectId).stream().collect(Collectors.toMap(u -> Integer.parseInt(u.get("id")), v -> v.get(type)));
        }
        return Map.of();
    }

}
