package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.enums.CpsProjectEnum;
import com.overseas.common.utils.DateUtils;
import com.overseas.service.market.dto.cps.CpsDailyRoiDTO;
import com.overseas.service.market.entity.cps.CpsRecycleData;
import com.overseas.service.market.mapper.cps.CpsRecycleDataMapper;
import com.overseas.service.market.service.CpsRecycleDataService;
import com.overseas.service.market.vo.cps.CpsDailyRecycleListVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class CpsRecycleDataServiceImpl extends ServiceImpl<CpsRecycleDataMapper, CpsRecycleData>
        implements CpsRecycleDataService {
    @Override
    public void saveCpsRecycleData() {
        Date date = DateUtils.formatHour(new Date(), -3);
        QueryWrapper<CpsRecycleData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id", CpsProjectEnum.AE.getId())
                .gt("update_time", date)
                .orderByAsc("id");
        // 更新最近三小时内有更新的订单信息到recycle表
        this.baseMapper.saveCpsRecycleData(queryWrapper);

        // 格式化数据
        this.baseMapper.setRecycleBidHour();
        this.baseMapper.setCampaignId();

        // 设置session gmv类型
        this.baseMapper.setCpsRecycleSessionGmvTime(queryWrapper);
        this.baseMapper.setCpsRecycleSessionGmvType1();
        this.baseMapper.setCpsRecycleSessionGmvType2();
    }
}


