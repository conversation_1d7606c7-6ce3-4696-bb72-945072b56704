package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.overseas.common.dto.chart.LegendDTO;
import com.overseas.common.dto.chart.MultiIndexChartDTO;
import com.overseas.common.dto.chart.SeriesDTO;
import com.overseas.common.dto.common.BaseExcelDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.TimeZoneEnum;
import com.overseas.common.enums.market.cps.recycle.RecycleTypeEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.DateUtils;
import com.overseas.common.utils.ExcelUtils;
import com.overseas.common.vo.market.cps.recycle.CpsRecycleChartVO;
import com.overseas.common.vo.market.cps.recycle.CpsRecycleListVO;
import com.overseas.service.market.dto.cps.recycle.CpsRecycleBarDTO;
import com.overseas.service.market.dto.cps.recycle.CpsRecycleListDTO;
import com.overseas.service.market.entity.Campaign;
import com.overseas.service.market.entity.Plan;
import com.overseas.service.market.entity.cps.CpsRecycleData;
import com.overseas.service.market.feign.FgReportService;
import com.overseas.service.market.mapper.CampaignMapper;
import com.overseas.service.market.mapper.PlanMapper;
import com.overseas.service.market.mapper.cps.CpsRecycleDataMapper;
import com.overseas.service.market.service.CpsRecycleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class CpsRecycleServiceImpl implements CpsRecycleService {

    private final CpsRecycleDataMapper cpsRecycleDataMapper;

    private final FgReportService fgReportService;

    private final PlanMapper planMapper;

    private final CampaignMapper campaignMapper;

    @Override
    public CpsRecycleBarDTO bar(CpsRecycleListVO listVO) {
        //获取 sum cost data
        Map<String, BigDecimal> costMap = this.cost(listVO);
        final BigDecimal[] all = {BigDecimal.ZERO};
        costMap.values().forEach(v -> all[0] = all[0].add(v));
        costMap.put("0", all[0]);
        //获取sum recycle data
        listVO.setGroupBy("all");
        Map<String, List<CpsRecycleListDTO>> list = this.list(listVO, false);
        if (list.isEmpty()) {
            list.put("0", List.of(new CpsRecycleListDTO()));
        }
        list.forEach((key, val) ->
                val.forEach(v -> {
                    v.setCost(costMap.getOrDefault("0", BigDecimal.ZERO));
                    if (!BigDecimal.ZERO.equals(v.getCost().stripTrailingZeros())) {
                        v.setRoi(v.getActualCommission().divide(v.getCost(), 3, RoundingMode.HALF_UP));
                    }
                })
        );
        return list.values().stream().findFirst().orElse(List.of()).stream().findFirst().orElse(null);
    }

    @Override
    public MultiIndexChartDTO chart(CpsRecycleChartVO chartVO) {
        Map<String, List<CpsRecycleListDTO>> list = this.completeDataMap(chartVO);
        RecycleTypeEnum recycleTypeEnum = ICommonEnum.get(chartVO.getRecycleType(), RecycleTypeEnum.class);
        MultiIndexChartDTO multiIndexChartDTO = new MultiIndexChartDTO();
        multiIndexChartDTO.setXAxis(List.of(new LegendDTO(this.intervalDay(chartVO))));
        Map<String, String> legendNameMap = this.getInfoName(chartVO, new ArrayList<>(list.keySet()));
        list.forEach((key, val) -> {
            List<Object> data = new ArrayList<>();
            switch (recycleTypeEnum) {
                case DAY:
                    Map<Integer, JSONObject> dayMap = val.stream().collect(Collectors.toMap(CpsRecycleListDTO::getIntervalDay,
                            v -> JSONObject.parseObject(JSONObject.toJSONString(v))));
                    for (int i = 1; i <= Math.min(chartVO.getRecycleNum(), 100); i++) {
                        data.add(dayMap.containsKey(i) ? dayMap.get(i).getBigDecimal(chartVO.getRecycleField())
                                : (i == 1 ? BigDecimal.ZERO : data.get(i - 2))
                        );
                    }
                    break;
                case HOUR:
                    Map<Integer, JSONObject> hourMap = val.stream().collect(Collectors.toMap(CpsRecycleListDTO::getIntervalHour,
                            v -> JSONObject.parseObject(JSONObject.toJSONString(v))));
                    for (int i = 1; i <= Math.min(chartVO.getRecycleNum(), 24); i++) {
                        data.add(hourMap.containsKey(i) ? hourMap.get(i).getBigDecimal(chartVO.getRecycleField())
                                : (i == 1 ? BigDecimal.ZERO : data.get(i - 2))
                        );
                    }
                    break;
                default:
            }
            multiIndexChartDTO.getSeries().add(new SeriesDTO(
                    legendNameMap.getOrDefault(key, defaultName(chartVO.getGroupBy())), "line", data));
        });
        multiIndexChartDTO.getSeries().sort(Comparator.comparing(SeriesDTO::getName));
        multiIndexChartDTO.setLegend(new LegendDTO(list.keySet().stream().map(k -> legendNameMap.getOrDefault(k, defaultName(chartVO.getGroupBy()))).collect(Collectors.toList())));
        return multiIndexChartDTO;
    }

    @Override
    public void download(CpsRecycleChartVO chartVO, HttpServletResponse response) throws IOException {
        Map<String, List<CpsRecycleListDTO>> list = this.completeDataMap(chartVO);
        Map<String, String> legendNameMap = this.getInfoName(chartVO, new ArrayList<>(list.keySet()));
        List<BaseExcelDTO> excels = new ArrayList<>();
        list.forEach((key, val) -> {
            BaseExcelDTO baseExcelDTO = new BaseExcelDTO();
            baseExcelDTO.setAClass(CpsRecycleListDTO.class);
            for (int i = 0; i < val.size(); i++) {
                if (null != val.get(i).getRecycleDate()) {
                    val.get(i).setRecycleDateStr(DateUtils.long2String(val.get(i).getRecycleDate().longValue(),
                            DateUtils.DATE_PATTERN));
                }
                if (i != 0) {
                    val.get(i).setCost(BigDecimal.ZERO);
                }
            }
            baseExcelDTO.setData(val);
            baseExcelDTO.setSheetName(legendNameMap.getOrDefault(key, defaultName(chartVO.getGroupBy())));
            excels.add(baseExcelDTO);
        });
        excels.sort(Comparator.comparing(BaseExcelDTO::getSheetName));
        RecycleTypeEnum recycleTypeEnum = ICommonEnum.get(chartVO.getRecycleType(), RecycleTypeEnum.class);
        ExcelUtils.responseWithSheet(response, excels, "回收报表",
                RecycleTypeEnum.HOUR.equals(recycleTypeEnum)
                        ? List.of("intervalDay", "recycleDateStr")
                        : List.of("intervalHour")
        );
    }

    /**
     * 筛选数据
     *
     * @param listVO 筛选条件
     * @return 返回数据
     */
    private Map<String, List<CpsRecycleListDTO>> list(CpsRecycleListVO listVO, boolean isChart) {
        TimeZoneEnum timeZoneEnum = TimeZoneEnum.get(listVO.getTimeZone());
        if (null == timeZoneEnum) {
            throw new CustomException("时区不合法");
        }
        RecycleTypeEnum recycleTypeEnum = ICommonEnum.get(listVO.getRecycleType(), RecycleTypeEnum.class);
        LambdaQueryWrapper<CpsRecycleData> queryWrapper = new QueryWrapper<CpsRecycleData>()
                .select("UNIX_TIMESTAMP(FROM_UNIXTIME( ( bid_hour + " + timeZoneEnum.getFormatHour() * 3600 + "), '%Y-%m-%d' )) AS bid_time_date, " +
                        "FROM_UNIXTIME( ( bid_hour + " + timeZoneEnum.getFormatHour() * 3600 + "), '%H' ) AS bid_time_hour, " +
                        "UNIX_TIMESTAMP(FROM_UNIXTIME( ( recycle_hour + " + timeZoneEnum.getFormatHour() * 3600 + "), '%Y-%m-%d ' )) AS recycle_time_date, " +
                        "FROM_UNIXTIME( ( recycle_hour + " + timeZoneEnum.getFormatHour() * 3600 + "), '%H' ) AS recycle_time_hour, " +
                        "ROUND(( UNIX_TIMESTAMP( FROM_UNIXTIME( recycle_hour + " + timeZoneEnum.getFormatHour() * 3600 + ", '%Y-%m-%d' )) -" +
                        " UNIX_TIMESTAMP( FROM_UNIXTIME( bid_hour + " + timeZoneEnum.getFormatHour() * 3600 + ", '%Y-%m-%d' ) ))/ 86400, 0 ) + 1 AS interval_time_day," +
                        "ROUND(( recycle_hour - bid_hour) / 3600 , 0 ) + 1 AS interval_time_hour," +
                        "campaign_id, plan_id, " +
                        "`estimate_amount`, " +
                        "`estimate_commission`," +
                        "IF(order_status = 4, 0, `estimate_amount`) AS actual_amount, " +
                        "IF(order_status = 4, 0,`estimate_commission`) AS actual_commission, " +
                        // 取指定类型gmv数据
                        "IF(session_gmv_type = 2, 0,`estimate_amount`) AS session_gmv, " +
                        "1 as order_count ")
                .lambda()
                .ne(CpsRecycleData::getBidDate , 0)
                .in(CpsRecycleData::getMasterId, listVO.getMasterIds())
                .in(CollectionUtils.isNotEmpty(listVO.getCampaignIds()), CpsRecycleData::getCampaignId, listVO.getCampaignIds())
                .in(CollectionUtils.isNotEmpty(listVO.getPlanIds()), CpsRecycleData::getPlanId, listVO.getPlanIds())
                .between(CpsRecycleData::getBidHour,
                        DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(DateUtils.string2Date(listVO.getStartDate()), listVO.getTimeZone(), 0)),
                        DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(DateUtils.string2Date(listVO.getEndDate()), listVO.getTimeZone(), 23))
                );
        String lastSql = RecycleTypeEnum.DAY.equals(recycleTypeEnum) ? String.format(" WHERE t.interval_time_day <= %s", listVO.getRecycleNum())
                : String.format(" WHERE t.interval_time_day = 1 AND t.interval_time_hour <= %s %s ", listVO.getRecycleNum(),
                CollectionUtils.isNotEmpty(listVO.getHours()) ?
                        String.format(" AND t.bid_time_hour IN (%s)", listVO.getHours().stream().map(String::valueOf).collect(Collectors.joining(","))) : ""
        );
        List<CpsRecycleListDTO> list;
        if (isChart) {
            switch (recycleTypeEnum) {
                case DAY:
                    lastSql = String.format(" %s group by t.interval_time_day, t.bid_time_date ", lastSql);
                    break;
                case HOUR:
                    lastSql = String.format(" %s group by t.interval_time_hour, t.bid_time_hour ", lastSql);
                    break;
                default:
            }
            if (!"all".equals(listVO.getGroupBy())) {
                lastSql = String.format("%s , t.%s", lastSql, this.groupBy(listVO.getGroupBy()));
            }
            list = cpsRecycleDataMapper.list(queryWrapper, timeZoneEnum.getFormatHour() * 3600, lastSql);
        } else {
            CpsRecycleListDTO total = cpsRecycleDataMapper.total(queryWrapper, timeZoneEnum.getFormatHour() * 3600, lastSql);
            if (null == total) {
                return new HashMap<>();
            }
            list = new ArrayList<>() {{
                add(total);
            }};
        }
        if (list.size() == 1 && null == list.get(0)) {
            return new HashMap<>();
        }
        return list.stream().filter(u -> {
            if (CollectionUtils.isEmpty(listVO.getHours())) {
                return true;
            }
            return listVO.getHours().contains(u.getBidHour());
        }).collect(Collectors.groupingBy(u -> {
            String key = "";
            switch (recycleTypeEnum) {
                case DAY:
                    key = key + u.getBidDate();
                    break;
                case HOUR:
                    key = key + u.getBidHour();
                    break;
                default:
                    return "0";
            }
            switch (listVO.getGroupBy()) {
                case "campaignId":
                    return key + "_" + u.getCampaignId();
                case "planId":
                    return key + "_" + u.getPlanId();
                case "all":
                default:
                    return key;
            }
        }));
    }

    /**
     * 查询消耗数据
     *
     * @param listVO 筛选条件
     * @return 返回数据
     */
    private Map<String, BigDecimal> cost(CpsRecycleListVO listVO) {
        return fgReportService.recycleCost(listVO).getData();
    }


    /**
     * 完善数据
     *
     * @param chartVO 条件
     * @return 返回数据
     */
    private Map<String, List<CpsRecycleListDTO>> completeDataMap(CpsRecycleChartVO chartVO) {
        Map<String, BigDecimal> costMap = this.cost(chartVO);
        Map<String, List<CpsRecycleListDTO>> list = this.list(chartVO, true);
        Set<String> keySet = new HashSet<>() {{
            addAll(new ArrayList<>(costMap.keySet()));
        }};
        Map<String, List<CpsRecycleListDTO>> newList = new HashMap<>();
        RecycleTypeEnum recycleTypeEnum = ICommonEnum.get(chartVO.getRecycleType(), RecycleTypeEnum.class);
        keySet.forEach(key -> {
            List<CpsRecycleListDTO> val = list.getOrDefault(key, new ArrayList<>());
            Date firstDay = null;
            Map<Integer, CpsRecycleListDTO> dataMap;
            switch (recycleTypeEnum) {
                case DAY:
                    dataMap = val.stream().collect(Collectors.toMap(CpsRecycleListDTO::getIntervalDay, Function.identity()));
                    firstDay = DateUtils.long2Date(Long.parseLong(key.split("_")[0]));
                    break;
                case HOUR:
                    dataMap = val.stream().collect(Collectors.toMap(CpsRecycleListDTO::getIntervalHour, Function.identity()));
                    break;
                default:
                    dataMap = new HashMap<>();
            }

            for (int i = 1; i <= chartVO.getRecycleNum(); i++) {
                CpsRecycleListDTO v, old;
                if (dataMap.containsKey(i)) {
                    v = dataMap.get(i);
                    old = dataMap.getOrDefault(i - 1, null);
                } else {
                    v = new CpsRecycleListDTO();
                    old = dataMap.getOrDefault(i - 1, null);
                    switch (recycleTypeEnum) {
                        case DAY:
                            v.setIntervalDay(i);
                            v.setRecycleDate(DateUtils.date2Long(DateUtils.afterDay(firstDay, i - 1)).intValue());
                            break;
                        case HOUR:
                            v.setIntervalHour(i);
                            break;
                        default:
                    }
                }
                v.setSumActualAmount(v.getActualAmount().add(null == old ? BigDecimal.ZERO : old.getSumActualAmount()));
                v.setSumActualCommission(v.getActualCommission().add(null == old ? BigDecimal.ZERO : old.getSumActualCommission()));
                v.setSumEstimateAmount(v.getEstimateAmount().add(null == old ? BigDecimal.ZERO : old.getSumEstimateAmount()));
                v.setSumEstimateCommission(v.getEstimateCommission().add(null == old ? BigDecimal.ZERO : old.getSumEstimateCommission()));
                v.setCost(costMap.getOrDefault(key, BigDecimal.ZERO));
                if (!BigDecimal.ZERO.equals(v.getCost().stripTrailingZeros())) {
                    v.setRoi(v.getSumActualCommission().divide(v.getCost(), 3, RoundingMode.HALF_UP));
                } else {
                    v.setRoi(BigDecimal.ZERO);
                }
                dataMap.put(i, v);
            }
            newList.put(key, new ArrayList<>(dataMap.values()));
        });
        return newList;
    }

    /**
     * 获取 group by 字段
     *
     * @param groupBy group by 字段
     * @return 返回数据
     */
    private String groupBy(String groupBy) {
        switch (groupBy) {
            case "planId":
                return "plan_id";
            case "campaignId":
                return "campaign_id";
            case "all":
            default:
                return "";
        }
    }

    /**
     * 获取数据
     *
     * @param listVO 条件
     * @return 返回数据
     */
    private List<String> intervalDay(CpsRecycleListVO listVO) {
        List<String> days = new ArrayList<>();
        if ("hour".equals(listVO.getRecycleType())) {
            for (int i = 1; i <= Math.min(listVO.getRecycleNum(), 24); i++) {
                days.add(String.format("%s小时", i));
            }
        } else {
            for (int i = 1; i <= Math.min(listVO.getRecycleNum(), 100); i++) {
                days.add(String.format("%s天", i));
            }
        }
        return days;
    }

    /**
     * 获取 info Name
     *
     * @param listVO 列表
     * @param legend 返回数据
     * @return 获取信息结果
     */
    private Map<String, String> getInfoName(CpsRecycleListVO listVO, List<String> legend) {
        if (legend.isEmpty()) {
            return Map.of();
        }
        List<String> ids = new ArrayList<>();
        if (legend.get(0).contains("_")) {
            legend.forEach(le -> {
                ids.add(le.split("_")[1]);
            });
        }
        RecycleTypeEnum recycleTypeEnum = ICommonEnum.get(listVO.getRecycleType(), RecycleTypeEnum.class);
        Map<String, String> nameMap;
        switch (listVO.getGroupBy()) {
            case "planId":
                nameMap = planMapper.selectBatchIds(ids).stream().collect(Collectors.toMap(k -> k.getId().toString(), Plan::getPlanName));
                return legend.stream().collect(Collectors.toMap(k -> k, le -> {
                    String[] les = le.split("_");
                    return dayName(les[0], recycleTypeEnum) + " " + nameMap.getOrDefault(les[1], defaultName(listVO.getGroupBy()));
                }));
            case "campaignId":
                nameMap = campaignMapper.selectBatchIds(ids).stream().collect(Collectors.toMap(k -> k.getId().toString(), Campaign::getCampaignName));
                return legend.stream().collect(Collectors.toMap(k -> k, le -> {
                    String[] les = le.split("_");
                    return dayName(les[0], recycleTypeEnum) + " " + nameMap.getOrDefault(les[1], defaultName(listVO.getGroupBy()));
                }));
            case "all":
                return legend.stream().collect(Collectors.toMap(k -> k, v -> dayName(v, recycleTypeEnum)));
            default:
                return Map.of();
        }
    }

    /**
     * 时间名称
     *
     * @param v               时间内容
     * @param recycleTypeEnum 时间类型
     * @return 返回数据
     */
    private String dayName(String v, RecycleTypeEnum recycleTypeEnum) {
        switch (recycleTypeEnum) {
            case DAY:
                return DateUtils.long2String(Long.parseLong(v));
            case HOUR:
                return String.format("%s点", v);
            default:
                return "";
        }
    }

    /**
     * 获取数据
     *
     * @param groupBy 分组
     * @return 返回数据
     */
    private String defaultName(String groupBy) {
        return "all".equals(groupBy) ? "全部" : "未知";
    }
}
