package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.common.BaseExcelDTO;
import com.overseas.common.dto.market.cps.amazon.CpsAmazonReportDTO;
import com.overseas.common.dto.market.cps.lazada.CpsLazadaExcelDTO;
import com.overseas.common.enums.*;
import com.overseas.common.enums.market.cps.CpsSyncStatusEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.*;
import com.overseas.common.vo.market.cps.*;
import com.overseas.service.market.dto.cps.order.*;
import com.overseas.service.market.dto.lazada.LazadaOppoAdDTO;
import com.overseas.service.market.entity.CountryAll;
import com.overseas.service.market.entity.CpsAmazonCate;
import com.overseas.service.market.entity.CpsOrder;
import com.overseas.service.market.entity.User;
import com.overseas.service.market.entity.ae.AeChannelPlace;
import com.overseas.service.market.entity.cps.CpsProductCategory;
import com.overseas.service.market.mapper.*;
import com.overseas.service.market.mapper.ae.AeChannelPlaceMapper;
import com.overseas.service.market.mapper.cps.CpsProductCategoryMapper;
import com.overseas.service.market.service.CpsAmazonOrderService;
import com.overseas.service.market.service.CpsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CpsServiceImpl implements CpsService {

    private final CpsOrderMapper cpsOrderMapper;

    private final CpsAmazonCateMapper cpsAmazonCateMapper;

    private final CpsProductCategoryMapper cpsProductCategoryMapper;

    private final Long PROJECT_ID = 26L;

    private final CountryAllMapper countryAllMapper;

    private final CpsOrderLazadaMapper cpsOrderLazadaMapper;

    private final CpsAmazonOrderService cpsAmazonOrderService;

    private final AeChannelPlaceMapper aeChannelPlaceMapper;

    private final ChannelInfoMapper channelInfoMapper;

    @Override
    public void saveCps(CpsSaveVO saveVO, Integer userId) {
        switch (saveVO.getIdentify()) {
            case "lazada_cps":
                this.saveCpsLazada(saveVO, userId);
                break;
            default:
                this.saveCpsAmazon(saveVO, userId);
        }
    }

    @Override
    public void saveCpsAmazon(CpsSaveVO saveVO, Integer userId) {
        cpsAmazonOrderService.saveOrderInfo(saveVO, userId);
        CompletableFuture.runAsync(() -> {
            cpsAmazonOrderService.mateOrderAndEarning();
            cpsAmazonOrderService.transformCpsOrder();
        });
    }

    @Override
    public void saveCpsLazada(CpsSaveVO saveVO, Integer userId) {
        String filePath = UploadUtils.getUploadPath(saveVO.getFilePath());
        // 1.拉取excel中数据
        List<CpsLazadaExcelDTO> cpsLazadaExcels = ExcelUtils.read(filePath,
                1, "Lazada Report", CpsLazadaExcelDTO.class);
        if (cpsLazadaExcels.isEmpty()) {
            throw new CustomException("excel内容为空，请确认后再试");
        }
        if (cpsLazadaExcels.stream().anyMatch(u -> StringUtils.isBlank(u.getOrderId()) || StringUtils.isBlank(u.getSubOrderId()))) {
            throw new CustomException("excel内容中order id 或者sub order id 含有为空行，请确认后再试");
        }
        if (ObjectUtils.isNotNullOrZero(saveVO.getAppId())) {
            saveVO.setAppId(0);
        }
        // 2. 数据入库
        int batchNo = 1000;
        for (int i = 0; i < Math.ceil((double) cpsLazadaExcels.size() / batchNo); i++) {
            this.cpsOrderLazadaMapper.insertByUk(cpsLazadaExcels.subList(i * batchNo, Math.min((i + 1) * batchNo, cpsLazadaExcels.size())),
                    saveVO.getAppId());
        }
    }

    @Override
    public PageUtils<?> listCps(CpsListVO listVO) {

        if (listVO.getIdentify().equals("lazada_cps")) {
            return this.listCpsLazadaList(listVO);
        }
        TimeZoneEnum timeZoneEnum = TimeZoneEnum.UTC_8;
        if (StringUtils.isBlank(listVO.getSortField())) {
            listVO.setSortField("orderTime");
        }
        if (listVO.getDimensions().isEmpty()) {
            listVO.setDimensions(new ArrayList<>() {{
                add("orderTime");
            }});
        }
        IPage<CpsOrderListDTO> pageData = new Page<>(listVO.getPage(), listVO.getPageNum());
        QueryWrapper<CpsOrder> cpsOrderQueryWrapper = new QueryWrapper<CpsOrder>()
                .eq("co.master_id", listVO.getMasterId())
                .in(CollectionUtils.isNotEmpty(listVO.getCampaignIds()),
                        "plan.campaign_id", listVO.getCampaignIds())
                .in(CollectionUtils.isNotEmpty(listVO.getPlanIds()),
                        "plan.id", listVO.getPlanIds())
                .in(CollectionUtils.isNotEmpty(listVO.getOrderStatus()),
                        "co.order_status", listVO.getOrderStatus())
                .and(StringUtils.isNotBlank(listVO.getSearch()), q -> q
                        .like("co.product_id", listVO.getSearch())
                        .or().like("plan.id", listVO.getSearch())
                        .or().like("plan.plan_name", listVO.getSearch())
                        .or().eq("co.tracking_id", listVO.getSearch())
                        .or().like(StringUtils.isNotBlank(listVO.getSearch()),
                                "co.ifly_uid", listVO.getSearch())
                );
        if (ObjectUtils.isNotNullOrZero(listVO.getCountryId())) {
            CountryAll countryAll = countryAllMapper.selectOne(new LambdaQueryWrapper<CountryAll>()
                    .eq(CountryAll::getCountryId, listVO.getCountryId())
                    .last("limit 1")
            );
            if (null == countryAll) {
                return new PageUtils<>(pageData);
            }
            cpsOrderQueryWrapper.eq("co.country", countryAll.getCountryAlias());
        }
        if (StringUtils.isNotBlank(listVO.getStartDate())) {
            cpsOrderQueryWrapper.between("co.order_time", DateUtils.string2Long(listVO.getStartDate()),
                    DateUtils.string2Long(listVO.getEndDate() + " 23:59:59"));
        }
        pageData = this.cpsOrderMapper.listCpsOrder(pageData, cpsOrderQueryWrapper,
                new QueryWrapper<CpsOrder>()
                        .groupBy(listVO.getDimensions().stream().map(this::transformWeiDu).collect(Collectors.toList()))
                        .orderBy(true, "asc".equalsIgnoreCase(listVO.getSortType()), transformWeiDu(listVO.getSortField())),
                timeZoneEnum.getFormatHour()
        );
        //为空返回
        if (pageData.getTotal() == 0) {
            return new PageUtils<>(pageData);
        }
        CpsOrderListDTO total = this.cpsOrderMapper.totalCpsOrder(cpsOrderQueryWrapper, timeZoneEnum.getFormatHour());
        Map<String, String> countryMap = new HashMap<>();
        Map<Long, String> categoryMap = new HashMap<>();
        if (!pageData.getRecords().isEmpty()) {
            switch (listVO.getIdentify()) {
                case "amazon_cps":
                    categoryMap = cpsAmazonCateMapper.selectList(new LambdaQueryWrapper<>()).stream()
                            .collect(Collectors.toMap(CpsAmazonCate::getId, CpsAmazonCate::getCateName));
                    break;
                default:
                    categoryMap = cpsProductCategoryMapper.selectList(new LambdaQueryWrapper<CpsProductCategory>()
                            .eq(CpsProductCategory::getProjectId, 25)
                            .eq(CpsProductCategory::getIsDel, IsDelEnum.NORMAL.getId())
                    ).stream().collect(Collectors.toMap(
                            CpsProductCategory::getCategoryId, CpsProductCategory::getCategoryName
                    ));
                    break;
            }
            countryMap = countryAllMapper.selectList(new LambdaQueryWrapper<CountryAll>()
                    .in(CountryAll::getCountryAlias, pageData.getRecords().stream().map(CpsOrder::getCountry).distinct().collect(Collectors.toList()))
            ).stream().collect(Collectors.toMap(CountryAll::getCountryAlias, CountryAll::getCountryName, (o, n) -> n));
        }
        for (CpsOrderListDTO record : pageData.getRecords()) {
            record.setReportDayName(DateUtils.long2String(record.getReportDay()));
            record.setOrderTimeName(transformDate(record.getOrderTime(), timeZoneEnum));
            record.setFinishTimeName(transformDate(record.getFinishedTime(), timeZoneEnum));
            record.setPayTimeName(transformDate(record.getPayTime(), timeZoneEnum));
            record.setSettlementTimeName(transformDate(record.getSettlementTime(), timeZoneEnum));
            record.setIsSyncName(ICommonEnum.getNameById(record.getIsSync(), CpsSyncStatusEnum.class));
            record.setCategoryName(categoryMap.getOrDefault(record.getCategory(), ConstantUtils.PLACEHOLDER));
            record.setCountryName(countryMap.getOrDefault(record.getCountry(), ConstantUtils.PLACEHOLDER));
        }
        List<CpsOrderListDTO> result = new ArrayList<>();
        if (null == total) {
            total = new CpsOrderListDTO();
            total.setNewBuyerCommission(BigDecimal.ZERO);
            total.setActualAmount(BigDecimal.ZERO);
            total.setActualCommission(BigDecimal.ZERO);
            total.setEstimateAmount(BigDecimal.ZERO);
            total.setEstimateCommission(BigDecimal.ZERO);
            total.setOrderId(ConstantUtils.PLACEHOLDER);
            total.setCountryName(ConstantUtils.PLACEHOLDER);
        }
        result.add(transformTotal(total));
        result.addAll(pageData.getRecords());
        return new PageUtils<>(result, pageData.getTotal());
    }

    @Override
    public void exportCps(CpsListExportVO listExportVO, HttpServletResponse response) throws IOException {
        listExportVO.setPage(1L);
        listExportVO.setPageNum(1000000L);
        PageUtils<?> pageUtils = this.listCps(listExportVO);
        ExcelUtils.download(response, "CPS回传订单", "汇总", pageUtils.getData(), listExportVO.getFields());
    }

    @Override
    public void syncDataToPlanHour() {
        // 获取未同步的数据
        log.info("cps amazon insert schedule start");
        List<CpsAmazonReportDTO> cpsAmazons = this.cpsOrderMapper.listCpsAmazonReportData(
                new QueryWrapper<CpsOrder>()
                        .eq("amazon.is_sync", CpsSyncStatusEnum.NOT_SYNC.getId())
                        .ne("amazon.plan_id", 0)
                        .eq("amazon.project_id", PROJECT_ID)
        );
        if (cpsAmazons.isEmpty()) {
            log.info("schedule end, no data to insert");
            return;
        }
        log.info("start insert cps data");
        Map<String, CpsAmazonReportDTO> cpsAmazonMap = new HashMap<>();
        //如果是4的话处理数据
        cpsAmazons.forEach(u -> {
            if (u.getOrderStatus().equals(4)) {
                u.setOrderCountOff(u.getOrderCount());
                u.setAmountOff(u.getAmount());
                u.setCommissionOff(u.getCommission());
                u.setOrderCount(0L);
                u.setAmount(0L);
                u.setCommission(0L);
            } else {
                u.setOrderCountOff(0L);
                u.setAmountOff(0L);
                u.setCommissionOff(0L);
            }
            String key = String.format("%s-%s", u.getReportHour(), u.getPlanId());
            if (!cpsAmazonMap.containsKey(key)) {
                cpsAmazonMap.put(key, u);
            } else {
                CpsAmazonReportDTO map = cpsAmazonMap.get(key);
                if (u.getOrderStatus().equals(4)) {
                    map.setOrderCountOff(u.getOrderCountOff() + map.getOrderCountOff());
                    map.setAmountOff(u.getAmountOff() + map.getAmountOff());
                    map.setCommissionOff(u.getCommissionOff() + map.getCommissionOff());
                } else {
                    map.setOrderCount(u.getOrderCount() + map.getOrderCount());
                    map.setAmount(u.getAmount() + map.getAmount());
                    map.setCommission(u.getCommission() + map.getCommission());
                }
            }
        });
        // 执行同步入库
        this.cpsOrderMapper.batchSaveCpsAmazonToPlanHour(new ArrayList<>(cpsAmazonMap.values()));
        // 将是否同步字段置为已同步
        CpsOrder cpsOrder = new CpsOrder();
        cpsOrder.setIsSync(CpsSyncStatusEnum.SYNC.getId());
        // 更新
        this.cpsOrderMapper.update(cpsOrder, new QueryWrapper<CpsOrder>().lambda()
                .eq(CpsOrder::getProjectId, PROJECT_ID)
                .ne(CpsOrder::getPlanId, 0)
                .eq(CpsOrder::getIsSync, CpsSyncStatusEnum.NOT_SYNC.getId())
        );
    }

    @Override
    public List<SelectDTO> aeChannelPlace() {
        return aeChannelPlaceMapper.selectList(new LambdaQueryWrapper<AeChannelPlace>()
                .ne(AeChannelPlace::getChannelName, "")
        ).stream().map(u -> new SelectDTO(u.getId(), u.getChannelName())).collect(Collectors.toList());
    }

    @Override
    public PageUtils<?> aeChannelCps(CpsAeChannelListVO listVO, User user) {
        String timestamp = "IF( pay_time < ********** , 57600, 54000)";
        IPage<CpsAeChannelOrderListDTO> iPage = new Page<>(listVO.getPage(), listVO.getPageNum());
        QueryWrapper<?> queryWrapper = aeChannelCpsQueryWrapper(listVO, user);
        if (null == queryWrapper) {
            return new PageUtils<>(iPage);
        }
        iPage = this.cpsOrderMapper.pageAeChannel(iPage, queryWrapper,
                new QueryWrapper<>()
                        .groupBy("orderTime")
                        .groupBy("sid")
                        .groupBy("cn")
                        .groupBy(isInner(user), "country")
                        .orderByDesc("orderTime")
                        .orderByDesc("sid")
                        .orderByDesc("cn")
                        .orderByDesc(isInner(user), "country"),
                timestamp,
                aeChannelSelect(user),
                aeChannelSelect2(user),
                getChannelRatio(user.getId(), listVO.getChannelPlaceId(), timestamp)
        );
        if (isInner(user) && !iPage.getRecords().isEmpty()) {
            Map<String, String> countryMap = countryAllMapper.selectList(new LambdaQueryWrapper<CountryAll>()
                            .ne(CountryAll::getCountryAlias, ""))
                    .stream().collect(Collectors.toMap(CountryAll::getCountryAlias, CountryAll::getCountryName));
            iPage.getRecords().forEach(u -> u.setCountryName(countryMap.getOrDefault(u.getCountry(), ConstantUtils.PLACEHOLDER)));
            //增加总计
            if (iPage.getTotal() != 0) {
                CpsAeChannelOrderListDTO total = this.cpsOrderMapper.totalAeChannel(
                        this.aeChannelCpsSumQueryWrapper(listVO, user),
                        timestamp,
                        aeChannelSelect(user),
                        aeChannelSelect2(user),
                        getChannelRatio(user.getId(), listVO.getChannelPlaceId(), timestamp)
                );
                total.setOrderTime("汇总");
                total.setSid(ConstantUtils.PLACEHOLDER);
                total.setCn(ConstantUtils.PLACEHOLDER);
                total.setCountryName(ConstantUtils.PLACEHOLDER);
                return new PageUtils<>(iPage, total);
            }
        }
        return new PageUtils<>(iPage);
    }

    @Override
    public void exportAeChannelCps(CpsAeChannelListVO listVO, User user, HttpServletResponse response) throws IOException {
        String timestamp = "IF(pay_time < 1748761200 , 57600, 54000)";
        QueryWrapper<?> queryWrapper = aeChannelCpsQueryWrapper(listVO, user);
        List<CpsAeChannelOrderListDTO> all = new ArrayList<>(), list = new ArrayList<>();
        if (null != queryWrapper) {
            list = this.cpsOrderMapper.listAeChannel(queryWrapper,
                    new QueryWrapper<>()
                            .groupBy("orderTime")
                            .groupBy("sid")
                            .groupBy("cn")
                            .groupBy(isInner(user), "country")
                            .orderByDesc("orderTime")
                            .orderByDesc("sid")
                            .orderByDesc("cn")
                            .orderByDesc(isInner(user), "country"),
                    timestamp,
                    aeChannelSelect(user),
                    aeChannelSelect2(user),
                    getChannelRatio(user.getId(), listVO.getChannelPlaceId(), timestamp)
            );
        }
        if (isInner(user) && !list.isEmpty()) {
            Map<String, String> countryMap = countryAllMapper.selectList(new LambdaQueryWrapper<CountryAll>()
                            .ne(CountryAll::getCountryAlias, ""))
                    .stream().collect(Collectors.toMap(CountryAll::getCountryAlias, CountryAll::getCountryName));
            list.forEach(u -> u.setCountryName(countryMap.getOrDefault(u.getCountry(), ConstantUtils.PLACEHOLDER)));
            CpsAeChannelOrderListDTO total = this.cpsOrderMapper.totalAeChannel(
                    this.aeChannelCpsSumQueryWrapper(listVO, user),
                    timestamp, aeChannelSelect(user),
                    aeChannelSelect2(user),
                    getChannelRatio(user.getId(), listVO.getChannelPlaceId(), timestamp)
            );
            total.setOrderTime("汇总");
            total.setSid(ConstantUtils.PLACEHOLDER);
            total.setCn(ConstantUtils.PLACEHOLDER);
            total.setCountryName(ConstantUtils.PLACEHOLDER);
            all.add(total);
            all.addAll(list);
        } else {
            all = list;
        }

        ExcelUtils.downloadBuilder(response, "GrowoneAE-CPS", CpsAeChannelOrderListDTO.class)
                .excludeColumnFieldNames(excludeAeChannelSelect(user))
                .doWrite(all);
    }

    @Override
    public void exportAeChannelCps2(CpsAeChannelListVO listVO, User user, HttpServletResponse response) throws IOException {
        String timestamp = "IF( pay_time < ********** , 57600, 54000)";
        List<String> times = new ArrayList<>() {{
            add("2024-11-01_2024-11-30");
            add("2024-12-01_2024-12-31");
            add("2025-01-01_2025-01-31");
            add("2025-02-01_2025-02-28");
            add("2025-03-01_2025-03-31");
            add("2025-04-01_2025-04-30");
        }};
        Map<String, List<CpsAeChannelOrderList2DTO>> result = new HashMap<>();
        times.forEach(time -> {
            String[] timeArr = time.split("_");
            listVO.setStartDate(timeArr[0]);
            listVO.setEndDate(timeArr[1]);
            Map<String, List<CpsAeChannelOrderList2DTO>> list = exportAeChannelCps2Detail(listVO, user, timestamp);
            result.putAll(list);
        });
        AtomicInteger atomicInteger = new AtomicInteger(0);
        List<BaseExcelDTO> list = new ArrayList<>();
        result.forEach((name, val) -> {
            BaseExcelDTO baseExcelDTO = new BaseExcelDTO();
            baseExcelDTO.setSheetKey(atomicInteger.getAndIncrement());
            baseExcelDTO.setData(val);
            baseExcelDTO.setAClass(CpsAeChannelOrderList2DTO.class);
            baseExcelDTO.setSheetName(name);
            list.add(baseExcelDTO);
        });
        ExcelUtils.downloadWithSheet("/Users/<USER>/Downloads/uploads/lazada_cps/汇总结算.xlsx", list, new ArrayList<>());
    }

    /**
     * 获取 map
     *
     * @param userId         id
     * @param channelPlaceId 条件
     * @return 返回数据
     */
    private String getChannelRatio(Integer userId, Long channelPlaceId, String timestamp) {
        Map<Integer, String> map = new HashMap<>() {{
            put(20034, "IF( pay_time - %s > 1739721600 , 0.85, 0.9)");
            put(20043, "IF( pay_time - %s < 1748761200, 0.85, 0.9)");
        }};
        if (null == map.get(userId)) {
            Map<Long, Integer> channelMap = new HashMap<>() {{
                put(1L, 20034);
                put(8L, 20043);
            }};
            userId = ObjectUtils.isNullOrZero(channelPlaceId) ? 20034 : channelMap.getOrDefault(channelPlaceId, 0);
        }
        return String.format(map.getOrDefault(userId, "0.85"), timestamp);
    }

    private Map<String, List<CpsAeChannelOrderList2DTO>> exportAeChannelCps2Detail(CpsAeChannelListVO listVO, User user,
                                                                                   String timestamp) {
        QueryWrapper<?> queryWrapper = aeChannelCpsSumQueryWrapper(listVO, user);
        List<CpsAeChannelOrderList2DTO> list = new ArrayList<>();
        Map<String, CpsAeChannelOrderList2DTO> allMap = new HashMap<>();
        if (null != queryWrapper) {
            list = this.cpsOrderMapper.listAeChannelAll(queryWrapper,
                    new QueryWrapper<>()
                            .groupBy("orderTime")
                            .groupBy("sid")
                            .groupBy("cn")
                            .groupBy("order_status")
                            .groupBy("settlementTime")
                            .orderByAsc("order_status")
                            .orderByDesc("orderTime")
                            .orderByDesc("settlementTime")
                            .orderByDesc("sid")
                            .orderByDesc("cn"),
                    timestamp,
                    getChannelRatio(user.getId(), listVO.getChannelPlaceId(), timestamp));

            allMap = this.allMap(listVO, user);
        }
        Map<Integer, String> nameMap = this.nameMap();
        Map<String, BigDecimal> completePaymentMap = new HashMap<>();
        Map<String, BigDecimal> estimatedPaymentMap = new HashMap<>();
        Map<String, Long> orderQuantityMap = new HashMap<>();
        list.forEach(d -> {
            if (StringUtils.isNotBlank(d.getOrderTime()) && !d.getOrderTime().contains("月")) {
                d.setOrderTime(String.format("%s月", Integer.parseInt(d.getOrderTime())));
            }
            d.setOrderStatusName(nameMap.getOrDefault(d.getOrderStatus(), ConstantUtils.PLACEHOLDER));
            if (d.getOrderStatus().equals(4)) {
                d.setSettlementTime("");
                return;
            }
            String key = String.format("%s--%s", d.getCn(), d.getSid());
            BigDecimal c = completePaymentMap.computeIfAbsent(key, k -> BigDecimal.ZERO);
            completePaymentMap.put(key, c.add(d.getCompletedPayments()));
            BigDecimal e = estimatedPaymentMap.computeIfAbsent(key, k -> BigDecimal.ZERO);
            estimatedPaymentMap.put(key, e.add(d.getEstimatedPayments()));
            Long count = orderQuantityMap.computeIfAbsent(key, k -> 0L);
            orderQuantityMap.put(key, count + d.getOrderQuantity());
        });

        for (CpsAeChannelOrderList2DTO d : list) {
            if (!d.getOrderStatus().equals(4)) {
                continue;
            }
            String key = String.format("%s--%s", d.getCn(), d.getSid());
            CpsAeChannelOrderList2DTO all = allMap.get(key);
            if (null == all) {
                continue;
            }
            BigDecimal c = all.getCompletedPayments().subtract(completePaymentMap.getOrDefault(key, BigDecimal.ZERO));
            BigDecimal e = all.getEstimatedPayments().subtract(estimatedPaymentMap.getOrDefault(key, BigDecimal.ZERO));
            Long orderQuantity = all.getOrderQuantity() - orderQuantityMap.getOrDefault(key, 0L);
            allMap.remove(key);
            d.setCompletedPayments(c);
            d.setEstimatedPayments(e);
            d.setOrderQuantity(orderQuantity);
        }
        String name = list.get(0).getOrderTime();
        for (String k : allMap.keySet()) {
            String[] ar = k.split("--");
            CpsAeChannelOrderList2DTO data = new CpsAeChannelOrderList2DTO();
            data.setCn(ar[0]);
            data.setSid(ar[1]);
            data.setOrderStatus(4);
            data.setOrderStatusName("Invalid");
            data.setOrderTime(name);
            data.setSettlementTime("");
            CpsAeChannelOrderList2DTO all = allMap.get(k);
            BigDecimal c = all.getCompletedPayments().subtract(completePaymentMap.getOrDefault(k, BigDecimal.ZERO));
            BigDecimal e = all.getEstimatedPayments().subtract(estimatedPaymentMap.getOrDefault(k, BigDecimal.ZERO));
            long orderQuantity = all.getOrderQuantity() - orderQuantityMap.getOrDefault(k, 0L);
            data.setCompletedPayments(c);
            data.setEstimatedPayments(e);
            if (orderQuantity == 0) {
                orderQuantity = 1L;
            }
            data.setOrderQuantity(orderQuantity);
            list.add(data);
        }
        list = list.stream().sorted((o, n) -> {
            if (o.getOrderStatus().equals(n.getOrderStatus())) {
                if (o.getOrderTime().equals(n.getOrderTime())) {
                    if (o.getSid().equals(n.getSid())) {
                        return o.getCn().compareTo(n.getCn());
                    }
                    return o.getSid().compareTo(n.getSid());
                }
                return o.getOrderTime().compareTo(n.getOrderTime());
            }
            return n.getOrderStatus().compareTo(o.getOrderStatus());
        }).collect(Collectors.toList());
        return Map.of(name, list);
    }

    @Override
    public void lazadaImportAd(CpsLazadaImportAdVO importAdVO, Integer userId) {
        List<LazadaOppoAdDTO> result = ExcelUtils.read(importAdVO.getFilePath(), LazadaOppoAdDTO.class);
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(result)) {
            this.channelInfoMapper.batchInsertAd(result, userId);
        }
    }

    @Override
    public void aeXiaoMiExport(String day) throws IOException {
        if (StringUtils.isBlank(day)) {
            return;
        }
        Date date = DateUtils.string2Date(day);
        List<CpsAeXiaoMiListDTO> today = cpsOrderMapper.aeXiaoMiList(new QueryWrapper<>()
                .eq("project_id", 25)
                .in("ifly_uid", "xfxmcps641")
                .ne("order_status", 4)
                .groupBy("ifly_uid")
                .groupBy("cn")
                .groupBy("orderTime")
                .orderByAsc("orderTime")
                .orderByAsc("ifly_uid")
                .orderByAsc("cn")
                .between("pay_time",
                        DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(date, TimeZoneEnum.UTC_0.getId(), 0)),
                        DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(date, TimeZoneEnum.UTC_0.getId(), 24))
                ));
        List<CpsAeXiaoMiListDTO> all = cpsOrderMapper.aeXiaoMiList(new QueryWrapper<>()
                .eq("project_id", 25)
                .in("ifly_uid", "xfxmcps641")
                .ne("order_status", 4)
                .groupBy("ifly_uid")
                .groupBy("cn")
                .groupBy("orderTime")
                .orderByAsc("orderTime")
                .orderByAsc("ifly_uid")
                .orderByAsc("cn")
                .between("pay_time",
                        DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(DateUtils.string2Date("2025-05-01"), TimeZoneEnum.UTC_0.getId(), 0)),
                        DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(date, TimeZoneEnum.UTC_0.getId(), 24))
                ));
        List<BaseExcelDTO> list = new ArrayList<>();
        list.add(new BaseExcelDTO(0, "当日", today, CpsAeXiaoMiListDTO.class));
        list.add(new BaseExcelDTO(1, "汇总", all, CpsAeXiaoMiListDTO.class));
        String path = UploadUtils.getUploadPath("/ae_cps/" + day + ".xlsx");
        ExcelUtils.downloadWithSheet(path, list, new ArrayList<>());
        MailUtils.sendHtmlEmailMultiple(List.of("<EMAIL>", "<EMAIL>", "<EMAIL>"),
                "AE_CPS_小米渠道数据_" + day, "如上，内容见附件</br>",
                List.of(path)
        );
    }

    @Override
    public void exportAeByDayAndMail(CpsExportByDayVO byDayVO, HttpServletResponse response) throws IOException {
        this.exportAeByDay(byDayVO.getStartDate(), byDayVO.getEndDate(), byDayVO.getChannelPlaceId(), response);
    }

    /**
     * 所有数据
     *
     * @param listVO 条件
     * @param user   用户
     * @return 返回数据
     */
    private Map<String, CpsAeChannelOrderList2DTO> allMap(CpsAeChannelListVO listVO, User user) {
        listVO.setPage(1L);
        listVO.setPageNum(10000000L);
        Map<String, CpsAeChannelOrderList2DTO> allMap = new HashMap<>();
        List<CpsAeChannelOrderListDTO> list = (List<CpsAeChannelOrderListDTO>) this.aeChannelCps(listVO, user).getData();
        if (list.isEmpty()) {
            return allMap;
        }
        list = list.subList(1, list.size());
        list.forEach(data -> {
            String key = String.format("%s--%s", data.getCn(), data.getSid());
            CpsAeChannelOrderList2DTO all = allMap.computeIfAbsent(key, k -> {
                CpsAeChannelOrderList2DTO one = new CpsAeChannelOrderList2DTO();
                one.setCn(data.getCn());
                one.setSid(data.getSid());
                one.setCompletedPayments(BigDecimal.ZERO);
                one.setEstimatedPayments(BigDecimal.ZERO);
                one.setOrderQuantity(0L);
                one.setRealCompletedPayments(BigDecimal.ZERO);
                one.setRealEstimatedPayments(BigDecimal.ZERO);
                return one;
            });
            try {
                all.setCompletedPayments(all.getCompletedPayments().add(data.getCompletedPayments()));
                all.setEstimatedPayments(all.getEstimatedPayments().add(data.getEstimatedPayments()));
                all.setOrderQuantity(all.getOrderQuantity() + data.getOrderQuantity());
                all.setRealCompletedPayments(all.getRealCompletedPayments().add(null == data.getRealCompletedPayments() ? BigDecimal.ZERO : data.getRealCompletedPayments()));
                all.setRealCompletedPayments(all.getRealEstimatedPayments().add(null == data.getRealEstimatedPayments() ? BigDecimal.ZERO : data.getRealEstimatedPayments()));
            } catch (Exception e) {
                log.info(e.getMessage(), e);
            }
        });
        return allMap;
    }

    /**
     * 状态名称
     *
     * @return 状态名称map
     */
    private Map<Integer, String> nameMap() {
        return new HashMap<>() {{
            put(1, "Completed Payments");
            put(2, "Completed Orders");
            put(3, "Completed settlement");
            put(4, "Invalid");
        }};
    }

    /**
     * 返回数据
     *
     * @param user 用户
     * @return 数据
     */
    private String aeChannelSelect(User user) {
        if (isInner(user)) {
            return ",`estimate_amount` as `real_completed_payments`, " +
                    "`estimate_commission` as `real_estimated_payments`," +
                    "country";
        }
        return "";
    }

    /**
     * 返回数据
     *
     * @param user 用户
     * @return 数据
     */
    private String aeChannelSelect2(User user) {
        if (isInner(user)) {
            return ",ROUND(SUM(`real_completed_payments`),2) as `real_completed_payments`, " +
                    "ROUND(SUM(`real_estimated_payments`),2) as `real_estimated_payments`," +
                    "country";
        }
        return "";
    }


    /**
     * 返回数据
     *
     * @param user 用户
     * @return 数据
     */
    private List<String> excludeAeChannelSelect(User user) {
        if (isInner(user)) {
            return List.of();
        }
        return List.of("realCompletedPayments", "realEstimatedPayments", "country");
    }

    /**
     * 是否内部用户
     *
     * @param user 用户
     * @return 返回数据
     */
    private boolean isInner(User user) {
        return SecurityUserUtils.isInner(user.getRoleId());
    }

    /**
     * 获取渠道数据
     *
     * @param listVO 条件
     * @return 返回数据
     */
    private QueryWrapper<?> aeChannelCpsQueryWrapper(CpsAeChannelListVO listVO, User user) {
        QueryWrapper<?> queryWrapper = aeChannelCpsSumQueryWrapper(listVO, user);
        if (null == queryWrapper) {
            return null;
        }
        return queryWrapper;
    }

    /**
     * 获取总数数据
     *
     * @param listVO 列表
     * @param user   用户
     * @return 返回数据
     */
    private QueryWrapper<?> aeChannelCpsSumQueryWrapper(CpsAeChannelListVO listVO, User user) {
        AeChannelPlace aeChannelPlace = aeChannelPlaceMapper.selectOne(new LambdaQueryWrapper<AeChannelPlace>()
                .eq(AeChannelPlace::getUserId, user.getId())
                .eq(AeChannelPlace::getIsDel, IsDelEnum.NORMAL.getId())
        );
        if (null == aeChannelPlace) {
            return null;
        }
        List<String> places = JSONObject.parseArray(aeChannelPlace.getChannelPlace(), String.class);
        if (CollectionUtils.isEmpty(places)) {
            return null;
        }
        //查询 渠道place id的内容
        List<String> places2 = null;
        if (ObjectUtils.isNotNullOrZero(listVO.getChannelPlaceId())) {
            AeChannelPlace channelPlace = aeChannelPlaceMapper.selectOne(new LambdaQueryWrapper<AeChannelPlace>()
                    .eq(AeChannelPlace::getId, listVO.getChannelPlaceId())
                    .eq(AeChannelPlace::getIsDel, IsDelEnum.NORMAL.getId())
            );
            if (null == channelPlace) {
                return null;
            }
            places2 = JSONObject.parseArray(channelPlace.getChannelPlace(), String.class);
            if (CollectionUtils.isEmpty(places2)) {
                return null;
            }
        }
        String countryAlias = null;
        if (ObjectUtils.isNotNullOrZero(listVO.getCountryId())) {
            CountryAll countryAll = countryAllMapper.selectOne(new LambdaQueryWrapper<CountryAll>()
                    .eq(CountryAll::getCountryId, listVO.getCountryId())
                    .last("limit 1")
            );
            if (null == countryAll) {
                return null;
            }
            countryAlias = countryAll.getCountryAlias();
        }
        //"cf", "cf001", "cf002", "cf003", "cf004" 根据条件展示内容
        return new QueryWrapper<>()
                .eq("project_id", CpsProjectEnum.AE.getId())
                .between("pay_time",
                        DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(DateUtils.string2Date(listVO.getStartDate()),
                                listVO.getStartDate().compareTo("2025-06-01") >= 0 ? TimeZoneEnum.UTC_17.getId() : TimeZoneEnum.UTC_16.getId(), 0)),
                        DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(DateUtils.string2Date(listVO.getEndDate() + " 23:59:59"),
                                listVO.getEndDate().compareTo("2025-05-31") >= 0 ? TimeZoneEnum.UTC_17.getId() : TimeZoneEnum.UTC_16.getId(), 0))
                ).in("ifly_uid", places)
                .in(CollectionUtils.isNotEmpty(listVO.getOrderStatus()) && !listVO.getOrderStatus().contains(-1), "order_status", listVO.getOrderStatus())
                .eq(StringUtils.isNotBlank(countryAlias), "country", countryAlias)
                .in(CollectionUtils.isNotEmpty(places2), "co.ifly_uid", places2)
                .like(StringUtils.isNotBlank(listVO.getSearch()), "ifly_uid", listVO.getSearch())
                .like(StringUtils.isNotBlank(listVO.getCn()), "bid_id", listVO.getCn());
    }

    /**
     * lazada list
     *
     * @param listVO 条件
     * @return 返回数据
     */
    private PageUtils<?> listCpsLazadaList(CpsListVO listVO) {
        IPage<CpsOrderLazadaListDTO> iPage = new Page<>();
        if (StringUtils.isBlank(listVO.getSortField())) {
            listVO.setSortField("conversionTime");
        }
        if (listVO.getDimensions().isEmpty()) {
            listVO.setDimensions(new ArrayList<>() {{
                add("conversionTime");
            }});
        }
        QueryWrapper<?> queryWrapper = new QueryWrapper<>()
                .in(CollectionUtils.isNotEmpty(listVO.getCampaignIds()), "plan.campaign_id", listVO.getCampaignIds())
                .in(CollectionUtils.isNotEmpty(listVO.getPlanIds()), "plan.id", listVO.getPlanIds())
                .in(CollectionUtils.isNotEmpty(listVO.getOrderStatus()), "co.order_status", listVO.getOrderStatus())
                .and(StringUtils.isNotBlank(listVO.getSearch()), q -> q
                        .or().like("plan.id", listVO.getSearch())
                        .or().like("plan.plan_name", listVO.getSearch())
                );
        if (ObjectUtils.isNotNullOrZero(listVO.getCountryId())) {
            CountryAll countryAll = countryAllMapper.selectOne(new LambdaQueryWrapper<CountryAll>()
                    .eq(CountryAll::getCountryId, listVO.getCountryId()));
            if (null == countryAll) {
                return new PageUtils<>(iPage);
            }
            queryWrapper.eq("col.country", countryAll.getCountryAlias());
        }
        if (StringUtils.isNotBlank(listVO.getStartDate())) {
            queryWrapper.between("col.conversion_time", listVO.getStartDate(),
                    listVO.getEndDate());
        }
        iPage = this.cpsOrderLazadaMapper.listCps(iPage, queryWrapper
                .groupBy(listVO.getDimensions().stream().map(this::transformWeiDu).distinct().collect(Collectors.toList()))
                .orderBy(true, "asc".equalsIgnoreCase(listVO.getSortType()), transformWeiDu(listVO.getSortField()))
        );
        if (iPage.getTotal() == 0) {
            return new PageUtils<>(iPage);
        }
        Map<String, String> countryMap = new HashMap<>();
        if (!iPage.getRecords().isEmpty()) {
            countryMap = countryAllMapper.selectList(new LambdaQueryWrapper<CountryAll>()
                    .in(CountryAll::getCountryAlias, iPage.getRecords().stream().map(CpsOrderLazadaListDTO::getCountry).distinct().collect(Collectors.toList()))
            ).stream().collect(Collectors.toMap(CountryAll::getCountryAlias, CountryAll::getCountryName, (o, n) -> n));
        }
        for (CpsOrderLazadaListDTO u : iPage.getRecords()) {
            u.setIsReportName(1 == u.getIsReport() ? "已上报" : "未上报");
            u.setNewUserName(1 == u.getNewUser() ? "新用户" : "非新用户");
            u.setCountryName(countryMap.get(u.getCountry()));
        }
        return new PageUtils<>(iPage);
    }


    /**
     * 根据名称获取 cate rate
     *
     * @param cateName 分类名称
     * @param cateMap  cate map
     * @return 返回rate
     */
    private CpsAmazonCate findCpsCateRate(String cateName, Map<String, CpsAmazonCate> cateMap) {
        CpsAmazonCate cate = cateMap.get(cateName);
        if (null != cate) {
            return cate;
        }
        for (String name : cateName.split(" & ")) {
            cate = cateMap.get(name);
            if (null != cate) {
                return cate;
            }
        }
        return null;
    }

    private String transformWeiDu(String val) {
        if (List.of("reportDayName", "orderTimeName", "finishTineName", "categoryName", "planIdName",
                        "campaignIdName", "countryName", "brandIdName", "sellerIdName", "skuName", "offerIdName", "newUserName")
                .contains(val)) {
            return HumpLineUtils.humpToLine2(val.replace("Name", ""));
        }
        return HumpLineUtils.humpToLine2(val);
    }

    /**
     * 转换时间
     *
     * @param date         日期
     * @param timeZoneEnum 时区
     * @return 返回数据
     */
    private String transformDate(Long date, TimeZoneEnum timeZoneEnum) {
        if (0L == date) {
            return ConstantUtils.PLACEHOLDER;
        }
        return DateUtils.format(TimeZoneEnum.getTimeZoneDate(DateUtils.long2Date(date), timeZoneEnum.getId(), 0), DateUtils.DATE_TIME_PATTERN);
    }

    private CpsOrderListDTO transformTotal(CpsOrderListDTO data) {
        data.setCategoryName(ConstantUtils.PLACEHOLDER);
        data.setPlanIdName(ConstantUtils.PLACEHOLDER);
        data.setCampaignIdName(ConstantUtils.PLACEHOLDER);
        data.setReportDayName(ConstantUtils.PLACEHOLDER);
        data.setTrackingId(ConstantUtils.PLACEHOLDER);
        data.setOrderTimeName(ConstantUtils.PLACEHOLDER);
        data.setPayTimeName(ConstantUtils.PLACEHOLDER);
        data.setProductId(ConstantUtils.PLACEHOLDER);
        return data;
    }

    @Override
    public void exportAeByDay(String startDate, String endDate, Long channelPlaceId, HttpServletResponse response) throws IOException {
        // 获取AE应用枚举
        AeChannelPlace aeChannelPlace = this.aeChannelPlaceMapper.selectById(channelPlaceId);
        if (null == channelPlaceId) {
            return;
        }
        List<String> channelPlaceList = JSONObject.parseArray(aeChannelPlace.getChannelPlace(), String.class);
        // 获取按天统计的广告数据
        Map<String, List<CpsAeByDayDTO>> byDayMap = this.cpsOrderMapper.listAeByDay(new QueryWrapper<>()
                .eq("tcr.channel_id", 5)
                .eq("tcr.channel_account_id", 240)
                .ne("tci.country", "")
                .between("tcr.dim_day", DateUtils.string2Long(startDate), DateUtils.string2Long(endDate))
                .groupBy("tcr.dim_day").groupBy("tci.country")
        ).stream().collect(Collectors.groupingBy(CpsAeByDayDTO::getCountry));
        User user = new User();
        user.setRoleId(3);
        user.setId(3);
        String timestamp = "IF( pay_time < ********** , 57600, 54000)";
        String ratio = "0.85";
        // 获取订单佣金数据
        Map<String, CpsAeChannelOrderListDTO> cpsOrderAeMap = this.cpsOrderMapper.listAeChannel2(
                new QueryWrapper<>()
                        .eq("project_id", CpsProjectEnum.AE.getId())
                        .in("ifly_uid", channelPlaceList)
                        .between("pay_time",
                                DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(DateUtils.string2Date(startDate),
                                        startDate.compareTo("2025-06-01") >= 0 ? TimeZoneEnum.UTC_17.getId() : TimeZoneEnum.UTC_16.getId(), 0)),
                                DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(DateUtils.string2Date(endDate + " 23:59:59"),
                                        endDate.compareTo("2025-05-31") >= 0 ? TimeZoneEnum.UTC_17.getId() : TimeZoneEnum.UTC_16.getId(), 0))
                        ),
                new QueryWrapper<>()
                        .groupBy("orderTime")
                        .groupBy("country")
                        .orderByAsc("country")
                        .orderByAsc("orderTime"),
                timestamp, getChannelRatio(user.getId(), channelPlaceId, ratio)
        ).stream().collect(Collectors.toMap(u -> String.format("%s_%s", u.getCountry(), u.getOrderTime()), Function.identity()));
        // excel 数据
        List<String> days = DateUtils.getBetweenDate(startDate, endDate, DateUtils.DATE_PATTERN);

        Map<String, List<CpsAeByDayDTO>> result = new HashMap<>();

        byDayMap.forEach((country, byDays) -> {
            Map<String, CpsAeByDayDTO> byDaysMap = byDays.stream().collect(Collectors.toMap(CpsAeByDayDTO::getDay, Function.identity()));
            List<CpsAeByDayDTO> byDayList = days.stream().map(day -> {
                CpsAeByDayDTO byDay = byDaysMap.get(day);
                if (null == byDay) {
                    byDay = new CpsAeByDayDTO();
                    byDay.setCountry(country);
                    byDay.setDay(day);
                }
                String cpsDayKey = String.format("%s_%s", country, day);
                CpsAeChannelOrderListDTO orderInfo = cpsOrderAeMap.get(cpsDayKey);
                if (null != orderInfo) {
                    byDay.setAmount(orderInfo.getCompletedPayments());
                    byDay.setCommission(orderInfo.getRealEstimatedPayments());
                    byDay.setOrderCount(orderInfo.getOrderQuantity());
                }
                return byDay;
            }).collect(Collectors.toList());
            result.put(country, byDayList);
        });
        List<String> head1 = new ArrayList<>() {{
            add("日期");
            add("日期（营销平台填写）");
            add("合计当日收益");
            add("合计成本消耗\n" +
                    "（oppoCPC）");
            add("合计媒体点击数");
            add("合计媒体下发条数");
        }};
        List<String> head2 = new ArrayList<>() {{
            add("日期");
            add("日期（营销平台填写）");
            add("合计当日收益");
            add("合计成本消耗\n" +
                    "（oppoCPC）");
            add("合计媒体点击数");
            add("合计媒体下发条数");
        }};
        List<String> countryMap = new ArrayList<>(result.keySet());
        countryMap.forEach(country -> {
            for (int i = 0; i < 16; i++) {
                head1.add(country);
            }
            head2.add("当日收入");
            head2.add("push下发数");
            head2.add("Show number");
            head2.add("Click number");
            head2.add("Click Rate");
            head2.add("联盟点击");
            head2.add("点击转化率");
            head2.add("OPPO的CPC价格政策");
            head2.add("OPPO的CPC收入");
            head2.add("回收CPC($)");
            head2.add("订单数");
            head2.add("GMV($)");
            head2.add("点击商品转化率\n" +
                    "（商品/点击）");
            head2.add("客单价\n" +
                    "（GMV/商品数）");
            head2.add("佣金比");
            head2.add("ROI");
        });

        List<List<String>> headers = new ArrayList<>();
        for (int i = 0; i < head1.size(); i++) {
            int finalI = i;
            headers.add(new ArrayList<>() {{
                add(head1.get(finalI));
                add(head2.get(finalI));
            }});
        }

        Map<String, Integer> pushNumMap = new HashMap<>() {{
            put("MX", 3);
        }};

        List<List<String>> excels = days.stream().map(day -> new ArrayList<String>() {{
            add(day);
            add(day);
        }}).collect(Collectors.toList());
        countryMap.forEach(country -> {
            List<CpsAeByDayDTO> byDays = result.get(country);
            Map<String, CpsAeByDayDTO> byDaysMap = byDays.stream().collect(Collectors.toMap(CpsAeByDayDTO::getDay, Function.identity()));
            for (int i = 0; i < days.size(); i++) {
                String day = days.get(i);
                CpsAeByDayDTO byDay = byDaysMap.get(day);
                List<String> excel = excels.get(i);
                BigDecimal amount = byDay.getAmount();
                BigDecimal commission = byDay.getCommission();
                String pushNum = pushNumMap.getOrDefault(country, 3).toString();
                this.excelAdd(excel, fillZero(commission), 2);
                this.excelAdd(excel, fillZero(byDay.getCost()), 3);
                this.excelAdd(excel, fillZeroObject(byDay.getClick()), 4);
                this.excelAdd(excel, pushNum, 5);
                excel.add(fillZero(commission));
                excel.add(pushNum);
                excel.add(fillZeroObject(byDay.getImpress()));
                excel.add(fillZeroObject(byDay.getClick()));
                excel.add(ObjectUtils.getObjectCalculatedResult(byDay.getClick() * 100, byDay.getImpress(), "/") + "%");
                excel.add("");
                excel.add("");
                excel.add("0.025");
                excel.add(ObjectUtils.getObjectCalculatedResult("0.025", byDay.getClick(), "*").toString());
                excel.add(ObjectUtils.getObjectCalculatedResult(commission, byDay.getClick(), "/", 3).toString());
                excel.add(fillZeroObject(byDay.getOrderCount()));
                excel.add(fillZero(amount));
                excel.add(ObjectUtils.getObjectCalculatedResult(byDay.getOrderCount() * 100, byDay.getClick(), "/") + "%");
                excel.add(ObjectUtils.getObjectCalculatedResult(amount, byDay.getOrderCount(), "/").toString());
                excel.add(ObjectUtils.getObjectCalculatedResult(commission.multiply(BigDecimal.valueOf(100)), amount, "/") + "%");
                excel.add(ObjectUtils.getObjectCalculatedResult(commission.multiply(BigDecimal.valueOf(100)), byDay.getCost(), "/", 0) + "%");
            }
        });
        if (null == response) {
            String filePath = UploadUtils.getUploadPath(String.format("/ae_cps/AE_CPS_BY_DAY_%s.xlsx", endDate));
            ExcelUtils.download(filePath, excels, headers, "渠道数据");
        } else {
            ExcelUtils.download(response, String.format("AE_CPS_BY_DAY_%s.xlsx", endDate), excels, headers, "渠道数据");
        }
    }


    private String fillZero(BigDecimal zero) {
        if (null == zero) {
            return "0";
        }
        return zero.toPlainString();
    }

    private String fillZeroObject(Object zero) {
        if (ObjectUtils.isNullOrZero(zero)) {
            return "0";
        }
        return zero.toString();
    }

    private void excelAdd(List<String> list, String data, int index) {
        if (list.size() > index) {
            list.set(index, ObjectUtils.getObjectCalculatedResult(list.get(index), data, "+").toString());
        } else {
            list.add(index, data);
        }
    }

}


