package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.service.market.entity.CreativeLabel;
import com.overseas.service.market.enums.creativeLabel.CreativeLabelTypeEnum;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.vo.market.creativeLabel.CreativeLabelSelectGetVO;
import com.overseas.service.market.mapper.CreativeLabelMapper;
import com.overseas.service.market.service.CreativeLabelService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class CreativeLabelServiceImpl extends ServiceImpl<CreativeLabelMapper, CreativeLabel> implements CreativeLabelService {

    @Override
    public List<SelectDTO> selectCreativeLabel(CreativeLabelSelectGetVO getVO) {

        return this.baseMapper.selectCreativeLabel(new QueryWrapper<CreativeLabel>().lambda()
                .eq(CreativeLabel::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getCreativeLabelType()), CreativeLabel::getCreativeLabelType, getVO.getCreativeLabelType())
                .orderByDesc(CreativeLabel::getId));
    }

    @Override
    public Map<String, List<SelectDTO>> selectCreativeLabelMap(CreativeLabelSelectGetVO getVO) {

        return this.baseMapper.selectList(new QueryWrapper<CreativeLabel>().lambda()
                .eq(CreativeLabel::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getCreativeLabelType()), CreativeLabel::getCreativeLabelType, getVO.getCreativeLabelType())
                .orderByDesc(CreativeLabel::getId)).stream().collect(Collectors.toMap(
                creativeLabel -> ICommonEnum.get(creativeLabel.getCreativeLabelType(), CreativeLabelTypeEnum.class).getField(), creativeLabel -> new ArrayList<SelectDTO>() {{
                    add(new SelectDTO(creativeLabel.getId(), creativeLabel.getCreativeLabelName()));
                }}, (newVal, oldVal) -> {
                    newVal.addAll(oldVal);
                    return newVal;
                }));
    }
}
