package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.market.creativeUnit.CreativeUnitAllDataListDTO;
import com.overseas.common.dto.market.creativeUnit.CreativeUnitMaterialSaveDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.enums.ResultStatusEnum;
import com.overseas.common.enums.market.PlanSlotTypeEnum;
import com.overseas.common.enums.market.PutEnum;
import com.overseas.common.enums.market.copyWriting.CopyWritingTypeEnum;
import com.overseas.common.enums.report.ReportTypeEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.utils.SmsUtils;
import com.overseas.common.vo.market.copyWriting.CopyWritingBatchSaveItemVO;
import com.overseas.common.vo.market.copyWriting.CopyWritingBatchSaveVO;
import com.overseas.common.vo.market.creative.CreativeUpdateAssetWaitCountVO;
import com.overseas.common.vo.market.creative.unit.CreativeUnitBatchSaveVO;
import com.overseas.common.vo.market.material.*;
import com.overseas.common.vo.report.ReportListVO;
import com.overseas.service.market.dto.assetTask.AssetCpsAppIdDTO;
import com.overseas.service.market.dto.creative.AutoAssetUpdateListDTO;
import com.overseas.service.market.dto.creative.CreativeGetDTO;
import com.overseas.service.market.entity.*;
import com.overseas.service.market.entity.assetTask.AssetTask;
import com.overseas.service.market.enums.BatchUpdateEnum;
import com.overseas.common.enums.OsTypeEnum;
import com.overseas.service.market.enums.assets.AssetTypeEnum;
import com.overseas.common.enums.market.campaign.CampaignMarketTargetEnum;
import com.overseas.common.enums.market.campaign.CampaignPutOnTargetEnum;
import com.overseas.service.market.enums.creative.CreatievIsAssetUpdateEnum;
import com.overseas.service.market.enums.creative.CreativeTypeEnum;
import com.overseas.service.market.enums.material.MaterialAssetTypeEnum;
import com.overseas.service.market.enums.plan.PlanModeEnum;
import com.overseas.common.enums.market.plan.PlanStatusEnum;
import com.overseas.service.market.events.creativeUnitAudit.CreativeUnitAuditEvent;
import com.overseas.service.market.mapper.*;
import com.overseas.service.market.mapper.assetTask.AssetTaskMapper;
import com.overseas.service.market.mapper.assetTask.TaskProductAssetMapper;
import com.overseas.service.market.mapper.call.CallNoticeMapper;
import com.overseas.service.market.service.*;
import com.overseas.service.market.vo.creative.AutoAssetUpdateBatchVO;
import com.overseas.service.market.vo.creative.AutoAssetUpdateListVO;
import com.overseas.service.market.vo.creative.CreativeGetVO;
import com.overseas.service.market.vo.creative.CreativeSaveVO;
import com.overseas.service.market.vo.trackingId.TrackingIdChannelPoolCheckVO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class CreativeServiceImpl extends ServiceImpl<CreativeMapper, Creative> implements CreativeService {

    private final PlanService planService;

    private final AssetService assetService;

    private final MaterialService materialService;

    private final CreativeUnitService creativeUnitService;

    private final MasterMapper masterMapper;

    private final CampaignMapper campaignMapper;

    private final PlanMapper planMapper;

    private final PlanDirectMapper planDirectMapper;

    private final RtaStrategyMapper rtaStrategyMapper;

    private final ApplicationContext applicationContext;

    private final CountryAllMapper countryAllMapper;

    private final MonitorEventService monitorEventService;

    private final CtxMapper ctxMapper;

    private final CopyWritingService copyWritingService;

    private final AssetGroupMapper assetGroupMapper;

    private final ResChannelPoolMapper resChannelPoolMapper;

    private final TaskProductAssetMapper taskProductAssetMapper;

    private final SmsUtils smsUtils;

    private final CallNoticeMapper callNoticeMapper;

    private final AssetTaskMapper assetTaskMapper;

    private final TextLibraryMapper textLibraryMapper;

    @Override
    public List<CreativeUnitAllDataListDTO> listCreativeUnitAllData(ReportListVO listVO, List<Integer> masterIds) {

        if (masterIds.isEmpty()) {
            return List.of();
        }

        ReportTypeEnum reportTypeEnum = ReportTypeEnum.getById(listVO.getReportType());

        Map<Integer, List<String>> reportTypeMap = new HashMap<>() {{
            put(ReportTypeEnum.MASTER.getId(), List.of("uu.id", "uu.company_name"));
            put(ReportTypeEnum.CAMPAIGN.getId(), List.of("mcn.id", "mcn.campaign_name"));
            put(ReportTypeEnum.PLAN.getId(), List.of("mp.id", "mp.plan_name"));
            put(ReportTypeEnum.CREATIVE.getId(), List.of("mcu.id", ""));
        }};

        List<Long> optimizeTargets = new ArrayList<>() {{
            if (ObjectUtils.isNotNullOrZero(listVO.getOptimizeTargetId())) {
                {
                    addAll(monitorEventService.listMonitorEventIdsByAction(listVO.getMasterId(), List.of(listVO.getOptimizeTargetId())));
                }
            }
        }};

        QueryWrapper<?> queryWrapper = new QueryWrapper<>()
                // 账号过滤
                .eq(ObjectUtils.isNotNullOrZero(listVO.getMasterId()), "uu.id", listVO.getMasterId())
                .in(ObjectUtils.isNullOrZero(listVO.getMasterId()), "uu.id", masterIds)
                // 账户报表查询
                .eq(ObjectUtils.isNotNullOrZero(listVO.getAgentId()), "uu.parent_id", listVO.getAgentId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getAuditMasterId()), "mm.audit_master_id", listVO.getAuditMasterId())
                // 活动报表查询
                .eq(ObjectUtils.isNotNullOrZero(listVO.getMarketTarget()), "mcn.market_target", listVO.getMarketTarget())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getPutOnTarget()), "mcn.put_on_target", listVO.getPutOnTarget())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getReportMode()), "mcn.campaign_mode", listVO.getReportMode())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getSlotType()), "mp.slot_type", listVO.getSlotType())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getOsType()), "mp.os_type", listVO.getOsType())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getReportMode()), "mp.plan_mode", listVO.getReportMode())
                // 计划报表查询
                .eq(ObjectUtils.isNotNullOrZero(listVO.getCampaignId()), "mcn.id", listVO.getCampaignId())
                .in(CollectionUtils.isNotEmpty(listVO.getCampaignIds()), "mcn.id", listVO.getCampaignIds())
                .in(CollectionUtils.isNotEmpty(optimizeTargets), "mp.optimize_target_id", optimizeTargets)
                //切换至创意维度
                .in(CollectionUtils.isNotEmpty(listVO.getTemplateIds()), "mcu.template_id", listVO.getTemplateIds())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getBidType()), "mp.bid_type", listVO.getBidType())
                .in(CollectionUtils.isNotEmpty(listVO.getRtaStrategyIds()), "JSON_UNQUOTE(JSON_EXTRACT(direct.direct_value, '$.rtaStrategyId'))", listVO.getRtaStrategyIds())
                // 创意报表查询
                .eq(ObjectUtils.isNotNullOrZero(listVO.getPlanId()), "mp.id", listVO.getPlanId())
                .in(CollectionUtils.isNotEmpty(listVO.getPlanIds()), "mp.id", listVO.getPlanIds())
                .and(StringUtils.isNotBlank(listVO.getSearch()) && reportTypeMap.get(listVO.getReportType()) != null, q -> q
                        .or().like(StringUtils.isNotBlank(reportTypeMap.get(listVO.getReportType()).get(0)),
                                reportTypeMap.get(listVO.getReportType()).get(0), listVO.getSearch())
                        .or().like(StringUtils.isNotBlank(reportTypeMap.get(listVO.getReportType()).get(1)),
                                reportTypeMap.get(listVO.getReportType()).get(1), listVO.getSearch()));

        List<CreativeUnitAllDataListDTO> list = new ArrayList<>();
        switch (reportTypeEnum) {
            case MASTER:
                list = this.masterMapper.getMasterDataList(queryWrapper);
                break;
            case CAMPAIGN:
                list = this.campaignMapper.getCampaignDataList(queryWrapper);
                break;
            case TIME:
            case TIME_HOUR:
            case TIME_COMPARE:
            case PLAN:
            case PLAN_GROUP:
                list = CollectionUtils.isNotEmpty(listVO.getRtaStrategyIds())
                        ? this.planMapper.getPlanDataListJoinDirect(queryWrapper)
                        : this.planMapper.getPlanDataList(queryWrapper);
                break;
            case CREATIVE:
                list = this.baseMapper.selectCreativeUnitDataList(queryWrapper);
                if (!list.isEmpty()) {
                    Map<Long, List<MaterialAssetVO>> materialMap = materialService.getMaterialMap(
                            list.stream().map(CreativeUnitAllDataListDTO::getMaterialId).collect(Collectors.toList()),
                            listVO.getMasterId()
                    );
                    list.forEach(u -> {
                        u.setAssets(materialMap.getOrDefault(u.getMaterialId(), List.of()));
                    });
                }
                break;
            case RTA:
                QueryWrapper<?> rtaWrapper = new QueryWrapper<>()
                        .eq(ObjectUtils.isNotNullOrZero(listVO.getMasterId()), "master_id", listVO.getMasterId())
                        .and(StringUtils.isNotBlank(listVO.getSearch()), q -> q
                                .like("id", listVO.getSearch())
                                .or().like("rta_strategy_name", listVO.getSearch()));
                list = this.rtaStrategyMapper.getRtaList(rtaWrapper);
                break;
            case REGION:
                list = this.countryAllMapper.listCountry(new QueryWrapper<>()
                        .and(StringUtils.isNotBlank(listVO.getSearch()), q -> q
                                .like("country_id", listVO.getSearch())
                                .or().like("country_name", listVO.getSearch())));
                break;
            default:
        }

        if (list.isEmpty()) {
            return List.of();
        }
        list.forEach(entity -> {
            entity.setCreativeTypeName(ObjectUtils.isNotNullOrZero(entity.getCreativeType()) ?
                    ICommonEnum.getNameById(entity.getCreativeType(), CreativeTypeEnum.class) : "");
            entity.setOsTypeName(ObjectUtils.isNotNullOrZero(entity.getOsType()) ?
                    ICommonEnum.getNameById(entity.getOsType(), OsTypeEnum.class) : "");
            entity.setSlotTypeName(ObjectUtils.isNotNullOrZero(entity.getSlotType()) ?
                    ICommonEnum.getNameById(entity.getSlotType(), PlanSlotTypeEnum.class) : "");
            entity.setMarketTargetName(ObjectUtils.isNotNullOrZero(entity.getMarketTarget()) ?
                    ICommonEnum.getNameById(entity.getMarketTarget(), CampaignMarketTargetEnum.class) : "");
            entity.setPutOnTargetName(ObjectUtils.isNotNullOrZero(entity.getPutOnTarget()) ?
                    ICommonEnum.getNameById(entity.getPutOnTarget(), CampaignPutOnTargetEnum.class) : "");
        });
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveCreative(CreativeSaveVO creativeSaveVO, Integer userId) {
        // 第一步：进行数据校验
        Plan plan = this.planService.getPlan(creativeSaveVO.getPlanId(), creativeSaveVO.getCampaignId(), creativeSaveVO.getMasterId());
        if (plan.getPlanMode().equals(PlanModeEnum.TEMPLATE.getId())) {
            planMapper.updateIepTemplateDelete(plan.getId());
        }
        boolean isAdd = false;
        Creative creativeSave = new Creative();
        // 第二步：保存创意数据
        if (ObjectUtils.isNullOrZero(creativeSaveVO.getId())) {
            BeanUtils.copyProperties(creativeSaveVO, creativeSave);
            creativeSave.setCreateUid(userId);
            creativeSave.setAssetTaskIds(JSONObject.toJSONString(creativeSaveVO.getAssetTaskIds()));
            creativeSave.setTextLibraryIds(JSONObject.toJSONString(creativeSaveVO.getTextLibraryIds()));
            if (ObjectUtils.isNotNullOrZero(creativeSaveVO.getCreativeMonitorId())) {
                this.assetGroupMapper.insertMonitor(creativeSave.getMasterId(), creativeSaveVO.getCreativeMonitorId(), creativeSaveVO.getPlanId(), userId);
            }
            save(creativeSave);
            isAdd = true;
        } else {
            creativeSave = getCreative(creativeSaveVO.getId(), creativeSaveVO.getMasterId());
            //替换监测
            if (!creativeSave.getCreativeMonitorId().equals(creativeSaveVO.getCreativeMonitorId())) {
                this.assetGroupMapper.delMonitor(creativeSave.getCreativeMonitorId(), creativeSaveVO.getPlanId(), userId);
                if (ObjectUtils.isNotNullOrZero(creativeSaveVO.getCreativeMonitorId())) {
                    this.assetGroupMapper.insertMonitor(creativeSave.getMasterId(), creativeSaveVO.getCreativeMonitorId(), creativeSaveVO.getPlanId(), userId);
                }
            }
            BeanUtils.copyProperties(creativeSaveVO, creativeSave, "masterId", "campaignId", "planId", "creativeType");
            creativeSave.setAssetTaskIds(JSONObject.toJSONString(creativeSaveVO.getAssetTaskIds()));
            creativeSave.setTextLibraryIds(JSONObject.toJSONString(creativeSaveVO.getTextLibraryIds()));
            creativeSave.setUpdateUid(userId);
            updateById(creativeSave);
        }
        creativeSaveVO.setId(creativeSave.getId());

        // 第三步：新增素材 && 删除需要删除的创意单元，新增新创意单元信息
        this.saveCreativeUnit(creativeSaveVO, userId);

        // 第四部：保存文案信息
        if (CollectionUtils.isNotEmpty(creativeSaveVO.getUnits())) {
            CopyWritingBatchSaveVO saveVO = new CopyWritingBatchSaveVO();
            List<CopyWritingBatchSaveItemVO> copyWritingList = new ArrayList<>();
            creativeSaveVO.getUnits().forEach(unit -> {
                if (StringUtils.isNotBlank(unit.getAssets().getTitle().getContent()) && ObjectUtils.isNotNullOrZero(unit.getAssets().getTitle().getCountryId())) {
                    CopyWritingBatchSaveItemVO saveItemVO = new CopyWritingBatchSaveItemVO();
                    saveItemVO.setCountryId(unit.getAssets().getTitle().getCountryId());
                    saveItemVO.setCopyWriting(unit.getAssets().getTitle().getContent());
                    saveItemVO.setAssetId(unit.getAssets().getTitle().getAssetId());
                    saveItemVO.setTranslatedText(unit.getAssets().getTitle().getTranslatedText());
                    saveItemVO.setCopyWritingType(CopyWritingTypeEnum.TITLE.getId());
                    copyWritingList.add(saveItemVO);
                }
                if (StringUtils.isNotBlank(unit.getAssets().getDescription().getContent()) && ObjectUtils.isNotNullOrZero(unit.getAssets().getDescription().getCountryId())) {
                    CopyWritingBatchSaveItemVO saveItemVO = new CopyWritingBatchSaveItemVO();
                    saveItemVO.setCountryId(unit.getAssets().getDescription().getCountryId());
                    saveItemVO.setCopyWriting(unit.getAssets().getDescription().getContent());
                    saveItemVO.setAssetId(unit.getAssets().getDescription().getAssetId());
                    saveItemVO.setTranslatedText(unit.getAssets().getDescription().getTranslatedText());
                    saveItemVO.setCopyWritingType(CopyWritingTypeEnum.DESC.getId());
                    copyWritingList.add(saveItemVO);
                }
            });
            if (CollectionUtils.isNotEmpty(copyWritingList)) {
                saveVO.setCopyWritingList(copyWritingList);
                saveVO.setMasterId(creativeSaveVO.getMasterId().longValue());
                this.copyWritingService.batchSaveCopyWriting(saveVO, userId);
            }
        }
        //shein计划创建通知
        if (isAdd && planService.isSheinPlan(plan)) {
            try {
                Campaign campaign = campaignMapper.selectById(plan.getCampaignId());
                smsUtils.sendSheinMsg("Shein计划创建通知",
                        String.format("Shein活动 [%s] 下计划 [%s] 创建成功，请前往系统审核", campaign.getCampaignName(), plan.getPlanName())
                );
                callNoticeMapper.callSheinPlanUpdate(plan.getId());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return creativeSave.getId();
    }

    @Override
    public void saveCreativeUnit(CreativeSaveVO creativeSaveVO, Integer userId) {
        // 第三步：新增素材
        MaterialSaveVO saveVO = new MaterialSaveVO();
        saveVO.setAssets(creativeSaveVO.getUnits());
        saveVO.setMasterId(creativeSaveVO.getMasterId().longValue());

        Map<String, CreativeUnitMaterialSaveDTO> materialMap = this.materialService.saveMaterial(saveVO, userId);

        // 第四步：删除需要删除的创意单元，新增新创意单元信息
        this.creativeUnitService.batchSaveCreativeUnit(creativeSaveVO, materialMap, userId);
    }

    @Override
    public void batchSaveCreativeUnit(CreativeUnitBatchSaveVO saveVO, Integer userId) {
        saveVO.getUnitAssets().forEach(unitAsset -> {
            CreativeSaveVO creativeSaveVO = new CreativeSaveVO();
            creativeSaveVO.setId(unitAsset.getCreativeId());
            creativeSaveVO.setPlanId(unitAsset.getPlanId());
            creativeSaveVO.setCampaignId(unitAsset.getCampaignId());
            creativeSaveVO.setMasterId(saveVO.getMasterId().intValue());
            List<MaterialVO> materialVOS = unitAsset.getUnits().stream().map(unit -> {
                MaterialVO materialVO = new MaterialVO();
                materialVO.setCreativeUnitId(unit.getCreativeUnitId());
                MaterialGroupVO materialGroupVO = new MaterialGroupVO();
                if (unit.getAssets().getAsset() != null) {
                    MaterialAssetVO asset = new MaterialAssetVO();
                    BeanUtils.copyProperties(unit.getAssets().getAsset(), asset);
                    materialGroupVO.setAsset(asset);
                }
                if (unit.getAssets().getTitle() != null) {
                    MaterialAssetVO title = new MaterialAssetVO();
                    BeanUtils.copyProperties(unit.getAssets().getTitle(), title);
                    materialGroupVO.setTitle(title);
                }
                if (unit.getAssets().getDescription() != null) {
                    MaterialAssetVO description = new MaterialAssetVO();
                    BeanUtils.copyProperties(unit.getAssets().getDescription(), description);
                    materialGroupVO.setDescription(description);
                }
                // 如果endcard不为空，且主元素素材为视频类型，填充endcard
                if (unit.getAssets().getEndcard() != null
                        && null != unit.getAssets().getAsset()
                        && unit.getAssets().getAsset().getAssetType().equals(AssetTypeEnum.VIDEO.getId())
                ) {
                    MaterialAssetVO endcard = new MaterialAssetVO();
                    BeanUtils.copyProperties(unit.getAssets().getEndcard(), endcard);
                    materialGroupVO.setEndcard(endcard);
                }
                // 如果Concurrent不为空，且主元素素材为视频类型，填充Concurrent
                if (unit.getAssets().getConcurrent() != null
                        && null != unit.getAssets().getAsset()
                        && unit.getAssets().getAsset().getAssetType().equals(AssetTypeEnum.VIDEO.getId())
                ) {
                    MaterialAssetVO concurrent = new MaterialAssetVO();
                    BeanUtils.copyProperties(unit.getAssets().getConcurrent(), concurrent);
                    materialGroupVO.setConcurrent(concurrent);
                }
                // 如果image不为空，且主元素为图片类型，填充image
                if (unit.getAssets().getImg() != null
                        && null != unit.getAssets().getAsset()
                        && unit.getAssets().getAsset().getAssetType().equals(AssetTypeEnum.IMG.getId())
                ) {
                    MaterialAssetVO img = new MaterialAssetVO();
                    BeanUtils.copyProperties(unit.getAssets().getImg(), img);
                    materialGroupVO.setImg(img);
                }
                materialVO.setAssets(materialGroupVO);
                materialVO.setLandingUrl(unit.getLandingUrl());
                materialVO.setTemplateAttr(unit.getTemplateAttr());
                materialVO.setTemplateId(unit.getTemplateId());
                materialVO.setCreativeUnitName(unit.getCreativeUnitName());
                return materialVO;
            }).collect(Collectors.toList());
            creativeSaveVO.setUnits(materialVOS);
            this.saveCreativeUnit(creativeSaveVO, userId);
            // 通知中控
            this.applicationContext.publishEvent(new CreativeUnitAuditEvent(this, unitAsset.getPlanId()));
        });
    }

    @Override
    public CreativeGetDTO getCreativeDetailByPlanId(Long planId, Integer masterId) {

        Creative creativeInDb = getCreativeByPlanId(planId, masterId);
        CreativeGetDTO result = new CreativeGetDTO();
        BeanUtils.copyProperties(creativeInDb, result);
        result.setAssetTaskIds(StringUtils.isBlank(creativeInDb.getAssetTaskIds())
                ? List.of() : JSONObject.parseArray(creativeInDb.getAssetTaskIds(), Long.class));
        result.setTextLibraryIds(StringUtils.isBlank(creativeInDb.getTextLibraryIds())
                ? List.of() : JSONObject.parseArray(creativeInDb.getTextLibraryIds(), Long.class));
        if (ObjectUtils.isNotNullOrZero(result.getAppIconId())) {
            result.setAppIconUrl(assetService.getAsset(result.getAppIconId()).getHttpUrl());
        }
        if (ObjectUtils.isNotNullOrZero(result.getBrandLogoId())) {
            result.setBrandLogoUrl(assetService.getAsset(result.getBrandLogoId()).getHttpUrl());
        }
        // 获取创意下素材
        MaterialListVO listVO = new MaterialListVO();
        listVO.setCreativeId(creativeInDb.getId());
        listVO.setMasterId(masterId.longValue());
        result.setUnits(this.materialService.getCreativeMaterialList(listVO));
        // 获取行动号召国家ID
        Ctx ctx = this.ctxMapper.selectById(creativeInDb.getCtaId());
        if (ctx != null) {
            result.setCountryId(ctx.getCountryId());
        }

        return result;
    }


    @Override
    public void deleteByPlanId(Long planId, Integer operateUserId) {
        Creative creative = new Creative();
        creative.setIsDel(IsDelEnum.DELETE.getId().intValue());
        creative.setUpdateUid(operateUserId);
        this.update(creative, new LambdaQueryWrapper<Creative>().eq(Creative::getPlanId, planId));
    }

    @Override
    public PageUtils<?> listAutoAssetUpdate(AutoAssetUpdateListVO listVO, User user) {
        IPage<AutoAssetUpdateListDTO> iPage = new Page<>(listVO.getPage(), listVO.getPageNum());
        iPage = this.baseMapper.listAssetUpdate(iPage, new QueryWrapper<>()
                .eq("creative.master_id", listVO.getMasterId())
                .eq("mp.is_plan_put", PutEnum.IS_PUT.getId())
                .eq("mp.plan_mode", PlanModeEnum.NORMAL.getId())
                .eq("mp.is_del", IsDelEnum.NORMAL.getId())
                .eq("mc.is_del", IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getPlanStatus()), "mp.plan_status", listVO.getPlanStatus())
                .in(CollectionUtils.isNotEmpty(listVO.getCampaignIds()), "creative.campaign_id", listVO.getCampaignIds())
                .in(CollectionUtils.isNotEmpty(listVO.getPlanIds()), "creative.plan_id", listVO.getPlanIds())
                .eq(ObjectUtils.isNotAll(listVO.getIsAssetUpdate()), "creative.is_asset_update", listVO.getIsAssetUpdate())
                .and(CollectionUtils.isNotEmpty(listVO.getAssetTaskIds()), q -> {
                    q.ne("creative.asset_task_ids", "").and(q1 -> {
                        for (int i = 0; i < listVO.getAssetTaskIds().size(); i++) {
                            if (i == 0) {
                                q1.like("creative.asset_task_ids", listVO.getAssetTaskIds().get(i));
                            } else {
                                q1.or().like("creative.asset_task_ids", listVO.getAssetTaskIds().get(i));
                            }
                        }
                    });
                }).and(CollectionUtils.isNotEmpty(listVO.getTextLibraryIds()), q -> {
                    q.ne("creative.text_library_ids", "").and(q1 -> {
                        for (int i = 0; i < listVO.getTextLibraryIds().size(); i++) {
                            if (i == 0) {
                                q1.like("creative.text_library_ids", listVO.getTextLibraryIds().get(i));
                            } else {
                                q1.or().like("creative.text_library_ids", listVO.getTextLibraryIds().get(i));
                            }
                        }
                    });
                }).eq(ObjectUtils.isNotAll(listVO.getCreativeMonitorId()), "creative.creative_monitor_id", listVO.getCreativeMonitorId())
                .and(StringUtils.isNotBlank(listVO.getSearch()), q ->
                        q.like("mp.plan_name", listVO.getSearch())
                                .or().like("mc.campaign_name", listVO.getSearch())
                ).orderByDesc("mp.id")
        );
        if (iPage.getRecords().isEmpty()) {
            return new PageUtils<>(iPage);
        }
        List<Long> assetTaskIds = iPage.getRecords().stream().flatMap(u -> {
            if (StringUtils.isBlank(u.getAssetTaskIds())) {
                return Stream.of();
            }
            return JSONObject.parseArray(u.getAssetTaskIds(), Long.class).stream();
        }).distinct().collect(Collectors.toList());
        Map<Long, AssetTask> assetTaskMap = assetTaskIds.isEmpty() ? new HashMap<>() :
                assetTaskMapper.selectList(new LambdaQueryWrapper<AssetTask>()
                        .in(AssetTask::getId, assetTaskIds)
                        .eq(AssetTask::getIsDel, IsDelEnum.NORMAL.getId())
                ).stream().collect(Collectors.toMap(AssetTask::getId, Function.identity()));

        List<Long> libraryIds = iPage.getRecords().stream().flatMap(u -> {
            if (StringUtils.isBlank(u.getTextLibraryIds())) {
                return Stream.of();
            }
            return JSONObject.parseArray(u.getTextLibraryIds(), Long.class).stream();
        }).collect(Collectors.toList());
        Map<Long, TextLibrary> textLibraryMap = libraryIds.isEmpty() ? new HashMap<>() :
                textLibraryMapper.selectList(new LambdaQueryWrapper<TextLibrary>()
                                .in(TextLibrary::getId, libraryIds))
                        .stream().collect(Collectors.toMap(TextLibrary::getId, Function.identity()));
        iPage.getRecords().forEach(u -> {
            if (StringUtils.isNotBlank(u.getAssetTaskIds())) {
                u.setAssetTasks(
                        JSONObject.parseArray(u.getAssetTaskIds(), Long.class).stream().map(id -> {
                            if (assetTaskMap.containsKey(id)) {
                                return new AutoAssetUpdateListDTO.AssetTaskInfo(id, assetTaskMap.get(id).getAssetTaskName());
                            }
                            return null;
                        }).filter(Objects::nonNull).collect(Collectors.toList())
                );
            } else {
                u.setAssetTasks(List.of());
            }
            if (StringUtils.isNotBlank(u.getTextLibraryIds())) {
                u.setTextLibraryInfos(
                        JSONObject.parseArray(u.getTextLibraryIds(), Long.class).stream().map(id -> {
                            if (textLibraryMap.containsKey(id)) {
                                return new AutoAssetUpdateListDTO.TextLibraryInfo(id, textLibraryMap.get(id).getLibraryName());
                            }
                            return null;
                        }).filter(Objects::nonNull).collect(Collectors.toList())
                );
            } else {
                u.setTextLibraryInfos(List.of());
            }
            u.setIsAssetUpdateName(ICommonEnum.getNameById(u.getIsAssetUpdate(), CreatievIsAssetUpdateEnum.class));
            u.setPlanStatusName(ICommonEnum.getNameById(u.getPlanStatus(), PlanStatusEnum.class));
        });
        return new PageUtils<>(iPage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void autoAssetUpdateBatch(AutoAssetUpdateBatchVO batchVO, User user) {
        List<Creative> creatives = this.baseMapper.selectList(new LambdaQueryWrapper<Creative>()
                .in(Creative::getPlanId, batchVO.getPlanIds())
                .eq(Creative::getMasterId, batchVO.getMasterId())
                .eq(Creative::getIsDel, IsDelEnum.NORMAL.getId())
        );
        if (creatives.isEmpty()) {
            return;
        }
        Creative creative = null;
        if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchVO.getChangeCreativeMonitorId())) {
            creative = new Creative();
            creative.setCreativeMonitorId(batchVO.getCreativeMonitorId());
            //del 原计划创意规则
            List<Long> putMonitorIds = creatives.stream().map(Creative::getCreativeMonitorId).distinct().filter(ObjectUtils::isNotNullOrZero).collect(Collectors.toList());
            List<Long> planIds = creatives.stream().map(Creative::getPlanId).distinct().collect(Collectors.toList());
            if (!putMonitorIds.isEmpty()) {
                this.assetGroupMapper.delMonitors(putMonitorIds, planIds, batchVO.getMasterId(), user.getId());
            }
            //add 新计划创意规则
            if (ObjectUtils.isNotNullOrZero(batchVO.getCreativeMonitorId())) {
                planIds.forEach(planId -> this.assetGroupMapper.insertMonitor(batchVO.getMasterId(), batchVO.getCreativeMonitorId(), planId, user.getId()));
            }
        }
        if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchVO.getChangeIsAssetUpdate())) {
            if (null == creative) {
                creative = new Creative();
            }
            creative.setIsAssetUpdate(batchVO.getIsAssetUpdate());
            if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchVO.getChangeAssetUpdateCount())) {
                creative.setAssetUpdateCount(batchVO.getAssetUpdateCount());
            }
            if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchVO.getChangeAssetTaskIds())) {
                creative.setAssetTaskIds(JSONObject.toJSONString(batchVO.getAssetTaskIds()));
            }
            if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchVO.getChangeTextLibraryIds())) {
                creative.setTextLibraryIds(JSONObject.toJSONString(batchVO.getTextLibraryIds()));
            }
        }
        if (null != creative) {
            this.baseMapper.update(creative, new LambdaQueryWrapper<Creative>()
                    .in(Creative::getPlanId, batchVO.getPlanIds()));
        }
    }

    @Override
    public Integer autoAssetWaitCount(CreativeGetVO creativeGetVO) {
        try {
            return this.getCreativeByPlanId(creativeGetVO.getPlanId(), creativeGetVO.getMasterId()).getAssetWaitCount();
        } catch (CustomException e) {
            log.error(e.getMessage());
            return 0;
        }
    }

    @Override
    public void updateAssetWaitCount(CreativeUpdateAssetWaitCountVO countVO) {
        this.baseMapper.updateAssetWaitCount(countVO.getPlanId(), countVO.getCount());
    }

    @Override
    public void checkPlanChannelPool(Long planId, String channelPoolDirect) {
        if (ObjectUtils.isNotNullOrZero(planId)) {
            List<Long> assetIds = this.baseMapper.listAssetIdsByPlan(new QueryWrapper<Creative>()
                    .eq("mcu.plan_id", planId)
                    .eq("mcu.is_del", IsDelEnum.NORMAL.getId())
                    .eq("mma.field_type", MaterialAssetTypeEnum.PRIMARY_ASSET.getId())
            );
            TrackingIdChannelPoolCheckVO checkVO = new TrackingIdChannelPoolCheckVO(planId, channelPoolDirect, assetIds);
            this.showCheckResult(this.checkChannelPoolAndTrackingId(List.of(checkVO)));
        }
    }

    private void showCheckResult(List<String> checkResult) {
        if (!checkResult.isEmpty()) {
            String result = StringUtils.join(checkResult, "；");
            log.debug("channel pool check result : " + result);
            throw new CustomException(result);
        }
    }

    /**
     * 根据计划ID和计划关联的素材，检查CPS项目下渠道池和素材关联的三方开发者账号是否一致
     *
     * @param checkVOS 要检查的计划和素材信息
     * @return 错误信息
     */
    private List<String> checkChannelPoolAndTrackingId(List<TrackingIdChannelPoolCheckVO> checkVOS) {
        List<String> errorInfos = new ArrayList<>();
        checkVOS.forEach(checkVO -> {
            String directValue;
            if (StringUtils.isNotBlank(checkVO.getChannelPoolDirect())) {
                directValue = checkVO.getChannelPoolDirect();
            } else {
                PlanDirect planDirect = this.planDirectMapper.selectOne(new QueryWrapper<PlanDirect>().lambda()
                        .eq(PlanDirect::getPlanId, checkVO.getPlanId())
                        .eq(PlanDirect::getDirectId, 1049));
                directValue = null == planDirect ? null : planDirect.getDirectValue();
            }
            if (null != directValue) {
                List<Integer> channelPoolDirect = JSONObject.parseObject(directValue,
                        new TypeReference<>() {
                        });
                if (channelPoolDirect.isEmpty()) {
                    return;
                }
                List<ResChannelPool> resChannelPools = this.resChannelPoolMapper.listChannelPool(
                        new QueryWrapper<ResChannelPool>().lambda()
                                .in(ResChannelPool::getId, channelPoolDirect)
                );
                if (null != resChannelPools && !checkVO.getAssetIds().isEmpty()) {
                    List<Integer> cpsAppIds = resChannelPools.stream().map(ResChannelPool::getCpsAppId)
                            .collect(Collectors.toList());
                    List<AssetCpsAppIdDTO> assetCpsAppInfos = this.taskProductAssetMapper.listAssetCpsAppId(
                            new QueryWrapper<>()
                                    .in("mtpa.asset_id", checkVO.getAssetIds())
                                    .eq("mtpa.is_del", IsDelEnum.NORMAL.getId())
                    );
                    List<Integer> assetCpsAppIds = assetCpsAppInfos.stream().map(AssetCpsAppIdDTO::getCpsAppId)
                            .distinct().collect(Collectors.toList());
                    if (assetCpsAppIds.size() > 1) {
                        errorInfos.add("计划ID：" + checkVO.getPlanId() + "，素材关联tracking ID对应多个账号，请检查");
                    } else if (cpsAppIds.size() == assetCpsAppIds.size()
                            && !cpsAppIds.get(0).equals(assetCpsAppIds.get(0))) {
                        errorInfos.add("计划ID：" + checkVO.getPlanId() + "，渠道池和素材对应的开发者账号不一致");
                    }
                }
            }
        });
        return errorInfos;
    }

    /**
     * 获取创意数据
     *
     * @param creativeId 创意ID
     * @param masterId   广告主ID
     * @return 返回数据
     */
    private Creative getCreative(Long creativeId, Integer masterId) {
        Creative one = getOne(new LambdaQueryWrapper<Creative>().eq(Creative::getId, creativeId)
                .eq(Creative::getMasterId, masterId)
                .eq(Creative::getIsDel, IsDelEnum.NORMAL.getId()));
        if (null == one) {
            throw new CustomException("创意ID不存在，请检查后重试");
        }
        return one;
    }

    private Creative getCreativeByPlanId(Long planId, Integer masterId) {
        Creative one = getOne(new LambdaQueryWrapper<Creative>().eq(Creative::getPlanId, planId)
                .eq(Creative::getMasterId, masterId)
                .eq(Creative::getIsDel, IsDelEnum.NORMAL.getId()));
        if (null == one) {
            // 此处返回码改为404，前端识别404表示为新增创意
            throw new CustomException(ResultStatusEnum.NOT_FOUND.getCode(), "创意不存在，请检查后重试");
        }
        return one;
    }
}
