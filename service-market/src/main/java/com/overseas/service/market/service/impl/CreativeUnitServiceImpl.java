package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.configuration.SheinConfiguration;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.asset.AssetDTO;
import com.overseas.common.dto.market.copyWriting.CopyWritingDTO;
import com.overseas.common.dto.market.creativeUnit.*;
import com.overseas.common.dto.market.market.MarketBatchListDTO;
import com.overseas.common.dto.market.material.MaterialDTO;
import com.overseas.common.dto.report.ReportListDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.enums.SortTypeEnum;
import com.overseas.common.enums.market.campaign.CampaignModeEnum;
import com.overseas.common.enums.market.campaign.CampaignStatusEnum;
import com.overseas.common.enums.market.plan.PlanStatusEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.utils.UploadUtils;
import com.overseas.common.vo.market.creative.CreativeUnitListVO;
import com.overseas.common.vo.market.creative.CreativeUnitPutStatusVO;
import com.overseas.common.vo.market.creative.unit.CreativeUnitBatchUpdateVO;
import com.overseas.common.vo.market.creative.unit.CreativeUnitFillSizeVO;
import com.overseas.common.vo.market.creative.unit.CreativeUnitGetVO;
import com.overseas.common.vo.market.market.MarketBatchListVO;
import com.overseas.common.vo.market.market.RecordBatchDeleteVO;
import com.overseas.common.vo.market.market.RecordBatchSwitchVO;
import com.overseas.common.vo.market.master.MasterPageFirstVO;
import com.overseas.common.vo.market.material.MaterialAssetVO;
import com.overseas.common.vo.report.ReportHasCostListVO;
import com.overseas.service.market.dto.market.MasterPageFirstDTO;
import com.overseas.service.market.dto.plan.WebPlanStatusContainerDTO;
import com.overseas.service.market.entity.*;
import com.overseas.service.market.enums.assets.AssetIsUsedEnum;
import com.overseas.service.market.enums.assets.AssetTypeEnum;
import com.overseas.service.market.enums.creative.CreativeAuditStatusEnum;
import com.overseas.service.market.enums.creative.units.CreativeUnitStatusEnum;
import com.overseas.service.market.enums.material.MaterialAssetTypeEnum;
import com.overseas.service.market.enums.plan.PlanModeEnum;
import com.overseas.service.market.events.notifyControl.ControlContants;
import com.overseas.service.market.events.notifyControl.ControlCreativeUnitEvent;
import com.overseas.service.market.feign.FgAbroadEngine;
import com.overseas.service.market.feign.FgReportService;
import com.overseas.service.market.mapper.*;
import com.overseas.service.market.service.CreativeUnitService;
import com.overseas.service.market.service.MarketPageListService;
import com.overseas.service.market.service.MaterialService;
import com.overseas.service.market.service.SheinCreativeService;
import com.overseas.service.market.vo.creative.CreativeSaveVO;
import com.overseas.service.market.vo.creative.CreativeUnitSelectGetVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-25 10:33
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CreativeUnitServiceImpl extends ServiceImpl<CreativeUnitMapper, CreativeUnit> implements CreativeUnitService, MarketPageListService {

    private final MaterialService materialService;

    private final FgReportService fgReportService;

    private final FgAbroadEngine fgAbroadEngine;

    private final AssetMapper assetMapper;

    private final MaterialAssetMapper materialAssetMapper;

    private final ApplicationContext applicationContext;

    private final SheinCreativeService sheinCreativeService;

    private final AssetResourceMapper assetResourceMapper;

    private final SheinConfiguration sheinConfiguration;

    private final CopyWritingMapper copyWritingMapper;

    @Override
    public void batchSaveCreativeUnit(CreativeSaveVO creativeSaveVO, Map<String, CreativeUnitMaterialSaveDTO> materialMap, Integer userId) {

        // 查询创意下的创意单元
        List<CreativeUnit> creativeUnits = this.baseMapper.selectList(new QueryWrapper<CreativeUnit>().lambda()
                .eq(CreativeUnit::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(CreativeUnit::getCreativeId, creativeSaveVO.getId()));

        if (CollectionUtils.isNotEmpty(creativeUnits)) {
            // 查询待删除的创意单元
            List<Long> toBeDeleteUnitIds = creativeUnits.stream()
                    .filter(unit -> !materialMap.containsKey(this.materialKey(unit)))
                    .map(CreativeUnit::getId).collect(Collectors.toList());
            // 校验toBeDeleteUnits中的创意单元是否有消耗数据，如果有则置为归档，如果没有则删除
            if (CollectionUtils.isNotEmpty(toBeDeleteUnitIds)) {
                ReportHasCostListVO hasCostListVO = new ReportHasCostListVO();
                hasCostListVO.setIds(toBeDeleteUnitIds);
                hasCostListVO.setListType("creativeUnit");
                List<Long> hasCostResultIds = this.fgReportService.listHasCostRecord(hasCostListVO).getData();
                if (CollectionUtils.isNotEmpty(hasCostResultIds)) {
                    CreativeUnit toBeFilledCreativeUnit = new CreativeUnit();
                    toBeFilledCreativeUnit.setCreativeUnitStatus(CreativeUnitStatusEnum.FILE.getId());
                    toBeFilledCreativeUnit.setUpdateUid(userId);
                    this.baseMapper.update(toBeFilledCreativeUnit, new QueryWrapper<CreativeUnit>().lambda()
                            .in(CreativeUnit::getId, hasCostResultIds));
                    toBeDeleteUnitIds.removeAll(hasCostResultIds);
                }
                if (CollectionUtils.isNotEmpty(toBeDeleteUnitIds)) {
                    CreativeUnit toBeDeleteCreativeUnit = new CreativeUnit();
                    toBeDeleteCreativeUnit.setIsDel(IsDelEnum.DELETE.getId().intValue());
                    toBeDeleteCreativeUnit.setUpdateUid(userId);
                    this.baseMapper.update(toBeDeleteCreativeUnit, new QueryWrapper<CreativeUnit>().lambda()
                            .ne(CreativeUnit::getCreativeUnitStatus, CreativeUnitStatusEnum.FILE.getId())
                            .in(CreativeUnit::getId, toBeDeleteUnitIds));
                }
            }
            // 查询不需要重复save的创意单元
            creativeUnits.forEach(unit -> {
                String materialKey = this.materialKey(unit);
                CreativeUnitMaterialSaveDTO materialSaveDTO = materialMap.get(materialKey);
                if (null == materialSaveDTO) {
                    return;
                }
                if (!toBeDeleteUnitIds.contains(unit.getId()) &&
                        (!unit.getLandingUrl().equals(materialSaveDTO.getLandingUrl()) ||
                                !unit.getLandingUrl().equals(materialSaveDTO.getCreativeUnitName()))) {
                    CreativeUnit update = new CreativeUnit();
                    update.setLandingUrl(materialSaveDTO.getLandingUrl());
                    update.setTemplateId(materialSaveDTO.getTemplateId());
                    update.setTemplateAttr(materialSaveDTO.getTemplateAttr());
                    update.setCreativeUnitName(materialSaveDTO.getCreativeUnitName());
                    update.setUpdateUid(userId);
                    this.baseMapper.update(update, new LambdaQueryWrapper<CreativeUnit>()
                            .eq(CreativeUnit::getId, unit.getId())
                            .eq(CreativeUnit::getMaterialId, unit.getMaterialId())
                    );
                }
                materialMap.remove(materialKey);
            });
        }
        if (materialMap.isEmpty()) {
            return;
        }
        // 获取该计划下创意单元最大下标
        Long index = this.baseMapper.getLastCreativeIndex(new QueryWrapper<CreativeUnit>().lambda()
                .eq(CreativeUnit::getCreativeId, creativeSaveVO.getId())
                .eq(CreativeUnit::getPlanId, creativeSaveVO.getPlanId())
                .orderByDesc(CreativeUnit::getCreativeIndex));

        AtomicLong atomicLong = new AtomicLong(ObjectUtils.isNotNullOrZero(index) ? index + 1 : 1L);

        List<Long> assetIds = new ArrayList<>() {{
            creativeSaveVO.getUnits().forEach(units -> {
                add(units.getAssets().getAsset().getAssetId());
                //endcard
                if (units.getAssets().getEndcard() != null) {
                    add(units.getAssets().getEndcard().getAssetId());
                }
                //随图
                if (units.getAssets().getConcurrent() != null) {
                    add(units.getAssets().getConcurrent().getAssetId());
                }
                //PUSH ICON副图
                if (units.getAssets().getImg() != null) {
                    add(units.getAssets().getImg().getAssetId());
                }
            });
        }};

        // 将已使用素材ID状态更新为已使用
        Asset asset = new Asset();
        asset.setIsUsed(AssetIsUsedEnum.IS_USED.getId());
        this.assetMapper.update(asset, new QueryWrapper<Asset>().lambda()
                .in(Asset::getId, assetIds.stream().distinct().collect(Collectors.toList())));

        //更新账户素材使用情况
        AssetResource assetResource = new AssetResource();
        assetResource.setIsMasterUsed(AssetIsUsedEnum.IS_USED.getId());
        this.assetResourceMapper.update(assetResource, new LambdaQueryWrapper<AssetResource>()
                .eq(AssetResource::getMasterId, creativeSaveVO.getMasterId())
                .in(AssetResource::getAssetId, assetIds.stream().distinct().collect(Collectors.toList()))
        );

        List<CreativeUnit> creativeUnitList = new ArrayList<>();

        materialMap.forEach((materialId, materialSaveDTO) -> {
            CreativeUnit creativeUnit = new CreativeUnit();
            BeanUtils.copyProperties(creativeSaveVO, creativeUnit, "id", "landingUrl");
            creativeUnit.setCreativeId(creativeSaveVO.getId());
            creativeUnit.setCreativeIndex(atomicLong.getAndIncrement());
            creativeUnit.setCreativeUnitType(materialSaveDTO.getAssetType());
            creativeUnit.setLandingUrl(materialSaveDTO.getLandingUrl());
            creativeUnit.setMaterialId(materialSaveDTO.getMaterialId());
            creativeUnit.setTemplateId(materialSaveDTO.getTemplateId());
            creativeUnit.setTemplateAttr(materialSaveDTO.getTemplateAttr());
            if (StringUtils.isNotBlank(materialSaveDTO.getCreativeUnitName()) && !"新素材".equals(materialSaveDTO.getCreativeUnitName())) {
                creativeUnit.setCreativeUnitName(materialSaveDTO.getCreativeUnitName());
            } else {
                creativeUnit.setCreativeUnitName("");
            }
            creativeUnit.setCreateUid(userId);
            creativeUnitList.add(creativeUnit);
        });
        this.baseMapper.batchCreativeUnit(creativeUnitList);
    }

    /**
     * 素材 key
     *
     * @param unit unit
     * @return 返回key
     */
    private String materialKey(CreativeUnit unit) {
        return String.format("%s-%s-%s-%s", unit.getMaterialId(), unit.getLandingUrl(), unit.getTemplateId(), unit.getTemplateAttr());
    }

    @Override
    public CreativeUnit getCreativeUnit(Long id, Integer masterId) {
        CreativeUnit plan = this.getOne(new LambdaQueryWrapper<CreativeUnit>().eq(CreativeUnit::getId, id)
                .eq(CreativeUnit::getMasterId, masterId));
        if (null == plan) {
            throw new CustomException("创意ID不存在，请检查后重试");
        }
        return plan;
    }

    @Override
    public IPage<MasterPageFirstDTO> pageMarket(MasterPageFirstVO listVO, List<Integer> permissionMasterIds,
                                                List<Long> ids, WebPlanStatusContainerDTO webPlanStatusContainerDTO) {
        try {
            IPage<MasterPageFirstDTO> iPage = new Page<>(listVO.getPage(), listVO.getPageNum());
            QueryWrapper<CreativeUnit> queryWrapper = getQueryWrapper(listVO, permissionMasterIds, ids);
            fillSizeToWrapper(queryWrapper, listVO);
            fillAssetToWrapper(queryWrapper, listVO);
            return this.baseMapper.pageMarket(iPage, queryWrapper);
        } catch (Exception e) {
            log.error("查询创意单元列表异常", e);
            return new Page<>(listVO.getPage(), listVO.getPageNum());
        }
    }

    @Override
    public List<Long> listAllIdMarket(MasterPageFirstVO listVO, List<Integer> permissionMasterIds,
                                      WebPlanStatusContainerDTO webPlanStatusContainerDTO) {
        try {
            QueryWrapper<CreativeUnit> queryWrapper = this.getQueryWrapper(listVO, permissionMasterIds, null);
            fillSizeToWrapper(queryWrapper, listVO);
            fillAssetToWrapper(queryWrapper, listVO);
            return this.baseMapper.listUnitId(queryWrapper);
        } catch (Exception e) {
            log.error("查询创意单元列表异常", e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<Long, ReportListDTO> getReportMapMarket(List<ReportListDTO> reportList) {
        return reportList.stream().collect(Collectors.toMap(ReportListDTO::getCreativeUnitId, Function.identity()));
    }

    @Override
    public List<Long> listReportIdMarket(List<ReportListDTO> reportList) {
        return reportList.stream().map(ReportListDTO::getCreativeUnitId).collect(Collectors.toList());
    }

    @Override
    public Long getValueById(ReportListDTO reportDTO) {
        return reportDTO.getCreativeUnitId();
    }

    @Override
    public boolean batchDelete(RecordBatchDeleteVO batchDeleteVO) {
        if (!batchDeleteVO.getIds().isEmpty()) {
            UpdateWrapper<CreativeUnit> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().in(CreativeUnit::getId, batchDeleteVO.getIds())
                    .eq(CreativeUnit::getMasterId, batchDeleteVO.getMasterId())
                    .eq(CreativeUnit::getIsDel, IsDelEnum.NORMAL.getId());
            CreativeUnit creativeUnit = new CreativeUnit();
            creativeUnit.setIsDel(IsDelEnum.DELETE.getId().intValue());
            creativeUnit.setUpdateUid(batchDeleteVO.getUserId());
            try {
                this.baseMapper.update(creativeUnit, updateWrapper);
                return true;
            } catch (Exception e) {
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean batchSwitch(RecordBatchSwitchVO batchSwitchVO) {
        CreativeUnit creativeUnit = new CreativeUnit();
        creativeUnit.setCreativeUnitStatus(batchSwitchVO.getPutStatus());
        creativeUnit.setUpdateUid(batchSwitchVO.getUserId());
        this.baseMapper.update(creativeUnit, new UpdateWrapper<CreativeUnit>().lambda()
                .eq(ObjectUtils.isNotNullOrZero(batchSwitchVO.getMasterId()),
                        CreativeUnit::getMasterId, batchSwitchVO.getMasterId())
                .in(CollectionUtils.isNotEmpty(batchSwitchVO.getMasterIds()),
                        CreativeUnit::getMasterId, batchSwitchVO.getMasterIds())
                .in(CreativeUnit::getId, batchSwitchVO.getIds())
                .eq(CreativeUnit::getIsDel, IsDelEnum.NORMAL.getId())
        );
        return true;
    }

    @Override
    public void formatListMarket(List<MasterPageFirstDTO> list, WebPlanStatusContainerDTO webPlanStatusContainerDTO,
                                 Long masterId) {

        Map<Long, List<MaterialAssetVO>> materialAssetMap = this.materialService.getMaterialMap(list
                .stream().map(MasterPageFirstDTO::getMaterialId).distinct().collect(Collectors.toList()), masterId);
        for (MasterPageFirstDTO record : list) {
            record.setCreativeUnitStatusName(ICommonEnum.getNameById(record.getCreativeUnitStatus(),
                    CreativeUnitStatusEnum.class));
            List<MaterialAssetVO> assets = materialAssetMap.getOrDefault(record.getMaterialId(), List.of());
            if (CollectionUtils.isNotEmpty(assets)) {
                assets.sort(Comparator.comparing(MaterialAssetVO::getFieldType));
            }
            record.setAssets(assets);
        }
    }

    @Override
    public void deleteByPlanId(Long planId, Integer operateUserId) {
        CreativeUnit creativeUnit = new CreativeUnit();
        creativeUnit.setIsDel(IsDelEnum.DELETE.getId().intValue());
        creativeUnit.setUpdateUid(operateUserId);
        this.update(creativeUnit, new LambdaQueryWrapper<CreativeUnit>().eq(CreativeUnit::getPlanId, planId));
    }

    @Override
    public List<SelectDTO> getCreativeUnitSelect(CreativeUnitSelectGetVO getVO) {
        return this.baseMapper.getCreativeUnitSelect(new QueryWrapper<CreativeUnit>().lambda()
                .eq(CreativeUnit::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(CreativeUnit::getMasterId, getVO.getMasterId())
                .eq(CreativeUnit::getPlanId, getVO.getPlanId())
                .orderByAsc(CreativeUnit::getId));
    }

    @Override
    public List<CreativeUnitAssetDTO> listCreativeUnitAsset(CreativeUnitListVO listVO) {
        List<CreativeUnitAssetDTO> list = this.baseMapper.listCreativeUnitMaterialAsset(new QueryWrapper<CreativeUnit>()
                .eq("mcu.is_del", IsDelEnum.NORMAL.getId())
                .eq("ma.is_del", IsDelEnum.NORMAL.getId())
                .ne(ObjectUtils.isNullOrZero(listVO.getIncludeFiledUnit()),
                        "mcu.creative_unit_status", CreativeUnitStatusEnum.FILE.getId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getMasterId()),
                        "mcu.master_id", listVO.getMasterId())
                .in(CollectionUtils.isNotEmpty(listVO.getPlanIds()),
                        "mcu.plan_id", listVO.getPlanIds())
                .in(CollectionUtils.isNotEmpty(listVO.getCreativeUnitIds()),
                        "mcu.id", listVO.getCreativeUnitIds()));
        if (list.isEmpty()) {
            return list;
        }
        List<Long> assetIds = list.stream().filter(u -> u.getAssetType().equals(AssetTypeEnum.TEXT.getId()))
                .map(CreativeUnitAssetDTO::getAssetId).distinct().collect(Collectors.toList());
        Map<String, CopyWritingDTO> copyWritingMap = copyWritingMapper.getCopyWritingList(new QueryWrapper<>()
                .eq("writing.master_id", listVO.getMasterId())
                .in("writing.asset_id", assetIds)
        ).stream().collect(Collectors.toMap(u -> String.format("%s-%s", u.getAssetId(), u.getCopyWritingType()), Function.identity()));
        return list.stream().map(u -> {
            if (StringUtils.isNotBlank(u.getTemplateAttrStr())) {
                u.setTemplateAttr(JSONObject.parseArray(u.getTemplateAttrStr(), Long.class));
            }
            if (!u.getAssetType().equals(AssetTypeEnum.TEXT.getId())) {
                return u;
            }
            CopyWritingDTO copyWriting = copyWritingMap.get(String.format("%s-%s", u.getAssetId(), u.getFieldType()));
            if (null != copyWriting) {
                u.setTranslatedText(copyWriting.getTranslatedText());
                u.setCountryId(copyWriting.getCountryId());
                u.setCountryName(copyWriting.getCountryName());
            } else {
                u.setTranslatedText("");
                u.setCountryName("");
            }
            return u;
        }).collect(Collectors.toList());
    }

    @Override
    public IPage<MarketBatchListDTO> listCreativeUnitAsset(MarketBatchListVO listVO) {

        IPage<MarketBatchListDTO> pageData = this.baseMapper.listCreativeUnitAsset(new Page<>(listVO.getPage(),
                listVO.getPageNum()), new QueryWrapper<CreativeUnit>()
                .eq("mcu.is_del", IsDelEnum.NORMAL.getId())
                .eq("mp.is_del", IsDelEnum.NORMAL.getId())
                .eq("mc.is_del", IsDelEnum.NORMAL.getId())
                .eq("mc.campaign_mode", CampaignModeEnum.NORMAL.getId())
                .eq("mp.plan_mode", PlanModeEnum.NORMAL.getId())
                .eq("mp.is_plan_put", listVO.getIsPut())
                .ne("mcu.creative_unit_status", CreativeUnitStatusEnum.FILE.getId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getMasterId()), "mcu.master_id", listVO.getMasterId())
                .in(CollectionUtils.isNotEmpty(listVO.getCampaignIds()), "mc.id", listVO.getCampaignIds())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getCampaignStatus()), "mc.campaign_status", listVO.getCampaignStatus())
                .in(CollectionUtils.isNotEmpty(listVO.getPlanIds()), "mp.id", listVO.getPlanIds())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getPlanStatus()), "mp.plan_status", listVO.getPlanStatus())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getCreativeUnitStatus()), "mcu.creative_unit_status", listVO.getCreativeUnitStatus())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getCreativeUnitType()), "mcu.creative_unit_type", listVO.getCreativeUnitType())
                .like(StringUtils.isNotBlank(listVO.getSearch()), "mcu.id", listVO.getSearch())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getSlotType()), "mp.slot_type", listVO.getSlotType())
                .orderByDesc("mcu.id"));

        if (pageData.getRecords().isEmpty()) {
            return new Page<>();
        }
        // 获取素材
        CreativeUnitListVO unitListVO = new CreativeUnitListVO();
        unitListVO.setMasterId(listVO.getMasterId());
        unitListVO.setCreativeUnitIds(pageData.getRecords().stream().map(MarketBatchListDTO::getCreativeUnitId).collect(Collectors.toList()));
        List<CreativeUnitAssetDTO> unitAssetDTOList = this.listCreativeUnitAsset(unitListVO);

        // 将素材列表根据计划——创意单元——素材进行分类
        Map<Long, Map<Long, List<CreativeUnitAssetDTO>>> planUnitAssetMap = this.getPlanUnitAssetMap(unitAssetDTOList);
        pageData.getRecords().forEach(record -> {
            Map<Long, List<CreativeUnitAssetDTO>> unitAssetMap = planUnitAssetMap.get(record.getPlanId());
            if (unitAssetMap == null) {
                return;
            }
            List<CreativeUnitMaterialDTO> unitMaterialDTOS = new ArrayList<>();
            unitAssetMap.forEach((unitId, value) -> {
                CreativeUnitMaterialDTO unitMaterialDTO = new CreativeUnitMaterialDTO();
                unitMaterialDTO.setCreativeUnitId(unitId);
                List<CreativeUnitAssetDTO> unitAssets = unitAssetMap.getOrDefault(unitId, List.of());
                MaterialDTO assets = this.getCreativeUnitMaterial(unitAssets);
                unitMaterialDTO.setAssets(assets);
                if (unitId.equals(record.getCreativeUnitId())) {
                    record.setAssets(assets);
                }
                if (!unitAssets.isEmpty()) {
                    unitMaterialDTO.setLandingUrl(unitAssets.get(0).getLandingUrl());
                    unitMaterialDTO.setTemplateId(unitAssets.get(0).getTemplateId());
                    unitMaterialDTO.setTemplateAttr(unitAssets.get(0).getTemplateAttr());
                } else {
                    unitMaterialDTO.setLandingUrl("");
                    unitMaterialDTO.setTemplateId(0L);
                }
                unitMaterialDTOS.add(unitMaterialDTO);
            });
            record.setUnits(unitMaterialDTOS);
            // 设置相关素材信息
            record.setAsset(record.getAssets().getAsset() == null ? "" : record.getAssets().getAsset().getHttpUrl());
            record.setTitle(record.getAssets().getTitle() == null ? "" : record.getAssets().getTitle().getContent());
            record.setDescription(record.getAssets().getDescription() == null ? "" : record.getAssets().getDescription().getContent());
            record.setEndcard(record.getAssets().getEndcard() == null ? "" : record.getAssets().getEndcard().getHttpUrl());
            record.setConcurrent(record.getAssets().getConcurrent() == null ? "" : record.getAssets().getConcurrent().getHttpUrl());
            record.setImg(record.getAssets().getImg() == null ? "" : record.getAssets().getImg().getHttpUrl());
        });
        return pageData;
    }

    @Override
    public Map<Long, Map<Long, List<CreativeUnitAssetDTO>>> getPlanUnitAssetMap(
            List<CreativeUnitAssetDTO> unitAssetDTOList) {

        // 将素材根据计划ID分类
        Map<Long, List<CreativeUnitAssetDTO>> planAssetMap = unitAssetDTOList.stream()
                .collect(Collectors.groupingBy(CreativeUnitAssetDTO::getPlanId));
        // 将分类后的素材根据创意单元ID再次分类
        Map<Long, Map<Long, List<CreativeUnitAssetDTO>>> planUnitAssetMap = new HashMap<>();
        planAssetMap.forEach((planId, assets) -> planUnitAssetMap.put(planId, assets.stream()
                .collect(Collectors.groupingBy(CreativeUnitAssetDTO::getCreativeUnitId))));
        return planUnitAssetMap;
    }


    @Override
    public MaterialDTO getCreativeUnitMaterial(List<CreativeUnitAssetDTO> unitAssets) {

        if (unitAssets.isEmpty()) {
            return new MaterialDTO();
        }
        Map<Integer, AssetDTO> assetMap = unitAssets.stream().collect(
                Collectors.toMap(CreativeUnitAssetDTO::getFieldType, Function.identity()));
        MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setAsset(assetMap.getOrDefault(MaterialAssetTypeEnum.PRIMARY_ASSET.getId(), null));
        if (materialDTO.getAsset() != null) {
            materialDTO.getAsset().setHttpUrl(
                    UploadUtils.getNetworkUrl(materialDTO.getAsset().getContent(), materialDTO.getAsset().getIsUpload())
            );
        }
        materialDTO.setTitle(assetMap.getOrDefault(MaterialAssetTypeEnum.TITLE.getId(), new AssetDTO()));
        materialDTO.setDescription(assetMap.getOrDefault(MaterialAssetTypeEnum.DESCRIPTION.getId(), new AssetDTO()));
        materialDTO.setEndcard(assetMap.getOrDefault(MaterialAssetTypeEnum.END_CARD.getId(), null));
        materialDTO.setConcurrent(assetMap.getOrDefault(MaterialAssetTypeEnum.CONCURRENT.getId(), null));
        materialDTO.setImg(assetMap.getOrDefault(MaterialAssetTypeEnum.IMG.getId(), null));
        if (materialDTO.getEndcard() != null) {
            materialDTO.getEndcard().setHttpUrl(
                    UploadUtils.getNetworkUrl(
                            materialDTO.getEndcard().getContent(), materialDTO.getEndcard().getIsUpload()
                    )
            );
        }
        if (materialDTO.getConcurrent() != null) {
            materialDTO.getConcurrent().setHttpUrl(
                    UploadUtils.getNetworkUrl(
                            materialDTO.getConcurrent().getContent(), materialDTO.getConcurrent().getIsUpload()
                    )
            );
        }
        if (materialDTO.getImg() != null) {
            materialDTO.getImg().setHttpUrl(
                    UploadUtils.getNetworkUrl(
                            materialDTO.getImg().getContent(), materialDTO.getImg().getIsUpload()
                    )
            );
        }
        return materialDTO;
    }

    @Override
    public PageUtils<CreativeUnitListDTO> listCreativeUnitByAssetId(CreativeUnitListVO listVO, User user) {
        IPage<CreativeUnitListDTO> pageData = this.baseMapper.listCreativeUnitByAssetId(new Page<>(listVO.getPage(), listVO.getPageNum()), new QueryWrapper<CreativeUnit>()
                .eq("mp.is_plan_put", sheinConfiguration.isPut(null, user.getRoleId()))
                .eq("mc.is_del", IsDelEnum.NORMAL.getId())
                .eq("mp.is_del", IsDelEnum.NORMAL.getId())
                .eq("mp.plan_mode", PlanModeEnum.NORMAL.getId())
                .eq("mcu.is_del", IsDelEnum.NORMAL.getId())
                .eq("mcu.audit_status", CreativeAuditStatusEnum.PASS.getId())
                //传入字段类型，则限制字段类型
                .in(CollectionUtils.isNotEmpty(listVO.getFieldTypes()), "mma.field_type", listVO.getFieldTypes())
                //如果不穿入字段类型主元素
                .eq(CollectionUtils.isEmpty(listVO.getFieldTypes()), "mma.field_type", MaterialAssetTypeEnum.PRIMARY_ASSET.getId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getMasterId()), "mcu.master_id", listVO.getMasterId())
                .in(CollectionUtils.isNotEmpty(listVO.getCampaignIds()), "mcu.campaign_id", listVO.getCampaignIds())
                .in(CollectionUtils.isNotEmpty(listVO.getPlanIds()), "mcu.plan_id", listVO.getPlanIds())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getCreativeUnitStatus()), "mcu.creative_unit_status", listVO.getCreativeUnitStatus())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getPlanStatus()), "mp.plan_status", listVO.getPlanStatus())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getCampaignStatus()), "mc.campaign_status", listVO.getCampaignStatus())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getAssetId()), "mma.asset_id", listVO.getAssetId())
                .in(CollectionUtils.isNotEmpty(listVO.getAssetIds()), "mma.asset_id", listVO.getAssetIds())
                .between(StringUtils.isNotBlank(listVO.getCreateStartDate()), "mcu.create_time",
                        String.format("%s 00:00:00", listVO.getCreateStartDate()), String.format("%s 23:59:59", listVO.getCreateEndDate()))
                .between(StringUtils.isNotBlank(listVO.getUpdateEndDate()), "mcu.update_time",
                        String.format("%s 00:00:00", listVO.getUpdateStartDate()), String.format("%s 23:59:59", listVO.getUpdateEndDate()))
                .like(StringUtils.isNotBlank(listVO.getSearch()), "mcu.id", listVO.getSearch()));
        if (pageData.getRecords().isEmpty()) {
            return new PageUtils<>(List.of(), 0L);
        }

        // 获取素材
        CreativeUnitListVO unitListVO = new CreativeUnitListVO();
        unitListVO.setMasterId(listVO.getMasterId());
        unitListVO.setCreativeUnitIds(pageData.getRecords().stream().map(CreativeUnitListDTO::getCreativeUnitId).collect(Collectors.toList()));
        unitListVO.setIncludeFiledUnit(1);
        List<CreativeUnitAssetDTO> unitAssetDTOList = this.listCreativeUnitAsset(unitListVO);

        // 将素材列表根据计划——创意单元——素材进行分类
        Map<Long, List<CreativeUnitAssetDTO>> unitAssetMap = unitAssetDTOList.stream().collect(Collectors.groupingBy(CreativeUnitAssetDTO::getCreativeUnitId));
        pageData.getRecords().forEach(record -> {
            if (unitAssetMap.get(record.getCreativeUnitId()) == null) {
                return;
            }
            List<CreativeUnitAssetDTO> unitAssets = unitAssetMap.getOrDefault(record.getCreativeUnitId(), List.of());
            record.setAssets(this.getCreativeUnitMaterial(unitAssets));
            if (!unitAssets.isEmpty()) {
                record.setLandingUrl(unitAssets.get(0).getLandingUrl());
            } else {
                record.setLandingUrl("");
            }
            if (StringUtils.isNotBlank(record.getTemplateAttrStr())) {
                record.setTemplateAttr(JSONObject.parseArray(record.getTemplateAttrStr(), Long.class));
            }
        });
        return new PageUtils<>(pageData);
    }

    @Override
    public String getTemplateInfoById(CreativeUnitGetVO getVO) {

        // 先获取素材信息
        List<CreativeUnitTemplateAssetDTO> assets = this.baseMapper.listUnitTemplateAsset(new QueryWrapper<CreativeUnit>()
                        .eq("unit.id", getVO.getId()))
                .stream().peek(asset -> {
                    if (StringUtils.isEmpty(asset.getFieldName())) {
                        asset.setFieldName(ICommonEnum.get(asset.getFieldType(), AssetTypeEnum.class).getType());
                    }
                }).collect(Collectors.toList());

        // 获取计划信息
        CreativeUnitTemplateDTO creativeUnitTemplateDTO = this.baseMapper.getUnitTemplate(new QueryWrapper<CreativeUnit>()
                .eq("unit.id", getVO.getId()));
        Map<String, Object> params = new HashMap<>() {{
            put("unique_id", getVO.getId());
            put("assets", assets);
            put("landing", creativeUnitTemplateDTO.getLanding());
            put("deeplink", creativeUnitTemplateDTO.getDeeplink());
            put("monitorView", List.of(creativeUnitTemplateDTO.getMonitorViewUrl1(), creativeUnitTemplateDTO.getMonitorViewUrl2()));
            put("monitorClick", List.of(creativeUnitTemplateDTO.getMonitorClickUrl1(), creativeUnitTemplateDTO.getMonitorClickUrl2()));
            put("templateInfo", new HashMap<>() {{
                put("template", creativeUnitTemplateDTO.getTemplate());
                put("macros", creativeUnitTemplateDTO.getMacros());
            }});
        }};
        log.info("get template by unit id params : {}", JSONObject.toJSONString(params));
        Map<String, Object> result = this.fgAbroadEngine.getTemplateInfoByUnitId(new JSONObject(params));
        log.info("get template by unit id result : {}", JSONObject.toJSONString(result));
        switch (result.get("code").toString()) {
            case "0":
                return result.get("data").toString();
            case "1001":
                throw new CustomException("unique_id不能为空");
            case "1002":
                throw new CustomException("计划模版为空");
            case "1003":
                throw new CustomException("没有该创意单元");
            case "-1":
            default:
                throw new CustomException("内部服务异常，请联系研发人员");
        }
    }

    @Override
    public void batchUpdateCreativeUnit(CreativeUnitBatchUpdateVO updateVO, Integer userId) {

        List<CreativeUnit> creativeUnits = this.baseMapper.selectList(new QueryWrapper<CreativeUnit>().lambda()
                .in(CreativeUnit::getId, updateVO.getIds()));
        if (creativeUnits.isEmpty()) {
            return;
        }

        CreativeUnit creativeUnit = new CreativeUnit();
        creativeUnit.setCreativeUnitStatus(updateVO.getStatus());
        creativeUnit.setUpdateUid(userId);
        this.baseMapper.update(creativeUnit, new QueryWrapper<CreativeUnit>().lambda()
                .in(CreativeUnit::getId, updateVO.getIds()));

        // 通知中控
        updateVO.getIds().forEach(id -> this.applicationContext.publishEvent(new ControlCreativeUnitEvent(
                this, ControlContants.METHOD_UPDATE, id)));
        //记录日志
        this.sheinCreativeService.logCreativeUpdate(creativeUnits.stream().map(CreativeUnit::getPlanId).distinct().collect(Collectors.toList()), userId);

    }

    @Override
    public List<CreativeUnitPutStatusDTO> creativeUnitPutStatus(CreativeUnitPutStatusVO unitPutStatusVO) {
        return this.baseMapper.creativePutStatus(new QueryWrapper<>().in("unit.id", unitPutStatusVO.getCreativeUnitIds()))
                .stream().peek(u -> {
                    //必须活动，计划，创意单元全部投放中才返回1，否则返回0
                    if (PlanStatusEnum.MARKETING.getId().equals(u.getPlanStatus())
                            && CampaignStatusEnum.OPEN.getId().equals(u.getCampaignStatus())
                            && CreativeUnitStatusEnum.MARKETING.getId().equals(u.getCreativeUnitStatus())) {
                        u.setPutStatus(1);
                    } else {
                        u.setPutStatus(0);
                    }
                }).collect(Collectors.toList());
    }

    @Override
    public List<Long> fillSizeToCreativeUnitId(CreativeUnitFillSizeVO fillSizeVO) {
        List<Long> assetIds = this.fillSizeToAssetIds(fillSizeVO.getSizes(), null, fillSizeVO.getMasterId());
        if (CollectionUtils.isEmpty(assetIds)) {
            return List.of();
        }
        return this.materialAssetMapper.getCreativeUnitIdsByInfo(new QueryWrapper<>()
                .in("mma.asset_id", assetIds)
                .eq("mma.field_type", 1)
                .eq("mcu.master_id", fillSizeVO.getMasterId())
                .eq("mcu.is_del", 0)
        );
    }

    /**
     * 公共方法获取查询条件（适用于推广页面）
     *
     * @param listVO              查询条件对象
     * @param permissionMasterIds 允许的广告主ID
     * @param ids                 指定id查询
     * @return 结果集
     */
    private QueryWrapper<CreativeUnit> getQueryWrapper(MasterPageFirstVO listVO, List<Integer> permissionMasterIds, List<Long> ids) {
        QueryWrapper<CreativeUnit> queryWrapper = new QueryWrapper<CreativeUnit>()
                .eq("unit.is_del", IsDelEnum.NORMAL.getId())
                //添加投放计划过滤
                .eq("plan.plan_mode", PlanModeEnum.NORMAL.getId())
                //Shein投放过滤
                .eq("plan.is_plan_put", listVO.getIsPut());
        if (CollectionUtils.isEmpty(ids)) {
            queryWrapper
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getPlanStatus()), "plan.plan_status", listVO.getPlanStatus())
                    .in(CollectionUtils.isNotEmpty(listVO.getSheinCampaignIds()), "plan.campaign_id", listVO.getSheinCampaignIds())
                    .in(CollectionUtils.isNotEmpty(listVO.getSheinPlanIds()), "plan.id", listVO.getSheinPlanIds())
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getSlotType()), "plan.slot_type", listVO.getSlotType())
                    .in("unit.master_id", permissionMasterIds)
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getMasterId()), "unit.master_id", listVO.getMasterId())
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getCampaignId()), "unit.campaign_id", listVO.getCampaignId())
                    .in(CollectionUtils.isNotEmpty(listVO.getCampaignIds()), "unit.campaign_id", listVO.getCampaignIds())
                    .in(CollectionUtils.isNotEmpty(listVO.getTemplateIds()), "unit.template_id", listVO.getTemplateIds())
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getCampaignStatus()), "campaign_status", listVO.getCampaignStatus())
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getPlanId()), "unit.plan_id", listVO.getPlanId())
                    .in(CollectionUtils.isNotEmpty(listVO.getPlanIds()), "unit.plan_id", listVO.getPlanIds())
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getAssetType()), "unit.creative_unit_type", listVO.getAssetType())
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getCreativeUnitStatus()), "unit.creative_unit_status", listVO.getCreativeUnitStatus())
                    .like(StringUtils.isNotBlank(listVO.getSearch()), "unit.id", listVO.getSearch())
                    .and(CollectionUtils.isNotEmpty(listVO.getSearches()), q -> {
                        if (CollectionUtils.isNotEmpty(listVO.getSearches())) {
                            listVO.getSearches().forEach(u -> q.like("unit.id", u));
                        }
                    })
                    .ge(StringUtils.isNotEmpty(listVO.getCreateStartDate()), "unit.create_time", listVO.getCreateStartDate() + " 00:00:00")
                    .le(StringUtils.isNotEmpty(listVO.getCreateEndDate()), "unit.create_time", listVO.getCreateEndDate() + " 23:59:59")
                    .eq(listVO.getDpaState() != null && !listVO.getDpaState().equals(-1),
                            "plan.dpa_state", listVO.getDpaState());
        } else {
            queryWrapper.in("unit.id", ids);
        }
        queryWrapper.orderBy(true, SortTypeEnum.ASC.getSortType().equals(listVO.getSortType()), "unit.id")
                .groupBy("unit.id");
        return queryWrapper;
    }


    /**
     * 素材条件过滤
     *
     * @param queryWrapper      条件
     * @param masterPageFirstVO 返回数据
     */
    private void fillAssetToWrapper(QueryWrapper<CreativeUnit> queryWrapper, MasterPageFirstVO masterPageFirstVO) {
        if (StringUtils.isBlank(masterPageFirstVO.getAssetId())) {
            return;
        }
        List<Long> creativeUnitIds = this.baseMapper.listUnitByAssetId(new QueryWrapper<CreativeUnit>()
                        .eq("mar.asset_id", masterPageFirstVO.getAssetId())
                        .in("mar.master_id", masterPageFirstVO.getMasterId())
                        .eq("mar.is_del", IsDelEnum.NORMAL.getId())
                        .eq("mcu.is_del", IsDelEnum.NORMAL.getId())
                        .eq("mma.field_type", 1))
                .stream().map(CreativeUnit::getId).collect(Collectors.toList());
        queryWrapper.in("unit.id", CollectionUtils.isNotEmpty(creativeUnitIds) ? creativeUnitIds : List.of(0));
    }

    /**
     * 将 创意列表的尺寸放入创意筛选中
     *
     * @param queryWrapper      条件
     * @param masterPageFirstVO 条件
     */
    private void fillSizeToWrapper(QueryWrapper<CreativeUnit> queryWrapper, MasterPageFirstVO masterPageFirstVO) {
        if (CollectionUtils.isEmpty(masterPageFirstVO.getAssetSizes())) {
            return;
        }
        //获取符合素材分组信息
        List<Long> assetIds = this.fillSizeToAssetIds(masterPageFirstVO.getAssetSizes(), masterPageFirstVO.getAssetType(), masterPageFirstVO.getMasterId());
        //获取符合素材分组信息
        if (CollectionUtils.isEmpty(assetIds)) {
            throw new CustomException("无符合尺寸素材");
        }
        List<Long> materialIds = materialAssetMapper.getMaterialIdsByInfo(new LambdaQueryWrapper<MaterialAsset>()
                .in(MaterialAsset::getAssetId, assetIds));
        if (CollectionUtils.isEmpty(materialIds)) {
            throw new CustomException("无符合尺寸素材组");
        }
        queryWrapper.in("material_id", CollectionUtils.isEmpty(materialIds) ? List.of(0) : materialIds);
    }

    /**
     * 获取创意单元数据
     *
     * @param assetSizes 尺寸
     * @param assetType  类型
     * @param masterId   账户
     * @return 返回数据
     */
    private List<Long> fillSizeToAssetIds(List<String> assetSizes, Integer assetType, Long masterId) {
        if (CollectionUtils.isEmpty(assetSizes)) {
            return null;
        }
        List<List<Integer>> sizeList;
        try {
            sizeList = assetSizes.stream()
                    .map(u -> Arrays.stream(u.split("[*]")).map(Integer::parseInt).collect(Collectors.toList()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw new CustomException("素材尺寸输入错误");
        }
        if (CollectionUtils.isEmpty(sizeList)) {
            throw new CustomException("素材尺寸无法获取到创意");
        }
        //获取符合素材资产信息
        return assetMapper.getAssetIdsByInfo(new QueryWrapper<Asset>()
                .eq(ObjectUtils.isNotNullOrZero(assetType), "ma.asset_type", assetType)
                .eq(ObjectUtils.isNotNullOrZero(masterId), "mar.master_id", masterId)
                .inSql("(ma.width,ma.height)",
                        sizeList.stream().map(u -> String.format("(%s,%s)", u.get(0), u.get(1))).collect(Collectors.joining(","))
                ).eq("ma.is_del", IsDelEnum.NORMAL.getId())
                .eq("mar.is_del", IsDelEnum.NORMAL.getId())
        );
    }

}
