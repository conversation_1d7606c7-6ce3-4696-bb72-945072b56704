package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.service.market.entity.Creative;
import com.overseas.service.market.entity.Ctx;
import com.overseas.service.market.events.notifyControl.ControlCampaignEvent;
import com.overseas.service.market.events.notifyControl.ControlContants;
import com.overseas.common.dto.CascaderDTO;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.ctx.CtxGetDTO;
import com.overseas.common.dto.market.ctx.CtxListDTO;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.ctx.CtxGetVO;
import com.overseas.common.vo.market.ctx.CtxListVO;
import com.overseas.common.vo.market.ctx.CtxSaveVO;
import com.overseas.common.vo.market.ctx.CtxUpdateVO;
import com.overseas.service.market.mapper.CreativeMapper;
import com.overseas.service.market.mapper.CtxMapper;
import com.overseas.service.market.service.CtxService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CtxServiceImpl extends ServiceImpl<CtxMapper, Ctx> implements CtxService {

    private final CreativeMapper creativeMapper;

    private final ApplicationContext applicationContext;

    @Override
    public List<SelectDTO> selectCtx() {
        List<SelectDTO> result = new ArrayList<>();
        QueryWrapper<Ctx> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del", IsDelEnum.NORMAL.getId())
                .select("id", "ctx_name", "country").orderByAsc("id");
        List<Ctx> ctxes = baseMapper.selectList(queryWrapper);
        ctxes.forEach(adx -> result.add(new SelectDTO(adx.getId().longValue(), adx.getCtxName() + "(" + adx.getCountry() + ")")));
        return result;
    }

    @Override
    public void saveCtx(CtxSaveVO saveVO, Integer userId) {

        long count = this.baseMapper.selectCount(new QueryWrapper<Ctx>().lambda()
                .eq(Ctx::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(Ctx::getCountryId, saveVO.getCountryId())
                .eq(Ctx::getCtxName, saveVO.getCtxName()));
        if (count > 0) {
            throw new CustomException("当前国家下该行动号召已存在");
        }

        Ctx ctx = new Ctx();
        BeanUtils.copyProperties(saveVO, ctx);
        ctx.setCreateUid(userId);
        this.baseMapper.insert(ctx);
    }

    @Override
    public void updateCtx(CtxUpdateVO updateVO, Integer userId) {

        long count = this.baseMapper.selectCount(new QueryWrapper<Ctx>().lambda()
                .eq(Ctx::getIsDel, IsDelEnum.NORMAL.getId())
                .ne(Ctx::getId, updateVO.getId())
                .eq(Ctx::getCountryId, updateVO.getCountryId())
                .eq(Ctx::getCtxName, updateVO.getCtxName()));
        if (count > 0) {
            throw new CustomException("当前国家下该行动号召已存在");
        }

        Ctx ctx = new Ctx();
        ctx.setCtxName(updateVO.getCtxName());
        ctx.setTranslatedText(updateVO.getTranslatedText());
        ctx.setUpdateUid(userId);
        this.baseMapper.update(ctx, new QueryWrapper<Ctx>().lambda().eq(Ctx::getId, updateVO.getId()));

        // 获取需要重推的活动ID
        List<Long> campaignIds = this.creativeMapper.selectList(new QueryWrapper<Creative>().lambda()
                        .eq(Creative::getIsDel, IsDelEnum.NORMAL.getId())
                        .eq(Creative::getCtaId, updateVO.getId()))
                .stream().map(Creative::getCampaignId).distinct().collect(Collectors.toList());

        if (campaignIds.isEmpty()) {
            return;
        }
        campaignIds.forEach(campaignId -> this.applicationContext.publishEvent(new ControlCampaignEvent(this, ControlContants.METHOD_UPDATE, campaignId)));
    }

    @Override
    public void deleteCtx(CtxGetVO getVO, Integer userId) {

        // 先查询当前行动号召是否关联了创意
        long count = this.creativeMapper.selectCount(new QueryWrapper<Creative>().lambda()
                .eq(Creative::getCtaId, getVO.getId())
                .eq(Creative::getIsDel, IsDelEnum.NORMAL.getId()));

        if (count > 0) {
            throw new CustomException("该行动号召已关联了创意，无法删除");
        }

        Ctx ctx = new Ctx();
        ctx.setIsDel(IsDelEnum.DELETE.getId().intValue());
        ctx.setUpdateUid(userId);
        this.baseMapper.update(ctx, new QueryWrapper<Ctx>().lambda().eq(Ctx::getId, getVO.getId()));
    }

    @Override
    public CtxGetDTO getCtx(CtxGetVO getVO) {
        CtxGetDTO ctx = this.baseMapper.getCtx(new QueryWrapper<Ctx>()
                .eq("ctx.is_del", IsDelEnum.NORMAL.getId())
                .eq("ctx.id", getVO.getId()));
        if (ctx == null) {
            throw new CustomException("该行动号召不存在，请确认后再试");
        }
        return ctx;
    }

    @Override
    public PageUtils<CtxListDTO> listCtx(CtxListVO listVO) {

        return new PageUtils<>(this.baseMapper.listCtxPage(new Page<>(listVO.getPage(), listVO.getPageNum()), new QueryWrapper<Ctx>()
                .eq("ctx.is_del", IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getCountryId()), "ctx.country_id", listVO.getCountryId())
                .and(StringUtils.isNotEmpty(listVO.getSearch()), q -> q
                        .like("ctx.id", listVO.getSearch())
                        .or().like("ctx.ctx_name", listVO.getSearch())
                        .or().like("ctx.translated_text", listVO.getSearch()))
                .orderByDesc("ctx.id")));
    }

    @Override
    public List<CascaderDTO> listCtxCascader() {

        List<CtxGetDTO> ctxList = this.baseMapper.listCtx(new QueryWrapper<Ctx>()
                .eq("ctx.is_del", IsDelEnum.NORMAL.getId())
                .orderByAsc("ctx.id"));
        if (ctxList.isEmpty()) {
            return List.of();
        }
        Map<Long, List<CtxGetDTO>> ctxMap = ctxList.stream().collect(Collectors.groupingBy(CtxGetDTO::getCountryId));
        List<CascaderDTO> cascaderDTOS = new ArrayList<>();
        ctxMap.forEach((countryId, list) -> {
            CascaderDTO cascaderDTO = new CascaderDTO();
            cascaderDTO.setValue(countryId);
            cascaderDTO.setLabel(list.get(0).getCountryName());
            cascaderDTO.setChildren(list.stream().map(entity -> {
                CascaderDTO cascader = new CascaderDTO();
                cascader.setValue(entity.getId());
                cascader.setLabel(entity.getCtxName() + (StringUtils.isNotBlank(entity.getTranslatedText())
                        ? "（" + entity.getTranslatedText() + "）" : ""));
                return cascader;
            }).collect(Collectors.toList()));
            cascaderDTOS.add(cascaderDTO);
        });
        return cascaderDTOS;
    }

    @Override
    public List<SelectDTO> selectCountry() {

        return this.baseMapper.selectCtxCountry(new QueryWrapper<Ctx>()
                .eq("ctx.is_del", IsDelEnum.NORMAL.getId())
                .ne("ctx.country_id", 0)
                .groupBy("ctx.country_id")
                .orderByAsc("ctx.country_id"));
    }
}
