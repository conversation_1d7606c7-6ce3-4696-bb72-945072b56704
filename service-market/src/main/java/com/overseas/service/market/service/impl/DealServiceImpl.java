package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.market.deal.*;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.enums.market.DealTypeEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.deal.*;
import com.overseas.service.market.entity.Deal;
import com.overseas.service.market.entity.Plan;
import com.overseas.service.market.enums.StatusEnum;
import com.overseas.service.market.mapper.DealMapper;
import com.overseas.service.market.mapper.PlanMapper;
import com.overseas.service.market.service.DealService;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class DealServiceImpl extends ServiceImpl<DealMapper, Deal> implements DealService {

    private PlanMapper planMapper;

    @Override
    public Deal saveDeal(DealSaveVO dealSaveVO, Integer loginUserId) {
        // 检查名称和dealId是否重复
        this.isDealRepeated(dealSaveVO.getDealName(), dealSaveVO.getDealId(), 0);

        Deal deal = new Deal();
        BeanUtils.copyProperties(dealSaveVO, deal);
        deal.setCreateUid(loginUserId);
        this.baseMapper.insert(deal);
        return deal;
    }

    @Override
    public DealGetDTO getDeal(DealGetVO dealGetVO) {
        Deal deal = this.baseMapper.selectOne(new QueryWrapper<Deal>().lambda()
                .eq(Deal::getId, dealGetVO.getId()).eq(Deal::getMasterId, dealGetVO.getMasterId())
        );
        if (null == deal) {
            throw new CustomException("deal id不存在");
        }
        DealGetDTO dealGetDTO = new DealGetDTO();
        BeanUtils.copyProperties(deal, dealGetDTO);
        return dealGetDTO;
    }

    @Override
    public Integer updateDeal(DealUpdateVO dealUpdateVO, Integer loginUserId) {
        // 检查名称和dealId是否重复
        this.isDealRepeated(dealUpdateVO.getDealName(), dealUpdateVO.getDealId(), dealUpdateVO.getId());

        Deal deal = new Deal();
        BeanUtils.copyProperties(dealUpdateVO, deal);
        deal.setUpdateUid(loginUserId);
        return this.baseMapper.update(deal, new UpdateWrapper<Deal>().lambda().eq(Deal::getId, dealUpdateVO.getId()));
    }

    @Override
    public Integer deleteDeal(DealGetVO dealGetVO, Integer loginUserId) {
        DealGetDTO dealGetDTO = this.getDeal(dealGetVO);
        Long orderNums = this.planMapper.selectCount(new QueryWrapper<Plan>().lambda()
                .eq(Plan::getDealId, dealGetDTO.getDealId()).eq(Plan::getIsDel, IsDelEnum.NORMAL.getId())
        );
        if (orderNums > 0) {
            throw new CustomException("deal id已被计划引用不可删除");
        }
        Deal deal = new Deal();
        deal.setUpdateUid(loginUserId);
        deal.setIsDel(IsDelEnum.DELETE.getId());
        return this.baseMapper.update(deal, new UpdateWrapper<Deal>().lambda()
                .eq(Deal::getId, dealGetVO.getId()).eq(Deal::getMasterId, dealGetVO.getMasterId())
        );
    }

    @Override
    public PageUtils<?> listDeal(DealListVO dealListVO) {
        QueryWrapper<Deal> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deal.is_del", IsDelEnum.NORMAL.getId()).orderByDesc("deal.create_time")
                .lambda().eq(Deal::getMasterId, dealListVO.getMasterId())
                .eq(ObjectUtils.isNotNullOrZero(dealListVO.getAdxId()), Deal::getAdxId, dealListVO.getAdxId())
                .eq(ObjectUtils.isNotNullOrZero(dealListVO.getDealType()), Deal::getDealType, dealListVO.getDealType())
                .eq(StringUtils.isNotBlank(dealListVO.getSearch()), Deal::getDealId, dealListVO.getSearch());
        IPage<DealListDTO> page = this.baseMapper.listDeal(
                new Page<>(dealListVO.getPage(), dealListVO.getPageNum()), queryWrapper);
        for (DealListDTO dealListDTO : page.getRecords()) {
            dealListDTO.setDealTypeName(ICommonEnum.getNameById(dealListDTO.getDealType(), DealTypeEnum.class));
        }
        return new PageUtils<>(page);
    }

    @Override
    public Integer switchDeal(DealSwitchVO dealSwitchVO, Integer loginUserId) {
        Deal deal = new Deal();
        deal.setUpdateUid(loginUserId);
        deal.setDealStatus(dealSwitchVO.getDealStatus());
        return this.baseMapper.update(deal, new UpdateWrapper<Deal>().lambda()
                .eq(Deal::getId, dealSwitchVO.getId()).eq(Deal::getMasterId, dealSwitchVO.getMasterId())
        );
    }

    @Override
    public List<DealSelectDTO> selectDeal(DealSelectVO dealSelectVO) {
        QueryWrapper<Deal> queryWrapper = new QueryWrapper<>();
        //增加指定deal id
        queryWrapper.and(q -> q.eq("deal.is_del", IsDelEnum.NORMAL.getId()).lambda()
                        .eq(Deal::getDealStatus, StatusEnum.ENABLE.getId())
                        .eq(Deal::getMasterId, dealSelectVO.getMasterId())
                        .eq(ObjectUtils.isNotNullOrZero(dealSelectVO.getDealType()),
                                Deal::getDealType, dealSelectVO.getDealType())
                ).or(StringUtils.isNotBlank(dealSelectVO.getDealId()), q -> q.eq("deal.deal_id", dealSelectVO.getDealId()))
                .orderByDesc("deal.id");
        return this.baseMapper.selectDeal(queryWrapper);
    }

    /**
     * 检查dealName或DealId是否重复
     *
     * @param dealName deal别名
     * @param dealId   dealId内容
     * @param id       记录id
     */
    private void isDealRepeated(String dealName, String dealId, Integer id) {
        Deal dealInDb = this.baseMapper.selectOne(
                new QueryWrapper<Deal>().lambda().eq(Deal::getIsDel, IsDelEnum.NORMAL.getId())
                        .and(i -> i.eq(Deal::getDealName, dealName).or().eq(Deal::getDealId, dealId))
                        .ne(ObjectUtils.isNotNullOrZero(id), Deal::getId, id)
        );
        if (null != dealInDb) {
            if (dealInDb.getDealId().equals(dealId) && dealInDb.getDealName().equals(dealName)) {
                throw new CustomException("名称和deal id已存在");
            } else if (dealInDb.getDealId().equals(dealId)) {
                throw new CustomException("deal id已存在");
            } else {
                throw new CustomException("名称已存在");
            }
        }
    }
}
