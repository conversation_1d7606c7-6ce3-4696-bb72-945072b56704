package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.overseas.common.dto.market.template.TemplateSelectDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.service.market.dto.ep.EpListDTO;
import com.overseas.service.market.entity.*;
import com.overseas.service.market.enums.adx.AdxStatusEnum;
import com.overseas.service.market.enums.template.TemplateIsAutoLinkEnum;
import com.overseas.service.market.enums.template.TemplateIsShakeEnum;
import com.overseas.service.market.enums.template.TemplateTouchStartEnum;
import com.overseas.service.market.mapper.*;
import com.overseas.service.market.service.EpService;
import com.overseas.service.market.vo.ep.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class EpServiceImpl implements EpService {

    private final AdxMapper adxMapper;

    private final EpMapper epMapper;

    private final PlanMapper planMapper;

    private final SlotMapper slotMapper;

    private final PlanDirectMapper planDirectMapper;

    private final TemplateConfigMapper templateConfigMapper;

    private final TemplateResourceMapper templateResourceMapper;

    @Override
    public PageUtils<?> list(EpListVO listVO) {
        IPage<EpListDTO> iPage = new Page<>(listVO.getPage(), listVO.getPageNum());
        iPage = epMapper.list(iPage, new QueryWrapper<>()
                .eq(ObjectUtils.isNotNullOrZero(listVO.getAdxId()), "ep.adx_id", listVO.getAdxId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getAdxStatus()), "adx.adx_status", listVO.getAdxStatus())
                .and(StringUtils.isNotBlank(listVO.getSearch()), q -> q.like("ep.path", listVO.getSearch())
                        .or().like("ep.ep_name", listVO.getSearch())
                )
                .eq("ep.is_del", IsDelEnum.NORMAL.getId())
                .eq("adx.is_del", IsDelEnum.NORMAL.getId())
                .orderByDesc("ep.id")
        );
        if (CollectionUtils.isEmpty(iPage.getRecords())) {
            return new PageUtils<>(iPage);
        }
        Map<Long, TemplateSelectDTO> templateSelectMap = templateConfigMapper.getTemplateSelect(new LambdaQueryWrapper<>())
                .stream().collect(Collectors.toMap(TemplateSelectDTO::getId, Function.identity()));
        Map<Long, List<TemplateResource>> templateResourceMap = templateResourceMapper.getByResourceAndType(
                iPage.getRecords().stream().map(EpListDTO::getId).collect(Collectors.toList()), 2
        ).stream().collect(Collectors.groupingBy(TemplateResource::getResourceId));
        iPage.getRecords().forEach(ep -> {
            ep.setAdxStatusName(ICommonEnum.getNameById(ep.getAdxStatus(), AdxStatusEnum.class));
            ep.setIsShakeName(ICommonEnum.getNameById(ep.getIsShake(), TemplateIsShakeEnum.class));
            ep.setIsAutoLinkName(ICommonEnum.getNameById(ep.getIsAutoLink(), TemplateIsAutoLinkEnum.class));
            ep.setIsTouchstartName(ICommonEnum.getNameById(ep.getIsTouchstart(), TemplateTouchStartEnum.class));
            ep.setAdxTemplates(templateResourceMap.getOrDefault(ep.getId(), new ArrayList<>())
                    .stream().map(u -> templateSelectMap.getOrDefault(u.getTemplateId(), null))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList())
            );
        });
        return new PageUtils<>(iPage);
    }

    @Override
    public void save(EpSaveVO saveVO, Integer operateUid) {
        this.checkEp(saveVO.getEpName(), saveVO.getPath(), null);
        Ep ep = new Ep();
        BeanUtils.copyProperties(saveVO, ep);
        ep.setCreateUid(operateUid);
        ep.setEpGroup(ep.getAdxId().intValue());
        this.epMapper.insert(ep);
    }

    @Override
    public Ep get(EpGetVO getVO) {
        Ep ep = this.epMapper.selectById(getVO.getId());
        if (null == ep || IsDelEnum.DELETE.getId().intValue() == ep.getIsDel()) {
            throw new CustomException("EP 不存在");
        }
        return ep;
    }

    @Override
    public void update(EpUpdateVO updateVO, Integer operateUid) {
        this.checkEp(updateVO.getEpName(), "", updateVO.getId());
        Ep oldEp = this.epMapper.selectById(updateVO.getId());
        if (null == oldEp) {
            throw new CustomException("EP信息不存在");
        }
        throw new CustomException("不允许修改EP信息！");
    }

    @Override
    public void bind(EpBindVO bindVO, Integer operateUid) {
        bindVO.setAdxTemplates(bindVO.getAdxTemplates().stream().distinct().collect(Collectors.toList()));
        List<TemplateResource> templateResources = this.epMapper.selectBatchIds(bindVO.getEpIds())
                .stream().flatMap(ep ->
                        bindVO.getAdxTemplates().stream().map(templateId -> {
                            TemplateResource templateResource = new TemplateResource();
                            templateResource.setResourceId(ep.getId());
                            templateResource.setResourceType(2);
                            templateResource.setTemplateId(templateId);
                            templateResource.setCreateUid(operateUid);
                            return templateResource;
                        })
                ).collect(Collectors.toList());
        if (!templateResources.isEmpty()) {
            this.templateResourceMapper.batchInsert(templateResources);
        }
    }

    @Override
    public void unbind(EpUnbindVO unbindVO, Integer operateUid) {
        Ep ep = this.epMapper.selectById(unbindVO.getEpId());
        if (null == ep) {
            throw new CustomException("Ep信息不存在");
        }
        String planIds = planMapper.listAdxDirect(new QueryWrapper<>()
                        .in("p.template_id", unbindVO.getAdxTemplates())
                        .eq("p.is_del", IsDelEnum.NORMAL.getId())
                        .eq("c.is_del", IsDelEnum.NORMAL.getId())
                        .like("direct.direct_value", unbindVO.getEpId()), 1032)
                .stream().filter(direct -> {
                    List<Long> directValues = JSONObject.parseArray(direct.getDirectValue(), Long.class);
                    return directValues.contains(unbindVO.getEpId());
                }).map(u -> u.getPlanId().toString()).collect(Collectors.joining(","));
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(planIds)) {
            throw new CustomException("白名单减少的模板已被使用，相关计划ID：" + planIds);
        }
        TemplateResource templateResource = new TemplateResource();
        templateResource.setIsDel(IsDelEnum.DELETE.getId().intValue());
        templateResource.setUpdateUid(operateUid);
        this.templateResourceMapper.update(templateResource, new LambdaQueryWrapper<TemplateResource>()
                .eq(TemplateResource::getResourceId, unbindVO.getEpId())
                .in(TemplateResource::getTemplateId, unbindVO.getAdxTemplates())
                .eq(TemplateResource::getResourceType, 2)
        );
    }

    @Override
    public void del(EpDeleteVO deleteVO, Integer operateUid) {
        Ep del = new Ep();
        del.setIsDel(IsDelEnum.DELETE.getId().intValue());
        del.setUpdateUid(operateUid);
        this.epMapper.update(del, new LambdaQueryWrapper<Ep>().eq(Ep::getId, deleteVO.getId()));
    }

    @Override
    public void checkEpTemplate(List<Long> epIds, List<Long> templateIds) {
//        if (CollectionUtils.isEmpty(epIds) || CollectionUtils.isEmpty(templateIds)
//                || templateIds.stream().filter(ObjectUtils::isNotNullOrZero).count() > 1) {
//            return;
//        }
//        List<Ep> epList = this.epMapper.selectBatchIds(epIds);
//        if (CollectionUtils.isEmpty(epIds)) {
//            throw new CustomException("Ep信息不存在");
//        }
//        List<TemplateConfig> templates = templateConfigMapper.selectList(new LambdaQueryWrapper<TemplateConfig>()
//                .in(TemplateConfig::getId, templateIds)
//                .groupBy(TemplateConfig::getIsAutoLink)
//                .groupBy(TemplateConfig::getIsShake)
//        );
//        if (templates.size() > 1) {
//            throw new CustomException("修改计划中含有模板AutoLink和摇一摇属性不相同");
//        }
//        TemplateConfig template = templates.get(0);
//        if (epList.stream().anyMatch(ep -> {
//            if (template.getIsAutoLink().equals(1) && !ep.getIsAutoLink().equals(template.getIsAutoLink())) {
//                return true;
//            }
//            if (template.getIsShake().equals(1) && !ep.getIsShake().equals(template.getIsShake())) {
//                return true;
//            }
//            return false;
//        })) {
//            throw new CustomException("当前设置的EP中含有与模板AutoLink和摇一摇属性不同");
//        }
//        Map<Long, List<TemplateResource>> templateResourceMap = templateResourceMapper.getByResourceAndType(epIds, 2)
//                .stream().collect(Collectors.groupingBy(TemplateResource::getResourceId));
//        String notAllowAdx = epList.stream().map(ep -> {
//            List<Long> adxTemplateIds = templateResourceMap.getOrDefault(ep.getId(), new ArrayList<>())
//                    .stream().map(TemplateResource::getTemplateId).collect(Collectors.toList());
//            if (CollectionUtils.isEmpty(adxTemplateIds)) {
//                return null;
//            }
//            if (new HashSet<>(adxTemplateIds).containsAll(templateIds)) {
//                return null;
//            }
//            return ep.getEpName();
//        }).filter(Objects::nonNull).collect(Collectors.joining(","));
//        if (StringUtils.isNotBlank(notAllowAdx)) {
//            throw new CustomException("Ep（" + notAllowAdx + "）不支持选中模板使用");
//        }
    }


    @Override
    public void updateEpDirectByAdx(Integer adxId) {
//        this.addSlot(adxId);
        log.info("update adx direct, adx id : {}", adxId);
        Adx adx = this.adxMapper.selectById(adxId);
        if (null == adx) {
            log.info("update adx direct, 不支持该adx");
            throw new CustomException("不支持该adx");
        }
        // 获取要更改的adx下ep映射关系
        List<Ep> growAdxEps = this.epMapper.selectList(new QueryWrapper<Ep>().lambda().eq(Ep::getEpGroup, adxId));
        if (growAdxEps.isEmpty()) {
            log.info("update adx direct, 无此ADX在GrowADX下EP映射关系");
            throw new CustomException("无此ADX在GrowADX下EP映射关系");
        }
        Map<Long, Long> growAdxEpMap = growAdxEps.stream().collect(Collectors.toMap(Ep::getAdxEp, Ep::getId, (o, n) -> o));

        // 查询包含了次ADX定向的计划
        List<PlanDirect> planAdxDirects = this.planDirectMapper.selectList(new QueryWrapper<PlanDirect>().lambda()
                .eq(PlanDirect::getDirectId, 1031).like(PlanDirect::getDirectValue, adxId)
        );
        planAdxDirects.forEach(planDirect -> {
            log.info("update adx direct, plan id : {}, update start", planDirect.getPlanId());
            log.info("update adx direct, plan id : {}, adx old direct : {}", planDirect.getPlanId(),
                    planDirect.getDirectValue());
            List<Integer> adxIds = JSONObject.parseArray(planDirect.getDirectValue(), Integer.class);
            if (adxIds.contains(adxId)) {
                PlanDirect epDirect = this.planDirectMapper.selectOne(new LambdaQueryWrapper<PlanDirect>()
                        .eq(PlanDirect::getDirectId, 1032)
                        .eq(PlanDirect::getPlanId, planDirect.getPlanId())
                );
                if (null != epDirect) {
                    log.info("update adx direct, plan id : {}, ep old direct : {}", epDirect.getPlanId(),
                            epDirect.getDirectValue());
                    List<Long> epIds = JSONObject.parseArray(epDirect.getDirectValue(), Long.class);
                    List<Long> growAdxEpIds = new ArrayList<>();
                    epIds.forEach(epId -> {
                        Long growAdxEpId = growAdxEpMap.get(epId);
                        if (null != growAdxEpId) {
                            growAdxEpIds.add(growAdxEpId);
                        }
                    });
                    adxIds.add(162);
                    epIds.addAll(growAdxEpIds);
                    planDirect.setDirectValue(
                            JSONObject.toJSONString(adxIds.stream().distinct().collect(Collectors.toList())));
                    epDirect.setDirectValue(
                            JSONObject.toJSONString(epIds.stream().distinct().collect(Collectors.toList())));
                    log.info("update adx direct plan id : {}, adx new direct : {}", planDirect.getPlanId(),
                            planDirect.getDirectValue());
                    log.info("update adx direct plan id : {}, ep new direct : {}", epDirect.getPlanId(),
                            epDirect.getDirectValue());
                    this.planDirectMapper.updateById(planDirect);
                    this.planDirectMapper.updateById(epDirect);
                    return;
                } else {
                    log.info("update adx direct, plan id : {} not direct ep.", planDirect.getPlanId());
                }
            } else {
                log.info("update adx direct, plan id : {} not direct {} adx id.", planDirect.getPlanId(), adxId);
            }
        });
    }

    private void addSlot(Integer adxId) {
        List<PlanDirect> directs = this.planDirectMapper.selectList(new QueryWrapper<PlanDirect>()
                .lambda().ne(PlanDirect::getDirectValue, "[]").eq(PlanDirect::getDirectId, 1026));
        directs.forEach(planDirect -> {
            List<Long> sIds = JSONObject.parseArray(planDirect.getDirectValue(), Long.class);
            log.info("old slot ids : {}", JSONObject.toJSONString(sIds));
            List<Slot> slots = this.slotMapper.selectList(new QueryWrapper<Slot>()
                    .lambda().in(Slot::getId, sIds).eq(Slot::getAdxId, 157));
            if (!slots.isEmpty()) {
                List<Slot> newSlots = this.slotMapper.selectList(new QueryWrapper<Slot>()
                        .lambda().in(Slot::getAdxSlotId, slots.stream().map(Slot::getAdxSlotId).collect(Collectors.toList()))
                        .eq(Slot::getAdxId, 162));
                List<Long> slotIds = newSlots.stream().map(Slot::getId).collect(Collectors.toList());
                log.info("new slot ids : {}", JSONObject.toJSONString(slotIds));
                sIds.addAll(slotIds);
                log.info("all slot ids : {}", JSONObject.toJSONString(sIds));
                planDirect.setDirectValue(JSONObject.toJSONString(sIds.stream().distinct().collect(Collectors.toList())));
                this.planDirectMapper.updateById(planDirect);
            }
            log.info("=========");
        });
    }

    private void deleteEP(Integer adxId) {
        List<Ep> eps = this.epMapper.selectList(new QueryWrapper<Ep>().lambda().eq(Ep::getAdxId, adxId));
        List<PlanDirect> planDirects = this.planDirectMapper.selectList(new QueryWrapper<PlanDirect>().lambda()
                .eq(PlanDirect::getDirectId, 1031).like(PlanDirect::getDirectValue, 158));
        List<PlanDirect> planEpDirects = this.planDirectMapper.selectList(new QueryWrapper<PlanDirect>().lambda()
                .in(PlanDirect::getPlanId, planDirects.stream().map(PlanDirect::getPlanId).collect(Collectors.toList()))
                .eq(PlanDirect::getDirectId, 1032)
        );
        List<Long> epIds = eps.stream().map(Ep::getId).collect(Collectors.toList());
        epIds.add(0L);
        planEpDirects.forEach(planDirect -> {
            List<Long> directs = JSONObject.parseArray(planDirect.getDirectValue(), Long.class);
            log.info("ep directs : {}", directs);
            directs.removeAll(epIds);
            if (directs.isEmpty()) {
                directs.add(0L);
            }
            log.info("left ep directs : {}", directs);
            planDirect.setDirectValue(JSONObject.toJSONString(directs));
            this.planDirectMapper.updateById(planDirect);
        });
        planDirects.forEach(planDirect -> {
            List<Long> directs = JSONObject.parseArray(planDirect.getDirectValue(), Long.class);
            log.info("directs : {}", directs);
            if (directs.contains(158L)) {
                directs.remove(158L);
                if (directs.isEmpty()) {
                    directs.add(0L);
                }
                log.info("left directs : {}", directs);
                planDirect.setDirectValue(JSONObject.toJSONString(directs));
                this.planDirectMapper.updateById(planDirect);
            }
        });
    }

    /**
     * 校验EP唯一
     *
     * @param epName ep名称
     * @param path   ep路径
     * @param id     id
     */
    private void checkEp(String epName, String path, Long id) {
        Ep ep = this.epMapper.selectOne(new LambdaQueryWrapper<Ep>()
                .and(q -> q.eq(Ep::getEpName, epName)
                        .or().eq(Ep::getPath, path)
                ).ne(ObjectUtils.isNotNullOrZero(id), Ep::getId, id)
                .eq(Ep::getIsDel, IsDelEnum.NORMAL.getId())
                .last("limit 1")
        );
        if (null != ep) {
            if (ep.getEpName().equals(epName)) {
                throw new CustomException("Ep名称已存在，请修改后再试");
            }
            if (ep.getPath().equals(path)) {
                throw new CustomException("Ep路径已存在，请修改后再试");
            }
            throw new CustomException("EP已存在，请修改后再试");
        }
    }
}
