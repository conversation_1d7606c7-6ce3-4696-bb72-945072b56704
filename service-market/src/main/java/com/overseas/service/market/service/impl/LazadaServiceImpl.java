package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.lazada.lazop.api.LazopClient;
import com.lazada.lazop.api.LazopRequest;
import com.lazada.lazop.api.LazopResponse;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.common.BaseExcelDTO;
import com.overseas.common.dto.market.cps.lazada.*;
import com.overseas.common.enums.CpsProjectEnum;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.*;
import com.overseas.service.market.dto.cps.order.CpsOrderLazadaResponseDTO;
import com.overseas.service.market.dto.lazada.*;
import com.overseas.service.market.dto.lazada.topProduct.LazadaErrorInfoDTO;
import com.overseas.service.market.dto.lazada.topProduct.LazadaTopProductDataDTO;
import com.overseas.service.market.dto.lazada.topProduct.LazadaTopProductUrlDTO;
import com.overseas.service.market.entity.ChannelAccount;
import com.overseas.service.market.entity.CountryAll;
import com.overseas.service.market.entity.CpsOrder;
import com.overseas.service.market.entity.CpsOrderLazada;
import com.overseas.service.market.entity.ae.AeApp;
import com.overseas.service.market.entity.ae.AeAuthorization;
import com.overseas.service.market.entity.cps.CpsProduct;
import com.overseas.service.market.entity.cps.CpsProductCategory;
import com.overseas.service.market.entity.cps.CpsProductLazada;
import com.overseas.service.market.entity.cps.CpsProductMaterial;
import com.overseas.service.market.entity.lazada.TopProduct;
import com.overseas.service.market.enums.cps.CpsLazadaProductTypeEnum;
import com.overseas.service.market.enums.cps.CpsMaterialTypeEnum;
import com.overseas.service.market.enums.lazada.LazadaAppEnum;
import com.overseas.service.market.mapper.*;
import com.overseas.service.market.mapper.ae.AeAppMapper;
import com.overseas.service.market.mapper.ae.AeAuthorizationMapper;
import com.overseas.service.market.mapper.cps.CpsProductCategoryMapper;
import com.overseas.service.market.mapper.cps.CpsProductLazadaMapper;
import com.overseas.service.market.mapper.cps.CpsProductMapper;
import com.overseas.service.market.mapper.cps.CpsProductMaterialMapper;
import com.overseas.service.market.mapper.lazada.TopProductMapper;
import com.overseas.service.market.service.LazadaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class LazadaServiceImpl implements LazadaService {

    private final CpsProductMapper cpsProductMapper;

    private final CpsProductMaterialMapper cpsProductMaterialMapper;

    private final CpsProductLazadaMapper cpsProductLazadaMapper;

    private final AeAuthorizationMapper aeAuthorizationMapper;

    private final AeAppMapper aeAppMapper;

    private final CpsProductCategoryMapper cpsProductCategoryMapper;

    private final CpsOrderMapper cpsOrderMapper;

    private final TopProductMapper topProductMapper;

    private final CpsOrderLazadaMapper cpsOrderLazadaMapper;

    private final CountryAllMapper countryAllMapper;

    private final ChannelAccountMapper channelAccountMapper;

    @Override
    public void pullCategories() {
        List<AeAuthorization> aeAuthorization = getAuth();
        if (aeAuthorization.isEmpty()) {
            return;
        }
        aeAuthorization.forEach(auth -> {
            try {
                pullCategory(auth);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
    }

    @Override
    public void pullProducts() {
        List<AeAuthorization> aeAuthorization = getAuth();
        if (aeAuthorization.isEmpty()) {
            return;
        }
        aeAuthorization.forEach(auth -> {
            try {
                AeApp aeApp = aeAppMapper.selectById(auth.getAppId());
                if (null == aeApp) {
                    return;
                }
                this.pullInfo(auth, aeApp, null);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
    }

    @Override
    public void pullProductsByCpsOrder() {
        List<CpsOrder> cpsOrders = cpsOrderMapper.selectList(new LambdaQueryWrapper<CpsOrder>()
                .eq(CpsOrder::getProductStatus, 0)
                .eq(CpsOrder::getProjectId, CpsProjectEnum.LAZADA.getId())
                .groupBy(CpsOrder::getProductId)
        );
        Map<String, List<String>> map = new HashMap<>();
        cpsOrders.forEach(cpsOrder -> map.computeIfAbsent(cpsOrder.getCountry(), k -> new ArrayList<>()).add(cpsOrder.getProductId()));
        map.forEach((country, productIds) -> {
            try {
                AeAuthorization aeAuthorization = aeAuthorizationMapper.selectOne(new LambdaQueryWrapper<AeAuthorization>()
                        .eq(AeAuthorization::getAccountId, country.toLowerCase())
                        .eq(AeAuthorization::getProjectId, CpsProjectEnum.LAZADA.getId())
                );
                if (null == aeAuthorization) {
                    return;
                }
                AeApp aeApp = aeAppMapper.selectById(aeAuthorization.getAppId());
                for (int i = 0; i < Math.ceil((double) productIds.size() / 15); i++) {
                    List<String> search = productIds.subList(i * 15, Math.min((i + 1) * 15, productIds.size()));
                    if (search.isEmpty()) {
                        break;
                    }
                    List<String> productInfo = this.pullInfo(aeAuthorization, aeApp, search);
                    search.forEach(u -> {
                        CpsOrder update = new CpsOrder();
                        if (productInfo.contains(u)) {
                            update.setProductStatus(2);
                        } else {
                            update.setProductStatus(1);
                        }
                        cpsOrderMapper.update(update, new LambdaQueryWrapper<CpsOrder>().eq(CpsOrder::getProductId, u)
                                .eq(CpsOrder::getProjectId, CpsProjectEnum.LAZADA.getId())
                                .eq(CpsOrder::getProductStatus, 0)
                        );
                    });
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });

    }

    @Override
    public void pullProductByIds(List<String> productIds) {
        List<AeAuthorization> aeAuthorization = getAuth();
        if (aeAuthorization.isEmpty()) {
            return;
        }
        aeAuthorization.forEach(auth -> {
            try {
                AeApp aeApp = aeAppMapper.selectById(auth.getAppId());
                if (null == aeApp) {
                    return;
                }
                for (int i = 0; i < Math.ceil((double) productIds.size() / 15); i++) {
                    List<String> search = productIds.subList(i * 15, Math.min((i + 1) * 15, productIds.size()));
                    if (search.isEmpty()) {
                        break;
                    }
                    this.pullInfo(auth, aeApp, search);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
    }

    /**
     * 拉取商品数据
     *
     * @param aeAuthorization ae
     * @param aeApp           app
     * @param productIds      商品ID
     */
    public List<String> pullInfo(AeAuthorization aeAuthorization, AeApp aeApp, List<String> productIds) {
        LazopClient client = new LazopClient(aeAuthorization.getSp(), aeApp.getAppKey(), aeApp.getAppSecret());
        LazopRequest request = new LazopRequest();
        request.setApiName("/marketing/product/feed");
        request.setHttpMethod("GET");
        request.addApiParameter("offerType", CpsLazadaProductTypeEnum.REGULAR_OFFER.getId().toString());
        request.addApiParameter("limit", "20");
        request.addApiParameter("userToken", aeAuthorization.getAccessToken());
        if (CollectionUtils.isNotEmpty(productIds)) {
            request.addApiParameter("productIds", JSONObject.toJSONString(productIds));
        }
        String pageUrl = getPageUrl(aeAuthorization.getAccountId());
        Integer categoryCountry = Integer.parseInt(aeAuthorization.getHavanaId());
        int page = 1;
        List<String> productInfo = new ArrayList<>();
        try {
            boolean flag;
            do {
                request.addApiParameter("page", Integer.toString(page));
                LazopResponse response = client.execute(request);
                log.debug("返回结果：{}", response.getBody());
                try {
                    LazadaResponseDTO<LazadaProductListDTO> result = this.formatAeResult(response.getBody(), new TypeReference<>() {
                    });
                    if (null == result || null == result.getResult() || CollectionUtils.isEmpty(result.getResult().getData())) {
                        flag = false;
                    } else {
                        flag = !result.getResult().getData().isEmpty();
                        result.getResult().getData().forEach(res -> {
                            try {
                                this.saveLazadaProduct(res, CpsLazadaProductTypeEnum.REGULAR_OFFER, aeAuthorization.getAccountId(), categoryCountry, false, pageUrl);
                            } catch (Exception e) {
                                log.error(e.getMessage(), e);
                            }
                        });
                        if (!productIds.isEmpty()) {
                            productInfo.addAll(result.getResult().getData().stream().map(LazadaProductDTO::getProductId).collect(Collectors.toList()));
                        }
                    }
                } catch (Exception e) {
                    flag = false;
                }
                page++;
            } while (flag);
        } catch (com.lazada.lazop.util.ApiException e) {
            log.info("request error : " + e.getErrorMessage(), e);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return productInfo;
    }


    /**
     * @param lazadaProductDTO lazada 数据
     * @param typeEnum         枚举
     * @param categoryCountry  国家
     * @param addOnly          数据
     * @param pageUrl          链接
     */
    public void saveLazadaProduct(LazadaProductDTO lazadaProductDTO, CpsLazadaProductTypeEnum typeEnum,
                                  String language, Integer categoryCountry, boolean addOnly, String pageUrl) {
        CpsProduct product = new CpsProduct();
        CpsProductLazada productLazada = new CpsProductLazada();
        this.convertProductInfo(lazadaProductDTO, product, productLazada);
        product.setProductType(typeEnum.getId());
        product.setCategoryCountry(categoryCountry);
        product.setProductLanguage(language);
        Map<Integer, String> currencyMap = new HashMap<>() {{
            put(1, "VND");
            put(2, "THB");
            put(3, "PHP");
            put(4, "MYR");
        }};
        String currency = currencyMap.getOrDefault(categoryCountry, "");
        product.setDiscountedPriceCurrency(currency);
        product.setProductPriceCurrency(currency);
        product.setProductDetailUrl(String.format("%s/products/i%s.html", pageUrl, product.getProductSign()));
        try {
            CpsProduct productInDb = this.cpsProductMapper.selectOne(new QueryWrapper<CpsProduct>().lambda()
                    .eq(CpsProduct::getProjectId, CpsProjectEnum.LAZADA.getId())
                    .eq(CpsProduct::getProductSign, lazadaProductDTO.getProductId())
            );
            Long productId;
            // 插入或更新主表及特有信息
            if (null == productInDb) {
                // 插入
                this.cpsProductMapper.insert(product);
                productId = product.getId();
                // 插入lazada信息表数据
                productLazada.setProductId(productId);
                cpsProductLazadaMapper.insert(productLazada);
                this.saveOrUpdateMaterial(productId, lazadaProductDTO);
            } else {
                if (!addOnly) {
                    // 更新
                    BeanUtils.copyProperties(product, productInDb, "id");
                    productId = productInDb.getId();
                    this.cpsProductMapper.updateById(productInDb);
                    // 更新lazada信息表数据
                    this.cpsProductLazadaMapper.update(productLazada,
                            new QueryWrapper<CpsProductLazada>().lambda().eq(CpsProductLazada::getProductId, productId));
                    // 更新ae信息表数据
                    this.saveOrUpdateMaterial(productId, lazadaProductDTO);
                }
            }
        } catch (Exception e) {
            log.info("add product error : {}", e.getMessage());
        }
    }

    @Override
    public void listConversionReport(String day) {
        for (LazadaAppEnum lazadaAppEnum : LazadaAppEnum.values()) {
            int page = 1, pageNum = 400;
            boolean flag = true;
            do {
                try {
                    String resp = this.getCpsOrder(lazadaAppEnum, day, day, page, pageNum);
                    if (null == resp) {
                        return;
                    }
                    CpsOrderLazadaResponseDTO response = JSONObject.parseObject(resp, new TypeReference<>() {
                    });
                    if (response.getCode().equals(0)) {
                        if (response.getResult() != null && !response.getResult().getData().isEmpty()) {
                            this.cpsOrderLazadaMapper.insertByUk2(response.getResult().getData(), lazadaAppEnum.getId());
                            if (response.getResult().getData().size() != pageNum) {
                                flag = false;
                            }
                        } else {
                            flag = false;
                        }
                    }

                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                page++;
            } while (page < 50 && flag);
        }
    }

    @Override
    public void exportConversion(List<String> days) {
        //
        days.forEach(day -> {
            try {
                MailUtils.sendHtmlEmailMultiple(
                        List.of("<EMAIL>", "<EMAIL>",
                                "<EMAIL>", "<EMAIL>"),
                        "LAZADA_CPS_渠道数据_" + day, "如上，内容见附件</br>",
                        List.of(
                                this.exportExcel(day, "oppo"),
                                this.exportExcel(day, "dsp"),
                                this.exportByDay(String.format("%s-01", day.substring(0, 7)), day),
                                this.exportByAd(day)
                        )
                );
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
    }

    private String exportExcel(String day, String platform) {
        List<Integer> ids = Stream.of(LazadaAppEnum.values()).filter(u -> platform.equals(u.getPlatform()))
                .map(LazadaAppEnum::getId).collect(Collectors.toList());
        List<CpsLazadaExcelDTO> list = new ArrayList<>();
        if (!ids.isEmpty()) {
            list = this.cpsOrderLazadaMapper.listExcel(new LambdaQueryWrapper<CpsOrderLazada>()
                    .eq(CpsOrderLazada::getConversionTime, day)
                    .in(CpsOrderLazada::getCpsAppId, ids)
            );
        }
        String filePath = UploadUtils.getUploadPath(String.format("/lazada_cps/LAZADA_CPS_%s_%s.xlsx", platform.toUpperCase(), day));
        ExcelUtils.download(filePath, "汇总", CpsLazadaExcelDTO.class, list);
        return filePath;
    }

    @Override
    public void generateProductCpsUrl() {
        List<SelectDTO> lazadaApps = ICommonEnum.list(LazadaAppEnum.class);
        lazadaApps.forEach(lazadaApp -> {
            LazadaAppEnum appEnum = ICommonEnum.get(lazadaApp.getName(), LazadaAppEnum.class);
            if (appEnum.getPlatform().equals("oppo")) {
                List<TopProduct> topProducts = this.topProductMapper.selectList(
                        new QueryWrapper<TopProduct>().lambda()
                                .eq(TopProduct::getVenture, lazadaApp.getName())
                                .eq(TopProduct::getUrlStatus, 0)
                                .gt(TopProduct::getCreateTime, "2025-03-10 0:00:00")
                                .last("LIMIT 50"));
                if (CollectionUtils.isEmpty(topProducts) || ObjectUtils.isNullOrZero(appEnum)) {
                    log.info("generate product cps url error : {} app is null or no products size : {}",
                            lazadaApp.getName(), topProducts.size());
                    return;
                }
                List<String> productUrls = topProducts.stream().map(TopProduct::getProductUrl).collect(Collectors.toList());
                LazadaTopProductUrlDTO topProductUrlResult = this.batchGetLink(appEnum.getApiUrl(), appEnum.getAppKey(),
                        appEnum.getAppSecret(), appEnum.getUserToken(), productUrls);
                if (null != topProductUrlResult) {
                    topProductUrlResult.getUrlBatchGetLinkInfoList().forEach(lazadaUrlBatchGetLinkInfoDTO -> {
                        TopProduct temp = new TopProduct();
                        temp.setUrlStatus(1);
                        temp.setProductCpsUrl(lazadaUrlBatchGetLinkInfoDTO.getRegularPromotionLink()
                                + "?sub_aff_id=oppo&sub_id1=Product&sub_id2=abcde");
                        this.topProductMapper.update(temp, new UpdateWrapper<TopProduct>().lambda()
                                .eq(TopProduct::getProductId, lazadaUrlBatchGetLinkInfoDTO.getProductId())
                                .gt(TopProduct::getCreateTime, "2025-03-10 0:00:00")
                        );
                    });
                    if (!topProductUrlResult.getErrorInfoList().isEmpty()) {
                        TopProduct temp = new TopProduct();
                        temp.setUrlStatus(2);
                        List<String> errorUrls = topProductUrlResult.getErrorInfoList().stream()
                                .map(LazadaErrorInfoDTO::getInputValue).collect(Collectors.toList());
                        this.topProductMapper.update(temp, new UpdateWrapper<TopProduct>().lambda()
                                .in(TopProduct::getProductUrl, errorUrls)
                                .gt(TopProduct::getCreateTime, "2025-03-10 0:00:00")
                        );
                    }
                }
            }
        });
    }


    @Override
    public String exportByAd(String day) throws IOException {
        Integer channelId = 5;
        //app 范围
        List<LazadaAppEnum> lazadaAppEnums = Arrays.stream(LazadaAppEnum.values())
                .filter(u -> "oppo".equals(u.getPlatform())).collect(Collectors.toList());
        //花费数据
        Map<String, List<CpsLazadaByAdDTO>> list = this.cpsOrderLazadaMapper.listByAd(new QueryWrapper<>()
                        .eq("tcr.channel_id", channelId)
                        .in("tcr.channel_account_id", this.channelAccountId(channelId))
                        .groupBy("tcr.channel_creative_id")
                ).stream()
                .filter(ad -> !ad.getChannelPlanName().contains("软件商店"))
                .peek(ad -> {
                    String[] adArr = ad.getChannelCreativeName().split("-");
                    try {
                        ad.setTid(adArr[0]);
                        ad.setCountry(adArr[1]);
                        ad.setHour(adArr[2]);
                        ad.setSlotTypeName(adArr[3]);
                        ad.setCrowd(adArr[4]);
                        ad.setDate(adArr[5]);
                        ad.setOther("");
                    } catch (Exception e) {
                        log.error(e.getMessage(), JSONObject.toJSONString(ad));
                        throw new CustomException(e.getMessage());
                    }
                    for (int i = 6; i <= adArr.length - 1; i++) {
                        ad.setOther(ad.getOther().concat(adArr[i]).concat("-"));
                    }
                    if (StringUtils.isNotBlank(ad.getOther())) {
                        ad.setOther(ad.getOther().substring(0, ad.getOther().length() - 1));
                    }
                }).collect(Collectors.groupingBy(CpsLazadaByAdDTO::getCountry));
        //subId2佣金数据
        Map<String, CpsOrderLazadaOrderDTO> sub2Map = cpsOrderLazadaMapper.orderInfo(new QueryWrapper<>()
                .select("cps_app_id, sub_id_2, sum(payout) as payout, SUM(order_amount) as order_amount, count(distinct sub_order_id) as order_count")
                .ne("sub_id_2", "")
                .notIn("status", List.of("rejected", "returned"))
                .in("cps_app_id", lazadaAppEnums.stream().map(LazadaAppEnum::getId).collect(Collectors.toList()))
                .groupBy("cps_app_id").groupBy("sub_id_2")
        ).stream().collect(Collectors.toMap(u -> String.format("%s-%s", u.getCpsAppId(), u.getSubId2()), Function.identity()));

        //afSubId佣金数据
        Map<String, CpsOrderLazadaOrderDTO> afSubMap = cpsOrderLazadaMapper.orderInfo(new QueryWrapper<>()
                .select("cps_app_id, aff_sub_id, sum(payout) as payout , SUM(order_amount) as order_amount, count(distinct sub_order_id) as order_count")
                .ne("aff_sub_id", "")
                .notIn("status", List.of("rejected", "returned"))
                .in("cps_app_id", lazadaAppEnums.stream().map(LazadaAppEnum::getId).collect(Collectors.toList()))
                .groupBy("cps_app_id").groupBy("aff_sub_id")
        ).stream().collect(Collectors.toMap(u -> String.format("%s-%s", u.getCpsAppId(), u.getAffSubId()), Function.identity()));
        //汇率
        Map<String, BigDecimal> countryCurrencyMap = getCountryCurrency();
        //app map
        Map<String, Integer> cpsAppMap = lazadaAppEnums.stream().collect(Collectors.toMap(u -> u.getName().toUpperCase(), LazadaAppEnum::getId));
        //crowd map
        Map<String, CpsLazadaCrowdMapDTO> crowdMap = this.cpsOrderLazadaMapper.crowdMap().stream().collect(Collectors.toMap(u -> u.getChannelMarketName().trim().toUpperCase(), Function.identity()));
        AtomicInteger atomicInteger = new AtomicInteger(0);
        //excel 数据
        List<BaseExcelDTO> excels = new ArrayList<>();
        list.forEach((country, ads) -> {
            Integer cpsAppId = cpsAppMap.get(country.toUpperCase());
            BigDecimal currency = countryCurrencyMap.get(country.toUpperCase());
            ads.forEach(ad -> {
                CpsLazadaCrowdMapDTO cpsLazadaCrowd = crowdMap.get(ad.getChannelPlanName().trim().toUpperCase());
                if (null != cpsLazadaCrowd) {
                    ad.setCrowdId(cpsLazadaCrowd.getChannelMarketId());
                    ad.setCrowdName(cpsLazadaCrowd.getCrowdName());
                } else {
                    log.info("OPPO：{}, 未找到人群包", ad.getChannelPlanName());
                }
                CpsOrderLazadaOrderDTO orderInfo = sub2Map.getOrDefault(String.format("%s-%s", cpsAppId, ad.getTid()), afSubMap.get(String.format("%s-%s", cpsAppId, ad.getTid())));
                if (null == orderInfo) {
                    return;
                }
                ad.setPayout(orderInfo.getPayout());
                ad.setOrderAmount(orderInfo.getOrderAmount());
                ad.setPayoutDollar(ad.getPayout().multiply(currency).setScale(2, RoundingMode.HALF_UP));
                ad.setOrderAmountDollar(ad.getOrderAmount().multiply(currency).setScale(2, RoundingMode.HALF_UP));
                ad.setOrderCount(orderInfo.getOrderCount());
                if (ad.getClick() == 0) {
                    ad.setCvr(BigDecimal.ZERO);
                } else {
                    ad.setCvr(ad.getOrderAmountDollar().divide(BigDecimal.valueOf(ad.getClick()), 3, RoundingMode.HALF_UP));
                }
                if (ad.getCost().compareTo(BigDecimal.ZERO) == 0) {
                    ad.setRoi(BigDecimal.ZERO);
                } else {
                    ad.setRoi(ad.getPayoutDollar().divide(ad.getCost(), 2, RoundingMode.HALF_UP));
                }

            });
            BaseExcelDTO baseExcelDTO = new BaseExcelDTO();
            baseExcelDTO.setSheetName(country);
            baseExcelDTO.setData(ads);
            baseExcelDTO.setAClass(CpsLazadaByAdDTO.class);
            baseExcelDTO.setSheetKey(atomicInteger.getAndIncrement());
            excels.add(baseExcelDTO);
        });
        String filePath = UploadUtils.getUploadPath(String.format("/lazada_cps/LAZADA_CPS_BY_AD_%s.xlsx", day));
        ExcelUtils.downloadWithSheet(filePath, excels, null);
        return filePath;

//        ExcelUtils.downloadWithSheet("/Users/<USER>/Downloads/uploads/lazada_cps/LAZADA_CPS_BY_AD.xlsx", excels, null);
//        ExcelUtils.responseWithSheet(response, excels, "LAZADA_CPS_BY_AD.xlsx", null);

    }

    @Override
    public String exportByDay(String startDate, String endDate) throws IOException {
        List<LazadaAppEnum> lazadaAppEnums = Arrays.stream(LazadaAppEnum.values()).filter(u -> "oppo".equals(u.getPlatform())).collect(Collectors.toList());

        Map<String, List<CpsLazadaByDayDTO>> byDayMap = this.cpsOrderLazadaMapper.listByDay(new QueryWrapper<>()
                .eq("tcr.channel_id", 5)
                .between("tcr.dim_day", DateUtils.string2Long(startDate), DateUtils.string2Long(endDate))
                .groupBy("tcr.dim_day").groupBy("tci.country")
        ).stream().collect(Collectors.groupingBy(CpsLazadaByDayDTO::getCountry));
        Map<String, CpsOrderLazadaOrderDTO> cpsOrderLazadaMap = cpsOrderLazadaMapper.orderInfo(new QueryWrapper<>()
                .select("SUM(`payout`) as payout, SUM(`order_amount`) as order_amount,count(`sub_order_id`) as order_count, fulfilled_time, cps_app_id")
                .in("cps_app_id", lazadaAppEnums.stream().map(LazadaAppEnum::getId).collect(Collectors.toList()))
                .between("fulfilled_time", startDate, endDate)
                .notIn("status", List.of("rejected", "returned"))
                .groupBy("fulfilled_time, cps_app_id")
        ).stream().collect(Collectors.toMap(u -> String.format("%s_%s", u.getCpsAppId(), u.getFulfilledTime()), Function.identity()));
        //afSubId佣金数据
        Map<String, CpsOrderLazadaOrderDTO> afSubMap = cpsOrderLazadaMapper.orderInfo(new QueryWrapper<>()
                .select("fulfilled_time, cps_app_id, aff_sub_id, sum(payout) as payout , SUM(order_amount) as order_amount, count(distinct sub_order_id) as order_count")
                .in("cps_app_id", lazadaAppEnums.stream().map(LazadaAppEnum::getId).collect(Collectors.toList()))
                .between("fulfilled_time", startDate, endDate)
                .in("aff_sub_id", List.of("oppo", "oppoSMS", "oppoRTA"))
                .notIn("status", List.of("rejected", "returned"))
                .groupBy("fulfilled_time, cps_app_id, aff_sub_id")
                .orderByAsc("fulfilled_time, cps_app_id, aff_sub_id")
        ).stream().collect(Collectors.toMap(u -> String.format("%s_%s_%s", u.getCpsAppId(), u.getFulfilledTime(), u.getAffSubId()), Function.identity()));
        //汇率
        Map<String, BigDecimal> countryCurrencyMap = this.countryAllMapper.listCountryAll(new QueryWrapper<CountryAll>().lambda()
                .in(CountryAll::getCountryAlias, lazadaAppEnums.stream().map(u -> u.getName().toUpperCase()).collect(Collectors.toList()))
                .select(CountryAll::getCountryAlias, CountryAll::getUsdCurrency)
        ).stream().collect(Collectors.toMap(CountryAll::getCountryAlias, CountryAll::getUsdCurrency));
        AtomicInteger atomicInteger = new AtomicInteger(0);
        //app map
        Map<String, Integer> cpsAppMap = lazadaAppEnums.stream().collect(Collectors.toMap(u -> u.getName().toUpperCase(), LazadaAppEnum::getId));
        //excel 数据
        List<BaseExcelDTO> excels = new ArrayList<>();
        List<String> days = DateUtils.getBetweenDate(startDate, endDate, DateUtils.DATE_PATTERN);
        byDayMap.forEach((country, byDays) -> {
            Integer cpsAppId = cpsAppMap.get(country.toUpperCase());
            BigDecimal currency = countryCurrencyMap.get(country.toUpperCase());

            Map<String, CpsLazadaByDayDTO> byDaysMap = byDays.stream().collect(Collectors.toMap(CpsLazadaByDayDTO::getDay, Function.identity()));
            List<CpsLazadaByDayDTO> byDayList = days.stream().map(day -> {
                CpsLazadaByDayDTO byDay = byDaysMap.get(day);
                if (null == byDay) {
                    byDay = new CpsLazadaByDayDTO();
                    byDay.setCountry(country);
                    byDay.setDay(day);
                }
                String cpsAppDayKey = String.format("%s_%s", cpsAppId, byDay.getDay());
                CpsOrderLazadaOrderDTO orderInfo = cpsOrderLazadaMap.get(cpsAppDayKey);
                if (null != orderInfo) {
                    byDay.setPayout(orderInfo.getPayout());
                    byDay.setPayoutDollar(orderInfo.getPayout().multiply(currency).setScale(2, RoundingMode.HALF_UP));
                    byDay.setOrderAmount(orderInfo.getOrderAmount());
                    byDay.setOrderAmountDollar(orderInfo.getOrderAmount().multiply(currency).setScale(2, RoundingMode.HALF_UP));
                    if (byDay.getClick() == 0L) {
                        byDay.setCpc(BigDecimal.ZERO);
                    } else {
                        byDay.setCpc(byDay.getPayoutDollar().divide(BigDecimal.valueOf(byDay.getClick()), 3, RoundingMode.HALF_UP));
                    }
                    byDay.setOrderCount(orderInfo.getOrderCount());
                }
                //填补OPPO佣金数据
                String oppoAfSubIdKey = String.format("%s_oppo", cpsAppDayKey);
                CpsOrderLazadaOrderDTO oppoAfSub = afSubMap.get(oppoAfSubIdKey);
                if (null != oppoAfSub) {
                    byDay.setOppoPayout(oppoAfSub.getPayout());
                    byDay.setOppoPayoutDollar(oppoAfSub.getPayout().multiply(currency).setScale(2, RoundingMode.HALF_UP));
                    byDay.setOppoOrderAmount(oppoAfSub.getOrderAmount());
                    byDay.setOppoOrderAmountDollar(oppoAfSub.getOrderAmount().multiply(currency).setScale(2, RoundingMode.HALF_UP));
                    byDay.setOppoOrderCount(oppoAfSub.getOrderCount());
                }
                //填补OPPO SMS佣金数据
                String oppoSmsAfSubIdKey = String.format("%s_oppoSMS", cpsAppDayKey);
                CpsOrderLazadaOrderDTO oppoSmsAfSub = afSubMap.get(oppoSmsAfSubIdKey);
                if (null != oppoSmsAfSub) {
                    byDay.setOppoSmsPayout(oppoSmsAfSub.getPayout());
                    byDay.setOppoSmsPayoutDollar(oppoSmsAfSub.getPayout().multiply(currency).setScale(2, RoundingMode.HALF_UP));
                    byDay.setOppoSmsOrderAmount(oppoSmsAfSub.getOrderAmount());
                    byDay.setOppoSmsOrderAmountDollar(oppoSmsAfSub.getOrderAmount().multiply(currency).setScale(2, RoundingMode.HALF_UP));
                    byDay.setOppoSmsOrderCount(oppoSmsAfSub.getOrderCount());
                }
                //填补OPPO RTA佣金数据
                String oppoRtaAfSubIdKey = String.format("%s_oppoRTA", cpsAppDayKey);
                CpsOrderLazadaOrderDTO oppoRtaAfSub = afSubMap.get(oppoRtaAfSubIdKey);
                if (null != oppoRtaAfSub) {
                    byDay.setOppoRtaPayout(oppoRtaAfSub.getPayout());
                    byDay.setOppoRtaPayoutDollar(oppoRtaAfSub.getPayout().multiply(currency).setScale(2, RoundingMode.HALF_UP));
                    byDay.setOppoRtaOrderAmount(oppoRtaAfSub.getOrderAmount());
                    byDay.setOppoRtaOrderAmountDollar(oppoRtaAfSub.getOrderAmount().multiply(currency).setScale(2, RoundingMode.HALF_UP));
                    byDay.setOppoRtaOrderCount(oppoRtaAfSub.getOrderCount());
                }
                return byDay;
            }).collect(Collectors.toList());
            BaseExcelDTO baseExcelDTO = new BaseExcelDTO();
            baseExcelDTO.setSheetName(country.toUpperCase());
            baseExcelDTO.setData(byDayList);
            baseExcelDTO.setAClass(CpsLazadaByDayDTO.class);
            baseExcelDTO.setSheetKey(atomicInteger.getAndIncrement());
            excels.add(baseExcelDTO);
        });
        String filePath = UploadUtils.getUploadPath(String.format("/lazada_cps/LAZADA_CPS_BY_DAY_%s.xlsx", endDate));
        ExcelUtils.downloadWithSheet(filePath, excels, null);
        return filePath;
//        ExcelUtils.downloadWithSheet("/Users/<USER>/Downloads/uploads/lazada_cps/LAZADA_CPS_BY_DAY.xlsx", excels, null);
    }


    private Map<String, BigDecimal> getCountryCurrency() {
        return this.countryAllMapper.listCountryAll(new QueryWrapper<CountryAll>().lambda()
                .in(CountryAll::getCountryAlias, Arrays.stream(LazadaAppEnum.values()).filter(u -> "oppo".equals(u.getPlatform()))
                        .map(u -> u.getName().toUpperCase()).collect(Collectors.toList()))
                .select(CountryAll::getCountryAlias, CountryAll::getUsdCurrency)
        ).stream().collect(Collectors.toMap(CountryAll::getCountryAlias, CountryAll::getUsdCurrency));
    }

    public LazadaTopProductUrlDTO batchGetLink(String apiUrl, String appKey, String appSecret, String userToken,
                                               List<String> productUrls) {
        LazopClient client = new LazopClient(apiUrl + "/rest", appKey, appSecret);
        LazopRequest request = new LazopRequest();
        request.setApiName("/marketing/getlink");
        request.setHttpMethod("GET");
        request.addApiParameter("inputType", "url");
        request.addApiParameter("inputValue", StringUtils.join(productUrls, ","));
        request.addApiParameter("userToken", userToken);
        try {
            LazopResponse response = client.execute(request);
            log.debug("返回结果：{}", response.getBody());
            LazadaResponseDTO<LazadaTopProductDataDTO> result = this.formatAeResult(response.getBody(),
                    new TypeReference<>() {
                    });
            return ObjectUtils.isNullOrZero(result) ? null : result.getResult().getData();
        } catch (com.lazada.lazop.util.ApiException e) {
            log.info("request error : " + e.getErrorMessage(), e);
            return null;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 商品分类获取
     *
     * @param aeAuthorization 数据
     */
    private void pullCategory(AeAuthorization aeAuthorization) {
        AeApp aeApp = aeAppMapper.selectById(aeAuthorization.getAppId());
        if (null == aeApp) {
            return;
        }
        LazopClient client = new LazopClient(aeAuthorization.getSp(), aeApp.getAppKey(), aeApp.getAppSecret());
        LazopRequest request = new LazopRequest();
        request.setApiName("/category/tree/get");
        request.setHttpMethod("GET");
        try {
            LazopResponse response = client.execute(request);
            log.debug("返回结果：{}", response.getBody());
            LazadaResponseDataDTO<List<LazadaCategoryDTO>> result = this.formatAeResult(response.getBody(), new TypeReference<>() {
            });
            if (null == result || null == result.getData()) {
                return;
            }
            result.getData().forEach(res -> {
                if (null == res.getChildren()) {
                    return;
                }
                log.debug("单个分类信息:{}", JSONObject.toJSONString(res));
                try {
                    //一级分类
                    List<CpsProductCategory> cpsProductCategories = new ArrayList<>();
                    CpsProductCategory one = new CpsProductCategory();
                    one.setCategoryId(res.getCategoryId());
                    one.setCategoryName(res.getName());
                    one.setProjectId(CpsProjectEnum.LAZADA.getId());
                    one.setCategoryCountry(Long.valueOf(aeAuthorization.getHavanaId()));
                    one.setCategoryLevel(1);
                    one.setCategoryDisplayName(res.getName());
                    one.setParentId(0L);
                    cpsProductCategories.add(one);
                    //二级分类
                    cpsProductCategories.addAll(res.getChildren().stream().map(child -> {
                        CpsProductCategory category = new CpsProductCategory();
                        category.setCategoryId(child.getCategoryId());
                        category.setCategoryName(child.getName());
                        category.setProjectId(CpsProjectEnum.LAZADA.getId());
                        category.setCategoryCountry(Long.valueOf(aeAuthorization.getHavanaId()));
                        category.setCategoryLevel(2);
                        category.setCategoryDisplayName(child.getName());
                        category.setParentId(res.getCategoryId());
                        return category;
                    }).collect(Collectors.toList()));
                    cpsProductCategoryMapper.insertByUk(cpsProductCategories);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            });
        } catch (com.lazada.lazop.util.ApiException e) {
            log.info("request error : " + e.getErrorMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 转化数据
     *
     * @param lazadaProductDTO lazada 数据
     * @param cpsProduct       cps主表数据
     * @param productLazada    lazada 副表数据
     */
    private void convertProductInfo(LazadaProductDTO lazadaProductDTO, CpsProduct cpsProduct, CpsProductLazada productLazada) {
        //复制商品主表内容
        cpsProduct.setProjectId(CpsProjectEnum.LAZADA.getId());
        cpsProduct.setProductSign(lazadaProductDTO.getProductId());
        cpsProduct.setProductTitle(lazadaProductDTO.getProductName());
        cpsProduct.setDiscountedPrice(lazadaProductDTO.getDiscountPrice());
        cpsProduct.setProductPrice(lazadaProductDTO.getDiscountPrice());
        cpsProduct.setShopId(lazadaProductDTO.getSellerId());
        cpsProduct.setProductBrand(lazadaProductDTO.getBrandName());
        cpsProduct.setFirstCategoryId(lazadaProductDTO.getCategoryL1());

        //复制lazada特殊内容
        BeanUtils.copyProperties(lazadaProductDTO, productLazada);
    }

    /**
     * 保存素材信息
     *
     * @param productId        内部商品ID
     * @param lazadaProductDTO lazada 商品信息
     */
    private void saveOrUpdateMaterial(Long productId, LazadaProductDTO lazadaProductDTO) {
        List<CpsProductMaterial> materials = this.getLazadaProductMaterial(productId, lazadaProductDTO);
        this.cpsProductMaterialMapper.batchInsertMaterial(materials);
    }

    /**
     * 从lazada 商品信息中获取主图和小图素材信息
     *
     * @param productId        商品ID
     * @param lazadaProductDTO lazada 返回商品信息
     * @return 素材集合
     */
    private List<CpsProductMaterial> getLazadaProductMaterial(Long productId, LazadaProductDTO lazadaProductDTO) {
        Map<String, CpsProductMaterial> materials = new HashMap<>();
        List<String> smallImageUrls = lazadaProductDTO.getPictures();
        int index = 0;
        for (String smallImageUrl : smallImageUrls) {
            CpsProductMaterial smallImageMaterial = new CpsProductMaterial();
            smallImageMaterial.setProductId(productId);
            smallImageMaterial.setMaterialMd5(Md5CalculateUtils.getUrlMD5(smallImageUrl));
            smallImageMaterial.setMaterialUrl(smallImageUrl);
            smallImageMaterial.setMaterialType(index > 0
                    ? CpsMaterialTypeEnum.PRODUCT_SMALL.getId() : CpsMaterialTypeEnum.PRODUCT_MAIN.getId());
            materials.put(smallImageMaterial.getMaterialMd5(), smallImageMaterial);
            index++;
        }
        return new ArrayList<>(materials.values());
    }

    /**
     * 格式化返回数据
     *
     * @param result 结果对象json串
     * @param clazz  要格式化的对象类
     * @param <T>    对象类
     * @return 结果集
     */
    private <T> T formatAeResult(String result, TypeReference<T> clazz) {
        try {
            return JSONObject.parseObject(result, clazz);
        } catch (Exception e) {
            log.info("format ae result error : {}", result);
            return null;
        }
    }

    /**
     * 返回可用 auth
     *
     * @return 返回数据
     */
    private List<AeAuthorization> getAuth() {
        return aeAuthorizationMapper.selectList(new LambdaQueryWrapper<AeAuthorization>()
                .eq(AeAuthorization::getProjectId, CpsProjectEnum.LAZADA.getId())
                .eq(AeAuthorization::getExpiresIn, 0)
        );
    }

    /**
     * 数据链接
     *
     * @param country 国家
     * @return 返回数据
     */
    private String getPageUrl(String country) {
        return new HashMap<>() {{
            put("ph", "https://www.lazada.com.ph");
            put("id", "https://www.lazada.co.id");
            put("th", "https://www.lazada.co.th");
            put("vn", "https://www.lazada.vn");
        }}.get(country.toLowerCase()).toString();
    }

    /**
     * 获取 cps 结果
     *
     * @param lazadaAppEnum enum参数
     * @param start         开始时间
     * @param end           结束时间
     * @param page          分页
     * @param pageNum       分页数量
     * @return 返回数据
     */
    private String getCpsOrder(LazadaAppEnum lazadaAppEnum, String start, String end,
                               Integer page, Integer pageNum) {
        LazopClient client = new LazopClient(lazadaAppEnum.getApiUrl() + "/rest",
                lazadaAppEnum.getAppKey(), lazadaAppEnum.getAppSecret()
        );
        LazopRequest request = new LazopRequest();
        request.setApiName("/marketing/conversion/report");
        request.setHttpMethod("GET");
        request.addApiParameter("dateStart", start);
        request.addApiParameter("dateEnd", end);
        request.addApiParameter("limit", pageNum.toString());
        request.addApiParameter("page", page.toString());
        request.addApiParameter("userToken", lazadaAppEnum.getUserToken());
        try {
            LazopResponse response = client.execute(request);
            log.debug("返回结果：{}", response.getBody());
            return response.getBody();
        } catch (com.lazada.lazop.util.ApiException e) {
            log.info("request error : " + e.getErrorMessage(), e);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 获取渠道账户ID
     *
     * @param channelId 渠道ID
     * @return 返回数据
     */
    private List<String> channelAccountId(Integer channelId) {
        return channelAccountMapper.selectList(new LambdaQueryWrapper<ChannelAccount>()
                .eq(ChannelAccount::getChannelId, channelId)
                .eq(ChannelAccount::getIsDel, IsDelEnum.NORMAL.getId())
        ).stream().map(ChannelAccount::getChannelAccountId).collect(Collectors.toList());
    }

}
