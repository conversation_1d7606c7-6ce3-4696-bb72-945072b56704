package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.overseas.common.configuration.SheinConfiguration;
import com.overseas.common.dto.SelectDTO2;
import com.overseas.common.dto.market.market.MarketBatchListDTO;
import com.overseas.common.dto.market.market.MarketDimensionInfoDTO;
import com.overseas.common.dto.report.BaseReportDTO;
import com.overseas.common.dto.report.ReportListDTO;
import com.overseas.common.dto.sys.project.ProjectByMasterDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.SortTypeEnum;
import com.overseas.common.enums.monitor.put.MonitorDimensionEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.*;
import com.overseas.common.vo.market.market.*;
import com.overseas.common.vo.market.master.MasterPageFirstExportVO;
import com.overseas.common.vo.market.master.MasterPageFirstVO;
import com.overseas.common.vo.market.master.MasterTimeZoneGetVO;
import com.overseas.common.vo.report.ReportHasCostListVO;
import com.overseas.common.vo.report.ReportListVO;
import com.overseas.common.vo.sys.project.ProjectByMasterVO;
import com.overseas.service.market.dto.market.MasterPageFirstDTO;
import com.overseas.service.market.dto.plan.WebPlanStatusContainerDTO;
import com.overseas.service.market.entity.CreativeUnit;
import com.overseas.service.market.entity.PlanGenerateMap;
import com.overseas.service.market.entity.User;
import com.overseas.service.market.enums.market.MarketListTypeEnum;
import com.overseas.service.market.enums.plan.PlanGenerateMapTypeEnum;
import com.overseas.service.market.events.creativeUnitAudit.CreativeUnitAuditEvent;
import com.overseas.service.market.events.notifyControl.ControlCampaignEvent;
import com.overseas.service.market.events.notifyControl.ControlContants;
import com.overseas.service.market.events.notifyControl.ControlCreativeUnitEvent;
import com.overseas.service.market.feign.FgReportService;
import com.overseas.service.market.feign.FgSystemService;
import com.overseas.service.market.mapper.PlanGenerateMapMapper;
import com.overseas.service.market.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-20 17:14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MarketServiceImpl implements MarketService {

    private final FgReportService fgReportService;

    private final ApplicationContext applicationContext;

    private final CampaignService campaignService;

    private final PlanService planService;

    private final FgSystemService fgSystemService;

    private final CreativeUnitService creativeUnitService;

    private final MasterService masterService;

    private final SheinConfiguration sheinConfiguration;

    private final PlanGenerateMapMapper planGenerateMapMapper;

    private final SheinCreativeService sheinCreativeService;

    @Override
    public PageUtils<MasterPageFirstDTO> page(MasterPageFirstVO listVO, List<Integer> permissionMasterIds, User loginUser) {
        // 第一步，进行基础设置，包括获取列表类型，定义报表的查询条件等
        // 添加默认排序方式
        listVO.setSortType(StringUtils.isBlank(listVO.getSortType()) ? SortTypeEnum.DESC.getSortType() : listVO.getSortType());
        // shein特殊处理
        // 设置是否投放功能, 如果shein代理商，设置查看非投放 活动/计划/创意， 如果是非shein 代理商查看 正常活动/计划/创意
        listVO.setIsPut(this.sheinConfiguration.isPut(listVO.getIsPut(), loginUser.getRoleId()));
        // 组织报表查询条件
        ReportListVO reportListVO = this.getReportListVO(listVO);
        MarketListTypeEnum listTypeEnum = ICommonEnum.get(listVO.getListType(), MarketListTypeEnum.class);
        reportListVO.setTimeZone(listVO.getTimeZone());
        reportListVO.setReportType(listTypeEnum.getReportType());
        PageUtils<MasterPageFirstDTO> emptyPage = new PageUtils<>(listVO.getPage(), listVO.getPageNum());
        // 第二步：开始查询，分为两种方式，id排序以主投放表为主查询，报表字段排序以报表查询为主查询
        // 以投放账户为主表查
        MarketPageListService objectService = getService(listTypeEnum);
        WebPlanStatusContainerDTO webPlanStatusContainerDTO = new WebPlanStatusContainerDTO(); // 初始化字段，后续对该字段进行赋值
        //shein活动/计划搜索
        if (CollectionUtils.isNotEmpty(listVO.getSheinCampaignIds())) {
            listVO.setSheinCampaignIds(this.getSheinMarketId(listVO.getSheinCampaignIds(), PlanGenerateMapTypeEnum.CAMPAIGN));
            if (CollectionUtils.isEmpty(listVO.getSheinCampaignIds())) {
                return emptyPage;
            }
        }
        if (CollectionUtils.isNotEmpty(listVO.getSheinPlanIds())) {
            listVO.setSheinPlanIds(this.getSheinMarketId(listVO.getSheinPlanIds(), PlanGenerateMapTypeEnum.PLAN));
            if (CollectionUtils.isEmpty(listVO.getSheinPlanIds())) {
                return emptyPage;
            }
        }
        //设置如果默认为空的排序，则不需要排序
        if (List.of("", "normal").contains(listVO.getSortType())) {
            listVO.setSortField("id");
            listVO.setSortType("desc");
        }
        if ("id".equals(listVO.getSortField()) && CollectionUtils.isEmpty(listVO.getFieldRules())) {
            // 获取主表数据
            IPage<MasterPageFirstDTO> masterPage = objectService.pageMarket(listVO, permissionMasterIds, null, webPlanStatusContainerDTO);
            if (CollectionUtils.isEmpty(masterPage.getRecords())) {
                return emptyPage;
            }
            // 获取报表数据
            reportListVO.setPage(1L);
            reportListVO.setSortField("bid");
            IPage<ReportListDTO> reportPage = pageReport(reportListVO, masterPage.getRecords().stream().map(MasterPageFirstDTO::getId).collect(Collectors.toList()));
            // 获取报表的总计数据
            ReportListDTO totalReport = this.getTotalReport(reportListVO, objectService.listAllIdMarket(listVO, permissionMasterIds, webPlanStatusContainerDTO));
            // 组装最后结果集
            return packageList(masterPage, reportPage, totalReport, 1, listTypeEnum, objectService, webPlanStatusContainerDTO, listVO.getMasterId());
        } else {
            // 以报表为主表查
            // 获取符合条件的所有id集合
            List<Long> allIds = objectService.listAllIdMarket(listVO, permissionMasterIds, webPlanStatusContainerDTO);
            if (CollectionUtils.isEmpty(allIds)) {
                return emptyPage;
            }
            // 获取报表数据
            IPage<ReportListDTO> reportPage = pageReport(reportListVO, allIds);
            if (CollectionUtils.isEmpty(reportPage.getRecords())) {
                //如果无消耗数据，则返回按照id排序
                if (reportPage.getTotal() == 0 && objectService instanceof MasterService) {
                    listVO.setSortType("desc");
                    listVO.setSortField("id");
                    return this.page(listVO, permissionMasterIds, loginUser);
                }
                //否则返回空数据
                return emptyPage;
            }
            // 获取报表总计数据
            ReportListDTO totalReport = this.getTotalReport(reportListVO, allIds);
            // 获取投放表数据
            listVO.setPage(1L);
            IPage<MasterPageFirstDTO> masterPage = objectService.pageMarket(listVO, permissionMasterIds, objectService.listReportIdMarket(reportPage.getRecords()), webPlanStatusContainerDTO);
            // 组装最后结果集
            return packageList(masterPage, reportPage, totalReport, 2, listTypeEnum, objectService, webPlanStatusContainerDTO, listVO.getMasterId());
        }
    }

    @Override
    public void exportPage(MasterPageFirstExportVO listVO, List<Integer> permissionMasterIds,
                           User loginUser, HttpServletResponse response) throws IOException {
        listVO.setPage(1L);
        listVO.setPageNum(10000000L);
        PageUtils<MasterPageFirstDTO> pageUtils = this.page(listVO, permissionMasterIds, loginUser);
        Map<String, String> nameMap = new HashMap<>() {{
            put("master", "账户列表");
            put("campaign", "活动列表");
            put("plan", "计划列表");
            put("creativeUnit", "创意列表");
        }};
        listVO.setCustoms(listVO.getCustoms().stream()
                .filter(u -> !List.of("assets", "creativeUnitStatus").contains(u.getKey()))
                .peek(u -> {
                    if ("id".equals(u.getKey())) {
                        u.setKey("idStr");
                    }
                }).collect(Collectors.toList())
        );
        pageUtils.getData().forEach(u -> {
                    if (u.getId().equals(0L)) {
                        u.setIdStr("汇总");
                    } else {
                        u.setIdStr(u.getId().toString());
                    }
                    if (CollectionUtils.isEmpty(u.getAssets())) {
                        return;
                    }
                    u.getAssets().forEach(asset -> {
                        switch (asset.getFieldType()) {
                            case 1:
                                u.setCreativeUnitSize(String.format("%s*%s", asset.getWidth(), asset.getHeight()));
                                break;
                            case 3:
                                u.setTitle(String.format("标题：%s \n译文：%s", asset.getContent(), asset.getTranslatedText()));
                                break;
                            case 4:
                                u.setDesc(String.format("描述：%s \n译文：%s", asset.getContent(), asset.getTranslatedText()));
                                break;
                            default:
                        }
                    });
                }
        );
        ExcelUtils.download(response, nameMap.getOrDefault(listVO.getListType(), "列表"), "数据",
                pageUtils.getData(),
                listVO.getCustoms().stream().map(u -> new SelectDTO2(u.getKey(), u.getTitle())).collect(Collectors.toList())
        );
    }

    @Override
    public ProjectByMasterDTO pageIdentify(MasterPageFirstVO listVO, List<Integer> permissionMasterIds, User loginUser) {
        List<Long> masterIds;
        if (ObjectUtils.isNotNullOrZero(listVO.getMasterId())) {
            masterIds = List.of(listVO.getMasterId());
        } else if (CollectionUtils.isNotEmpty(listVO.getMasterIds())) {
            masterIds = listVO.getMasterIds();
        } else {
            MarketListTypeEnum listTypeEnum = ICommonEnum.get(listVO.getListType(), MarketListTypeEnum.class);
            MarketPageListService objectService = getService(listTypeEnum);
            WebPlanStatusContainerDTO webPlanStatusContainerDTO = new WebPlanStatusContainerDTO();
            masterIds = objectService.listAllMasterIdMarket(listVO, permissionMasterIds, webPlanStatusContainerDTO);
        }
        if (CollectionUtils.isEmpty(masterIds)) {
            return ProjectByMasterDTO.builder().identify("").projectId(List.of()).build();
        }
        ProjectByMasterVO projectByMasterVO = new ProjectByMasterVO();
        projectByMasterVO.setMasterIds(masterIds);
        return fgSystemService.getProjectByMaster(projectByMasterVO).getData();
    }

    @Override
    public boolean batchDeleteRecord(RecordBatchDeleteVO batchDeleteVO) {
        MarketListTypeEnum listTypeEnum = ICommonEnum.get(batchDeleteVO.getListType(), MarketListTypeEnum.class);
        MarketPageListService objectService = getService(listTypeEnum);
        // 检查要删除的记录是否存在投放数据
        ReportHasCostListVO hasCostListVO = new ReportHasCostListVO();
        BeanUtils.copyProperties(batchDeleteVO, hasCostListVO);
        FeignR<List<Long>> hasCostResult = this.fgReportService.listHasCostRecord(hasCostListVO);
        // 去除有花费的id集合
        if (CollectionUtils.isNotEmpty(hasCostResult.getData())) {
            batchDeleteVO.getIds().removeAll(hasCostResult.getData());
        }
        boolean result = objectService.batchDelete(batchDeleteVO);
        // 检查是否有删除的记录ID，并发送中控通知
        if (result && !batchDeleteVO.getIds().isEmpty()) {
            this.noticeControl(listTypeEnum, batchDeleteVO.getIds(), batchDeleteVO.getUserId());
        }
        return result;
    }

    @Override
    public boolean batchSwitchRecord(RecordBatchSwitchVO batchSwitchVO) {
        MarketListTypeEnum listTypeEnum = ICommonEnum.get(batchSwitchVO.getListType(), MarketListTypeEnum.class);
        if (ObjectUtils.isNullOrZero(batchSwitchVO.getMasterId())) {
            FeignR<List<Integer>> feignR = this.fgSystemService.listMasterId();
            if (!feignR.getCode().equals(0)) {
                throw new CustomException("内部服务异常");
            }
            if (feignR.getData().isEmpty()) {
                throw new CustomException("账户信息异常");
            }
            batchSwitchVO.setMasterIds(feignR.getData().stream().map(Long::valueOf).collect(Collectors.toList()));
        }
        MarketPageListService objectService = getService(listTypeEnum);
        boolean result = objectService.batchSwitch(batchSwitchVO);
        if (result) {
            this.noticeControl(listTypeEnum, batchSwitchVO.getIds(), batchSwitchVO.getUserId());
        }
        return result;
    }

    @Override
    public void updateMarketBudgetTest(MarketBudgetUpdateVO updateVO) {

        if (updateVO.getType().equals("campaign")) {
            this.campaignService.updateCampaignBudgetByNextDay(DateUtils.hourString2Date(updateVO.getReportDate()));
        } else if (updateVO.getType().equals("plan")) {
            this.planService.updatePlanBudgetByNextDay(DateUtils.hourString2Date(updateVO.getReportDate()));
        }
    }

    @Override
    public PageUtils<MarketBatchListDTO> listMarketBatch(MarketBatchListVO listVO, User user) {

        listVO.setIsPut(this.sheinConfiguration.isPut(listVO.getIsPut(), user.getRoleId()));
        IPage<MarketBatchListDTO> pageData;
        switch (listVO.getType()) {
            case "link":
                pageData = this.planService.listLinkBatch(listVO);
                break;
            case "asset":
                pageData = this.creativeUnitService.listCreativeUnitAsset(listVO);
                break;
            case "creativeUnit":
                pageData = this.planService.listCreativeUnitBatch(listVO);
                break;
            default:
                return new PageUtils<>(List.of(), 0L);
        }
        return new PageUtils<>(pageData);
    }

    @Override
    public List<MarketDimensionInfoDTO> dimensionInfo(MarketDimensionInfoVO dimensionInfoVO) {
        MonitorDimensionEnum dimensionEnum = ICommonEnum.get(dimensionInfoVO.getDimension(), MonitorDimensionEnum.class);
        switch (dimensionEnum) {
            case MASTER:
                return masterService.getMasterTimeZone(MasterTimeZoneGetVO.builder().masterIds(dimensionInfoVO.getIds().stream().map(Long::intValue).collect(Collectors.toList())).build())
                        .stream()
                        .map(u -> MarketDimensionInfoDTO.builder().name(String.format("%s-%s", u.getMasterId(), u.getMasterName()))
                                .timeZone(u.getTimeZone()).id(u.getMasterId()).masterId(u.getMasterId()).build()).
                        collect(Collectors.toList());
            case CAMPAIGN:
                return campaignService.listByIds(dimensionInfoVO.getIds()).stream()
                        .map(u -> MarketDimensionInfoDTO.builder().id(u.getId()).name(String.format("%s-%s", u.getId(), u.getCampaignName()))
                                .masterId(u.getMasterId()).build())
                        .collect(Collectors.toList());
            case PLAN:
                return planService.listByIds(dimensionInfoVO.getIds()).stream()
                        .map(u -> MarketDimensionInfoDTO.builder().id(u.getId()).name(String.format("%s-%s", u.getId(), u.getPlanName()))
                                .campaignId(u.getCampaignId()).masterId(u.getMasterId().longValue()).build())
                        .collect(Collectors.toList());
            default:
        }
        return List.of();
    }

    @Override
    public void doAfterNoticeByShein(Long id, User user, String listType) {
        Object result = null;
        switch (listType) {
            case "campaign":
                result = this.campaignService.syncSheinCampaign(id, user);
                break;
            case "plan":
                result = this.planService.noticeSheinPlan(id, user);
                break;
            case "creative_unit":
                CreativeUnit creativeUnit = this.creativeUnitService.getById(id);
                if (null == creativeUnit) {
                    return;
                }
                //刷新活动下计划
                result = this.planService.noticeSheinPlan(creativeUnit.getPlanId(), user);
                break;
            default:
        }
        if (null == result) {
            return;
        }
        if (result instanceof RecordBatchSwitchVO) {
            this.batchSwitchRecord((RecordBatchSwitchVO) result);
        }
        if (result instanceof RecordBatchDeleteVO) {
            this.batchDeleteRecord((RecordBatchDeleteVO) result);
        }
        if (result instanceof RecordBatchNoticeVO) {
            noticeControl(MarketListTypeEnum.PLAN, ((RecordBatchNoticeVO) result).getIds(), user.getId());
        }
    }

    /**
     * 根据列表类型通知中控
     *
     * @param listTypeEnum 列表类型枚举
     * @param ids          要通知的记录ID集合
     */
    private void noticeControl(MarketListTypeEnum listTypeEnum, List<Long> ids, Integer operateUid) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        if (listTypeEnum.equals(MarketListTypeEnum.CREATIVE_UNIT)) {
            this.sheinCreativeService.logByUnitIds(ids, operateUid);
        }
        ids.forEach(id -> {
            // 通知中控
            switch (listTypeEnum) {
                case CAMPAIGN:
                    applicationContext.publishEvent(new ControlCampaignEvent(this,
                            ControlContants.METHOD_UPDATE, id));
                    break;
                case PLAN:
                    applicationContext.publishEvent(new CreativeUnitAuditEvent(this, id));
                    break;
                case CREATIVE_UNIT:
                    applicationContext.publishEvent(new ControlCreativeUnitEvent(this,
                            ControlContants.METHOD_UPDATE, id));
                    break;
                default:
            }
        });
    }

    /**
     * 获取报表的分页数据
     *
     * @param reportListVO 查询参数
     * @param ids          广告主ID集合、活动ID集合或者是计划ID集合
     * @return 结果集
     */
    private IPage<ReportListDTO> pageReport(ReportListVO reportListVO, List<Long> ids) {
        reportListVO.setSearchIds(ids);
        log.info("report vo {}", reportListVO);
        FeignR<List<ReportListDTO>> feignR = fgReportService.getReportList(reportListVO);
        if (!feignR.getCode().equals(0)) {
            throw new CustomException(feignR.getCode(), feignR.getMsg());
        }
        IPage<ReportListDTO> reportPage = new Page<>();
        reportPage.setRecords(feignR.getData());
        reportPage.setTotal(feignR.getTotal());

        return reportPage;
    }

    /**
     * 获取报表的总计数据
     *
     * @param reportListVO 查询参数
     * @param ids          ID集合
     * @return 结果集
     */
    private ReportListDTO getTotalReport(ReportListVO reportListVO, List<Long> ids) {
        reportListVO.setSearchIds(ids);
        FeignR<ReportListDTO> totalFeignR = fgReportService.totalReport(reportListVO);
        if (!totalFeignR.getCode().equals(0)) {
            throw new CustomException(totalFeignR.getCode(), totalFeignR.getMsg());
        }
        if (null == totalFeignR.getData()) {
            return new ReportListDTO();
        } else {
            return totalFeignR.getData();
        }
    }

    /**
     * 打包数据
     */
    private PageUtils<MasterPageFirstDTO> packageList(IPage<MasterPageFirstDTO> mainPage, IPage<ReportListDTO> reportPage,
                                                      ReportListDTO reportTotal, Integer type, MarketListTypeEnum listTypeEnum,
                                                      MarketPageListService objectService, WebPlanStatusContainerDTO webPlanStatusContainerDTO,
                                                      Long masterId) {
        long total;
        List<MasterPageFirstDTO> resultList = new ArrayList<>();
        List<MasterPageFirstDTO> pageList = new ArrayList<>();
        // 设置全部数据
        if (null != reportTotal) {
            resultList.add(formatTotal(reportTotal, listTypeEnum.getFieldOfTotal()));
        }
        // 以投放表为主体
        if (type.equals(1)) {
            Map<Long, ReportListDTO> reportMap = objectService.getReportMapMarket(reportPage.getRecords());
            mainPage.getRecords().forEach(dto -> {
                if (reportMap.containsKey(dto.getId())) {
                    BeanUtils.copyProperties(reportMap.get(dto.getId()), dto, "agentId", "masterId", "campaignId", "planId", "creativeId", "creativeUnitId");
                }
                pageList.add(dto);
            });
            total = mainPage.getTotal();
        } else {
            // 以报表为主体
            Map<Long, MasterPageFirstDTO> mainMap = mainPage.getRecords().stream().collect(Collectors.toMap(MasterPageFirstDTO::getId, Function.identity()));
            reportPage.getRecords().forEach(report -> {
                Long id = objectService.getValueById(report);
                MasterPageFirstDTO dto = mainMap.containsKey(id) ? mainMap.get(id) : new MasterPageFirstDTO();
                BeanUtils.copyProperties(report, dto, "agentId", "masterId", "campaignId", "planId", "creativeId", "creativeUnitId");
                pageList.add(dto);
            });
            total = reportPage.getTotal();
        }
        objectService.formatListMarket(pageList, webPlanStatusContainerDTO, masterId);
        resultList.addAll(pageList);

        return new PageUtils<>(resultList, total);
    }

    /**
     * 格式化总体数据
     */
    private MasterPageFirstDTO formatTotal(ReportListDTO total, String fieldOfTotal) {
        MasterPageFirstDTO masterPageFirstDTO = new MasterPageFirstDTO();
        BeanUtils.copyProperties(total, masterPageFirstDTO);
        List<Field> reportFields = Arrays.asList(BaseReportDTO.class.getDeclaredFields());
        for (Field field : MasterPageFirstDTO.class.getDeclaredFields()) {
            if (!reportFields.contains(field)) {
                Object value = null;
                Class<?> type = field.getType();
                if (type.equals(Integer.class)) {
                    value = 0;
                } else if (type.equals(Long.class)) {
                    value = 0L;
                } else if (type.equals(String.class)) {
                    if (field.getName().equals(fieldOfTotal)) {
                        value = ConstantUtils.ALL;
                    } else {
                        value = ConstantUtils.PLACEHOLDER;
                    }
                } else if (type.equals(BigDecimal.class)) {
                    value = BigDecimal.ZERO;
                }
                if (!type.equals(List.class)) {
                    ObjectUtils.setObjectValue(masterPageFirstDTO, field.getName(), value);
                }
            }
        }
        return masterPageFirstDTO;
    }

    /**
     * 获取服务
     */
    private MarketPageListService getService(MarketListTypeEnum listTypeEnum) {
        switch (listTypeEnum) {
            case MASTER:
                return SpringContextUtils.getBean(MasterServiceImpl.class);
            case CAMPAIGN:
                return SpringContextUtils.getBean(CampaignServiceImpl.class);
            case CREATIVE_UNIT:
                return SpringContextUtils.getBean(CreativeUnitServiceImpl.class);
            default:
                return SpringContextUtils.getBean(PlanServiceImpl.class);
        }
    }

    /**
     * 设置报表VO默认值
     */
    private ReportListVO getReportListVO(MasterPageFirstVO masterPageFirstVO) {
        ReportListVO listVO = new ReportListVO();
        BeanUtils.copyProperties(masterPageFirstVO, listVO);
        listVO.setComparisonIndicator("");
        listVO.setIndicator("");
        return listVO;
    }

    /**
     * 根据计划ID获取shein 投放ID
     *
     * @param sheinIds    shein ID
     * @param mapTypeEnum 类型
     * @return 返回shein计划ID
     */
    private List<Long> getSheinMarketId(List<Long> sheinIds, PlanGenerateMapTypeEnum mapTypeEnum) {
        return planGenerateMapMapper.selectList(new LambdaQueryWrapper<PlanGenerateMap>()
                .eq(PlanGenerateMap::getMapType, mapTypeEnum.getId())
                .in(PlanGenerateMap::getSheinId, sheinIds)
        ).stream().map(PlanGenerateMap::getMarketId).collect(Collectors.toList());
    }
}
