package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.CascaderDTO;
import com.overseas.common.dto.TreeNodeDTO;
import com.overseas.common.dto.TreeNodeDTO2;
import com.overseas.common.dto.market.master.*;
import com.overseas.common.entity.BaseServer;
import com.overseas.common.enums.*;
import com.overseas.common.enums.sys.finance.UserStatusEnum;
import com.overseas.common.utils.*;
import com.overseas.common.vo.market.master.*;
import com.overseas.common.vo.report.SelectSortByCostVO;
import com.overseas.common.vo.sys.user.UserCheckGetVO;
import com.overseas.service.market.common.utils.BudgetUtils;
import com.overseas.service.market.dto.market.MasterPageFirstDTO;
import com.overseas.service.market.dto.plan.WebPlanStatusContainerDTO;
import com.overseas.service.market.enums.BudgetTypeEnum;
import com.overseas.service.market.enums.master.MasterAuditMasterStatusEnum;
import com.overseas.service.market.enums.master.MasterAuditStatusEnum;
import com.overseas.service.market.enums.master.MasterOperateTypeEnum;
import com.overseas.service.market.enums.master.MasterUserStatusEnum;
import com.overseas.service.market.enums.user.UserTypeEnum;
import com.overseas.service.market.events.notifyControl.ControlContants;
import com.overseas.service.market.events.notifyControl.ControlMasterEvent;
import com.overseas.service.market.events.operateMaster.OperateMasterEvent;
import com.overseas.service.market.events.unbindMaster.UnbindMasterEvent;
import com.overseas.service.market.feign.FgReportService;
import com.overseas.service.market.feign.FgSystemService;
import com.overseas.service.market.mapper.AuditMasterMapper;
import com.overseas.service.market.mapper.BaseServerMapper;
import com.overseas.service.market.mapper.DeepBidConfigMapper;
import com.overseas.service.market.mapper.MasterMapper;
import com.overseas.service.market.service.AuditAgentMasterService;
import com.overseas.service.market.service.MarketPageListService;
import com.overseas.service.market.service.MasterService;
import com.overseas.service.market.vo.master.MasterListVO;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.report.ReportListDTO;
import com.overseas.common.dto.sys.finance.UserFinanceListDTO;
import com.overseas.common.dto.sys.user.UserMailDTO;
import com.overseas.common.enums.market.BatchUpdateEnum;
import com.overseas.common.enums.market.FrequencyCycleEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.vo.market.market.MarketMasterGetVO;
import com.overseas.common.vo.market.market.RecordBatchDeleteVO;
import com.overseas.common.vo.market.market.RecordBatchSwitchVO;
import com.overseas.service.market.entity.*;
import com.overseas.service.market.vo.master.MasterRatioListVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MasterServiceImpl extends ServiceImpl<MasterMapper, Master> implements MasterService, MarketPageListService {

    private final FgSystemService fgSystemService;

    private final AuditMasterMapper auditMasterMapper;

    private final ApplicationContext applicationContext;

    private final AuditAgentMasterService auditAgentMasterService;

    private final DeepBidConfigMapper deepBidConfigMapper;

    private final BaseServerMapper baseServerMapper;

    private final FgReportService fgReportService;

    @Override
    public List<SelectDTO> selectMaster(List<Integer> permissionMasterIds) {
        return this.baseMapper.selectMaster(new QueryWrapper<Master>()
                .in("uu.id", permissionMasterIds)
                .orderByDesc("uu.id"));
    }

    @Override
    public List<SelectDTO> selectMasterBySort(MasterSelectVO selectVO, List<Integer> permissionMasterIds) {
        List<SelectDTO> masters = this.selectMaster(permissionMasterIds);
        if (CollectionUtils.isEmpty(masters)) {
            return masters;
        }
        SelectSortByCostVO sortByCostVO = new SelectSortByCostVO();
        sortByCostVO.setSelects(masters);
        sortByCostVO.setType(3);
        sortByCostVO.setStartDate(DateUtils.format(new Date()));
        sortByCostVO.setEndDate(sortByCostVO.getStartDate());
        sortByCostVO.setTimeZone(TimeZoneEnum.UTC_8.getId());
        sortByCostVO.setSortByCost(1);
        List<SelectDTO> result = fgReportService.selectSorByCost(sortByCostVO).getData();
        //删除已有的数据
        List<Long> masterIds = result.stream().map(SelectDTO::getId).collect(Collectors.toList());
        masters.removeIf(master -> masterIds.contains(master.getId()));
        result.addAll(masters);
        return result;
    }

    @Override
    public List<SelectDTO> selectMasterParams(MasterSelectVO selectVO, List<Integer> permissionMasterIds) {
        return this.baseMapper.selectParams(new QueryWrapper<Master>()
                .in("mpr.resource_id", permissionMasterIds)
                .eq(ObjectUtils.isNotNullOrZero(selectVO.getProjectId()), "mpr.project_id", selectVO.getProjectId())
        );
    }

    @Override
    public List<SelectDTO> selectMasterByInfo(MasterSelectByInfoVO byInfoVO) {
        return this.baseMapper.selectMaster(new QueryWrapper<Master>()
                .in(CollectionUtils.isNotEmpty(byInfoVO.getMasterIds()), "uu.id", byInfoVO.getMasterIds())
                .orderByDesc("uu.id"));
    }

    @Override
    public List<SelectDTO> getAllMasterSelect() {
        return this.baseMapper.selectMaster(new QueryWrapper<Master>()
                .eq("uu.user_type", UserTypeEnum.MARKET.getId())
                .eq("uu.user_status", UserStatusEnum.SUCCESS.getId())
                .eq("uu.is_destroy", IsDelEnum.NORMAL.getId())
                .orderByDesc("uu.id"));
    }

    @Override
    public PageUtils<MasterListDTO> pageMaster(MasterListVO masterListVO, List<Integer> permissionMasterIds, User loginUser) {

        IPage<MasterListDTO> pageData = this.baseMapper.getMasterPage(new Page<>(masterListVO.getPage(), masterListVO.getPageNum()), new QueryWrapper<Master>()
                .in("market.id", permissionMasterIds)
                .eq(ObjectUtils.isNotNullOrZero(masterListVO.getUserStatus()), "market.user_status", masterListVO.getUserStatus())
                .eq(ObjectUtils.isNotNullOrZero(masterListVO.getAuditMasterStatus()), "aam.audit_status", masterListVO.getAuditMasterStatus())
                .eq(ObjectUtils.isNotNullOrZero(masterListVO.getAuditMasterId()), "mm.audit_master_id", masterListVO.getAuditMasterId())
                .eq(ObjectUtils.isNotNullOrZero(masterListVO.getAgentId()), "market.parent_id", masterListVO.getAgentId())
                .and(StringUtils.isNotBlank(masterListVO.getSearch()),
                        i -> i.like("market.id", masterListVO.getSearch())
                                .or().like("market.company_name", masterListVO.getSearch()))
                .groupBy("market.id").orderByDesc("market.id")
                .having(masterListVO.getBindStatus().equals(1), "COUNT(uur.user_id) > 1")
                .having(masterListVO.getBindStatus().equals(2), "COUNT(uur.user_id) <= 1"));

        if (pageData.getRecords().isEmpty()) {
            return new PageUtils<>(List.of(), 0L);
        }
        // 获取全部的投放账户ID
        List<Long> masterIds = pageData.getRecords().stream().map(MasterListDTO::getId).collect(Collectors.toList());
        // 查列表中的投放账户所绑定的管理账户
        Map<Long, List<UserMailDTO>> managerMap = this.baseMapper.getManagerUserList(new QueryWrapper<Master>()
                .in("uur.resource_id", masterIds)
                .eq("uu.user_type", UserTypeEnum.MANAGER.getId())).stream().collect(Collectors.groupingBy(UserMailDTO::getMasterId));


        pageData.getRecords().forEach(masterListDTO -> {
            masterListDTO.setUserStatusName(ICommonEnum.getNameById(masterListDTO.getUserStatus(), MasterUserStatusEnum.class));
            masterListDTO.setAuditMasterStatusName(ObjectUtils.isNotNullOrZero(masterListDTO.getAuditMasterStatus()) ?
                    ICommonEnum.getNameById(masterListDTO.getAuditMasterStatus(), MasterAuditMasterStatusEnum.class) : ConstantUtils.UNKNOWN);
            List<UserMailDTO> managers = managerMap.getOrDefault(masterListDTO.getId(), List.of());
            masterListDTO.setManagers(managers);
            masterListDTO.setManagerEmail(managers.stream().map(UserMailDTO::getEmail).collect(Collectors.joining(",")));
            // 设置账户操作类型；如果是代理商列表，则支持绑定、复制、解绑
            masterListDTO.setOperates(new ArrayList<>() {{
                if (loginUser.getUserType().equals(UserTypeEnum.AGENT.getId())) {
                    addAll(List.of("update", "bind", "copy"));
                    if (CollectionUtils.isNotEmpty(masterListDTO.getManagers())) {
                        add("unbind");
                    }
                } else {
                    // 如果是管理账户，则支持访问账户
                    add("in");
                }
            }});
        });

        List<MasterRatioDTO> result = this.masterRatios(MasterRatioListVO.builder().masterIds(masterIds).build());
        Map<String, Object> headerMap = this.getHeaderMap();
        Map<String, Object> tokenMap = ObjectUtils.toMap(JSONObject.parseObject(headerMap.get("access-tokens").toString()));
        List<BaseServer> baseServers = this.baseServerMapper.selectList(new QueryWrapper<BaseServer>().lambda()
                .eq(BaseServer::getIsDel, IsDelEnum.NORMAL.getId())
                .ne(BaseServer::getServerKey,
                        headerMap.getOrDefault("machine-room", MachineRoomEnum.SG.getMachineRoom()).toString()));
        // 5.针对每个机房都进行请求
        baseServers.forEach(baseServer -> {
            // 获取请求header
            Map<String, Object> headers = new HashMap<>() {{
                putAll(headerMap);
                put("access-token", tokenMap.get(baseServer.getServerKey()));
                put("machine-room", baseServer.getServerKey());
            }};
            // 填充该机房对应token信息
            // 请求更新另一个机房数据
            FeignR<List<MasterRatioDTO>> resMap = JSONObject.parseObject(
                    HttpUtils.post(baseServer.getDomain() + "/market/masters/list/ratio",
                            new HashMap<>() {{
                                put("masterIds", masterIds);
                            }}, headers), new TypeReference<>() {
                    });
            if (resMap.getCode().equals(0)) {
                result.addAll(resMap.getData());
            }
        });
        Map<Long, List<MasterRatioDTO>> ratioMap = result.stream().collect(Collectors.groupingBy(MasterRatioDTO::getMasterId));
        pageData.getRecords().forEach(u -> {
            List<MasterRatioDTO> ratios = ratioMap.getOrDefault(u.getId(), new ArrayList<>());
            u.setDailyCostRatio(ratios.stream().collect(Collectors.toMap(MasterRatioDTO::getMachineRoom, MasterRatioDTO::getDailyCostRatio)));
            u.setReportCostRatio(ratios.stream().collect(Collectors.toMap(MasterRatioDTO::getMachineRoom, MasterRatioDTO::getReportCostRatio)));
        });
        return new PageUtils<>(pageData);
    }

    @Override
    public List<MasterRatioDTO> masterRatios(MasterRatioListVO listVO) {
        if (CollectionUtils.isEmpty(listVO.getMasterIds())) {
            return List.of();
        }
        Map<String, Object> headerMap = this.getHeaderMap();
        String machineRoom = headerMap.getOrDefault("machine-room", MachineRoomEnum.SG.getMachineRoom()).toString();
        return this.baseMapper.selectList(new LambdaQueryWrapper<Master>()
                .in(Master::getUserId, listVO.getMasterIds())
        ).stream().map(u ->
                MasterRatioDTO.builder().dailyCostRatio(u.getDailyCostRatio())
                        .reportCostRatio(u.getReportCostRatio())
                        .masterId(u.getUserId().longValue())
                        .machineRoom(machineRoom)
                        .build()
        ).collect(Collectors.toList());
    }

    /**
     * 分页数据
     *
     * @param listVO              查询参数
     * @param permissionMasterIds 有权限的广告主ID集合
     * @return 结果集
     */
    @Override
    public IPage<MasterPageFirstDTO> pageMarket(MasterPageFirstVO listVO, List<Integer> permissionMasterIds, List<Long> ids, WebPlanStatusContainerDTO webPlanStatusContainerDTO) {
        IPage<MasterPageFirstDTO> iPage = new Page<>(listVO.getPage(), listVO.getPageNum());
        return this.baseMapper.pageMarket(iPage, this.getQueryWrapper(listVO, permissionMasterIds, ids));
    }

    @Override
    public List<Long> listAllIdMarket(MasterPageFirstVO listVO, List<Integer> permissionMasterIds, WebPlanStatusContainerDTO webPlanStatusContainerDTO) {
        return this.baseMapper.listMasterId(this.getQueryWrapper(listVO, permissionMasterIds, null));
    }

    @Override
    public List<Long> listAllMasterIdMarket(MasterPageFirstVO listVO, List<Integer> permissionMasterIds, WebPlanStatusContainerDTO webPlanStatusContainerDTO) {
        return this.listAllIdMarket(listVO, permissionMasterIds, webPlanStatusContainerDTO);
    }

    @Override
    public Map<Long, ReportListDTO> getReportMapMarket(List<ReportListDTO> reportList) {
        return reportList.stream().collect(Collectors.toMap(ReportListDTO::getMasterId, Function.identity()));
    }

    @Override
    public void formatListMarket(List<MasterPageFirstDTO> list, WebPlanStatusContainerDTO webPlanStatusContainerDTO, Long masterId) {
        // 获取账户余额数据
        Map<Integer, String> masterSurplusMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            FeignR<List<UserFinanceListDTO>> feignR = fgSystemService.listUserFinance(list.stream().map(i -> i.getId().intValue()).collect(Collectors.toList()));
            if (!feignR.getCode().equals(0)) {
                throw new CustomException(feignR.getMsg());
            }
            masterSurplusMap = feignR.getData().stream().collect(Collectors.toMap(UserFinanceListDTO::getUserId, i -> i.getBalance().toString()));
        }
        for (MasterPageFirstDTO record : list) {
            record.setBudgetDay(BudgetUtils.format(record.getBudgetDay(), BudgetTypeEnum.COST.getId()));
            record.setUserStatusName(ICommonEnum.getNameById(record.getUserStatus(), MasterUserStatusEnum.class));
            record.setMasterSurplus(masterSurplusMap.getOrDefault(record.getId().intValue(), "0"));
            record.setFrequencyCycleViewName(ICommonEnum.getNameById(record.getFrequencyCycleView(), FrequencyCycleEnum.class));
            record.setFrequencyCycleClickName(ICommonEnum.getNameById(record.getFrequencyCycleClick(), FrequencyCycleEnum.class));
            record.setTimeZoneName(TimeZoneEnum.getNameById(record.getTimeZone()));
        }
    }

    @Override
    public List<Long> listReportIdMarket(List<ReportListDTO> reportList) {
        return reportList.stream().map(ReportListDTO::getMasterId).collect(Collectors.toList());
    }

    /**
     * 公共方法获取查询条件（适用于推广页面）
     *
     * @param listVO              查询参数
     * @param permissionMasterIds 有权限的广告主ID集合
     * @param ids                 计划ID集合
     * @return 结果集
     */
    private QueryWrapper<Master> getQueryWrapper(MasterPageFirstVO listVO, List<Integer> permissionMasterIds, List<Long> ids) {
        QueryWrapper<Master> queryWrapper = new QueryWrapper<>();
        if (CollectionUtils.isEmpty(ids)) {
            queryWrapper.in("u.id", permissionMasterIds)
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getAuditMasterId()), "m.audit_master_id", listVO.getAuditMasterId())
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getAgentId()), "agent.id", listVO.getAgentId())
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getUserStatus()), "u.user_status", listVO.getUserStatus())
                    .and(StringUtils.isNotBlank(listVO.getSearch()),
                            i -> i.like("u.id", listVO.getSearch())
                                    .or().like("u.company_name", listVO.getSearch())
                    );
        } else {
            queryWrapper.in("u.id", ids);
        }
        queryWrapper.orderBy(true, SortTypeEnum.ASC.getSortType().equals(listVO.getSortType()), "u.id")
                .groupBy("u.id");
        return queryWrapper;
    }

    @Override
    @Transactional
    public Map<String, Object> saveMaster(MasterSaveVO masterSaveVO, Integer loginUserId) {
        Map<String, Object> result = new HashMap<>();
        // 保存用户数据
        User userSave = new User();
        BeanUtils.copyProperties(masterSaveVO, userSave);
        userSave.setParentId(loginUserId);
        userSave.setCreateUid(loginUserId);
        FeignR<Integer> userR = fgSystemService.saveMasterBaseInfo(userSave);
        if (!userR.getCode().equals(0)) {
            if (ObjectUtils.isNotNullOrZero(masterSaveVO.getCopyId())) {
                this.applicationContext.publishEvent(new OperateMasterEvent(
                        this,
                        masterSaveVO.getCopyId(),
                        "复制",
                        "失败，" + userR.getMsg(),
                        IPUtils.getIpAddr(),
                        loginUserId));
            }
            throw new CustomException(userR.getMsg());
        }
        // 保存广告主数据
        Master masterSave = new Master();
        BeanUtils.copyProperties(masterSaveVO, masterSave);
        // 如果VO中masterId不为空，则表明需要设置指定ID
        if (ObjectUtils.isNotNullOrZero(masterSaveVO.getMasterId())) {
            masterSave.setId(masterSaveVO.getMasterId());
        }
        // 增加资质的审核状态
        // 临时去除投放帐号资质审核状态设置逻辑
        if (ObjectUtils.isNotNullOrZero(masterSaveVO.getAuditMasterId())) {
            AuditAgentMaster agentMasterInDb = auditAgentMasterService.getAgentMaster(masterSaveVO.getAuditMasterId(), loginUserId);
            MasterAuditStatusEnum masterAuditStatus = MasterAuditStatusEnum.getMasterAuditStatus(agentMasterInDb.getAuditStatus());
            masterSave.setAuditStatus(masterAuditStatus.getId());
            masterSave.setReason(MasterAuditStatusEnum.PASS.equals(masterAuditStatus) ? "" : "资质不可用");
        } else {
            masterSave.setAuditStatus(MasterAuditStatusEnum.PASS.getId());
            masterSave.setReason("");
        }
        result.put("userId", userR.getData());
        masterSave.setUserId(userR.getData());
        this.save(masterSave);
        result.put("masterId", masterSave.getId());
        if (ObjectUtils.isNotNullOrZero(masterSaveVO.getCopyId())) {
            this.applicationContext.publishEvent(new OperateMasterEvent(
                    this,
                    masterSaveVO.getCopyId(),
                    "复制",
                    "成功",
                    IPUtils.getIpAddr(),
                    loginUserId));
        }
        this.applicationContext.publishEvent(new OperateMasterEvent(
                this,
                masterSave.getUserId(),
                "新建",
                "成功",
                IPUtils.getIpAddr(),
                loginUserId));
        return result;
    }

    @Override
    public void updateMaster(MasterSaveVO saveVO, Integer userId) {

        UserCheckGetVO getVO = new UserCheckGetVO();
        getVO.setCompanyName(saveVO.getCompanyName());
        getVO.setId(saveVO.getId().longValue());
        User user = this.fgSystemService.checkUserIsExist(getVO).getData();
        if (user != null) {
            throw new CustomException("账号名称重复，请确认后再试");
        }
        Master master = new Master();
        BeanUtils.copyProperties(saveVO, master);
        this.baseMapper.update(master, new QueryWrapper<Master>().lambda()
                .eq(Master::getId, saveVO.getId()));

        user = new User();
        BeanUtils.copyProperties(saveVO, user);
        this.fgSystemService.updateUser(user);
    }

    @Override
    public MasterGetDTO getMaster(MasterGetVO masterGetVO, Integer loginUserId) {
        FeignR<User> userFeignR = fgSystemService.getMasterBaseInfo(masterGetVO.getId());
        if (!userFeignR.getCode().equals(0)) {
            throw new CustomException("获取投放账号基础信息失败");
        }
        MasterGetDTO masterGetDTO = new MasterGetDTO();
        BeanUtils.copyProperties(userFeignR.getData(), masterGetDTO);
        // 补充投放账号表信息
        Master master = getMaster(masterGetVO.getId());
        BeanUtils.copyProperties(master, masterGetDTO);
        Integer agentId = userFeignR.getData().getParentId();
        masterGetDTO.setAgentId(agentId);
        FeignR<User> agentR = fgSystemService.getUserBaseInfo(agentId);
        if (!agentR.getCode().equals(0)) {
            throw new CustomException("获取代理商基础信息失败");
        }
        masterGetDTO.setAgentName(agentR.getData().getCompanyName());
        // 补充主体信息
        AuditMaster auditMaster = auditMasterMapper.selectById(master.getAuditMasterId());
        if (null != auditMaster) {
            masterGetDTO.setAuditMasterName(auditMaster.getBusinessLicence());
        } else {
            masterGetDTO.setAuditMasterName(ConstantUtils.UNKNOWN);
        }
        return masterGetDTO;
    }

    @Override
    public void bind(MasterBindVO masterBindVO, User loginUser) {
        // 管家不具备解绑权限
        if (UserTypeEnum.MANAGER.getId().equals(loginUser.getUserType()) && masterBindVO.getOperateType().equals(2)) {
            String error = "管家不具备解绑权限";
            this.applicationContext.publishEvent(new OperateMasterEvent(
                    this,
                    masterBindVO.getId(),
                    "解除授权",
                    "失败，" + error,
                    IPUtils.getIpAddr(),
                    loginUser.getId()));
            throw new CustomException(error);
        }
        if (StringUtils.isEmpty(masterBindVO.getEmail()) && masterBindVO.getOperateType().equals(1)) {
            String error = "绑定时邮箱不能为空";
            this.applicationContext.publishEvent(new OperateMasterEvent(
                    this,
                    masterBindVO.getId(),
                    UserTypeEnum.MANAGER.getId().equals(loginUser.getUserType()) ? "换绑" : "授权",
                    "失败，" + error,
                    IPUtils.getIpAddr(),
                    loginUser.getId()));
            throw new CustomException(error);
        }

        FeignR<Integer> managerR = fgSystemService.bindManager(masterBindVO);
        if (!managerR.getCode().equals(0)) {
            this.applicationContext.publishEvent(new OperateMasterEvent(
                    this,
                    masterBindVO.getId(),
                    UserTypeEnum.MANAGER.getId().equals(loginUser.getUserType()) ? "换绑" : "授权",
                    "失败，" + managerR.getMsg(),
                    IPUtils.getIpAddr(),
                    loginUser.getId()));
            throw new CustomException(managerR.getMsg());
        }
        // 广告主表增加管家ID
        this.applicationContext.publishEvent(new OperateMasterEvent(
                this,
                masterBindVO.getId(),
                masterBindVO.getOperateType().equals(2)
                        ? "解除授权"
                        : (UserTypeEnum.MANAGER.getId().equals(loginUser.getUserType()) ? "换绑" : "授权"),
                "成功",
                IPUtils.getIpAddr(),
                loginUser.getId()));
        if (masterBindVO.getOperateType().equals(MasterOperateTypeEnum.UN_BIND.getId())) {
            List<User> managers = this.fgSystemService.getManagersByMaster(
                    new MarketMasterGetVO(masterBindVO.getId().longValue())).getData();
            // 当账户没有关联的管理账户时，解绑成功需要暂停该账户下的活动、计划和创意，发布解绑事件
            if (managers.isEmpty()) {
                applicationContext.publishEvent(new UnbindMasterEvent(this, masterBindVO.getId()));
            }
        }
    }

    @Override
    public MasterBindInfoGetDTO getBindInfo(MasterBindInfoGetVO masterBindInfoGetVO) {

        MasterBindInfoGetDTO masterBindInfoGetDTO = new MasterBindInfoGetDTO();
        User user = this.fgSystemService.getMasterBaseInfo(masterBindInfoGetVO.getId()).getData();
        masterBindInfoGetDTO.setId(user.getId());
        masterBindInfoGetDTO.setCompanyName(user.getCompanyName());
        masterBindInfoGetDTO.setManagerEmail(user.getUserName());
        masterBindInfoGetDTO.setManagerId(Long.valueOf(user.getId()));
        masterBindInfoGetDTO.setManagerName(user.getRealName());

        List<MasterBindInfoGetDTO> masterBindInfoGetDTOs = this.baseMapper.getBindManagers(new QueryWrapper<Master>()
                .eq("manager.user_type", UserTypeEnum.MANAGER.getId())
                .in("uur.resource_id", masterBindInfoGetVO.getId()));
        if (masterBindInfoGetDTOs.isEmpty()) {
            return masterBindInfoGetDTO;
        }

        masterBindInfoGetDTO.setManagers(masterBindInfoGetDTOs.stream().map(u -> {
            UserMailDTO userMailDTO = new UserMailDTO();
            userMailDTO.setId(u.getManagerId());
            userMailDTO.setEmail(u.getManagerEmail());
            userMailDTO.setName(u.getManagerName());
            return userMailDTO;
        }).collect(Collectors.toList()));
        return masterBindInfoGetDTO;
    }

    @Override
    public void updateBudgetDay(MasterBudgetUpdateVO masterBudgetUpdateVO, Integer loginUserId) {
        Master master = getMaster(masterBudgetUpdateVO.getId());
        master.setBudgetDay(BudgetUtils.inDb(new BigDecimal(masterBudgetUpdateVO.getBudgetDay()), BudgetTypeEnum.COST.getId()));
        this.updateById(master);
    }

    @Override
    public void updateReportCostRoute(MasterCostRatioVO costRatioVO) {
        BaseServer baseServer = baseServerMapper.selectOne(new LambdaQueryWrapper<BaseServer>()
                .eq(BaseServer::getServerKey, costRatioVO.getMachineRoom())
                .eq(BaseServer::getIsDel, IsDelEnum.NORMAL.getId())
                .orderByDesc(BaseServer::getId)
                .last("limit 1")
        );
        if (null == baseServer) {
            throw new CustomException("机房信息不存在");
        }
        Map<String, Object> headerMap = this.getHeaderMap();
        Map<String, Object> tokenMap = ObjectUtils.toMap(JSONObject.parseObject(headerMap.get("access-tokens").toString()));
        // 获取请求header
        Map<String, Object> headers = new HashMap<>() {{
            putAll(headerMap);
            put("access-token", tokenMap.get(baseServer.getServerKey()));
        }};
        JSONObject resp = JSONObject.parseObject(
                HttpUtils.post(baseServer.getDomain() + "/market/masters/costRatio/update",
                        ObjectUtils.toMap(costRatioVO),
                        headers)
        );
        if (resp.getInteger("code") != 0) {
            throw new CustomException(resp.getString("message"));
        }
    }

    @Override
    public void updateReportCost(MasterCostRatioVO costRatioVO) {
        switch (costRatioVO.getCostType()) {
            case "reportCostRatio":
                this.baseMapper.updateReportCostRatio(costRatioVO.getCostRatio(), costRatioVO.getId());
                break;
            case "dailyCostRatio":
                this.baseMapper.updateDailyCostRatio(costRatioVO.getCostRatio(), costRatioVO.getId());
                break;
            default:
        }
    }

    @Override
    public List<SelectDTO> selectByMasterId(MasterGetVO getVO) {
        return this.baseMapper.selectByMasterId(getVO.getId().longValue());
    }

    @Override
    public Long getValueById(ReportListDTO reportDTO) {
        return reportDTO.getMasterId();
    }

    @Override
    public boolean batchDelete(RecordBatchDeleteVO batchDeleteVO) {
        return false;
    }

    @Override
    public boolean batchSwitch(RecordBatchSwitchVO batchSwitchVO) {
        return false;
    }

    private Master getMaster(Integer masterId) {
        Master master = this.getOne(new LambdaQueryWrapper<Master>().eq(Master::getUserId, masterId));
        if (null == master) {
            throw new CustomException("获取投放账号扩展信息失败");
        }
        return master;
    }

    @Override
    public void batchUpdateMaster(MasterBatchUpdateVO updateVO, Integer userId) {

        Master master = new Master();
        master.setUpdateUid(userId);
        // 修改账户预算
        if (BatchUpdateEnum.IS_CHANGE.getId().equals(updateVO.getUpdateBudgetDay())) {
            master.setBudgetDay(updateVO.getBudgetDay());
        }
        // 修改曝光频次
        if (BatchUpdateEnum.IS_CHANGE.getId().equals(updateVO.getUpdateFrequencyView())) {
            master.setFrequencyNumView(updateVO.getFrequencyNumView());
            master.setFrequencyCycleView(updateVO.getFrequencyCycleView());
        }
        // 修改点击频次
        if (BatchUpdateEnum.IS_CHANGE.getId().equals(updateVO.getUpdateFrequencyClick())) {
            master.setFrequencyNumClick(updateVO.getFrequencyNumClick());
            master.setFrequencyCycleClick(updateVO.getFrequencyCycleClick());
        }
        // 修改时区
        if (BatchUpdateEnum.IS_CHANGE.getId().equals(updateVO.getUpdateTimeZone())) {
            master.setTimeZone(updateVO.getTimeZone());
        }
        // 如果都不做修改，则直接返回
        if (BatchUpdateEnum.NOT_CHANGE.getId().equals(updateVO.getUpdateBudgetDay()) &&
                BatchUpdateEnum.NOT_CHANGE.getId().equals(updateVO.getUpdateFrequencyView()) &&
                BatchUpdateEnum.NOT_CHANGE.getId().equals(updateVO.getUpdateFrequencyClick()) &&
                BatchUpdateEnum.NOT_CHANGE.getId().equals(updateVO.getUpdateTimeZone())) {
            return;
        }
        this.baseMapper.update(master, new QueryWrapper<Master>().lambda().in(Master::getUserId, updateVO.getMasterIds()));

        // 通知中控
        updateVO.getMasterIds().forEach(masterId -> applicationContext.publishEvent(
                new ControlMasterEvent(this, ControlContants.METHOD_UPDATE, masterId)));
    }

    @Override
    public List<SelectDTO> getMasterActionSelect(MarketMasterGetVO getVO) {
        return this.deepBidConfigMapper.selectDeepBidConfig(new QueryWrapper<DeepBidConfig>().lambda()
                .eq(DeepBidConfig::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(DeepBidConfig::getMasterId, getVO.getMasterId())
                .orderByDesc(DeepBidConfig::getTrackerActionId));
    }

    @Override
    public List<MasterTimeZoneDTO> getMasterTimeZone(MasterTimeZoneGetVO getVO) {
        TimeZoneEnum timeZoneEnum = TimeZoneEnum.get(getVO.getTimeZone());
        return this.baseMapper.listMasterTimeZone(new QueryWrapper<Master>().lambda()
                .eq(ObjectUtils.isNotNullOrZero(getVO.getMasterId()), Master::getUserId, getVO.getMasterId())
                .in(CollectionUtils.isNotEmpty(getVO.getMasterIds()), Master::getUserId, getVO.getMasterIds())
                .eq(null != timeZoneEnum, Master::getTimeZone, getVO.getTimeZone()));
    }

    @Override
    public List<MasterProjectDTO> listMasterProject(MasterProjectGetVO getVO) {
        return this.baseMapper.listMasterProject(new QueryWrapper<Master>()
                .eq(ObjectUtils.isNotNullOrZero(getVO.getMasterId()), "mpr.resource_id", getVO.getMasterId())
                .in(CollectionUtils.isNotEmpty(getVO.getMasterIds()), "mpr.resource_id", getVO.getMasterIds())
                // 先过滤掉AE和lazada_rta的项目
                .ne("mp.id", 1)
                .eq("mpr.is_del", IsDelEnum.NORMAL.getId())
                .eq("mp.is_del", IsDelEnum.NORMAL.getId()));
    }

    @Override
    public List<CascaderDTO> listMasterCascader(MasterCascaderGetVO getVO) {

        List<TreeNodeDTO2> treeNodeDTO2s = this.getMasterTreeList(getVO);
        if (CollectionUtils.isEmpty(treeNodeDTO2s)) {
            return List.of();
        }
        Map<Long, List<TreeNodeDTO2>> map = treeNodeDTO2s.stream().collect(Collectors.groupingBy(TreeNodeDTO2::getPId));
        List<CascaderDTO> cascaderDTOS = new ArrayList<>();
        map.forEach((projectId, list) -> {
            CascaderDTO cascaderDTO = new CascaderDTO();
            cascaderDTO.setValue(projectId);
            cascaderDTO.setLabel(list.get(0).getPName());
            cascaderDTO.setChildren(list.stream().map(u -> {
                CascaderDTO cascader = new CascaderDTO();
                cascader.setValue(u.getId());
                cascader.setLabel(u.getName());
                return cascader;
            }).collect(Collectors.toList()));
            cascaderDTOS.add(cascaderDTO);
        });

        return cascaderDTOS;
    }

    @Override
    public List<TreeNodeDTO> listMasterTree(MasterCascaderGetVO getVO) {

        List<TreeNodeDTO2> treeNodeDTO2s = this.getMasterTreeList(getVO);
        if (CollectionUtils.isEmpty(treeNodeDTO2s)) {
            return List.of();
        }
        Map<Long, TreeNodeDTO> projectMap = new HashMap<>();
        List<TreeNodeDTO> childrenNodes = new ArrayList<>();
        treeNodeDTO2s.forEach(treeNodeDTO2 -> {
            projectMap.putIfAbsent(treeNodeDTO2.getPId(), new TreeNodeDTO(treeNodeDTO2.getPId(), treeNodeDTO2.getPName(), 0L));
            childrenNodes.add(new TreeNodeDTO(treeNodeDTO2.getId(), treeNodeDTO2.getName(), treeNodeDTO2.getPId()));
        });
        return new ArrayList<>() {{
            addAll(projectMap.values());
            addAll(childrenNodes);
        }};
    }

    private List<TreeNodeDTO2> getMasterTreeList(MasterCascaderGetVO getVO) {
        return this.baseMapper.listMasterCascader(new QueryWrapper<Master>()
                .eq("uu.user_status", UserStatusEnum.SUCCESS.getId())
                .eq("uu.is_destroy", IsDelEnum.NORMAL.getId())
                .eq("uu.user_type", UserTypeEnum.MARKET.getId())
                .and(q -> q.eq("mpr.is_del", IsDelEnum.NORMAL.getId())
                        .or().isNull("mpr.is_del"))
                .eq(ObjectUtils.isNotNullOrZero(getVO.getProjectId()), "mp.id", getVO.getProjectId())
                .orderByDesc("uu.id"));
    }


    /**
     * 获取当前header
     *
     * @return 返回header
     */
    private Map<String, Object> getHeaderMap() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert servletRequestAttributes != null;
        HttpServletRequest httpServletRequest = servletRequestAttributes.getRequest();
        Enumeration<String> headerNames = httpServletRequest.getHeaderNames();
        Map<String, Object> headerMap = new HashMap<>();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            headerMap.put(headerName, httpServletRequest.getHeader(headerName));
        }
        return headerMap;
    }
}
