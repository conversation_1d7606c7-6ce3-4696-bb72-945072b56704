package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.Gson;
import com.overseas.common.dto.market.creativeUnit.CreativeUnitMaterialSaveDTO;
import com.overseas.common.vo.market.copyWriting.CopyWritingBatchSaveItemVO;
import com.overseas.common.vo.market.copyWriting.CopyWritingBatchSaveVO;
import com.overseas.common.vo.market.material.*;
import com.overseas.service.market.entity.Asset;
import com.overseas.service.market.entity.CreativeUnit;
import com.overseas.service.market.entity.Material;
import com.overseas.service.market.entity.MaterialAsset;
import com.overseas.service.market.enums.assets.AssetTypeEnum;
import com.overseas.service.market.enums.creative.units.CreativeUnitStatusEnum;
import com.overseas.service.market.enums.material.MaterialAssetTypeEnum;
import com.overseas.service.market.mapper.CreativeUnitMapper;
import com.overseas.service.market.mapper.MaterialAssetMapper;
import com.overseas.service.market.mapper.MaterialMapper;
import com.overseas.service.market.service.AssetService;
import com.overseas.service.market.service.CopyWritingService;
import com.overseas.service.market.service.MaterialService;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ObjectUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-25 11:16
 */
@Service
@RequiredArgsConstructor
public class MaterialServiceImpl extends ServiceImpl<MaterialMapper, Material> implements MaterialService {

    private final AssetService assetService;

    private final MaterialAssetMapper materialAssetMapper;

    private final CreativeUnitMapper creativeUnitMapper;

    private final CopyWritingService copyWritingService;

    @Override
    @Transactional
    public Map<String, CreativeUnitMaterialSaveDTO> saveMaterial(MaterialSaveVO saveVO, Integer userId) {

        // 1：以素材ID组，平铺数据
        List<MaterialGroupSaveVO> materialGroupSaveVOList = new ArrayList<>();
        for (MaterialVO materialVO : saveVO.getAssets()) {
            // 如果素材组ID为0，则表示新增
            MaterialGroupSaveVO materialGroupSaveVO = new MaterialGroupSaveVO();
            List<MaterialAssetVO> assetList = new ArrayList<>();
            MaterialGroupVO assets = materialVO.getAssets();
            // 加入主素材
            assets.getAsset().setFieldType(MaterialAssetTypeEnum.PRIMARY_ASSET.getId());
            assetList.add(assets.getAsset());
            // 添加所有文本
            if (assets.getTitle() != null && StringUtils.isNotBlank(assets.getTitle().getContent())) {
                assets.getTitle().setFieldName(MaterialAssetTypeEnum.TITLE.getFieldName());
                assets.getTitle().setFieldType(MaterialAssetTypeEnum.TITLE.getId());
                assetList.add(assets.getTitle());
            }
            if (assets.getDescription() != null && StringUtils.isNotBlank(assets.getDescription().getContent())) {
                assets.getDescription().setFieldName(MaterialAssetTypeEnum.DESCRIPTION.getFieldName());
                assets.getDescription().setFieldType(MaterialAssetTypeEnum.DESCRIPTION.getId());
                assetList.add(assets.getDescription());
            }
            // 添加EndCard
            if (assets.getEndcard() != null) {
                assets.getEndcard().setFieldName(MaterialAssetTypeEnum.END_CARD.getFieldName());
                assets.getEndcard().setFieldType(MaterialAssetTypeEnum.END_CARD.getId());
                assetList.add(assets.getEndcard());
            }
            // 添加伴随广告
            if (assets.getConcurrent() != null) {
                assets.getConcurrent().setFieldName(MaterialAssetTypeEnum.CONCURRENT.getFieldName());
                assets.getConcurrent().setFieldType(MaterialAssetTypeEnum.CONCURRENT.getId());
                assetList.add(assets.getConcurrent());
            }
            // 添加ICON,PUSH副图广告
            if (assets.getImg() != null) {
                assets.getImg().setFieldName(MaterialAssetTypeEnum.IMG.getFieldName());
                assets.getImg().setFieldType(MaterialAssetTypeEnum.IMG.getId());
                assetList.add(assets.getImg());
            }

            if (CollectionUtils.isNotEmpty(assetList)) {
                assetList = assetList.stream().filter(i ->
                        !AssetTypeEnum.TEXT.getId().equals(i.getAssetType()) || StringUtils.isNotEmpty(i.getContent())
                ).collect(Collectors.toList());
                assetList.forEach(assetVO -> {
                    if (AssetTypeEnum.TEXT.getId().equals(assetVO.getAssetType())) {
                        assetVO.setAssetId(assetService.saveTextAsset(assetVO.getContent(), userId).getId());
                    }
                });
                //批量插入文案数据
                copyWritingService.batchCreativeSaveCopyWriting(
                        CopyWritingBatchSaveVO.builder().masterId(saveVO.getMasterId())
                                .copyWritingList(assetList.stream().filter(i -> AssetTypeEnum.TEXT.getId().equals(i.getAssetType()))
                                        .map(i -> {
                                            CopyWritingBatchSaveItemVO itemVO = new CopyWritingBatchSaveItemVO();
                                            itemVO.setCopyWritingType(i.getFieldType());
                                            itemVO.setCountryId(i.getCountryId());
                                            itemVO.setTranslatedText(i.getTranslatedText());
                                            itemVO.setCopyWriting(i.getContent());
                                            itemVO.setAssetId(i.getAssetId());
                                            return itemVO;
                                        }).collect(Collectors.toList())

                                ).build(), userId);
            }
            materialGroupSaveVO.setParentId(ObjectUtils.isNotNullOrZero(materialVO.getParentId()) ? materialVO.getParentId() : 0);
            materialGroupSaveVO.setAssetList(assetList);
            materialGroupSaveVO.setLandingUrl(materialVO.getLandingUrl());
            materialGroupSaveVO.setCreativeUnitName(materialVO.getCreativeUnitName());
            materialGroupSaveVO.setTemplateId(materialVO.getTemplateId());
            materialGroupSaveVO.setTemplateAttr(materialVO.getTemplateAttr());
            materialGroupSaveVOList.add(materialGroupSaveVO);
        }
        if (materialGroupSaveVOList.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, CreativeUnitMaterialSaveDTO> materialMap = new HashMap<>();
        // 2：存储素材组素材数据至m_material_asset
        for (MaterialGroupSaveVO materialGroupSaveVO : materialGroupSaveVOList) {
            // 根据内容生成MD5信息
            materialGroupSaveVO.setMd5(this.generateMd5(materialGroupSaveVO.getAssetList()));

            // 3：保存Material表数据
            Material materialInDb;
            try {
                materialInDb = getByMd5AndMasterId(materialGroupSaveVO.getMd5(), saveVO.getMasterId());
            } catch (CustomException e) {
                materialInDb = new Material();
                materialInDb.setMasterId(saveVO.getMasterId().intValue());
                materialInDb.setParentId(materialGroupSaveVO.getParentId());
                materialInDb.setMd5(materialGroupSaveVO.getMd5());
                save(materialInDb);

                // 4：保存MaterialAsset表数据
                Long materialId = materialInDb.getId();
                materialGroupSaveVO.getAssetList().forEach(assetVO -> {
                    MaterialAsset materialAsset = new MaterialAsset();
                    BeanUtils.copyProperties(assetVO, materialAsset);
                    materialAsset.setMaterialId(materialId);
                    materialAsset.setCreateUid(userId);
                    materialAssetMapper.insert(materialAsset);
                });
            }
            //如果模版不为空，则序列化templateAttr
            if (ObjectUtils.isNotNullOrZero(materialGroupSaveVO.getTemplateId())) {
                materialGroupSaveVO.setTemplateAttrStr(JSONObject.toJSONString(materialGroupSaveVO.getTemplateAttr().stream().sorted()
                        .collect(Collectors.toList())));
            } else {
                materialGroupSaveVO.setTemplateAttrStr("");
            }
            MaterialAssetVO assetVO = materialGroupSaveVO.getAssetList().stream()
                    .filter(materialAssetVO -> materialAssetVO.getFieldType().equals(MaterialAssetTypeEnum.PRIMARY_ASSET.getId()))
                    .collect(Collectors.toList()).get(0);
            materialMap.put(this.materialKey(materialInDb.getId(), materialGroupSaveVO),
                    CreativeUnitMaterialSaveDTO.builder()
                            .assetType(assetVO.getAssetType())
                            .landingUrl(materialGroupSaveVO.getLandingUrl())
                            .templateId(materialGroupSaveVO.getTemplateId())
                            .templateAttr(materialGroupSaveVO.getTemplateAttrStr())
                            .materialId(materialInDb.getId())
                            .creativeUnitName(materialGroupSaveVO.getCreativeUnitName())
                            .build()
            );
        }
        return materialMap;
    }

    /**
     * material key
     *
     * @param materialId          materialId
     * @param materialGroupSaveVO 素材内容
     * @return 返回数据
     */
    private String materialKey(Long materialId, MaterialGroupSaveVO materialGroupSaveVO) {
        return String.format("%s-%s-%s-%s", materialId, materialGroupSaveVO.getLandingUrl(),
                materialGroupSaveVO.getTemplateId(), materialGroupSaveVO.getTemplateAttrStr()
        );
    }

    @Override
    public List<MaterialAssetVO> getMaterialDetail(MaterialGetVO getVO) {
        List<MaterialAssetVO> assetVOS = ObjectUtils.isNotNullOrZero(getVO.getMasterId()) ?
                this.baseMapper.getAssetCopyWritingByMaterialId(new QueryWrapper<Material>()
                        .and(q -> q.eq("writing.master_id", getVO.getMasterId())
                                .or().isNull("writing.master_id"))
                        .eq("mma.material_id", getVO.getId()).orderByAsc("mma.id"))
                : this.baseMapper.getAssetByMaterialId(new QueryWrapper<Material>()
                .eq("mma.material_id", getVO.getId()).orderByAsc("mma.id"));
        assetVOS.forEach(assetVO -> {
            Asset assetTmp = new Asset();
            BeanUtils.copyProperties(assetVO, assetTmp);
            assetService.formatAsset(assetTmp);
            assetVO.setHttpUrl(assetTmp.getHttpUrl());
            assetVO.setCoverImgUrl(assetTmp.getCoverImgUrl());
        });
        // 填充素材文本信息
        this.fillMaterialTextAsset(assetVOS);
        return assetVOS;
    }

    @Override
    public List<MaterialVO> getCreativeMaterialList(MaterialListVO listVO) {

        // 1：获取创意下所有素材组ID
        List<MaterialVO> materialList = this.creativeUnitMapper.getMaterialList(new QueryWrapper<CreativeUnit>()
                .eq("mcu.is_del", IsDelEnum.NORMAL.getId())
                .eq("mm.is_del", IsDelEnum.NORMAL.getId())
                .eq("mcu.master_id", listVO.getMasterId())
                .eq("mcu.creative_id", listVO.getCreativeId())
                // 查询非归档状态下的创意单元
                .ne("mcu.creative_unit_status", CreativeUnitStatusEnum.FILE.getId())
                .orderByAsc("mcu.id"));
        if (materialList.isEmpty()) {
            return List.of();
        }
        // 2：获取素材组所有素材信息
        materialList.forEach(materialVO -> {
            MaterialGetVO getVO = new MaterialGetVO();
            getVO.setId(materialVO.getId());
            getVO.setMasterId(listVO.getMasterId());
            List<MaterialAssetVO> materialAssetVOList = this.getMaterialDetail(getVO);
            MaterialGroupVO materialGroupVO = new MaterialGroupVO();
            materialAssetVOList.forEach(materialAssetVO -> {
                switch (ICommonEnum.get(materialAssetVO.getFieldType(), MaterialAssetTypeEnum.class)) {
                    case PRIMARY_ASSET:
                        materialGroupVO.setAsset(materialAssetVO);
                        break;
                    case TITLE:
                        materialGroupVO.setTitle(materialAssetVO);
                        break;
                    case DESCRIPTION:
                        materialGroupVO.setDescription(materialAssetVO);
                        break;
                    case END_CARD:
                        materialGroupVO.setEndcard(materialAssetVO);
                        break;
                    case CONCURRENT:
                        materialGroupVO.setConcurrent(materialAssetVO);
                        break;
                    case IMG:
                        materialGroupVO.setImg(materialAssetVO);
                        break;
                    default:
                }
            });
            if (ObjectUtils.isNotNullOrZero(materialVO.getTemplateId())) {
                materialVO.setTemplateAttr(JSONObject.parseArray(materialVO.getTemplateAttrStr(), Long.class));
            }
            materialVO.setAssets(materialGroupVO);
        });
        return materialList;
    }

    @Override
    public MaterialVO getMaterialByUnitId(MaterialByIdVO byIdVO) {
        List<MaterialVO> materialList = this.creativeUnitMapper.getMaterialList(new QueryWrapper<CreativeUnit>()
                .eq("mcu.id", byIdVO.getCreativeUnitId()));
        if (materialList.isEmpty()) {
            return null;
        }
        // 2：获取素材组所有素材信息
        materialList.forEach(materialVO -> {
            MaterialGetVO getVO = new MaterialGetVO();
            getVO.setId(materialVO.getId());
            getVO.setMasterId(byIdVO.getMasterId().longValue());
            MaterialGroupVO materialGroupVO = this.materialGroupVO(getVO);
            materialVO.setAssets(materialGroupVO);
            if (ObjectUtils.isNotNullOrZero(materialVO.getTemplateId())) {
                materialVO.setTemplateAttr(JSONObject.parseArray(materialVO.getTemplateAttrStr(), Long.class));
            }
        });
        return materialList.get(0);
    }

    @Override
    public List<MaterialVO> getMaterialByUnitIds(List<Long> unitIds, Long masterId) {
        if (CollectionUtils.isEmpty(unitIds)) {
            return List.of();
        }
        List<MaterialVO> materialList = this.creativeUnitMapper.getMaterialList(new QueryWrapper<CreativeUnit>()
                .in("mcu.id", unitIds));
        if (materialList.isEmpty()) {
            return List.of();
        }
        // 2：获取素材组所有素材信息
        materialList.forEach(materialVO -> {
            MaterialGetVO getVO = new MaterialGetVO();
            getVO.setId(materialVO.getId());
            getVO.setMasterId(masterId);
            MaterialGroupVO materialGroupVO = this.materialGroupVO(getVO);
            materialVO.setAssets(materialGroupVO);
            if (ObjectUtils.isNotNullOrZero(materialVO.getTemplateId())) {
                materialVO.setTemplateAttr(JSONObject.parseArray(materialVO.getTemplateAttrStr(), Long.class));
            }
        });
        return materialList;
    }

    @Override
    public Map<Long, List<MaterialAssetVO>> getMaterialMap(List<Long> materialIds, Long masterId) {
        //删除null数据
        materialIds = materialIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (materialIds.isEmpty()) {
            return new HashMap<>();
        }
        List<MaterialAssetVO> assetVOS = ObjectUtils.isNotNullOrZero(masterId) ?
                this.baseMapper.getAssetCopyWritingByMaterialId(new QueryWrapper<Material>()
                        .and(q -> q.eq("writing.master_id", masterId)
                                .or().isNull("writing.master_id"))
                        .in("mma.material_id", materialIds).orderByAsc("mma.id"))
                : this.baseMapper.getAssetByMaterialId(new QueryWrapper<Material>()
                .in("mma.material_id", materialIds).orderByAsc("mma.id"));
        // 为素材设置HttpUrl地址信息
        assetVOS.forEach(assetVO -> {
            Asset assetTmp = new Asset();
            BeanUtils.copyProperties(assetVO, assetTmp);
            this.assetService.formatAsset(assetTmp);
            assetVO.setHttpUrl(assetTmp.getHttpUrl());
            assetVO.setCoverImgUrl(assetTmp.getCoverImgUrl());
        });
        Map<Long, List<MaterialAssetVO>> materialAssetMap = assetVOS.stream().collect(Collectors.groupingBy(MaterialAssetVO::getMaterialId));

        // 填充素材文本信息
        materialAssetMap.forEach((key, value) -> this.fillMaterialTextAsset(value));

        return materialAssetMap;
    }

    @Override
    public void hashRefresh() {
        int i = 0;
        long materialId = 0;
        do {
            List<Material> materials = this.baseMapper.selectList(new LambdaQueryWrapper<Material>()
                    .eq(Material::getIsDel, IsDelEnum.NORMAL.getId())
                    .gt(ObjectUtils.isNotNullOrZero(materialId), Material::getId, materialId)
                    .orderByAsc(Material::getId)
                    .last("limit 1000")
            );
            if (materials.isEmpty()) {
                return;
            }
            materialId = materials.get(materials.size() - 1).getId();
            for (Material material : materials) {
                List<MaterialAssetVO> materialAssets = this.materialAssetMapper.selectList(
                        new LambdaQueryWrapper<MaterialAsset>()
                                .eq(MaterialAsset::getMaterialId, material.getId())
                                .orderByAsc(MaterialAsset::getFieldType)
                ).stream().map(u -> {
                    MaterialAssetVO assetVO = new MaterialAssetVO();
                    assetVO.setFieldType(u.getFieldType());
                    assetVO.setFieldName(u.getFieldName());
                    assetVO.setAssetId(u.getAssetId());
                    return assetVO;
                }).collect(Collectors.toList());
                String hash = this.generateMd5(materialAssets);
                Material update = new Material();
                update.setMd5(hash);
                this.baseMapper.update(update, new LambdaQueryWrapper<Material>()
                        .eq(Material::getId, material.getId())
                );
            }
            i++;
        } while (i < 20);

    }

    /**
     * 填充素材文本信息
     *
     * @param assetVOS 待填充列表
     */
    private void fillMaterialTextAsset(List<MaterialAssetVO> assetVOS) {

        // 如果数据库中title和desc文本数量不匹配，则追加fieldName = desc，content内容为空的文本
        List<MaterialAssetVO> titleAssets = assetVOS.stream().filter(u -> u.getFieldName().equals("title")).collect(Collectors.toList());
        List<MaterialAssetVO> descAssets = assetVOS.stream().filter(u -> u.getFieldName().equals("desc")).collect(Collectors.toList());
        if (titleAssets.size() != descAssets.size()) {
            // 清除原有的文本素材
            assetVOS.removeAll(titleAssets);
            assetVOS.removeAll(descAssets);
            List<MaterialAssetVO> textAssets = new ArrayList<>();
            Map<Long, MaterialAssetVO> descMap = descAssets.stream().collect(Collectors.toMap(MaterialAssetVO::getMaterialId, Function.identity(), (o, d) -> o));
            // 重新编排文本素材
            titleAssets.forEach(asset -> {
                textAssets.add(asset);
                if (descMap.get(asset.getMaterialId()) == null) {
                    MaterialAssetVO descAsset = new MaterialAssetVO();
                    descAsset.setAssetId(0L);
                    descAsset.setContent("");
                    descAsset.setAssetType(AssetTypeEnum.TEXT.getId());
                    descAsset.setTranslatedText("");
                    descAsset.setFieldName("desc");
                    descAsset.setFieldType(MaterialAssetTypeEnum.DESCRIPTION.getId());
                    descAsset.setAssetGroup(asset.getAssetGroup());
                    descAsset.setMaterialId(asset.getMaterialId());
                    textAssets.add(descAsset);
                } else {
                    textAssets.add(descMap.get(asset.getMaterialId()));
                }
            });
            assetVOS.addAll(textAssets);
        }
    }


    /**
     * 将排序对象用assetId进行排序，然后生成MD5
     *
     * @param assetVOs 素材组中素材列表
     * @return 返回MD5数据
     */
    private String generateMd5(List<MaterialAssetVO> assetVOs) {
        List<String> assetIds = assetVOs.stream().sorted(Comparator.comparing(MaterialAssetVO::getFieldType))
                .map(u -> String.format("%s_%s_%s", u.getAssetId(), u.getFieldType(), null == u.getFieldName() ? "" : u.getFieldName()))
                .collect(Collectors.toList());
        return DigestUtils.md5DigestAsHex(new Gson().toJson(assetIds).getBytes());
    }

    /**
     * 通过Md5获取
     *
     * @param md5      md5
     * @param masterId 广告主ID
     * @return 获取素材
     */
    private Material getByMd5AndMasterId(String md5, Long masterId) {
        Material materialInDb = getOne(new LambdaQueryWrapper<Material>()
                .eq(Material::getMasterId, masterId)
                .eq(Material::getMd5, md5)
                .eq(Material::getIsDel, IsDelEnum.NORMAL.getId())
                .orderByDesc(Material::getId)
                .last("LIMIT 1"));
        if (null == materialInDb) {
            throw new CustomException("Material不存在");
        }
        return materialInDb;
    }

    /**
     * 组装数据，封装 material group vo
     *
     * @param getVO 获取数据
     * @return 返回数据
     */
    private MaterialGroupVO materialGroupVO(MaterialGetVO getVO) {
        List<MaterialAssetVO> materialAssetVOList = this.getMaterialDetail(getVO);
        MaterialGroupVO materialGroupVO = new MaterialGroupVO();
        materialAssetVOList.forEach(materialAssetVO -> {
            switch (ICommonEnum.get(materialAssetVO.getFieldType(), MaterialAssetTypeEnum.class)) {
                case PRIMARY_ASSET:
                    materialGroupVO.setAsset(materialAssetVO);
                    break;
                case TITLE:
                    materialGroupVO.setTitle(materialAssetVO);
                    break;
                case DESCRIPTION:
                    materialGroupVO.setDescription(materialAssetVO);
                    break;
                case END_CARD:
                    materialGroupVO.setEndcard(materialAssetVO);
                    break;
                case CONCURRENT:
                    materialGroupVO.setConcurrent(materialAssetVO);
                    break;
                case IMG:
                    materialGroupVO.setImg(materialAssetVO);
                    break;
                default:
            }
        });
        return materialGroupVO;
    }
}
