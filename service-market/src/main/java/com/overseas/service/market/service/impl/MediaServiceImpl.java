package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.service.market.dto.media.MediaDirectDTO;
import com.overseas.service.market.entity.Media;
import com.overseas.service.market.vo.plan.DirectResourceVO;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.media.MediaSelectGetVO;
import com.overseas.service.market.enums.media.MediaStatusEnum;
import com.overseas.service.market.mapper.MediaMapper;
import com.overseas.service.market.service.MediaService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-23 20:05
 */
@Service
public class MediaServiceImpl extends ServiceImpl<MediaMapper, Media> implements MediaService {

    @Override
    public PageUtils<MediaDirectDTO> pageMediaDirect(DirectResourceVO directResourceVO) {
        PageUtils<MediaDirectDTO> emptyPage = new PageUtils<>(List.of(), 0L);
        //如果ADX为空，则后续媒体全部为空
        if (CollectionUtils.isEmpty(directResourceVO.getAdxId())
                || (null != directResourceVO.getAppointedIds() && directResourceVO.getAppointedIds().isEmpty())
                || ObjectUtils.isNullOrZero(directResourceVO.getSlotType())) {
            return emptyPage;
        }

        IPage<MediaDirectDTO> iPage = new Page<>(directResourceVO.getPage(), directResourceVO.getPageNum());
        QueryWrapper<Media> queryWrapper = new QueryWrapper<Media>()
                .in(null != directResourceVO.getAppointedIds() && !directResourceVO.getAppointedIds().isEmpty(),
                        "media.id", directResourceVO.getAppointedIds())
                .notIn(null != directResourceVO.getExcludeIds() && !directResourceVO.getExcludeIds().isEmpty(),
                        "media.id", directResourceVO.getExcludeIds())
                .in("media.adx_id", directResourceVO.getAdxId())
                .eq("s.slot_type", directResourceVO.getSlotType())
                .in(ObjectUtils.isNotNullOrZero(directResourceVO.getOsType()), "app.os_type", List.of(0, directResourceVO.getOsType()))
                .eq("media.media_status", MediaStatusEnum.NORMAL.getId())
//                .gt(ObjectUtils.isNotNullOrZero(directResourceVO.getOnlyRequest()),
//                        "media.request_quantity", OrderResourceConstant.REQUEST_QUANTITY_COMPARE)
                .and(StringUtils.isNotEmpty(directResourceVO.getSearch()),
                        i -> i.like("media.id", directResourceVO.getSearch())
                                .or().like("media.media_name", directResourceVO.getSearch())
                                .or().like("adx.adx_name", directResourceVO.getSearch()))
                .orderByDesc("media.request_quantity");
        return new PageUtils<>(this.baseMapper.listMediaDirect(iPage, queryWrapper));
    }

    @Override
    public List<SelectDTO> getMediaSelect(MediaSelectGetVO getVO) {

        return this.baseMapper.getMediaSelect(new QueryWrapper<Media>().lambda()
                .eq(Media::getMediaStatus, MediaStatusEnum.NORMAL.getId())
                .in(!getVO.getAdxIds().isEmpty(), Media::getAdxId, getVO.getAdxIds())
                .orderByDesc(Media::getId));
    }
}
