package com.overseas.service.market.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.vo.market.monitor.monitorEvent.*;
import com.overseas.service.market.entity.BehaviorAppField;
import com.overseas.service.market.entity.MonitorEvent;
import com.overseas.service.market.entity.Plan;
import com.overseas.service.market.enums.monitor.MonitorTypeEnum;
import com.overseas.service.market.enums.monitor.monitorEvent.MonitorEventSourceTypeEnum;
import com.overseas.service.market.enums.monitor.monitorEvent.MonitorEventStatusEnum;
import com.overseas.service.market.mapper.PlanMapper;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.common.SaveDTO;
import com.overseas.common.dto.market.monitor.monitorEvent.MonitorEventGetDTO;
import com.overseas.common.dto.market.monitor.monitorEvent.MonitorEventListDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.service.market.enums.plan.PlanOptimizeTargetEnum;
import com.overseas.common.enums.market.plan.PlanStatusEnum;
import com.overseas.service.market.mapper.BehaviorAppFieldMapper;
import com.overseas.service.market.mapper.MonitorEventMapper;
import com.overseas.service.market.service.MonitorEventService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MonitorEventServiceImpl extends ServiceImpl<MonitorEventMapper, MonitorEvent> implements MonitorEventService {

    private final PlanMapper planMapper;

    private final BehaviorAppFieldMapper behaviorAppFieldMapper;

    private MonitorEvent checkMonitorEvent(Long id) {

        MonitorEvent monitorEvent = this.baseMapper.selectOne(new QueryWrapper<MonitorEvent>().lambda()
                .eq(MonitorEvent::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(id), MonitorEvent::getId, id));

        if (monitorEvent == null) {
            throw new CustomException("监测事件不存在，请确认后再试");
        }
        return monitorEvent;
    }

    @Override
    public List<SelectDTO> getEventSelect(MonitorEventSelectGetVO getVO) {

        return baseMapper.selectEvent(new QueryWrapper<MonitorEvent>()
                .eq("mme.is_del", IsDelEnum.NORMAL.getId())
                .eq("mme.event_status", MonitorEventStatusEnum.SUCCESS.getId())
                .eq("mme.master_id", getVO.getMasterId())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getMonitorId()), "mme.monitor_id", getVO.getMonitorId())
                .orderByDesc("mme.id"));
    }

    @Override
    public PageUtils<MonitorEventListDTO> getEventPage(MonitorEventListVO listVO) {

        IPage<MonitorEventListDTO> page = new Page<>(listVO.getPage(), listVO.getPageNum());
        IPage<MonitorEventListDTO> pageData = this.baseMapper.getEventPage(page, new QueryWrapper<MonitorEvent>()
                .eq("mme.is_del", IsDelEnum.NORMAL.getId())
                .eq("mme.master_id", listVO.getMasterId())
                .eq("mme.monitor_id", listVO.getMonitorId())
                .like(StringUtils.isNotBlank(listVO.getSearch()), "mme.id", listVO.getSearch())
                .orderByDesc("mme.id"));

        if (pageData.getRecords().isEmpty()) {
            return new PageUtils<>(List.of(), 0L);
        }

        pageData.getRecords().forEach(entity -> {
            entity.setSourceTypeName(MonitorTypeEnum.H5.getId().equals(entity.getEventType()) ? ConstantUtils.PLACEHOLDER_2 :
                    (ICommonEnum.getNameById(entity.getSourceType(), MonitorEventSourceTypeEnum.class)));
            entity.setEventStatusName(MonitorTypeEnum.H5.getId().equals(entity.getEventType()) ? ConstantUtils.PLACEHOLDER_2 :
                    (ICommonEnum.getNameById(entity.getEventStatus(), MonitorEventStatusEnum.class)));
            entity.setEventTypeName(ICommonEnum.getNameById(entity.getEventType(), MonitorTypeEnum.class));
        });
        return new PageUtils<>(pageData);
    }

    @Override
    public SaveDTO saveEvent(MonitorEventSaveVO saveVO, Integer userId) {

        long count = this.baseMapper.selectCount(new QueryWrapper<MonitorEvent>().lambda()
                .eq(MonitorEvent::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(MonitorEvent::getMonitorId, saveVO.getMonitorId())
                .eq(MonitorEvent::getMasterId, saveVO.getMasterId())
                .eq(MonitorEvent::getTargetType, saveVO.getTargetType()));

        if (count > 0L) {
            throw new CustomException("转化事件已存在，请确认后再试");
        }

        MonitorEvent monitorEvent = new MonitorEvent();
        BeanUtils.copyProperties(saveVO, monitorEvent);
        monitorEvent.setEventType(saveVO.getEventType().byteValue());
        monitorEvent.setSourceType(saveVO.getSourceType().byteValue());
        monitorEvent.setCreateUid(userId);
        // hhshi2:临时将新建的转化事件置为联调通过
        monitorEvent.setEventStatus(MonitorEventStatusEnum.SUCCESS.getId());
        this.baseMapper.insert(monitorEvent);

        return new SaveDTO(monitorEvent.getId().longValue());
    }

    @Override
    public void deleteEvent(MonitorEventGetVO getVO, Integer userId) {
        MonitorEvent monitorEvent = this.checkMonitorEvent(getVO.getId());
        monitorEvent.setUpdateUid(userId);
        monitorEvent.setIsDel(IsDelEnum.DELETE.getId().intValue());

        // 获取该监测事件已关联的所有计划ID
        List<Long> planIds = this.planMapper.selectList(new QueryWrapper<Plan>().lambda()
                .eq(Plan::getOptimizeTargetId, getVO.getId())
                .ne(Plan::getPlanStatus, PlanStatusEnum.FINISH.getId())
                .eq(Plan::getIsDel, IsDelEnum.NORMAL.getId())).stream().map(Plan::getId).distinct().collect(Collectors.toList());
        if (!planIds.isEmpty()) {
            throw new CustomException("当前监测事件已存在绑定计划，请确认后再试");
        }
        this.baseMapper.update(monitorEvent, new QueryWrapper<MonitorEvent>().lambda().eq(MonitorEvent::getId, monitorEvent.getId()));
    }

    @Override
    public void changeEventStatus(MonitorEventStatusGetVO getVO, Integer userId) {

        MonitorEvent monitorEvent = this.checkMonitorEvent(getVO.getId());
        monitorEvent.setEventStatus(getVO.getMonitorEventStatus());
        monitorEvent.setUpdateUid(userId);
        this.baseMapper.update(monitorEvent, new QueryWrapper<MonitorEvent>().lambda().eq(MonitorEvent::getId, monitorEvent.getId()));
    }

    @Override
    public MonitorEventGetDTO getEvent(MonitorEventGetVO getVO) {

        this.checkMonitorEvent(getVO.getId());

        MonitorEventGetDTO monitorEventGetDTO = this.baseMapper.getEvent(new QueryWrapper<MonitorEvent>()
                .eq("mme.id", getVO.getId()));


        // 获取App相关信息
        List<BehaviorAppField> behaviorAppFieldList = this.behaviorAppFieldMapper.selectList(new QueryWrapper<BehaviorAppField>().lambda()
                .eq(BehaviorAppField::getBehaviorAppId, monitorEventGetDTO.getBehaviorAppId()));

        behaviorAppFieldList.forEach(behaviorAppField -> {
            if (ObjectUtils.underlineToCamel(behaviorAppField.getFieldName()).equals("appDownloadUrl")) {
                ObjectUtils.setObjectValue(monitorEventGetDTO, "apkUrl", behaviorAppField.getContent());
            }
        });

        monitorEventGetDTO.setEventTypeName(ICommonEnum.getNameById(monitorEventGetDTO.getEventType(), MonitorTypeEnum.class));
        monitorEventGetDTO.setSourceTypeName(ICommonEnum.getNameById(monitorEventGetDTO.getSourceType(), MonitorEventSourceTypeEnum.class));
        monitorEventGetDTO.setEventStatusName(ICommonEnum.getNameById(monitorEventGetDTO.getEventStatus(), MonitorEventStatusEnum.class));
        return monitorEventGetDTO;
    }

    @Override
    public List<Long> listMonitorEventIdsByAction(Long masterId, List<Long> actionIds) {

        if (actionIds.isEmpty()) {
            return List.of();
        }

        List<Long> result = new ArrayList<>(), brandIds = PlanOptimizeTargetEnum.listIds();
        if (actionIds.contains(PlanOptimizeTargetEnum.VIEW.getId().longValue())) {
            result.add(PlanOptimizeTargetEnum.VIEW.getId().longValue());
        }
        if (actionIds.contains(PlanOptimizeTargetEnum.CLICK.getId().longValue())) {
            result.add(PlanOptimizeTargetEnum.CLICK.getId().longValue());
        }
        actionIds = actionIds.stream().filter(actionId -> !brandIds.contains(actionId)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(actionIds)) {
            result.addAll(baseMapper.selectEvent(new QueryWrapper<MonitorEvent>()
                            .eq("mme.master_id", masterId)
                            .eq("mme.event_status", MonitorEventStatusEnum.SUCCESS.getId())
                            .in("dta.id", actionIds))
                    .stream().map(SelectDTO::getId).collect(Collectors.toList()));
        }

        return result;
    }
}
