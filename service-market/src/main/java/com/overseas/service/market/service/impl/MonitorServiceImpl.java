package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.vo.market.monitor.*;
import com.overseas.service.market.mapper.PlanMapper;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.common.SaveDTO;
import com.overseas.common.dto.market.monitor.MonitorGetDTO;
import com.overseas.common.dto.market.monitor.MonitorListDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.enums.market.bahaviorApp.BehaviorAppTypeEnum;
import com.overseas.service.market.enums.monitor.MonitorStatusEnum;
import com.overseas.service.market.enums.monitor.MonitorTypeEnum;
import com.overseas.common.enums.market.plan.PlanStatusEnum;
import com.overseas.service.market.mapper.BehaviorAppFieldMapper;
import com.overseas.service.market.mapper.MonitorEventMapper;
import com.overseas.service.market.mapper.MonitorMapper;
import com.overseas.service.market.service.MonitorService;
import com.overseas.service.market.service.TrackerDayService;
import com.overseas.service.market.entity.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class MonitorServiceImpl extends ServiceImpl<MonitorMapper, Monitor> implements MonitorService {

    private final MonitorEventMapper monitorEventMapper;

    private final TrackerDayService trackerDayService;

    private final PlanMapper planMapper;

    private final BehaviorAppFieldMapper behaviorAppFieldMapper;

    private Monitor isExist(Long id) {

        Monitor monitor = this.baseMapper.selectOne(new QueryWrapper<Monitor>().lambda()
                .eq(Monitor::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(Monitor::getId, id));
        if (monitor == null) {
            throw new CustomException("监测站点不存在，请确认后再试");
        }
        return monitor;
    }

    private void checkMonitor(MonitorSaveVO saveVO) {

        List<Monitor> monitorList = this.baseMapper.selectList(new QueryWrapper<Monitor>().lambda()
                .eq(Monitor::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(Monitor::getMasterId, saveVO.getMasterId())
                .and(q -> q.or().eq(Monitor::getMonitorName, saveVO.getMonitorName())
                        .or().eq(Monitor::getAppId, saveVO.getAppId())));

        if (monitorList.isEmpty()) {
            return;
        }
        List<String> names = monitorList.stream().map(Monitor::getMonitorName).collect(Collectors.toList());
        List<Long> appIds = monitorList.stream().map(Monitor::getAppId).collect(Collectors.toList());
        if (names.contains(saveVO.getMonitorName())) {
            throw new CustomException("监测站点名称已存在，请确认后再试");
        }
        if (MonitorTypeEnum.APP.getId().equals(saveVO.getMonitorType()) && appIds.contains(saveVO.getAppId())) {
            throw new CustomException("该应用已被绑定，请确认后再试");
        }
    }

    @Override
    public PageUtils<MonitorListDTO> getMonitorPage(MonitorListVO listVO) {

        IPage<MonitorListDTO> page = new Page<>(listVO.getPage(), listVO.getPageNum());
        IPage<MonitorListDTO> pageData = this.baseMapper.getMonitorPage(page, new QueryWrapper<Monitor>()
                .eq("mm.is_del", IsDelEnum.NORMAL.getId())
                .eq("mm.master_id", listVO.getMasterId())
                .and(StringUtils.isNotBlank(listVO.getSearch()), q -> q
                        .or().like("mm.id", listVO.getSearch())
                        .or().like("mm.monitor_name", listVO.getSearch()))
                .orderByDesc("mm.id"));

        if (pageData.getRecords().isEmpty()) {
            return new PageUtils<>(List.of(), 0L);
        }
        List<Long> monitorIds = pageData.getRecords().stream().map(MonitorListDTO::getId).distinct().collect(Collectors.toList());

        // 获取转化事件数
        Map<Long, List<MonitorEvent>> monitorEventMap = this.monitorEventMapper.selectList(new QueryWrapper<MonitorEvent>().lambda()
                .eq(MonitorEvent::getIsDel, IsDelEnum.NORMAL.getId())
                .in(MonitorEvent::getMonitorId, monitorIds)).stream().collect(Collectors.toMap(MonitorEvent::getMonitorId, u -> new ArrayList<MonitorEvent>() {{
            add(u);
        }}, (newVal, oldVal) -> {
            newVal.addAll(oldVal);
            return newVal;
        }));

        // 获取上报事件数
//        Map<Long, Integer> monitorActionMap = this.trackerDayService.getTrackerDayList("si,SUM(actionNum) AS action_num",
//                monitorIds, List.of(), "si").stream().collect(Collectors.toMap(u -> Long.parseLong(u.getSi()), TrackerDay::getActionNum));
        // TODO : 临时屏蔽查询，优化表，分月，前端增加时间段过滤
        Map<Long, Integer> monitorActionMap = new HashMap<>();

        pageData.getRecords().forEach(entity -> {
            entity.setMonitorStatusName(ICommonEnum.getNameById(entity.getMonitorStatus(), MonitorStatusEnum.class));
            entity.setMonitorTypeName(ICommonEnum.getNameById(entity.getMonitorType(), MonitorTypeEnum.class));
            // 站点内容：类型-应用
            entity.setMonitorContent(entity.getMonitorTypeName() + (MonitorTypeEnum.H5.getId().equals(entity.getMonitorType()) ? "" :
                    ("-" + entity.getAppName())));
            entity.setEventNum(monitorEventMap.getOrDefault(entity.getId(), List.of()).size());
            entity.setActionNum(monitorActionMap.getOrDefault(entity.getId(), 0));
        });

        return new PageUtils<>(pageData);
    }

    @Override
    public MonitorGetDTO getMonitor(MonitorGetVO getVO) {

        this.isExist(getVO.getId());
        MonitorGetDTO monitorGetDTO = this.baseMapper.getMonitor(new QueryWrapper<Monitor>()
                .eq("mm.id", getVO.getId()));

        // 获取App相关信息
        List<BehaviorAppField> behaviorAppFieldList = this.behaviorAppFieldMapper.selectList(new QueryWrapper<BehaviorAppField>().lambda()
                .eq(BehaviorAppField::getBehaviorAppId, monitorGetDTO.getBehaviorAppId()));

        behaviorAppFieldList.forEach(behaviorAppField -> {
            if (ObjectUtils.underlineToCamel(behaviorAppField.getFieldName()).equals("packageName")) {
                ObjectUtils.setObjectValue(monitorGetDTO, "bundle", behaviorAppField.getContent());
            }
            if (ObjectUtils.underlineToCamel(behaviorAppField.getFieldName()).equals("appDownloadUrl")) {
                ObjectUtils.setObjectValue(monitorGetDTO, "apkUrl", behaviorAppField.getContent());
            }
        });
        monitorGetDTO.setMonitorTypeName(ICommonEnum.getNameById(monitorGetDTO.getMonitorType(), MonitorTypeEnum.class));
        monitorGetDTO.setMonitorStatusName(ICommonEnum.getNameById(monitorGetDTO.getMonitorStatus(), MonitorStatusEnum.class));
        if (MonitorTypeEnum.APP.getId().equals(monitorGetDTO.getMonitorType())) {
            monitorGetDTO.setBehaviorAppTypeName(ICommonEnum.getNameById(monitorGetDTO.getBehaviorAppType(), BehaviorAppTypeEnum.class));
        }
        return monitorGetDTO;
    }

    @Override
    public SaveDTO saveMonitor(MonitorSaveVO saveVO, Integer userId) {

        this.checkMonitor(saveVO);

        Monitor monitor = new Monitor();
        BeanUtils.copyProperties(saveVO, monitor);
        monitor.setCreateUid(userId);
        this.baseMapper.insert(monitor);

        return new SaveDTO(monitor.getId());
    }

    @Override
    public void deleteMonitor(MonitorGetVO getVO, Integer userId) {

        Monitor monitor = this.isExist(getVO.getId());

        // 获取该监测站点已关联的所有计划ID
        List<Long> planIds = this.planMapper.selectList(new QueryWrapper<Plan>().lambda()
                .ne(Plan::getPlanStatus, PlanStatusEnum.FINISH.getId())
                .eq(Plan::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(Plan::getMonitorId, getVO.getId())).stream().map(Plan::getId).distinct().collect(Collectors.toList());
        if (!planIds.isEmpty()) {
            throw new CustomException("当前监测站点已存在绑定计划，请确认后再试");
        }

        monitor.setIsDel(IsDelEnum.DELETE.getId().intValue());
        monitor.setUpdateUid(userId);
        this.baseMapper.update(monitor, new QueryWrapper<Monitor>().lambda().eq(Monitor::getId, monitor.getId()));

        // 删除该监测站点下的监测事件
        MonitorEvent monitorEvent = new MonitorEvent();
        monitorEvent.setIsDel(IsDelEnum.DELETE.getId().intValue());
        monitorEvent.setUpdateUid(userId);
        this.monitorEventMapper.update(monitorEvent, new QueryWrapper<MonitorEvent>().lambda()
                .eq(MonitorEvent::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(MonitorEvent::getMonitorId, getVO.getId()));
    }

    @Override
    public void changeMonitorStatus(MonitorStatusGetVO getVO, Integer userId) {

        Monitor monitor = this.isExist(getVO.getId());
        monitor.setMonitorStatus(getVO.getMonitorStatus());
        monitor.setUpdateUid(userId);
        this.baseMapper.update(monitor, new QueryWrapper<Monitor>().lambda().eq(Monitor::getId, monitor.getId()));
    }

    @Override
    public List<SelectDTO> getMonitorSelect(MonitorSelectGetVO getVO) {
        return this.baseMapper.getMonitorSelect(new QueryWrapper<Monitor>().lambda()
                .eq(Monitor::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(Monitor::getMonitorStatus, MonitorStatusEnum.OPEN.getId())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getMasterId()), Monitor::getMasterId, getVO.getMasterId())
                .orderByDesc(Monitor::getId));
    }
}
