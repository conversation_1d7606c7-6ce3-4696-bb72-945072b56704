package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.enums.TimeZoneEnum;
import com.overseas.common.enums.market.asset.AssetTypeEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.*;
import com.overseas.service.market.dto.openApi.campaign.OpenApiCampaignListDTO;
import com.overseas.service.market.dto.openApi.creativeUnit.OpenApiAssetListDTO;
import com.overseas.service.market.dto.openApi.creativeUnit.OpenApiCreativeUnitListDTO;
import com.overseas.service.market.dto.openApi.master.OpenApiMasterListDTO;
import com.overseas.service.market.dto.openApi.plan.OpenApiPlanListDTO;
import com.overseas.service.market.dto.openApi.report.OpenApiReportListDTO;
import com.overseas.service.market.entity.*;
import com.overseas.service.market.mapper.CampaignMapper;
import com.overseas.service.market.mapper.CreativeUnitMapper;
import com.overseas.service.market.mapper.MasterMapper;
import com.overseas.service.market.mapper.PlanMapper;
import com.overseas.service.market.service.OpenApiService;
import com.overseas.service.market.vo.openApi.campaign.OpenApiCampaignListVO;
import com.overseas.service.market.vo.openApi.creativeUnit.OpenApiCreativeUnitListVO;
import com.overseas.service.market.vo.openApi.master.OpenApiMasterListVO;
import com.overseas.service.market.vo.openApi.plan.OpenApiPlanListVO;
import com.overseas.service.market.vo.openApi.report.OpenApiReportListVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class OpenApiServiceImpl implements OpenApiService {

    private final CampaignMapper campaignMapper;

    private final CreativeUnitMapper creativeUnitMapper;

    private final MasterMapper masterMapper;

    private final PlanMapper planMapper;

    @Override
    public PageUtils<OpenApiMasterListDTO> listMaster(OpenApiMasterListVO listVO, Integer appKey) {
        QueryWrapper<Master> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("mar.app_key", appKey)
                .and(StringUtils.isNotBlank(listVO.getSearch()),
                        i -> i.like("uu.id", listVO.getSearch())
                                .or().like("uu.company_name", listVO.getSearch())
        ).orderByDesc("uu.id");
        IPage<OpenApiMasterListDTO> result = this.masterMapper.listOpenApiMaster(
                new Page<>(listVO.getPage(), listVO.getPageNum()), queryWrapper);
        return new PageUtils<>(result);
    }

    @Override
    public PageUtils<OpenApiCampaignListDTO> listCampaign(OpenApiCampaignListVO listVO, Integer appKey) {
        QueryWrapper<Campaign> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("mar.app_key", appKey)
                .eq("mc.is_campaign_put", 0)
                .eq("mc.is_del", IsDelEnum.NORMAL.getId())
                .eq("mc.master_id", listVO.getMasterId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getCampaignStatus()),
                        "mc.campaign_status", listVO.getCampaignStatus())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getCampaignType()),
                        "mc.campaign_type", listVO.getCampaignType())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getPromotedType()),
                        "mc.put_on_target", listVO.getPromotedType())
                .and(StringUtils.isNotBlank(listVO.getSearch()),
                        i -> i.like("mc.id", listVO.getSearch())
                                .or().like("mc.campaign_name", listVO.getSearch()))
                .orderByDesc("mc.id");
        IPage<OpenApiCampaignListDTO> result = this.campaignMapper.listOpenApiCampaign(
                new Page<>(listVO.getPage(), listVO.getPageNum()), queryWrapper);
        result.getRecords().forEach(record -> record.setRegion(listVO.getRegion()));
        return new PageUtils<>(result);
    }

    @Override
    public PageUtils<OpenApiPlanListDTO> listPlan(OpenApiPlanListVO listVO, Integer appKey) {
        QueryWrapper<Plan> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("mar.app_key", appKey)
                .eq("mc.is_campaign_put", 0)
                .eq("mc.is_del", IsDelEnum.NORMAL.getId())
                .eq("mp.is_plan_put", 0)
                .eq("mp.is_del", IsDelEnum.NORMAL.getId())
                .eq("mp.master_id", listVO.getMasterId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getCampaignId()),
                        "mp.campaign_id", listVO.getCampaignId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getPlanStatus()),
                        "mp.plan_status", listVO.getPlanStatus())
                .between(StringUtils.isNotBlank(listVO.getCreateStartDate())
                                && StringUtils.isNotBlank(listVO.getCreateEndDate()),
                        "mp.create_time",
                        listVO.getCreateStartDate() + " 0:00:00",
                        listVO.getCreateEndDate() + " 23:59:59")
                .and(StringUtils.isNotBlank(listVO.getSearch()),
                        i -> i.like("mp.id", listVO.getSearch())
                                .or().like("mp.plan_name", listVO.getSearch()))
                .orderByDesc("mp.id");
        Map<Integer, String> slotTypeMap = new HashMap<>() {
            {
                put(100, "Banner");
                put(300, "Native");
                put(400, "Video");
            }
        };
        IPage<OpenApiPlanListDTO> result = this.planMapper.listOpenApiPlan(
                new Page<>(listVO.getPage(), listVO.getPageNum()), queryWrapper);
        result.getRecords().forEach(record -> {
            record.setRegion(listVO.getRegion());
            try {
                String slotTypes = JSONObject.parseArray(record.getSlotType(), Integer.class)
                        .stream().sorted(Comparator.comparing(u -> u))
                        .map(u -> slotTypeMap.getOrDefault(u, "Unknown")
                        ).collect(Collectors.joining(","));
                record.setSlotType(slotTypes);
            } catch (Exception e) {
                record.setSlotType("Unknown");
                log.error(e.getMessage(), e);
            }
        });
        return new PageUtils<>(result);
    }

    @Override
    public PageUtils<OpenApiCreativeUnitListDTO> listCreativeUnit(OpenApiCreativeUnitListVO listVO, Integer appKey) {
        QueryWrapper<CreativeUnit> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("mar.app_key", appKey)
                .eq("mc.is_campaign_put", 0)
                .eq("mc.is_del", IsDelEnum.NORMAL.getId())
                .eq("mp.is_plan_put", 0)
                .eq("mp.is_del", IsDelEnum.NORMAL.getId())
                .eq("mcu.master_id", listVO.getMasterId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getCampaignId()),
                        "mcu.campaign_id", listVO.getCampaignId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getPlanId()),
                        "mcu.plan_id", listVO.getPlanId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getCreativeUnitStatus()),
                        "mcu.creative_unit_status", listVO.getCreativeUnitStatus())
                .between(StringUtils.isNotBlank(listVO.getCreateStartDate())
                                && StringUtils.isNotBlank(listVO.getCreateEndDate()),
                        "mcu.create_time",
                        listVO.getCreateStartDate() + " 0:00:00",
                        listVO.getCreateEndDate() + " 23:59:59")
                .orderByDesc("mcu.id");
        IPage<OpenApiCreativeUnitListDTO> result = this.creativeUnitMapper.listOpenApiCreativeUnit(
                new Page<>(listVO.getPage(), listVO.getPageNum()), queryWrapper);
        result.getRecords().forEach(openApiCreativeUnitListDTO -> {
            openApiCreativeUnitListDTO.setRegion(listVO.getRegion());
            QueryWrapper<CreativeUnit> assetQueryWrapper = new QueryWrapper<>();
            assetQueryWrapper.eq("mcu.is_del", IsDelEnum.NORMAL.getId())
                    .eq("mcu.id", openApiCreativeUnitListDTO.getCreativeUnitId());
            List<OpenApiAssetListDTO> assets = this.creativeUnitMapper.listOpenApiCreativeUnitAsset(assetQueryWrapper);
            if (!assets.isEmpty()) {
                assets.forEach(asset -> {
                    if (!AssetTypeEnum.TEXT.getId().equals(asset.getAssetType())) {
                        asset.setContent(UploadUtils.getCdnHttpsUrl(asset.getContent()));
                    }
                });
                openApiCreativeUnitListDTO.setAssets(assets);
            } else {
                openApiCreativeUnitListDTO.setAssets(List.of());
            }
        });
        return new PageUtils<>(result);
    }

    @Override
    public PageUtils<OpenApiReportListDTO> listReport(OpenApiReportListVO listVO, Integer appKey) {
        // 如果是小时报表，则查询小时，其他转化为天
        Date start = DateUtils.string2Date(listVO.getStartDate()),
                end = DateUtils.string2Date(listVO.getEndDate());
        // 分小时报表和其他报表做处理
        // 小时报表，时间对比报表前端传 yyyy-MM-dd HH，含带小时时间，因此不需要额外做小时的加减处理
        // 其他报表前端传 yyyy-MM-dd，不含带小时时间，因此需要对结束时间加23小时来查询结束时间的数据
        Long startDate = DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(start, listVO.getTimeZone(), 0)),
                endDate = DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(end, listVO.getTimeZone(), 23));
        Long startDay = DateUtils.string2Long(DateUtils.format(DateUtils.long2Date(startDate))),
                endDay = DateUtils.string2Long(DateUtils.format(DateUtils.long2Date(endDate)));

        int hourRatio = Objects.requireNonNull(TimeZoneEnum.get(listVO.getTimeZone())).getFormatHour();
        // 如果是时间报表，则根据时区去重新定义日期
        String selectFields = listVO.getDimensions().contains("day")
                ? "FROM_UNIXTIME(dim_report_hour + " + 3600 * hourRatio + ",'%Y-%m-%d') AS `report_date`,"
                : "dim_day AS `report_date`,";
        // 如果是小时报表，则额外补充小时
        selectFields += listVO.getDimensions().contains("hour")
                ? "FROM_UNIXTIME(dim_report_hour + " + 3600 * hourRatio + ",'%H') AS  `report_hour`,"
                : "";
        selectFields +=
                //"report.dim_day AS report_date,report.dim_hour AS report_hour," +
                "report.dim_master_id AS master_id,uu.company_name AS master_name," +
                "report.dim_campaign_id AS campaign_id,mc.campaign_name,report.dim_plan_id AS plan_id," +
                "mp.plan_name,report.dim_creative_unit_id AS creative_unit_id," +
                "CONCAT(report.dim_creative_unit_id,'素材',mcu.creative_index) AS creative_unit_name," +
                "report.dim_rta_id AS rta_id,drs.rta_strategy_name AS rta_name,report.dim_country_id AS country_id," +
                "dc.country_name," +
                "SUM(report.idx_bid) AS bid," +
                "SUM(report.idx_view) AS `view`," +
                "ROUND(SUM(`idx_view`)*100/SUM(`idx_bid`),2) AS view_rate," +
                "SUM(report.idx_click_uniq_session) AS click," +
                "ROUND(SUM(`idx_click_uniq_session`)*100/SUM(`idx_view`),2) AS click_rate," +
                "0 AS media_cost," +
                "0 AS `cpm`," +
                "0 AS `cpc`," +
                "0 AS `cpa`," +
                "SUM(report.idx_action39) AS purchase," +
                "SUM(report.idx_action22) AS checkout," +
                "SUM(report.idx_action23) AS add_to_cart," +
                "ROUND((SUM(report.`idx_action40`)/1000000),3) AS settlement ";

        QueryWrapper<Campaign> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(selectFields).eq("mar.app_key", appKey);
        // 时间筛选
        queryWrapper.between("dim_report_hour", startDate, endDate).between("dim_day", startDay, endDay);

        // day,hour,account,campaign,plan,creativeUnit,country,rta
        Map<String, String> dimensionMap = new HashMap<>() {{
            put("day", "report_date");
            put("hour", "report_hour");
            put("account", "dim_master_id");
            put("campaign", "dim_campaign_id");
            put("plan", "dim_plan_id");
            put("creativeUnit", "dim_creative_unit_id");
            put("country", "dim_country_id");
            put("rta", "dim_rta_id");
        }};
        List<String> groupFields = new ArrayList<>();
        listVO.getDimensions().forEach(dimension -> {
            if (dimensionMap.containsKey(dimension)) {
                groupFields.add(dimensionMap.get(dimension));
            } else {
                throw new CustomException("存在不支持的维度参数，请检查后再试");
            }
            if (!List.of("day", "hour").contains(dimension)) {
                String idJson = JSONObject.toJSONString(
                        ObjectUtils.getObjectValue(listVO.getFilters(), dimension + "Ids"));
                if (null != idJson && !idJson.equals("null")) {
                    List<Long> ids = JSONObject.parseObject(idJson, new TypeReference<>(){});
                    queryWrapper.in(!ids.isEmpty(), dimensionMap.get(dimension), ids);
                }
            }
        });
        queryWrapper.groupBy(StringUtils.join(groupFields, ","));
        IPage<OpenApiReportListDTO> result = this.campaignMapper.listOpenApiReport(
                new Page<>(listVO.getPage(), listVO.getPageNum()), queryWrapper);
        result.getRecords().forEach(report -> dimensionMap.forEach((key, value) -> {
            report.setRegion(listVO.getRegion());
            if (!listVO.getDimensions().contains(key)) {
                switch (key) {
                    case "day":
                        ObjectUtils.removeObjectField(report, "reportDate");
                        break;
                    case "hour":
                        ObjectUtils.removeObjectField(report, "reportHour");
                        break;
                    case "account":
                        ObjectUtils.removeObjectField(report, "masterId");
                        ObjectUtils.removeObjectField(report, "masterName");
                        break;
                    default:
                        ObjectUtils.removeObjectField(report, key + "Id");
                        ObjectUtils.removeObjectField(report, key + "Name");
                        break;
                }
            }
        }));
        return new PageUtils<>(result);
    }
}
