package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.service.market.dto.operate.log.OperateLogListDTO;
import com.overseas.service.market.entity.OperateLog;
import com.overseas.service.market.entity.User;
import com.overseas.service.market.vo.operate.log.OperateLogListVO;
import com.overseas.common.utils.PageUtils;
import com.overseas.service.market.mapper.OperateLogMapper;
import com.overseas.service.market.service.OperateLogService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-09-15 11:26
 */
@Service
public class OperateLogServiceImpl extends ServiceImpl<OperateLogMapper, OperateLog> implements OperateLogService {
    @Override
    public PageUtils<OperateLogListDTO> pageLog(OperateLogListVO vo, User loginUser) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(!vo.getOperateType().equals("全部"), "operate_type", vo.getOperateType())
                .eq("create_uid", loginUser.getId())
                .like(StringUtils.isNotEmpty(vo.getSearch()), "id", vo.getSearch())
                .orderByDesc("id");
        IPage<OperateLogListDTO> iPage = new Page<>(vo.getPage(), vo.getPageNum());
        iPage = this.baseMapper.pageLog(iPage, queryWrapper);
        return new PageUtils<>(iPage);
    }
}
