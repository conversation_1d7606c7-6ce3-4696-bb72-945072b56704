package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.market.oppoCopyWriting.ExcelCopyWritingDTO;
import com.overseas.common.dto.market.oppoCopyWriting.OppoCopyWritingListDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.enums.market.oppoCopyWriting.OppoCopyWritingUsageStatusEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ExcelUtils;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.utils.UploadUtils;
import com.overseas.common.vo.market.oppoCopyWriting.OppoCopyWritingListVO;
import com.overseas.common.vo.market.oppoCopyWriting.OppoCopyWritingSaveVO;
import com.overseas.service.market.entity.OppoCopyWriting;
import com.overseas.service.market.mapper.OppoCopyWritingMapper;
import com.overseas.service.market.service.OppoCopyWritingService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * OPPO文案服务实现类
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class OppoCopyWritingServiceImpl extends ServiceImpl<OppoCopyWritingMapper, OppoCopyWriting>
        implements OppoCopyWritingService {
    @Override
    public PageUtils<OppoCopyWritingListDTO> listOppoCopyWriting(OppoCopyWritingListVO listVO) {
        QueryWrapper<OppoCopyWriting> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OppoCopyWriting::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(StringUtils.isNotBlank(listVO.getCountryAlias()), OppoCopyWriting::getCountryAlias,
                        listVO.getCountryAlias())
                .eq(StringUtils.isNotBlank(listVO.getLanguageAlias()), OppoCopyWriting::getLanguageAlias,
                        listVO.getLanguageAlias())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getUsageStatus()), OppoCopyWriting::getUsageStatus,
                        listVO.getUsageStatus())
                .and(StringUtils.isNotBlank(listVO.getSearch()),
                        i->i.like(OppoCopyWriting::getTitleTranslation, listVO.getSearch())
                                .or().like(OppoCopyWriting::getDescTranslation, listVO.getSearch())
                                .or().like(OppoCopyWriting::getTitleText, listVO.getSearch())
                                .or().like(OppoCopyWriting::getDescText, listVO.getSearch()))
                .orderByDesc(OppoCopyWriting::getId);

        IPage<OppoCopyWritingListDTO> iPage = this.baseMapper.selectOppoCopyWritingPage(
                new Page<>(listVO.getPage(), listVO.getPageNum()), queryWrapper);
        iPage.getRecords().forEach(record -> record.setUsageStatusName(
                ICommonEnum.getNameById(record.getUsageStatus(), OppoCopyWritingUsageStatusEnum.class)));
        return new PageUtils<>(iPage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOppoCopyWriting(OppoCopyWritingSaveVO saveVO, Integer userId) {
        List<ExcelCopyWritingDTO> copyWritings = ExcelUtils.read(UploadUtils.getUploadPath(saveVO.getFilePath()),
                ExcelCopyWritingDTO.class);
        copyWritings.forEach(copyWriting->{
            if (StringUtils.isBlank(copyWriting.getRemark())) {
                copyWriting.setRemark("");
            }
        });
        if (copyWritings.isEmpty()) {
            throw new CustomException("附件中无有效文案组合");
        }

        this.baseMapper.batchInsertOppoCopyWritings(copyWritings, userId);
    }
} 