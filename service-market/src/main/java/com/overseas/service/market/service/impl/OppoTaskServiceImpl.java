package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.market.oppoCopyWriting.OppoTaskListDTO;
import com.overseas.common.dto.market.oppoTask.OppoAdCampaignDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.enums.market.oppoCopyWriting.OppoCopyWritingUsageStatusEnum;
import com.overseas.common.enums.market.oppoCopyWriting.OppoTaskStatusEnum;
import com.overseas.common.enums.market.oppoCopyWriting.OppoTaskTypeEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.*;
import com.overseas.common.vo.market.oppoTask.OppoTaskListVO;
import com.overseas.common.vo.market.oppoTask.OppoTaskSaveVO;
import com.overseas.service.market.entity.OppoCopyWriting;
import com.overseas.service.market.entity.OppoTask;
import com.overseas.service.market.mapper.OppoCopyWritingMapper;
import com.overseas.service.market.mapper.OppoTaskMapper;
import com.overseas.service.market.service.OppoTaskService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * OPPO任务服务实现类
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class OppoTaskServiceImpl extends ServiceImpl<OppoTaskMapper, OppoTask> implements OppoTaskService {
    
    private final OppoCopyWritingMapper oppoCopyWritingMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOppoTask(OppoTaskSaveVO saveVO, Integer userId) {
        // 参数校验
        if (saveVO == null) {
            throw new CustomException("任务信息不能为空");
        }

        // 检查是否已存在相同名称的任务
        OppoTask existingTask = this.baseMapper.selectOne(new QueryWrapper<OppoTask>()
                .lambda()
                .eq(OppoTask::getTaskName, saveVO.getTaskName())
                .eq(OppoTask::getIsDel, IsDelEnum.NORMAL.getId()));

        if (existingTask != null) {
            throw new CustomException("该任务名称已存在，请确认后再试");
        }

        // 创建新任务
        OppoTask oppoTask = new OppoTask();
        BeanUtils.copyProperties(saveVO, oppoTask);
        oppoTask.setRemark(ConstantUtils.EMPTY);
        oppoTask.setCreateUid(userId);

        this.baseMapper.insert(oppoTask);
    }

    @Override
    public PageUtils<OppoTaskListDTO> listOppoTask(OppoTaskListVO listVO) {
        QueryWrapper<OppoTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(OppoTask::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getTaskType()), OppoTask::getTaskType,
                        listVO.getTaskType())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getTaskStatus()), OppoTask::getTaskStatus,
                        listVO.getTaskStatus())
                .like(StringUtils.isNotBlank(listVO.getSearch()), OppoTask::getTaskName, listVO.getSearch())
                .orderByDesc(OppoTask::getId);

        IPage<OppoTaskListDTO> iPage = this.baseMapper.selectOppoTaskPage(
                new Page<>(listVO.getPage(), listVO.getPageNum()), queryWrapper);
        iPage.getRecords().forEach(record -> {
            record.setTaskTypeName(ICommonEnum.getNameById(record.getTaskType(), OppoTaskTypeEnum.class));
            record.setTaskStatusName(ICommonEnum.getNameById(record.getTaskStatus(), OppoTaskStatusEnum.class));
            record.setResultPath(UploadUtils.getHttpUrl(record.getResultPath()));
        });
        return new PageUtils<>(iPage);
    }

    /**
     * 生成OPPO广告计划数据
     * 读取待处理任务的Excel模板，与日期范围进行叉乘，生成广告计划记录
     *
     * @param taskId 用户ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateOppoAdCampaigns(Integer taskId) {
        // 1. 获取待处理的任务（任务状态为0的任务）
        List<OppoTask> pendingTasks = this.baseMapper.selectList(new QueryWrapper<OppoTask>()
                .lambda().eq(ObjectUtils.isNotNullOrZero(taskId), OppoTask::getId, taskId)
                .eq(OppoTask::getTaskStatus, OppoTaskStatusEnum.WAITING.getId()) // 待处理状态
                .eq(OppoTask::getIsDel, IsDelEnum.NORMAL.getId())
                .orderByAsc(OppoTask::getId));

        if (pendingTasks.isEmpty()) {
            return;
        }

        for (OppoTask task : pendingTasks) {
            try {
                // 2. 读取Excel模板文件
                List<OppoAdCampaignDTO> templateData = ExcelUtils.read(
                        UploadUtils.getUploadPath(task.getTemplatePath()),
//                        task.getTemplatePath(),
                        3, OppoAdCampaignDTO.class);
                
                if (templateData.isEmpty()) {
                    // 更新任务状态为失败
                    updateTaskStatus(task.getId(), OppoTaskStatusEnum.ERROR.getId(), "模版内容异常", "");
                    continue;
                }

                // 3. 生成日期列表（从开始日期到结束日期）
                List<String> dateList = generateDateList(task.getStartDate(), task.getEndDate());
                
                if (dateList.isEmpty()) {
                    // 更新任务状态为失败
                    updateTaskStatus(task.getId(), OppoTaskStatusEnum.ERROR.getId(), "日期处理异常", "");
                    continue;
                }

                // 4. 模板数据与日期进行叉乘，生成最终的广告计划数据
                List<OppoAdCampaignDTO> finalData = crossMultiplyWithDates(templateData, dateList, task.getId(),
                        task.getTaskType());

                // 5. 批量插入到oppo_ad_campaign表
                if (!finalData.isEmpty()) {
                    String resultPath = UploadUtils.getUploadPath("/oppo/Oppo计划模版"
                            + DateUtils.format(new Date()) + "-" + taskId +".xlsx");
//                    String resultPath = "/Users/<USER>/Desktop/Oppo计划模版1111.xlsx";
                    // oppoAdCampaignMapper.batchInsertOppoAdCampaigns(finalData, userId);
                    ExcelUtils.download(resultPath, "计划信息",
                            OppoAdCampaignDTO.class, finalData);
                    
                    // 更新任务状态为成功
                    updateTaskStatus(task.getId(), OppoTaskStatusEnum.SUCCESS.getId(), "",
                            UploadUtils.getBasePath(resultPath)); // 1: 处理成功
                }
            } catch (Exception e) {
                // 处理异常，更新任务状态为失败
                updateTaskStatus(task.getId(), OppoTaskStatusEnum.ERROR.getId(), "服务处理异常", "");
                throw new CustomException("任务ID [" + task.getId() + "] 处理失败: " + e.getMessage());
            }
        }
    }

    /**
     * 生成日期列表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 日期列表
     */
    private List<String> generateDateList(String startDate, String endDate) {
        List<String> dateList = new ArrayList<>();
        
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate start = LocalDate.parse(startDate, formatter);
            LocalDate end = LocalDate.parse(endDate, formatter);
            
            LocalDate current = start;
            while (!current.isAfter(end)) {
                dateList.add(current.format(formatter));
                current = current.plusDays(1);
            }
        } catch (Exception e) {
            return List.of();
        }
        return dateList;
    }

    /**
     * 模板数据与日期进行叉乘
     *
     * @param templateData 模板数据
     * @param dateList     日期列表
     * @param taskId       任务ID
     * @return 叉乘后的数据
     */
    private List<OppoAdCampaignDTO> crossMultiplyWithDates(List<OppoAdCampaignDTO> templateData, 
                                                           List<String> dateList, Long taskId, Integer taskType) {
        List<String> errorMessages = new ArrayList<>();
        List<OppoAdCampaignDTO> result = new ArrayList<>();
        List<OppoCopyWriting> oppoCopyWritings = this.oppoCopyWritingMapper.selectList(
                new QueryWrapper<OppoCopyWriting>().lambda().eq(OppoCopyWriting::getIsDel, IsDelEnum.NORMAL.getId())
                        .eq(OppoCopyWriting::getUsageStatus, OppoCopyWritingUsageStatusEnum.UNUSED.getId())
        );
        int serialNumber = 1;
        for (String date : dateList) {
            int indexNumber = 1;
            for (OppoAdCampaignDTO template : templateData) {
                OppoAdCampaignDTO newRecord = new OppoAdCampaignDTO();
                BeanUtils.copyProperties(template, newRecord);
                
                // 设置序号
                newRecord.setSerialNumber(String.valueOf(serialNumber++));
                String index = String.format("%03d", indexNumber++);
                List<String> pieces = new ArrayList<>(List.of(template.getAdName().split("-")));
                String sign = "";String country; String language; String remark;
                if (pieces.size() < 8) {
                    remark = "";
                    language = "";
                    country = "";
                    errorMessages.add(template.getAdName() + " 广告名称不符合规范");
                } else {
                    String dateStr = DateUtils.format(DateUtils.string2Date(date), "MMdd");
                    sign = pieces.get(0) + dateStr + index;
                    pieces.set(0, sign);
                    pieces.set(5, dateStr);
                    country = pieces.get(1);
                    language = pieces.get(6);
                    remark = pieces.get(7);
                }
                // 设置名字
                newRecord.setAdName(String.join("-", pieces));
                // 设置push日期相关字段
                newRecord.setPushStartDate(DateUtils.format(DateUtils.string2Date(date), DateUtils.DATE_PATTERN_3));
                newRecord.setPushEndDate(DateUtils.format(DateUtils.string2Date(date), DateUtils.DATE_PATTERN_3));
                // 替换链接中的变量
                if (OppoTaskTypeEnum.GENERAL.getId().equals(taskType)) {
                    newRecord.setH5Link(template.getH5Link().replace("__aff_sub2__", sign));
                } else if (OppoTaskTypeEnum.PRODUCT.getId().equals(taskType)) {
                    // TODO : 获取商品库链接
                }

                // 获取文案库
                newRecord.setPushTitle("");
                newRecord.setPushContent("");
                for (OppoCopyWriting oppoCopyWriting : oppoCopyWritings) {
                    if (oppoCopyWriting.getUsageStatus().equals(OppoCopyWritingUsageStatusEnum.UNUSED.getId())
                            && oppoCopyWriting.getCountryAlias().equals(country) // 国家
                            && oppoCopyWriting.getLanguageAlias().equals(language) // 语言
                            && oppoCopyWriting.getRemark().equals(remark)    // 备注
                    ) {
                        newRecord.setPushTitle(oppoCopyWriting.getTitleText());
                        newRecord.setPushContent(oppoCopyWriting.getDescText());
                        oppoCopyWriting.setUsageStatus(OppoCopyWritingUsageStatusEnum.USED.getId());
                        break;
                    }
                }
                if (StringUtils.isBlank(newRecord.getPushTitle()) || StringUtils.isBlank(newRecord.getPushContent())) {
                    errorMessages.add(template.getAdName() + "文案库未找到匹配项");
                }

                result.add(newRecord);
            }
        }
        if(errorMessages.isEmpty()) {
            // 设置文案为已使用
            OppoCopyWriting oppoCopyWriting = new OppoCopyWriting();
            oppoCopyWriting.setUsageStatus(OppoCopyWritingUsageStatusEnum.USED.getId());
            this.oppoCopyWritingMapper.update(oppoCopyWriting, new UpdateWrapper<OppoCopyWriting>().lambda()
                    .in(OppoCopyWriting::getId, oppoCopyWritings.stream()
                            .filter(u -> u.getUsageStatus().equals(OppoCopyWritingUsageStatusEnum.USED.getId()))
                            .map(OppoCopyWriting::getId)
                            .collect(Collectors.toList())));
            // 无异常信息，直接返回组装好的数据
            return result;
        } else {
            // 更新任务状态为失败，返回空List
            updateTaskStatus(taskId, OppoTaskStatusEnum.ERROR.getId(),
                    StringUtils.join(errorMessages.stream().distinct().collect(Collectors.toList()), "，"),
                    "");
            return List.of();
        }
    }

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 状态
     * @param remark 备注信息
     */
    private void updateTaskStatus(Long taskId, Integer status, String remark, String resultPath) {
        OppoTask updateTask = new OppoTask();
        updateTask.setId(taskId);
        updateTask.setTaskStatus(status);
        updateTask.setRemark(remark);
        updateTask.setResultPath(resultPath);
        this.baseMapper.updateById(updateTask);
    }
}