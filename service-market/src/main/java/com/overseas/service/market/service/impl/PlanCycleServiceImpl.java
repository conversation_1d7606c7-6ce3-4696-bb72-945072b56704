package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.service.market.entity.PlanCycle;
import com.overseas.service.market.mapper.PlanCycleMapper;
import com.overseas.service.market.vo.plan.PlanCycleVO;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.DateUtils;
import com.overseas.common.enums.market.plan.PutCycleEnum;
import com.overseas.service.market.service.PlanCycleService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-25 19:24
 */
@Service
public class PlanCycleServiceImpl extends ServiceImpl<PlanCycleMapper, PlanCycle> implements PlanCycleService {

    @Override
    public void savePlanCycle(PlanCycleVO cycleVO, Long planId, Integer loginUserId) {
        // 先对数据进行初始化
        if (StringUtils.isEmpty(cycleVO.getStartDate())) {
            cycleVO.setStartDate(DateUtils.getTodayStringDate());
        }
        if (StringUtils.isEmpty(cycleVO.getEndDate())) {
            cycleVO.setEndDate(DateUtils.getTodayStringDate());
        }
        // 保存数据
        PlanCycle planCycleInDb = getOne(new LambdaQueryWrapper<PlanCycle>().eq(PlanCycle::getPlanId, planId).eq(PlanCycle::getIsDel, IsDelEnum.NORMAL.getId()));
        if (null == planCycleInDb) {
            planCycleInDb = new PlanCycle();
            BeanUtils.copyProperties(cycleVO, planCycleInDb);
            planCycleInDb.setCreateUid(loginUserId);
            planCycleInDb.setPlanId(planId);
            save(planCycleInDb);
        } else {
            BeanUtils.copyProperties(cycleVO, planCycleInDb);
            planCycleInDb.setUpdateUid(loginUserId);
            updateById(planCycleInDb);
        }
    }

    @Override
    public PlanCycleVO getPlanCycle(Long planId, Integer putCycle) {
        PlanCycleVO result = new PlanCycleVO();
        PlanCycle planCycleInDb = getOne(new LambdaQueryWrapper<PlanCycle>().eq(PlanCycle::getPlanId, planId).eq(PlanCycle::getIsDel, IsDelEnum.NORMAL.getId()));
        if (null == planCycleInDb) {
            throw new CustomException("计划排期不存在，请检查后重试");
        }
        BeanUtils.copyProperties(planCycleInDb, result);
        if (PutCycleEnum.ALL_TIME.getId().equals(putCycle)) {
            result.setStartDate("");
            result.setEndDate("");
        }
        return result;
    }
}
