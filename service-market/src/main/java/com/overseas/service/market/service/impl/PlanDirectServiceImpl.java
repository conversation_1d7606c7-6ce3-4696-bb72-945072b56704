package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.SelectDTO2;
import com.overseas.common.dto.TreeNodeDTO;
import com.overseas.common.dto.market.rtaStrategy.RtaStrategyGetDTO;
import com.overseas.common.dto.sys.project.ProjectByMasterDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.common.vo.market.plan.direct.*;
import com.overseas.common.vo.market.rtaStrategy.RtaStrategyGetByPlanIdsVO;
import com.overseas.common.vo.sys.project.ProjectByMasterVO;
import com.overseas.service.market.entity.productLibrary.ProductLibrary;
import com.overseas.service.market.enums.BatchUpdateEnum;
import com.overseas.common.enums.market.campaign.CampaignMarketTargetEnum;
import com.overseas.service.market.enums.monitor.monitorEvent.MonitorEventStatusEnum;
import com.overseas.service.market.enums.plan.PlanDirectIncludeTypeEnum;
import com.overseas.service.market.feign.FgSystemService;
import com.overseas.service.market.mapper.*;
import com.overseas.service.market.mapper.productLibrary.ProductLibraryMapper;
import com.overseas.service.market.service.*;
import com.overseas.service.market.vo.rta.RtaStrategyCascaderGetVO;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.SelectDTO3;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.FeignR;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.vo.market.monitor.monitorEvent.MonitorEventSelectGetVO;
import com.overseas.common.vo.market.tag.TagListVO;
import com.overseas.common.vo.sys.area.AreaCountrySelectVO;
import com.overseas.common.enums.OsTypeEnum;
import com.overseas.service.market.enums.plan.PlanOptimizeTargetEnum;
import com.overseas.common.enums.market.rta.RtaStrategyTypeEnum;
import com.overseas.service.market.enums.tag.TagListTypeEnum;
import com.overseas.service.market.enums.tag.TagStatusEnum;
import com.overseas.service.market.entity.*;
import com.overseas.service.market.vo.plan.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-23 15:53
 */
@Service
@RequiredArgsConstructor
public class PlanDirectServiceImpl extends ServiceImpl<PlanDirectMapper, PlanDirect> implements PlanDirectService {

    /**
     * 订单定向排黑白名单
     */
    private static final Integer INCLUDE_IN = 1;
    private static final Integer INCLUDE_OUT = 2;
    private static final Integer DIRECT_VALUE_TYPE_1 = 1;
    private static final Integer DIRECT_VALUE_TYPE_2 = 2;
    private static final Integer DIRECT_VALUE_TYPE_3 = 3;

    /**
     * 二级广告形式类型
     */
    private static final Integer DIRECT_VALUE_TYPE_4 = 4;

    private final AdxService adxService;

    private final EpService epService;

    private final MonitorEventService monitorEventService;

    private final FgSystemService fgSystemService;

    private final PlanUpdateRecordService planUpdateRecordService;

    private final TagService tagService;

    private final MediaService mediaService;

    private final RtaStrategyService rtaStrategyService;

    private final SlotService slotService;

    private final CountryMapper countryMapper;

    private final CountryCityMapper countryCityMapper;

    private final CampaignMapper campaignMapper;

    private final DirectConfigMapper directConfigMapper;

    private final TrackerActionMapper trackerActionMapper;

    private final AntiFraudStrategyMapper antiFraudStrategyMapper;

    private final ResChannelPoolMapper resChannelPoolMapper;

    private final DwdDspModelPricePublicMapper dwdDspModelPricePublicMapper;

    private final ProductLibraryMapper productLibraryMapper;

    private final PlanMapper planMapper;

    private final EpMapper epMapper;

    @Override
    public Object getDirectResource(DirectResourceVO directResourceVO, List<Integer> permissionMasterIds) {
        Object res;
        switch (directResourceVO.getFieldName()) {
            case "osType":
                res = ICommonEnum.list(OsTypeEnum.class);
                break;
            case "adxId":
                res = adxService.selectWorkable();
                break;
            case "optimizeTargetId":
                res = selectOptimizeTarget(directResourceVO);
                break;
            case "crowdLabel":
                TagListVO tagListVO = new TagListVO();
                BeanUtils.copyProperties(directResourceVO, tagListVO);
                tagListVO.setTagStatus(TagStatusEnum.PROCESS_SUCCESS.getId());
                tagListVO.setListType(TagListTypeEnum.MARKET_LIST.getId());
                tagListVO.setIds(directResourceVO.getAppointedIds());
                res = tagService.getTagPage(tagListVO, permissionMasterIds);
                break;
            case "slot":
                res = slotService.pageSlotDirect(directResourceVO);
                break;
            case "media":
                res = mediaService.pageMediaDirect(directResourceVO);
                break;
            case "areaCountry":
                FeignR<List<SelectDTO3>> areaCountry = fgSystemService.selectCountry(new AreaCountrySelectVO());
                if (!areaCountry.getCode().equals(0)) {
                    throw new CustomException(areaCountry.getCode(), areaCountry.getMsg());
                }
                res = areaCountry.getData();
                break;
            case "areaCity":
                FeignR<List<TreeNodeDTO>> areaCity = fgSystemService.cityTree(new AreaCountrySelectVO());
                if (!areaCity.getCode().equals(0)) {
                    throw new CustomException(areaCity.getCode(), areaCity.getMsg());
                }
                res = areaCity.getData();
                break;
            case "languages":
                res = this.countryMapper.selectCountryLanguage(new QueryWrapper<Country>().lambda()
                        .ne(Country::getLanguageAlias, ConstantUtils.EMPTY).orderByAsc(Country::getLanguageAlias));
                break;
            case "country":
                res = this.countryMapper.selectCountry(new QueryWrapper<CountryAll>().lambda()
                        .ne(CountryAll::getCountryAlias, ConstantUtils.EMPTY).orderByAsc(CountryAll::getCountryAlias));
                break;
            case "rtaStrategy":
                RtaStrategyCascaderGetVO getVO = new RtaStrategyCascaderGetVO();
                getVO.setMasterId(directResourceVO.getMasterId().longValue());
                getVO.setRtaGroupType(RtaStrategyTypeEnum.CUSTOM_RTA.getId());
                res = this.rtaStrategyService.getRtaStrategyCascader(getVO);
                break;
            case "antiFraud":
                res = antiFraudStrategyMapper.select(new LambdaQueryWrapper<AntiFraudStrategy>()
                        .eq(AntiFraudStrategy::getIsDel, IsDelEnum.NORMAL.getId())
                        .orderByDesc(AntiFraudStrategy::getStrategyId));
                break;
            case "channelPool":
                ProjectByMasterDTO project = fgSystemService.getProjectByMaster(ProjectByMasterVO.builder()
                        .masterIds(List.of(directResourceVO.getMasterId().longValue())).build()
                ).getData();
                if (CollectionUtils.isEmpty(project.getProjectId())) {
                    return List.of();
                }
                res = resChannelPoolMapper.select(new QueryWrapper<>().eq("project_id", project.getProjectId().get(0))
                        .orderByDesc("id")
                );
                break;
            case "deviceBrand":
                res = this.dwdDspModelPricePublicMapper.selectList(new LambdaQueryWrapper<DwdDspModelPricePublic>()
                                .eq(DwdDspModelPricePublic::getIsTopBrand, 1)
                                .groupBy(DwdDspModelPricePublic::getMake))
                        .stream().map(u -> new SelectDTO2(u.getMake(), u.getMake())).collect(Collectors.toList());
                break;
            case "productLibrary":
                res = this.productLibraryMapper.selectList(new LambdaQueryWrapper<ProductLibrary>()
                                .eq(ProductLibrary::getMasterId, directResourceVO.getMasterId())
                                .orderByDesc(ProductLibrary::getId))
                        .stream().map(u -> new SelectDTO3(u.getId().toString(), u.getLibraryName()))
                        .collect(Collectors.toList());
                break;
            default:
                throw new CustomException("资源类型不合法，请检查后重试");
        }
        return res;
    }

    @Override
    public void savePlanDirects(PlanDirectSaveVO directSaveVO, Long planId, Integer loginUserId) {
        //删除数据
        List<Integer> ids = this.saveOrUpdateDirect(directSaveVO, planId, loginUserId).stream().map(PlanDirect::getId).collect(Collectors.toList());
        this.baseMapper.delete(new QueryWrapper<PlanDirect>()
                .lambda().eq(PlanDirect::getPlanId, planId)
                .notIn(!ids.isEmpty(), PlanDirect::getId, ids)
        );
    }


    /**
     * 更新 or 新增定向
     *
     * @param directSaveVO 条件
     * @param planId       计划ID
     * @param loginUserId  用户
     * @return 返回数据
     */
    private List<PlanDirect> saveOrUpdateDirect(PlanDirectSaveVO directSaveVO, Long planId, Integer loginUserId) {
        if (null != directSaveVO.getAreaCountry() && StringUtils.isNotBlank(directSaveVO.getAreaCountry().getValue())) {
            List<Long> areaIds = JSONObject.parseArray(directSaveVO.getAreaCountry().getValue(), Long.class);
            if (CollectionUtils.isNotEmpty(areaIds)) {
                List<Long> countryIds = this.countryCityMapper.selectList(new LambdaQueryWrapper<CountryCity>()
                                .in(CountryCity::getId, areaIds))
                        .stream().map(CountryCity::getCountryId)
                        .distinct().collect(Collectors.toList());
                if (countryIds.stream().anyMatch(areaIds::contains)) {
                    throw new CustomException("地域选择城市后也选中了国家");
                }
            }
        }
        List<PlanDirect> planDirectList = generatePlanDirects(directSaveVO, planId);
        //获取老定向数据，用directId_include 字段共同标识
        Map<String, PlanDirect> oldDirects = list(new QueryWrapper<PlanDirect>()
                .lambda().eq(PlanDirect::getPlanId, planId))
                .stream().collect(Collectors.toMap(i -> i.getDirectId() + "_" + i.getInclude(), Function.identity()));

        //比对新老定向数据
        planDirectList.forEach(direct -> {
            PlanDirect oldDirect = oldDirects.get(direct.getDirectId() + "_" + direct.getInclude());
            if (oldDirect == null) {
                direct.setCreateUid(loginUserId);
                this.baseMapper.insert(direct);
            } else {
                direct.setId(oldDirect.getId());
                if (!direct.getDirectValue().equals(oldDirect.getDirectValue())) {
                    direct.setUpdateUid(loginUserId);
                    this.baseMapper.updateById(direct);
                }
            }
        });
        return planDirectList;
    }

    @Override
    public PlanDirectSaveVO getPlanDirects(Long planId, Plan planInfo) {
        PlanDirectSaveVO orderDirectDTO = new PlanDirectSaveVO();
        List<PlanDirect> orderDirects = list(new LambdaQueryWrapper<PlanDirect>().eq(PlanDirect::getPlanId, planId));
        // 获取定向配置数据
        Map<Integer, DirectConfig> directMaps = this.getDirectConfig();
        Map<Integer, List<PlanDirect>> directs = orderDirects.stream()
                .collect(Collectors.groupingBy(PlanDirect::getDirectId));
        directMaps.forEach((k, directConfig) -> {
            List<PlanDirect> currentDirects = directs.getOrDefault(directConfig.getId(), List.of());
            if (directConfig.getDirectValueType().equals(DIRECT_VALUE_TYPE_1)) {
                String value = CollectionUtils.isEmpty(currentDirects)
                        ? directConfig.getDirectDefault() : currentDirects.get(0).getDirectValue();
                // 检查实体类是否包含对应字段
                if (ObjectUtils.isExistField(directConfig.getDirectField(), orderDirectDTO)) {
                    ObjectUtils.setObjectValue(orderDirectDTO, directConfig.getDirectField(), value);
                }
            } else if (directConfig.getDirectValueType().equals(DIRECT_VALUE_TYPE_2)) {
                PlanDirectValueVO orderDirectValueVO = new PlanDirectValueVO();
                if (CollectionUtils.isNotEmpty(currentDirects)) {
                    PlanDirect orderDirect = currentDirects.get(0);
                    orderDirectValueVO.setInclude(orderDirect.getInclude());
                    orderDirectValueVO.setValue(orderDirect.getDirectValue());
                } else {
                    orderDirectValueVO.setInclude(INCLUDE_IN);
                    orderDirectValueVO.setValue(directConfig.getDirectDefault());
                }
                // 检查实体类是否包含对应字段
                if (ObjectUtils.isExistField(directConfig.getDirectField(), orderDirectDTO)) {
                    ObjectUtils.setObjectValue(orderDirectDTO, directConfig.getDirectField(), orderDirectValueVO);
                }
            } else if (directConfig.getDirectValueType().equals(DIRECT_VALUE_TYPE_3)) {
                PlanDirectIncludeVO planDirectIncludeVO = new PlanDirectIncludeVO();
                Map<Integer, PlanDirect> currentMap = currentDirects.stream()
                        .collect(Collectors.toMap(PlanDirect::getInclude, Function.identity()));
                planDirectIncludeVO.setInclude(currentMap.containsKey(INCLUDE_IN)
                        ? currentMap.get(INCLUDE_IN).getDirectValue() : directConfig.getDirectDefault());
                planDirectIncludeVO.setExclude(currentMap.containsKey(INCLUDE_OUT)
                        ? currentMap.get(INCLUDE_OUT).getDirectValue() : directConfig.getDirectDefault());
                // 检查实体类是否包含对应字段
                if (ObjectUtils.isExistField(directConfig.getDirectField(), orderDirectDTO)) {
                    ObjectUtils.setObjectValue(orderDirectDTO, directConfig.getDirectField(), planDirectIncludeVO);
                }
            } else if (directConfig.getDirectValueType().equals(DIRECT_VALUE_TYPE_4)) {
                SubSlotTypeVO subSlotTypeVO = new SubSlotTypeVO();
                if (CollectionUtils.isNotEmpty(currentDirects)) {
                    PlanDirect orderDirect = currentDirects.get(0);
                    subSlotTypeVO = JSONObject.parseObject(orderDirect.getDirectValue(), SubSlotTypeVO.class);
                } else {
                    subSlotTypeVO.setSlotType(planInfo.getSlotType());
                }
                // 检查实体类是否包含对应字段
                if (ObjectUtils.isExistField(directConfig.getDirectField(), orderDirectDTO)) {
                    ObjectUtils.setObjectValue(orderDirectDTO, directConfig.getDirectField(), subSlotTypeVO);
                }
            }
        });

        return orderDirectDTO;
    }

    /**
     * 优化目标下拉
     *
     * @param directResourceVO 定向查询参数
     * @return 优化目标数据
     */
    private List<SelectDTO> selectOptimizeTarget(DirectResourceVO directResourceVO) {
        if (CampaignMarketTargetEnum.BRAND_RECOGNITION.getId().equals(directResourceVO.getMarketTarget())) {
            return PlanOptimizeTargetEnum.list();
        } else {
            MonitorEventSelectGetVO getVO = new MonitorEventSelectGetVO();
            getVO.setMasterId(directResourceVO.getMasterId().longValue());
            getVO.setMonitorId(directResourceVO.getMonitorId());
            return monitorEventService.getEventSelect(getVO);
        }
    }

    @Override
    public SelectDTO getOptimizeTarget(Long id, Integer campaignMarketTarget, Plan plan) {
        DirectResourceVO directResourceVO = new DirectResourceVO();
        directResourceVO.setMarketTarget(campaignMarketTarget);
        directResourceVO.setMasterId(plan.getMasterId());
        directResourceVO.setMonitorId(plan.getMonitorId());
        List<SelectDTO> list = selectOptimizeTarget(directResourceVO);
        for (SelectDTO dto : list) {
            if (dto.getId().equals(id)) {
                return dto;
            }
        }
        return null;
    }

    @Override
    public void batchSavePlanDirect(PlanDirectBatchSaveVO saveVO, Integer userId) {

        List<PlanDirect> planDirects = saveVO.getPlanIds().stream().map(planId -> {
            PlanDirect planDirect = new PlanDirect();
            BeanUtils.copyProperties(saveVO, planDirect);
            planDirect.setPlanId(planId);
            planDirect.setUpdateUid(userId);
            return planDirect;
        }).collect(Collectors.toList());

        this.baseMapper.batchSavePlanDirect(planDirects, userId);
    }

    @Override
    public List<SelectDTO> selectOptimizeTarget(PlanOptimizeTargetSelectGetVO getVO) {

        List<Integer> campaignMarketTargets = new ArrayList<>();
        // 如果活动不为空
        if (CollectionUtils.isNotEmpty(getVO.getCampaignIds())) {
            List<Campaign> campaigns = this.campaignMapper.selectList(new QueryWrapper<Campaign>().lambda()
                    .eq(Campaign::getIsDel, IsDelEnum.NORMAL.getId())
                    .eq(ObjectUtils.isNotNullOrZero(getVO.getMasterId()), Campaign::getMasterId, getVO.getMasterId())
                    .in(Campaign::getId, getVO.getCampaignIds()));
            // 获取这些品牌的推广目标
            campaignMarketTargets = campaigns.stream().map(Campaign::getMarketTarget).distinct().collect(Collectors.toList());
        }
        List<SelectDTO> result = new ArrayList<>();
        // 如果活动推广目标范围为空，或者包含品牌认知
        if (campaignMarketTargets.isEmpty() || campaignMarketTargets.contains(CampaignMarketTargetEnum.BRAND_RECOGNITION.getId())) {
            result.addAll(PlanOptimizeTargetEnum.list());
        }
        // 如果活动推广目标范围为空，或者包含行动转化
        if (campaignMarketTargets.isEmpty() || campaignMarketTargets.contains(CampaignMarketTargetEnum.ACTION_TRANSFORM.getId())) {
            result.addAll(this.trackerActionMapper.selectOptimizeTarget(new QueryWrapper<TrackerAction>()
                    .eq("mme.is_del", IsDelEnum.NORMAL.getId())
                    .eq("mme.master_id", getVO.getMasterId())
                    .eq("mme.event_status", MonitorEventStatusEnum.SUCCESS.getId())
                    .groupBy("action.source_action")
                    .orderByAsc("action.id")));
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> batchUpdatePlanDirect(PlanDirectBatchUpdateVO updateVO, Integer userId) {
        long count = this.planMapper.selectCount(new LambdaQueryWrapper<Plan>()
                .eq(Plan::getMasterId, updateVO.getMasterId())
                .in(Plan::getId, updateVO.getIds())
                .eq(Plan::getIsDel, IsDelEnum.NORMAL.getId())
        );
        if (count != updateVO.getIds().size()) {
            throw new CustomException("期望修改计划中含有不可编辑计划，请确认后再试");
        }
        Map<String, Object> map = new HashMap<>();
        List<Integer> deleteDirectId = new ArrayList<>();
        PlanDirectSaveVO planDirectSaveVO = new PlanDirectSaveVO();
        //ADX修改
        if (BatchUpdateEnum.IS_CHANGE.getId().equals(updateVO.getUpdateAdx())) {
            //先执行数据判定，如果是非替换则此方法执行，如果是替换，则后续方法执行
            if (!PlanDirectIncludeTypeEnum.REPLACE.getId().equals(updateVO.getIncludeAdxType())) {
                this.changeAdx(updateVO, userId);
            } else {
                //替换逻辑
                if (StringUtils.isNotBlank(updateVO.getAdxId()) || StringUtils.isNotBlank(updateVO.getEpId())) {
                    map.put("adx_id", updateVO.getAdxId());
                    map.put("ep_id", updateVO.getEpId());
                    planDirectSaveVO.setAdxId(updateVO.getAdxId());
                    planDirectSaveVO.setEpId(updateVO.getEpId());
//                    List<Long> templateIds = planMapper.selectList(new LambdaQueryWrapper<Plan>()
//                                    .ne(Plan::getTemplateId, 0)
//                                    .in(Plan::getId, updateVO.getIds())
//                                    .eq(Plan::getIsDel, IsDelEnum.NORMAL.getId())).stream()
//                            .map(Plan::getTemplateId).collect(Collectors.toList());
//                    this.epService.checkEpTemplate(JSONObject.parseArray(updateVO.getEpId(), Long.class), templateIds);
//                    this.adxService.checkAdxTemplate(JSONObject.parseArray(updateVO.getAdxId(), Long.class), templateIds);
                }
            }
        }
        if (BatchUpdateEnum.IS_CHANGE.getId().equals(updateVO.getUpdatePackageName())) {
            //增加替换包名
            if (PlanDirectIncludeTypeEnum.REPLACE.getId().equals(updateVO.getPackageName().getIncludeType())) {
                deleteDirectId.add(1045);
                PlanDirectValueVO packageName = new PlanDirectValueVO();
                packageName.setInclude(updateVO.getPackageName().getInclude());
                packageName.setValue(updateVO.getPackageName().getValue());
                planDirectSaveVO.setPackageName(packageName);
            } else {
                updateVO.getPackageName().setDirectId(1045);
                this.batchUpdatePackage(updateVO.getIds(), updateVO.getPackageName(), userId);
            }
        }
        if (BatchUpdateEnum.IS_CHANGE.getId().equals(updateVO.getUpdateDeviceBrand())) {
//            map.put("device_brand", updateVO.getDeviceBrand());
            planDirectSaveVO.setDeviceBrand(updateVO.getDeviceBrand());
            deleteDirectId.add(1053);
        }
        if (BatchUpdateEnum.IS_CHANGE.getId().equals(updateVO.getUpdateDevicePrice())) {
//            map.put("device_price", updateVO.getDevicePrice());
            planDirectSaveVO.setDevicePrice(updateVO.getDevicePrice());
            deleteDirectId.add(1055);
        }
        //如果含有排除和定向的内容，需要在deleteDirectId中增加数据，且在删除逻辑中增加必定删除
        if (BatchUpdateEnum.IS_CHANGE.getId().equals(updateVO.getUpdateSsp())) {
            map.put("ssp", updateVO.getSsp());
            planDirectSaveVO.setSsp(updateVO.getSsp());
            deleteDirectId.add(1061);
        }
        if (BatchUpdateEnum.IS_CHANGE.getId().equals(updateVO.getUpdateCrowdLabel())) {
            map.put("crowd_label", updateVO.getCrowdLabel());
            planDirectSaveVO.setCrowdLabel(updateVO.getCrowdLabel());
        }
        if (BatchUpdateEnum.IS_CHANGE.getId().equals(updateVO.getUpdateRtaStrategy())) {
            map.put("rta_strategy", null == updateVO.getRtaStrategy() ? "" : updateVO.getRtaStrategy());
            planDirectSaveVO.setRtaStrategy(updateVO.getRtaStrategy());
            deleteDirectId.add(1036);
            if (null != updateVO.getRtaStrategy()) {
                RtaStrategyVO rtaStrategyVO = JSONObject.parseObject(updateVO.getRtaStrategy().getValue(), RtaStrategyVO.class);
                Map<Integer, RtaStrategyGetDTO> rtaSlotTypeMap = rtaStrategyService.getRtaStrategyByPlanIds(
                        RtaStrategyGetByPlanIdsVO.builder().planIds(updateVO.getIds()).isReplaceMacro(1).id(rtaStrategyVO.getRtaStrategyId()).build()
                ).stream().collect(Collectors.toMap(RtaStrategyGetDTO::getSlotType, v -> {
                    if (StringUtils.isNotBlank(v.getDeeplink()) || StringUtils.isNotBlank(v.getLandingUrl())
                            || StringUtils.isNotBlank(v.getMonitorViewUrl()) || StringUtils.isNotBlank(v.getMonitorClickUrl())) {
                        v.setNeedChange(1);
                    } else {
                        v.setNeedChange(0);
                    }
                    return v;
                }));
                if (rtaSlotTypeMap.size() == 1) {
                    rtaSlotTypeMap.forEach((slotType, rta) -> this.changeRtaUrl(rta, updateVO.getIds(),
                            updateVO.getMasterId(), userId));
                } else {
                    Map<Integer, List<PlanDirect>> slotTypeMap = this.baseMapper.selectList(
                            new LambdaQueryWrapper<PlanDirect>()
                                    .eq(PlanDirect::getDirectId, 1047)
                                    .in(PlanDirect::getPlanId, updateVO.getIds())
                    ).stream().collect(Collectors.groupingBy(v -> {
                        SubSlotTypeVO subSlotTypeVO = JSONObject.parseObject(v.getDirectValue(), SubSlotTypeVO.class);
                        return subSlotTypeVO.getSlotType();
                    }));
                    slotTypeMap.forEach((slotType, planIds) -> {
                        RtaStrategyGetDTO rta = rtaSlotTypeMap.getOrDefault(slotType, null);
                        this.changeRtaUrl(rta, planIds.stream().map(PlanDirect::getPlanId).collect(Collectors.toList()), updateVO.getMasterId(), userId);
                    });
                }
            }
        }
        List<PlanDirect> planDirects = this.generatePlanDirects(planDirectSaveVO, null);
        //删除不限制内容
        deleteDirectId.forEach(dId -> {
            if (planDirects.stream().noneMatch(u -> u.getDirectId().equals(dId)) || List.of(1061, 1045, 1053, 1055).contains(dId)) {
                this.baseMapper.delete(new LambdaQueryWrapper<PlanDirect>().eq(PlanDirect::getDirectId, dId)
                        .in(PlanDirect::getPlanId, updateVO.getIds())
                );
            }
        });
        //记录日志
        if (CollectionUtils.isNotEmpty(map)) {
            List<PlanUpdateRecord> planUpdateRecords = updateVO.getIds().stream().map(planId -> {
                PlanUpdateRecord planUpdateRecord = new PlanUpdateRecord();
                planUpdateRecord.setPlanId(planId);
                planUpdateRecord.setContent(JSONObject.toJSONString(map));
                return planUpdateRecord;
            }).collect(Collectors.toList());
            this.planUpdateRecordService.savePlanUpdateRecord(planUpdateRecords, 0);
        }
        if (planDirects.isEmpty()) {
            return List.of();
        }
        planDirects.forEach(planDirect -> {
            List<PlanDirect> updateList = updateVO.getIds().stream().map(planId -> {
                PlanDirect n = new PlanDirect();
                BeanUtils.copyProperties(planDirect, n);
                n.setPlanId(planId);
                return n;
            }).collect(Collectors.toList());
            this.baseMapper.batchSavePlanDirect(updateList, userId);
        });
        return List.of();
    }

    /**
     * 修改Adx
     *
     * @param updateVO 修改内容
     * @param userId   用户ID
     */
    public void changeAdx(PlanDirectBatchUpdateVO updateVO, Integer userId) {
        if (null == updateVO.getIncludeAdxType()) {
            return;
        }
        if (StringUtils.isBlank(updateVO.getAdxId()) && StringUtils.isBlank(updateVO.getEpId())) {
            return;
        }
        PlanDirectIncludeTypeEnum includeTypeEnum = ICommonEnum.get(updateVO.getIncludeAdxType(), PlanDirectIncludeTypeEnum.class);
        if (null == includeTypeEnum) {
            return;
        }
        if (includeTypeEnum.equals(PlanDirectIncludeTypeEnum.REPLACE)) {
            return;
        }
        // 1.先获取所选计划的定向信息
        Map<Long, List<PlanDirect>> planDirectMap = this.baseMapper.selectList(new QueryWrapper<PlanDirect>().lambda()
                .in(PlanDirect::getDirectId, List.of(1031, 1032))
                .in(PlanDirect::getPlanId, updateVO.getIds())
                .orderByDesc(PlanDirect::getDirectId)
        ).stream().collect(Collectors.groupingBy(PlanDirect::getPlanId));
        Map<Long, Long> templateMap = planMapper.selectList(new LambdaQueryWrapper<Plan>()
                .ne(Plan::getTemplateId, 0)
                .in(Plan::getId, updateVO.getIds())
                .eq(Plan::getIsDel, IsDelEnum.NORMAL.getId())
        ).stream().collect(Collectors.toMap(Plan::getId, Plan::getTemplateId));
        List<Long> epIds = JSONObject.parseArray(updateVO.getEpId(), Long.class);
        //清空数据
        updateVO.setAdxId(null);
        updateVO.setEpId(null);
        Map<Long, SelectDTO> resMap = new HashMap<>();
        List<PlanDirect> planDirects = planDirectMap.values().stream().flatMap(values -> {
            Long tempId = templateMap.getOrDefault(values.get(0).getPlanId(), 0L);
            List<Long> afterAdxIds = new ArrayList<>();
            return values.stream()
                    .sorted(Comparator.comparingInt(PlanDirect::getDirectId).reversed())
                    .map(val -> {
                        List<Long> arrays = JSONObject.parseArray(val.getDirectValue(), Long.class);
                        List<Long> after;
                        try {
                            switch (val.getDirectId()) {
                                case 1031:
                                    after = afterAdxIds;
                                    if (CollectionUtils.isEmpty(after)) {
                                        return null;
                                    }
//                                    if (ObjectUtils.isNotNullOrZero(tempId)) {
//                                        this.adxService.checkAdxTemplate(after, List.of(tempId));
//                                    }
                                    break;
                                case 1032:
                                    //无修改或者无数据
                                    after = this.adxEpDealAfter(arrays, epIds, includeTypeEnum);
                                    if (null == after || after.size() == arrays.size()) {
                                        return null;
                                    }
//                                    if (ObjectUtils.isNotNullOrZero(tempId) && after.size() != arrays.size()) {
//                                        this.epService.checkEpTemplate(after, List.of(tempId));
//                                    }
                                    //如果排除后无任何ep，则提示问题
                                    if (CollectionUtils.isEmpty(after)) {
                                        throw new CustomException("批量操作后无可用Ep", val.getPlanId());
                                    }
                                    afterAdxIds.addAll(
                                            this.epMapper.selectBatchIds(after).stream().map(Ep::getAdxId)
                                                    .distinct().sorted(Comparator.comparingLong(u -> u))
                                                    .collect(Collectors.toList())
                                    );
                                    break;
                                default:
                                    return null;
                            }
                            val.setDirectValue(JSONObject.toJSONString(after));
                            val.setId(null);
                            return val;
                        } catch (CustomException e) {
                            resMap.put(val.getPlanId(), new SelectDTO(val.getPlanId(), e.getMessage()));
                            return null;
                        }
                    });
        }).filter(Objects::nonNull).collect(Collectors.toList());
        //返回异常
        if (!resMap.isEmpty()) {
            throw new CustomException(4002, "", resMap.values());
        }
        if (CollectionUtils.isEmpty(planDirects)) {
            return;
        }
        // 5.更新SQL
        this.baseMapper.batchSavePlanDirect(planDirects, userId);
        //修改记录
        List<PlanUpdateRecord> planUpdateRecords = planDirects.stream()
                .collect(Collectors.groupingBy(PlanDirect::getPlanId))
                .values().stream().map(values -> {
                    PlanUpdateRecord planUpdateRecord = new PlanUpdateRecord();
                    planUpdateRecord.setPlanId(values.get(0).getPlanId());
                    Map<String, Object> map = new HashMap<>();
                    values.forEach(val -> {
                        switch (val.getDirectId()) {
                            case 1031:
                                map.put("adx_id", val.getDirectValue());
                                break;
                            case 1032:
                                map.put("ep_id", val.getDirectValue());
                                break;
                            default:
                        }
                    });
                    if (map.isEmpty()) {
                        return null;
                    }
                    planUpdateRecord.setContent(JSONObject.toJSONString(map));
                    return planUpdateRecord;
                }).filter(Objects::nonNull).collect(Collectors.toList());
        this.planUpdateRecordService.savePlanUpdateRecord(planUpdateRecords, userId);

    }

    /**
     * 修改数据
     *
     * @param arrays          原数据
     * @param dealIds         需要处理数据
     * @param includeTypeEnum 类型
     * @return 返回结果
     */
    private List<Long> adxEpDealAfter(List<Long> arrays, List<Long> dealIds, PlanDirectIncludeTypeEnum includeTypeEnum) {
        if (CollectionUtils.isEmpty(dealIds)) {
            return null;
        }
        switch (includeTypeEnum) {
            case ADD:
                return Stream.of(arrays.stream(), dealIds.stream()).flatMap(u -> u).distinct().sorted(Comparator.comparingLong(Long::valueOf)).collect(Collectors.toList());
            case ELIMINATE:
                return arrays.stream().filter(u -> !dealIds.contains(u)).distinct().sorted(Comparator.comparingLong(Long::valueOf)).collect(Collectors.toList());
            default:
                return null;
        }
    }

    /**
     * 修改rta 链接
     *
     * @param rtaStrategyGetDTO rta对象
     * @param planIds           计划ID
     * @param masterId          账户ID
     * @param userId            用户ID
     */
    public void changeRtaUrl(RtaStrategyGetDTO rtaStrategyGetDTO, List<Long> planIds,
                             Integer masterId, Integer userId) {
        if (CollectionUtils.isEmpty(planIds) || ObjectUtils.isNullOrZero(masterId)) {
            return;
        }
        if (null == rtaStrategyGetDTO || 1 != rtaStrategyGetDTO.getNeedChange()) {
            return;
        }
        Map<String, String> map = new HashMap<>();
        Plan plan = new Plan();
        if (StringUtils.isNotBlank(rtaStrategyGetDTO.getDeeplink())) {
            plan.setDeeplink(rtaStrategyGetDTO.getDeeplink());
            map.put("deeplink", plan.getDeeplink());
        }
        if (StringUtils.isNotBlank(rtaStrategyGetDTO.getLandingUrl())) {
            plan.setLandingUrl(rtaStrategyGetDTO.getLandingUrl());
            map.put("landing_url", plan.getLandingUrl());
        }
        if (StringUtils.isNotBlank(rtaStrategyGetDTO.getMonitorViewUrl())) {
            plan.setMonitorViewUrl1(rtaStrategyGetDTO.getMonitorViewUrl());
            map.put("monitor_view_url1", plan.getMonitorViewUrl1());
        }
        if (StringUtils.isNotBlank(rtaStrategyGetDTO.getMonitorClickUrl())) {
            plan.setMonitorClickUrl1(rtaStrategyGetDTO.getMonitorClickUrl());
            map.put("monitor_click_url1", plan.getMonitorClickUrl1());
        }
        if (map.isEmpty()) {
            return;
        }
        plan.setUpdateUid(userId);
        this.planMapper.update(plan, new LambdaQueryWrapper<Plan>()
                .eq(Plan::getMasterId, masterId).in(Plan::getId, planIds)
        );
        List<PlanUpdateRecord> planUpdateRecords = planIds.stream().map(planId -> {
            PlanUpdateRecord planUpdateRecord = new PlanUpdateRecord();
            planUpdateRecord.setPlanId(planId);
            planUpdateRecord.setContent(JSONObject.toJSONString(map));
            return planUpdateRecord;
        }).collect(Collectors.toList());
        this.planUpdateRecordService.savePlanUpdateRecord(planUpdateRecords, 0);
    }

    /**
     * 修改包名
     */
    public void batchUpdatePackage(List<Long> ids, IncludeTypeUpdateVO updateVO, Integer userId) {
        List<String> res = new ArrayList<>();
        // 1.先获取所选计划的定向信息
        List<PlanDirect> planDirects = this.baseMapper.selectList(new QueryWrapper<PlanDirect>().lambda()
                .eq(PlanDirect::getDirectId, updateVO.getDirectId())
                .in(PlanDirect::getPlanId, ids));

        // 2.获取不符合选择类型的计划
        List<String> diffIncludePlanIds = planDirects.stream().filter(planDirect ->
                        !planDirect.getInclude().equals(updateVO.getInclude()))
                .map(planDirect -> planDirect.getPlanId().toString()).collect(Collectors.toList());

        // 3.获取待补充/剔除的包名列表
        List<String> packages = JSONObject.parseArray(updateVO.getValue(), String.class);
        // 4.遍历获取待修改的定向列表
        List<PlanDirect> planDirectList = new ArrayList<>();
        List<String> overrunPlanIds = new ArrayList<>();
        for (PlanDirect planDirect : planDirects) {
            // 如果是因为定向类型不同，则跳过不进行修改
            if (diffIncludePlanIds.contains(planDirect.getPlanId().toString())) {
                continue;
            }
            List<String> finalPackages = new ArrayList<String>() {{
                addAll(JSONObject.parseArray(planDirect.getDirectValue(), String.class));
                // 如果是剔除，则remove
                if (PlanDirectIncludeTypeEnum.ELIMINATE.getId().equals(updateVO.getIncludeType())) {
                    removeAll(packages);
                } else {
                    // 如果是增加，则补充
                    addAll(packages);
                }
            }}.stream().distinct().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            // 如果是包名上限超过了1000，则记录并且跳过
            if (finalPackages.size() > 1000) {
                overrunPlanIds.add(planDirect.getPlanId().toString());
                continue;
            }
            planDirect.setDirectValue(JSONObject.toJSONString(finalPackages));
            planDirectList.add(planDirect);
        }
        if (CollectionUtils.isNotEmpty(diffIncludePlanIds)) {
            res.add("计划ID：" + Strings.join(diffIncludePlanIds, ',')
                    + "的定向类型不符合");
        }
        if (CollectionUtils.isNotEmpty(overrunPlanIds)) {
            res.add("计划ID：" + Strings.join(overrunPlanIds, ',')
                    + "数量超出包名上限");
        }

        if (planDirectList.isEmpty()) {
            throw new CustomException(4001, "", res);
        }

        // 5.更新SQL
        this.baseMapper.batchSavePlanDirect(planDirectList, userId);

        // 6.记录编辑记录
        List<PlanUpdateRecord> planUpdateRecords = planDirectList.stream().map(planDirect -> {
            PlanUpdateRecord planUpdateRecord = new PlanUpdateRecord();
            planUpdateRecord.setPlanId(planDirect.getPlanId());
            Map<String, Object> map = new HashMap<>() {{
                put("package_include", planDirect.getInclude());
                put("package_value", planDirect.getDirectValue());
            }};
            planUpdateRecord.setContent(JSONObject.toJSONString(map));
            return planUpdateRecord;
        }).collect(Collectors.toList());
        this.planUpdateRecordService.savePlanUpdateRecord(planUpdateRecords, userId);
        // 8.返回错误信息
        if (CollectionUtils.isNotEmpty(res)) {
            res.add(0, "操作成功");
        }
    }

    /**
     * 根据 vo 获取 PlanDirectList
     *
     * @param planDirectSaveVO 定向数据
     * @param planId           订单ID
     * @return 返回数据
     */
    private List<PlanDirect> generatePlanDirects(PlanDirectSaveVO planDirectSaveVO, Long planId) {
        //获取定向配置数据
        Map<Integer, DirectConfig> directConfigMap = this.getDirectConfig();
        //获取新定向数据
        List<PlanDirect> PlanDirectList = new ArrayList<>();

        directConfigMap.forEach((directId, config) -> {
            Object forValue = ObjectUtils.getObjectValue(planDirectSaveVO, config.getDirectField());
            if (forValue != null) {
                if (forValue instanceof PlanDirectIncludeVO) {
                    PlanDirectIncludeVO planDirectIncludeVO = (PlanDirectIncludeVO) forValue;
                    if (StringUtils.isNotEmpty(planDirectIncludeVO.getInclude())) {
                        PlanDirect planDirect = new PlanDirect();
                        planDirect.setPlanId(planId);
                        planDirect.setDirectId(config.getId());
                        planDirect.setDirectValue(planDirectIncludeVO.getInclude());
                        planDirect.setInclude(INCLUDE_IN);
                        PlanDirectList.add(planDirect);
                    }
                    if (StringUtils.isNotEmpty(planDirectIncludeVO.getExclude())) {
                        PlanDirect planDirect = new PlanDirect();
                        planDirect.setPlanId(planId);
                        planDirect.setDirectId(config.getId());
                        planDirect.setDirectValue(planDirectIncludeVO.getExclude());
                        planDirect.setInclude(INCLUDE_OUT);
                        PlanDirectList.add(planDirect);
                    }
                } else if (forValue instanceof SubSlotTypeVO) {
                    PlanDirect planDirect = new PlanDirect();
                    planDirect.setPlanId(planId);
                    planDirect.setDirectId(config.getId());
                    planDirect.setDirectValue(JSONObject.toJSONString(forValue));
                    planDirect.setInclude(INCLUDE_IN);
                    PlanDirectList.add(planDirect);
                } else {
                    Integer include = INCLUDE_IN;
                    String strValue = "";
                    //黑白名单类数据展示不同
                    if (forValue instanceof PlanDirectValueVO) {
                        PlanDirectValueVO PlanDirectValueVO = (PlanDirectValueVO) forValue;
                        strValue = PlanDirectValueVO.getValue();
                        include = PlanDirectValueVO.getInclude();
                    }
                    if (forValue instanceof String) {
                        strValue = (String) forValue;
                    }
                    if (StringUtils.isNotBlank(strValue)) {
                        strValue = strValue.trim();
                        PlanDirect PlanDirect = new PlanDirect();
                        PlanDirect.setPlanId(planId);
                        PlanDirect.setDirectId(config.getId());
                        PlanDirect.setDirectValue(strValue);
                        PlanDirect.setInclude(include);
                        PlanDirectList.add(PlanDirect);
                    }
                }

            }
        });

        return PlanDirectList;
    }

    /**
     * 获取定向配置数据
     *
     * @return 配置列表
     */
    private Map<Integer, DirectConfig> getDirectConfig() {
        return this.directConfigMapper.selectList(
                new QueryWrapper<DirectConfig>().lambda()
                        .eq(DirectConfig::getIsDel, IsDelEnum.NORMAL.getId())
                        .eq(DirectConfig::getDirectLevel, 2)
        ).stream().collect(Collectors.toMap(DirectConfig::getId, Function.identity()));
    }
}
