package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.configuration.SheinConfiguration;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.TreeNodeDTO;
import com.overseas.common.dto.TreeNodeDTO2;
import com.overseas.common.dto.market.creativeUnit.CreativeUnitAssetDTO;
import com.overseas.common.dto.market.creativeUnit.CreativeUnitMaterialDTO;
import com.overseas.common.dto.market.market.MarketBatchListDTO;
import com.overseas.common.dto.market.monitor.MonitorGetDTO;
import com.overseas.common.dto.market.plan.*;
import com.overseas.common.dto.report.ReportListDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.enums.SortTypeEnum;
import com.overseas.common.enums.TimeZoneEnum;
import com.overseas.common.enums.market.DealTypeEnum;
import com.overseas.common.enums.market.PutEnum;
import com.overseas.common.enums.market.campaign.CampaignModeEnum;
import com.overseas.common.enums.market.campaign.CampaignPutOnTargetEnum;
import com.overseas.common.enums.market.plan.PlanStatusEnum;
import com.overseas.common.enums.market.plan.PutCycleEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.*;
import com.overseas.common.vo.common.GetVO;
import com.overseas.common.vo.market.behaviorApp.BehaviorAppGetVO;
import com.overseas.common.vo.market.campaign.CampaignSaveVO;
import com.overseas.common.vo.market.creative.CreativeUnitListVO;
import com.overseas.common.vo.market.market.MarketBatchListVO;
import com.overseas.common.vo.market.market.RecordBatchDeleteVO;
import com.overseas.common.vo.market.market.RecordBatchNoticeVO;
import com.overseas.common.vo.market.market.RecordBatchSwitchVO;
import com.overseas.common.vo.market.master.MasterPageFirstVO;
import com.overseas.common.vo.market.monitor.MonitorGetVO;
import com.overseas.common.vo.market.plan.*;
import com.overseas.common.vo.market.plan.direct.EpSelectGetVO;
import com.overseas.common.vo.market.plan.direct.RtaStrategyVO;
import com.overseas.common.vo.report.ReportHasCostGetVO;
import com.overseas.common.vo.report.SelectSortByCostVO;
import com.overseas.common.vo.sys.industry.IndustryListGetVO;
import com.overseas.service.market.common.utils.BudgetUtils;
import com.overseas.service.market.dto.market.MasterPageFirstDTO;
import com.overseas.service.market.dto.plan.*;
import com.overseas.service.market.entity.*;
import com.overseas.service.market.enums.BatchUpdateEnum;
import com.overseas.service.market.enums.BidTypeEnum;
import com.overseas.service.market.enums.BudgetTypeEnum;
import com.overseas.service.market.enums.creative.units.CreativeUnitStatusEnum;
import com.overseas.service.market.enums.plan.*;
import com.overseas.service.market.events.creativeUnitAudit.CreativeUnitAuditEvent;
import com.overseas.service.market.events.notifyControl.ControlContants;
import com.overseas.service.market.events.notifyControl.ControlPlanEvent;
import com.overseas.service.market.feign.FgReportService;
import com.overseas.service.market.feign.FgSystemService;
import com.overseas.service.market.mapper.*;
import com.overseas.service.market.mapper.call.CallNoticeMapper;
import com.overseas.service.market.service.*;
import com.overseas.service.market.vo.plan.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PlanServiceImpl extends ServiceImpl<PlanMapper, Plan> implements PlanService, MarketPageListService {

    private final FgReportService fgReportService;

    private final PlanDirectService planDirectService;

    private final PlanCycleService planCycleService;

    private final CampaignService campaignService;

    private final BehaviorAppService behaviorAppService;

    private final MonitorService monitorService;

    private final FgSystemService fgSystemService;

    private final PlanCycleMapper planCycleMapper;

    private final CreativeMapper creativeMapper;

    private final CreativeUnitService creativeUnitService;

    private final CreativeUnitMapper creativeUnitMapper;

    private final DeepBidConfigMapper deepBidConfigMapper;

    private final EpMapper epMapper;

    private final TemplateConfigMapper templateConfigMapper;

    private final ApplicationContext applicationContext;

    private final MasterMapper masterMapper;

    private final PlanUpdateRecordService planUpdateRecordService;

    private final MonitorEventService monitorEventService;

    private final AdxService adxService;

    private final EpService epService;

    private final SheinConfiguration sheinConfiguration;

    private final PlanGenerateMapMapper planGenerateMapMapper;

    private final SmsUtils smsUtils;

    private final ThreadPoolTaskExecutor controlEventPool;

    private final AssetGroupMapper assetGroupMapper;

    private final SheinCreativeService sheinCreativeService;

    private final CallNoticeMapper callNoticeMapper;

    private final SheinTagPlanMapper sheinTagPlanMapper;

    private final SheinTagMapper sheinTagMapper;

    private final SheinTagGroupMapper sheinTagGroupMapper;

    private final PlanDirectMapper planDirectMapper;

    private final CountryAllMapper countryAllMapper;

    private final RtaStrategyMapper rtaStrategyMapper;

    @Override
    public List<SelectDTO> selectPlan(PlanSelectGetVO getVO, List<Integer> permissionMasterIds, User user) {

        if (ObjectUtils.isNullOrZero(getVO.getMasterId()) && CollectionUtils.isEmpty(getVO.getMasterIds())) {
            getVO.setMasterIds(permissionMasterIds.stream().map(Long::valueOf).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(getVO.getPlanIds())) {
            //如果没有指定mode ，且非指定计划ID的情况下，则仅展示正计划
            if (CollectionUtils.isEmpty(getVO.getPlanMode())) {
                getVO.setPlanMode(List.of(PlanModeEnum.NORMAL.getId()));
            }
        }
        List<Long> planIds = null;
        if (CollectionUtils.isNotEmpty(getVO.getSheinPlanIds())) {
            planIds = getSheinMarketId(getVO.getSheinPlanIds(), PlanGenerateMapTypeEnum.PLAN);
        }
        QueryWrapper<Plan> queryWrapper = new QueryWrapper<>();
        if (ObjectUtils.isNotNullOrZero(getVO.getAuditStatus())) {
            queryWrapper.in("plan_status", PlanStatusEnum.MARKETING.getId().equals(getVO.getAuditStatus()) ?
                    List.of(PlanStatusEnum.WAIT.getId(), PlanStatusEnum.MARKETING.getId(), PlanStatusEnum.STOP.getId(), PlanStatusEnum.FINISH.getId()) :
                    List.of(getVO.getAuditStatus()));
        }
        List<SelectDTO> planSelect = this.baseMapper.selectPlan(queryWrapper.lambda()
                .eq(ObjectUtils.isNotNullOrZero(getVO.getMasterId()), Plan::getMasterId, getVO.getMasterId())
                .in(CollectionUtils.isNotEmpty(getVO.getMasterIds()), Plan::getMasterId, getVO.getMasterIds())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getExcludeDel()), Plan::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getCampaignId()), Plan::getCampaignId, getVO.getCampaignId())
                .in(CollectionUtils.isNotEmpty(getVO.getCampaignIds()), Plan::getCampaignId, getVO.getCampaignIds())
                .in(CollectionUtils.isNotEmpty(getVO.getPlanIds()), Plan::getId, getVO.getPlanIds())
                .in(CollectionUtils.isEmpty(getVO.getPlanIds()), Plan::getPlanMode, PlanModeEnum.normal())
                .in(CollectionUtils.isNotEmpty(getVO.getPlanMode()), Plan::getPlanMode, getVO.getPlanMode())
                .in(CollectionUtils.isNotEmpty(planIds), Plan::getId, planIds)
                .in(CollectionUtils.isNotEmpty(getVO.getPlanStatus()), Plan::getPlanStatus, getVO.getPlanStatus())
                .in(CollectionUtils.isNotEmpty(getVO.getOptimizeTargetIds()), Plan::getOptimizeTargetId, getVO.getOptimizeTargetIds())
                .in(CollectionUtils.isNotEmpty(getVO.getTemplateIds()), Plan::getTemplateId, getVO.getTemplateIds())
                .in(ObjectUtils.isNotNullOrZero(getVO.getBidType()), Plan::getBidType, getVO.getBidType())
                //投放状态筛选
                .eq(Plan::getIsPlanPut, sheinConfiguration.isPut(getVO.getIsPut(), user.getRoleId()))
                .orderByDesc(Plan::getId)
        );
        if (planSelect.isEmpty()) {
            return planSelect;
        }
        if (ObjectUtils.isNotNullOrZero(getVO.getSortByCost())) {
            SelectSortByCostVO sortByCostVO = new SelectSortByCostVO();
            BeanUtils.copyProperties(getVO, sortByCostVO);
            sortByCostVO.setSelects(planSelect);
            sortByCostVO.setType(2);
            return fgReportService.selectSorByCost(sortByCostVO).getData();
        }
        return planSelect;
    }

    @Override
    public List<SelectDTO> selectCopyPlan(PlanSelectGetVO getVO, List<Integer> permissionMasterIds, User user) {
        if (ObjectUtils.isNullOrZero(getVO.getMasterId()) && CollectionUtils.isEmpty(getVO.getMasterIds())) {
            getVO.setMasterIds(permissionMasterIds.stream().map(Long::valueOf).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(getVO.getPlanIds())) {
            //如果没有指定mode ，且非指定计划ID的情况下，则仅展示正计划
            if (CollectionUtils.isEmpty(getVO.getPlanMode())) {
                getVO.setPlanMode(List.of(PlanModeEnum.NORMAL.getId()));
            }
        }
        List<Long> planIds = null;
        if (CollectionUtils.isNotEmpty(getVO.getSheinPlanIds())) {
            planIds = getSheinMarketId(getVO.getSheinPlanIds(), PlanGenerateMapTypeEnum.PLAN);
        }
        QueryWrapper<Plan> queryWrapper = new QueryWrapper<>();
        if (ObjectUtils.isNotNullOrZero(getVO.getAuditStatus())) {
            queryWrapper.in("plan_status", PlanStatusEnum.MARKETING.getId().equals(getVO.getAuditStatus()) ?
                    List.of(PlanStatusEnum.WAIT.getId(), PlanStatusEnum.MARKETING.getId(), PlanStatusEnum.STOP.getId(), PlanStatusEnum.FINISH.getId()) :
                    List.of(getVO.getAuditStatus()));
        }
        List<SelectDTO> planSelect = this.baseMapper.selectPlan(queryWrapper.lambda()
                .eq(ObjectUtils.isNotNullOrZero(getVO.getMasterId()), Plan::getMasterId, getVO.getMasterId())
                .in(CollectionUtils.isNotEmpty(getVO.getMasterIds()), Plan::getMasterId, getVO.getMasterIds())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getExcludeDel()), Plan::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getCampaignId()), Plan::getCampaignId, getVO.getCampaignId())
                .in(CollectionUtils.isNotEmpty(getVO.getCampaignIds()), Plan::getCampaignId, getVO.getCampaignIds())
                .in(CollectionUtils.isNotEmpty(getVO.getPlanIds()), Plan::getId, getVO.getPlanIds())
                .in(CollectionUtils.isEmpty(getVO.getPlanIds()), Plan::getPlanMode, PlanModeEnum.normal())
                .in(CollectionUtils.isNotEmpty(getVO.getPlanMode()), Plan::getPlanMode, getVO.getPlanMode())
                .in(CollectionUtils.isNotEmpty(planIds), Plan::getId, planIds)
                .in(CollectionUtils.isNotEmpty(getVO.getPlanStatus()), Plan::getPlanStatus, getVO.getPlanStatus())
                .orderByDesc(Plan::getId)
        );
        if (planSelect.isEmpty()) {
            return planSelect;
        }
        if (ObjectUtils.isNotNullOrZero(getVO.getSortByCost())) {
            SelectSortByCostVO sortByCostVO = new SelectSortByCostVO();
            BeanUtils.copyProperties(getVO, sortByCostVO);
            sortByCostVO.setSelects(planSelect);
            sortByCostVO.setType(2);
            return fgReportService.selectSorByCost(sortByCostVO).getData();
        }
        return planSelect;
    }

    @Override
    public PageUtils<PlanAndCampaignAndMasterSelectDTO> selectPagePlanAndCampaignAndMaster(PlanAndCampaignAndMasterSelectVO selectVO,
                                                                                           List<Integer> permissionMasterIds, User user) {
        IPage<PlanAndCampaignAndMasterSelectDTO> iPage = new Page<>(selectVO.getPage(), selectVO.getPageNum());
        iPage = this.baseMapper.selectPagePlanAndCampaignAndMaster(iPage, new QueryWrapper<Plan>()
                .in("p.master_id", permissionMasterIds)
                .and(q ->
                        //如果是指定计划ID，则不需要过滤状态，直接展示
                        q.in(CollectionUtils.isNotEmpty(selectVO.getPlanIds()), "p.id", selectVO.getPlanIds())
                                //如果是非指定计划ID，则需要过滤删除，投放是否结束等状态
                                .or(CollectionUtils.isEmpty(selectVO.getPlanIds()),
                                        q1 -> q1.eq("c.is_del", IsDelEnum.NORMAL.getId())
                                                .eq("p.is_del", IsDelEnum.NORMAL.getId())
                                                .ne("p.plan_status", 4)
                                )
                ).eq("p.is_ab_plan", 0)
                .eq("c.is_campaign_put", PutEnum.IS_PUT.getId())
                .eq("p.is_plan_put", PutEnum.IS_PUT.getId())
                .eq("p.plan_mode", PlanModeEnum.NORMAL.getId())
                .in(CollectionUtils.isNotEmpty(selectVO.getCampaignIds()), "p.campaign_id", selectVO.getCampaignIds())
                .in(CollectionUtils.isNotEmpty(selectVO.getMasterIds()), "p.master_id", selectVO.getMasterIds())
                .like(StringUtils.isNotBlank(selectVO.getSearch()), "p.plan_name", selectVO.getSearch())
                .eq("p.is_plan_put", this.sheinConfiguration.isPut(selectVO.getIsPut(), user.getRoleId()))
                .orderByDesc("p.id"));
        return new PageUtils<>(iPage);
    }

    @Override
    public List<SelectDTO> selectPlanHasCost(PlanHasCostSelectGetVO getVO, User user) {
        ReportHasCostGetVO idsGetVO = new ReportHasCostGetVO();
        idsGetVO.setMasterId(getVO.getMasterId());
        idsGetVO.setType("plan");
        List<Long> ids = this.fgReportService.getHasCostIds(idsGetVO).getData();
        if (ids.isEmpty()) {
            return List.of();
        }
        return this.baseMapper.selectPlanAndCycle(new QueryWrapper<Plan>()
                .eq("mp.is_del", IsDelEnum.NORMAL.getId())
                .eq("mpc.is_del", IsDelEnum.NORMAL.getId())
                // 筛选在投计划
                .and(q -> q.or().in("mp.plan_status", List.of(1, 2, 3))
                        // 筛选投放结束但在3天以内的计划
                        .or(q1 -> q1.eq("mp.plan_status", PlanStatusEnum.FINISH.getId())
                                .gt("mpc.end_date", DateUtils.format(DateUtils.format(DateUtils.getTodayDate(), -3)))))
                .in("mp.id", ids)
                .in(!getVO.getCampaignIds().isEmpty(), "mp.campaign_id", getVO.getCampaignIds())
                .eq("mp.is_plan_put", this.sheinConfiguration.isPut(getVO.getIsPut(), user.getRoleId()))
                .orderByDesc("mp.id"));
    }

    @Override
    public Plan getPlan(Long id, Integer masterId) {
        Plan plan = this.getOne(new LambdaQueryWrapper<Plan>().eq(Plan::getId, id).eq(Plan::getMasterId, masterId));
        if (null == plan) {
            throw new CustomException("计划ID不存在，请检查后重试");
        }
        return plan;
    }

    @Override
    public boolean checkRepeatName(String name, Long campaignId, Long planId) {
        return !this.lambdaQuery()
                .eq(Plan::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(Plan::getPlanName, name)
                .eq(Plan::getCampaignId, campaignId)
                .ne(ObjectUtils.isNotNullOrZero(planId), Plan::getId, planId)
                .select(Plan::getId).list().isEmpty();
    }

    @Override
    public IPage<MasterPageFirstDTO> pageMarket(MasterPageFirstVO listVO, List<Integer> permissionMasterIds, List<Long> ids, WebPlanStatusContainerDTO webPlanStatusContainerDTO) {
        IPage<MasterPageFirstDTO> iPage = new Page<>(listVO.getPage(), listVO.getPageNum());
        return CollectionUtils.isNotEmpty(listVO.getRtaStrategyIds())
                ? this.baseMapper.pageMarketJoinDirect(iPage, getQueryWrapper(listVO, permissionMasterIds, ids))
                : this.baseMapper.pageMarket(iPage, getQueryWrapper(listVO, permissionMasterIds, ids));
    }

    @Override
    public List<Long> listAllIdMarket(MasterPageFirstVO listVO, List<Integer> permissionMasterIds, WebPlanStatusContainerDTO webPlanStatusContainerDTO) {
        return CollectionUtils.isNotEmpty(listVO.getRtaStrategyIds())
                ? this.baseMapper.listPlanIdJoinDirect(this.getQueryWrapper(listVO, permissionMasterIds, null))
                : this.baseMapper.listPlanId(this.getQueryWrapper(listVO, permissionMasterIds, null));
    }

    @Override
    public List<Long> listAllMasterIdMarket(MasterPageFirstVO listVO, List<Integer> permissionMasterIds, WebPlanStatusContainerDTO webPlanStatusContainerDTO) {
        return this.baseMapper.listIdsByPlan(this.getQueryWrapper(listVO, permissionMasterIds, null)
                .select("distinct p.master_id"));
    }

    @Override
    public Plan getPlan(Long id, Long campaignId, Integer masterId) {
        Plan plan = this.getOne(new LambdaQueryWrapper<Plan>().eq(Plan::getId, id)
                .eq(ObjectUtils.isNotNullOrZero(campaignId), Plan::getCampaignId, campaignId)
                .eq(Plan::getMasterId, masterId));
        if (null == plan) {
            throw new CustomException("计划ID不存在，请检查后重试");
        }
        return plan;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PlanSaveDTO savePlan(PlanSaveVO planSaveVO, User user) {
        // 第一步：基础信息校验
        if (this.checkRepeatName(planSaveVO.getPlanName(), planSaveVO.getCampaignId(), planSaveVO.getId())) {
            throw new CustomException("计划名称已存在，请更改后再试");
        }
        Integer loginUserId = user.getId();
        campaignService.getCampaign(planSaveVO.getCampaignId(), planSaveVO.getMasterId());
        // 设置计划的状态
        Master master = this.masterMapper.selectOne(new QueryWrapper<Master>().lambda()
                .eq(Master::getUserId, planSaveVO.getMasterId()));
        planSaveVO.setPlanStatus(PlanStatusEnum.resettingStatus(master.getTimeZone(), planSaveVO.getPlanStatus(), planSaveVO.getPutCycle(), planSaveVO.getStartDate(),
                planSaveVO.getEndDate()));

        // 第二步：保存数据
        // 保存计划主表数据
        Plan planInDb = ObjectUtils.isNullOrZero(planSaveVO.getId())
                ? new Plan() : getPlan(planSaveVO.getId(), planSaveVO.getCampaignId(), planSaveVO.getMasterId());
        // 如果是编辑计划
        if (ObjectUtils.isNotNullOrZero(planSaveVO.getId())) {
            // 如果计划出价类型是oCPM，且开启了深度优化出价；关闭了深度优化，或者更改出价类型，提示异常
            if (planInDb.getBidType().equals(BidTypeEnum.OCPM.getId()) && planInDb.getDeepBidStatus().equals(PlanDeepBidStatusEnum.OPENED.getId()) &&
                    (!planInDb.getBidType().equals(planSaveVO.getBidType()) || !planInDb.getDeepBidStatus().equals(planSaveVO.getDeepBidStatus()))) {
                throw new CustomException("当前计划出价类型为oCPM类型且开启了深度优化出价，不允许更改出价类型或关闭深度优化出价");
            }
            planSaveVO.setIsAbPlan(planInDb.getIsAbPlan());
            planSaveVO.setPlanMode(planInDb.getPlanMode());
        }
        BeanUtils.copyProperties(planSaveVO, planInDb);
        planInDb.setBudgetDay(BudgetUtils.inDb(planInDb.getBudgetDay(), planInDb.getBudgetType()));
        planInDb.setBudgetHour(BudgetUtils.inDb(planInDb.getBudgetHour(), planInDb.getBudgetType()));
        planInDb.setIndustryId(CollectionUtils.isEmpty(planSaveVO.getIndustryId())
                ? "" : JSONObject.toJSONString(planSaveVO.getIndustryId()));
        // 设置banner模版
//        this.setTemplateId(planSaveVO);
        // 检查竞价分布算法类型是否正确，非oCPM的计划，竞价分布算法出价类型设置为0
        planInDb.setBidAlgoType(BidTypeEnum.OCPM.getId().equals(planInDb.getBidType()) ? planInDb.getBidAlgoType() : 0);
        // 新增计划
        if (ObjectUtils.isNullOrZero(planSaveVO.getId())) {
            // 新增计划，出价类型为CPC和oCPM时默认开启CPC动态调价
            if (BidTypeEnum.CPC.getId().equals(planInDb.getBidType())
                    || BidTypeEnum.OCPM.getId().equals(planInDb.getBidType())) {
                planInDb.setIsDynamicCpcPrice(1);

            }
            //如果是iep计划,且iep动态cpc不为空，填入字段
            if (PlanModeEnum.EXPERIMENT.getId().equals(planSaveVO.getPlanMode())) {
                if (null != planSaveVO.getIepIsDynamicCpcPrice()) {
                    planInDb.setIsDynamicCpcPrice(planSaveVO.getIepIsDynamicCpcPrice());
                }
            } else {
                planInDb.setIsDynamicCpcPrice(planSaveVO.getIsDynamicCpcPrice());
            }
            // 新增计划，出价为CPC和CPM时默认流量探测关闭
            if (BidTypeEnum.CPC.getId().equals(planInDb.getBidType()) || BidTypeEnum.CPM.getId().equals(planInDb.getBidType())) {
                planInDb.setFlowDetectionState(PlanFlowDetectionStateEnum.CLOSED.getId());
            }
            // 新增计划时，根据流量探测开关设置默认学习状态：开启默认为1：待学习；关闭默认为0
            planInDb.setLearningState(planSaveVO.getFlowDetectionState().equals(PlanFlowDetectionStateEnum.OPENED.getId()) ?
                    PlanLearningStateEnum.TO_BE_LEARN.getId() : 0);
            // 如果学习期状态是关闭状态，学习期时长设置为0
            if (planInDb.getLearningState().equals(0)) {
                planInDb.setLearningLength(0);
            }
            //shein设置 是否投放计划
            if (sheinConfiguration.isRole(user.getRoleId())) {
                planInDb.setIsPlanPut(PutEnum.NOT_PUT.getId());
                planInDb.setBudgetDaily(planInDb.getBudgetDay());
            } else {
                //优化影子计划状态如果为待审核，则直接调整为暂停
                if (PlanStatusEnum.WAIT_AUDIT.getId().equals(planInDb.getPlanStatus())) {
                    planInDb.setPlanStatus(PlanStatusEnum.STOP.getId());
                }
                planInDb.setIsPlanPut(PutEnum.IS_PUT.getId());
            }
            planInDb.setCreateUid(loginUserId);
            planInDb.setTemplateId(0L);
            save(planInDb);
        } else {
            // 编辑计划
            planInDb.setUpdateUid(loginUserId);
            updateById(planInDb);
        }
        // 保存定向表数据
        planDirectService.savePlanDirects(planSaveVO, planInDb.getId(), loginUserId);
        // 保存排期数据
        PlanCycleVO planCycleVO = new PlanCycleVO();
        BeanUtils.copyProperties(planSaveVO, planCycleVO);
        planCycleService.savePlanCycle(planCycleVO, planInDb.getId(), loginUserId);
        // 判断是否复制计划，如果存在源计划ID，获取计划下创意、素材信息关联到新计划下
        if (ObjectUtils.isNotNullOrZero(planSaveVO.getOriginPlanId()) && ObjectUtils.isNullOrZero(planSaveVO.getId())) {
            Plan originPlan = getPlan(planSaveVO.getOriginPlanId(), planInDb.getMasterId());
            this.copyCreativeByPlan(planInDb, originPlan, planSaveVO);
            //shein计划创建通知
            if (PutEnum.NOT_PUT.getId().equals(planInDb.getIsPlanPut())) {
                try {
                    Campaign campaign = this.campaignService.getById(planInDb.getCampaignId());
                    smsUtils.sendSheinMsg("Shein计划创建通知",
                            String.format("Shein活动 [%s] 下计划 [%s] 创建成功，请前往系统审核", campaign.getCampaignName(), planInDb.getPlanName())
                    );
                    callNoticeMapper.callSheinPlanUpdate(planInDb.getId());
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        //如果是shein 账户
        if (sheinConfiguration.isMaster(planInDb.getMasterId())) {
            if (planInDb.getIsPlanPut().equals(PutEnum.IS_PUT.getId())) {
                try {
                    insertShienTagPlan(planSaveVO, planInDb);
                } catch (Exception e) {
                    throw new CustomException("SHEIN影子计划创建Tag失败，请重试");
                }
            }
        }
        //复制计划的时候
        //禁用录入关系
//        if (ObjectUtils.isNotNullOrZero(planSaveVO.getCopyPlanId()) && ObjectUtils.isNullOrZero(planSaveVO.getId())) {
//            //复制新创意
//            Plan copyPlan = getPlan(planSaveVO.getCopyPlanId(), planInDb.getMasterId());
//            //开关设置shein的计划关系
//            if (PutEnum.IS_PUT.getId().equals(planInDb.getIsPlanPut())) {
//                Long sheinPlanId;
//                if (PutEnum.NOT_PUT.getId().equals(copyPlan.getIsPlanPut())) {
//                    sheinPlanId = planSaveVO.getCopyPlanId();
//                } else {
//                    sheinPlanId = this.getSheinPlanId(planSaveVO.getCopyPlanId());
//                }
//                if (ObjectUtils.isNotNullOrZero(sheinPlanId)) {
//                    planGenerateMapMapper.insertMap(PlanGenerateMapTypeEnum.PLAN.getId(), sheinPlanId, planInDb.getId(), loginUserId);
//                }
//            }
//        }
        //保存计划信息更改日志
        this.savePlanUpdateRecordBySaveVO(planSaveVO, planInDb, user);
        return PlanSaveDTO.builder().planId(planInDb.getId()).campaignId(planInDb.getCampaignId()).build();
    }

    /**
     * 插入shein plan 计划信息
     *
     * @param planSaveVO 计划信息
     * @param planInDb   返回数据
     */
    private void insertShienTagPlan(PlanSaveVO planSaveVO, Plan planInDb) {
        // 创建投放计划（影子计划）
        if (ObjectUtils.isNullOrZero(planSaveVO.getId())) {
            SheinTagGroup sheinTagGroup = new SheinTagGroup();
            RtaStrategyVO rtaStrategyVO = JSONObject.parseObject(planSaveVO.getRtaStrategy().getValue(), RtaStrategyVO.class);
            if (null == rtaStrategyVO) {
                throw new CustomException("影子计划无RTA");
            }
            sheinTagGroup.setRtaId(rtaStrategyVO.getRtaStrategyId());
            List<Long> countryIds = JSONObject.parseArray(planSaveVO.getAreaCountry().getValue(), Long.class);
            if (CollectionUtils.isEmpty(countryIds)) {
                throw new CustomException("影子计划无地域");
            }
            sheinTagGroup.setCountryId(countryIds.get(0));
            SheinTagGroup originGroup = this.sheinTagGroupMapper.selectOne(new LambdaQueryWrapper<SheinTagGroup>()
                    .eq(SheinTagGroup::getCountryId, sheinTagGroup.getCountryId())
                    .eq(SheinTagGroup::getRtaId, sheinTagGroup.getRtaId())
            );
            if (null == originGroup) {
                CountryAll countryAll = countryAllMapper.selectOne(new LambdaQueryWrapper<CountryAll>()
                        .eq(CountryAll::getCountryId, sheinTagGroup.getCountryId())
                        .orderByAsc(CountryAll::getId)
                        .last("limit 1")
                );
                RtaStrategy rtaStrategy = rtaStrategyMapper.selectOne(new LambdaQueryWrapper<RtaStrategy>()
                        .eq(RtaStrategy::getId, sheinTagGroup.getRtaId())
                );
                sheinTagGroup.setCountryAlias(countryAll.getCountryAlias());
                sheinTagGroup.setIsDel(IsDelEnum.NORMAL.getId().intValue());
                sheinTagGroup.setCreateUid(planInDb.getCreateUid());
                sheinTagGroup.setGroupName(String.format("%s_rmt_%s", sheinTagGroup.getCountryAlias().toLowerCase(), rtaStrategy.getRtaStrategyAlias()));
                sheinTagGroupMapper.insert(sheinTagGroup);
            } else {
                sheinTagGroup.setId(originGroup.getId());
            }
            SheinTagPlan sheinTagPlan = new SheinTagPlan();
            sheinTagPlan.setTagGroupId(sheinTagGroup.getId());
            sheinTagPlan.setPlanId(planInDb.getId());
            sheinTagPlan.setCreateUid(planInDb.getCreateUid());
            sheinTagPlanMapper.insert(sheinTagPlan);
        }
    }

    @Override
    public PlanGetDTO getPlanDetail(Long id, Integer masterId, User user) {
        PlanGetDTO planGetDTO = new PlanGetDTO();
        Plan planInDb = getPlan(id, masterId);
        BeanUtils.copyProperties(planInDb, planGetDTO);
        planGetDTO.setPlanIsDel(planInDb.getIsDel());
        planGetDTO.setBudgetDay(BudgetUtils.format(planGetDTO.getBudgetDay(), planGetDTO.getBudgetType()));
        planGetDTO.setBudgetHour(BudgetUtils.format(planGetDTO.getBudgetHour(), planGetDTO.getBudgetType()));
        planGetDTO.setIndustryId(StringUtils.isNotBlank(planInDb.getIndustryId()) ? JSONObject.parseArray(planInDb.getIndustryId(), Long.class) : List.of());
        if (CollectionUtils.isNotEmpty(planGetDTO.getIndustryId())) {
            FeignR<String> feignR = this.fgSystemService.getIndustryName(new IndustryListGetVO(planGetDTO.getIndustryId()));
            if (!feignR.getCode().equals(0)) {
                throw new CustomException(feignR.getCode(), feignR.getMsg());
            }
            planGetDTO.setIndustryName(feignR.getData());
        }
        if (PutEnum.NOT_PUT.getId().equals(planInDb.getIsPlanPut())) {
            if (!sheinConfiguration.isRole(user.getRoleId())) {
                List<PlanGenerateMap> generateMaps = planGenerateMapMapper.selectList(new LambdaQueryWrapper<PlanGenerateMap>()
                        .eq(PlanGenerateMap::getMapType, PlanGenerateMapTypeEnum.CAMPAIGN.getId())
                        .eq(PlanGenerateMap::getSheinId, planInDb.getCampaignId())
                        .eq(PlanGenerateMap::getIsDel, IsDelEnum.NORMAL.getId())
                );
                if (!generateMaps.isEmpty()) {
                    planGetDTO.setCampaignId(generateMaps.get(0).getMarketId());
                }
            }
        }
        // 获取定向信息
        PlanDirectSaveVO directSaveVO = planDirectService.getPlanDirects(id, planInDb);
        BeanUtils.copyProperties(directSaveVO, planGetDTO);
        // 获取排期定向
        PlanCycleVO planCycleVO = planCycleService.getPlanCycle(id, planInDb.getPutCycle());
        BeanUtils.copyProperties(planCycleVO, planGetDTO);
        if (ObjectUtils.isNotNullOrZero(planInDb.getMonitorId())) {
            MonitorGetVO getVO = new MonitorGetVO();
            getVO.setId(planInDb.getMonitorId());
            MonitorGetDTO monitor = monitorService.getMonitor(getVO);
            planGetDTO.setMonitorName(null == monitor ? ConstantUtils.UNKNOWN : monitor.getMonitorName());
        }
        if (ObjectUtils.isNotNullOrZero(planInDb.getBehaviorAppId())) {
            BehaviorAppGetVO getVO = new BehaviorAppGetVO();
            getVO.setId(planInDb.getBehaviorAppId());
            planGetDTO.setBehaviorAppName(behaviorAppService.getBehaviorApp(getVO).getAppName());
        }
        Campaign campaignInDb = campaignService.getCampaign(planInDb.getCampaignId(), masterId);
        SelectDTO optimizeTargetDTO = planDirectService.getOptimizeTarget(planInDb.getOptimizeTargetId(), campaignInDb.getMarketTarget(), planInDb);
        planGetDTO.setOptimizeTargetName(null == optimizeTargetDTO ? ConstantUtils.UNKNOWN : optimizeTargetDTO.getName());
        return planGetDTO;
    }

    @Override
    public Map<Long, ReportListDTO> getReportMapMarket(List<ReportListDTO> reportList) {
        return reportList.stream().collect(Collectors.toMap(ReportListDTO::getPlanId, Function.identity()));
    }

    @Override
    public List<Long> listReportIdMarket(List<ReportListDTO> reportList) {
        return reportList.stream().map(ReportListDTO::getPlanId).collect(Collectors.toList());
    }

    @Override
    public Long getValueById(ReportListDTO reportDTO) {
        return reportDTO.getPlanId();
    }

    @Override
    public boolean batchDelete(RecordBatchDeleteVO batchDeleteVO) {
        if (!batchDeleteVO.getIds().isEmpty()) {
            UpdateWrapper<Plan> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda()
                    .in(Plan::getId, batchDeleteVO.getIds())
                    .eq(Plan::getIsDel, IsDelEnum.NORMAL.getId())
                    .eq(Plan::getMasterId, batchDeleteVO.getMasterId());
            Plan plan = new Plan();
            plan.setIsDel(IsDelEnum.DELETE.getId().intValue());
            plan.setUpdateUid(batchDeleteVO.getUserId());
            try {
                // 删除计划后删除创意和创意单元
                int deletePlan = this.baseMapper.update(plan, updateWrapper);
                if (deletePlan >= 0) {
                    Creative creative = new Creative();
                    creative.setUpdateUid(batchDeleteVO.getUserId());
                    creative.setIsDel(IsDelEnum.DELETE.getId().intValue());
                    creativeMapper.update(creative, new QueryWrapper<Creative>().lambda()
                            .in(Creative::getPlanId, batchDeleteVO.getIds()));

                    CreativeUnit creativeUnit = new CreativeUnit();
                    creativeUnit.setUpdateUid(batchDeleteVO.getUserId());
                    creativeUnit.setIsDel(IsDelEnum.DELETE.getId().intValue());
                    creativeUnitMapper.update(creativeUnit, new LambdaQueryWrapper<CreativeUnit>()
                            .in(CreativeUnit::getPlanId, batchDeleteVO.getIds()));
                }
                return true;
            } catch (Exception e) {
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean batchSwitch(RecordBatchSwitchVO batchSwitchVO) {
        List<Plan> plans = this.baseMapper.selectList(new QueryWrapper<Plan>().lambda()
                .in(Plan::getId, batchSwitchVO.getIds())
                .eq(ObjectUtils.isNotNullOrZero(batchSwitchVO.getMasterId()), Plan::getMasterId, batchSwitchVO.getMasterId())
                .in(CollectionUtils.isNotEmpty(batchSwitchVO.getMasterIds()), Plan::getMasterId, batchSwitchVO.getMasterIds())
                .eq(Plan::getIsDel, IsDelEnum.NORMAL.getId())
                .notIn(Plan::getPlanStatus, List.of(PlanStatusEnum.FINISH.getId(),
                        PlanStatusEnum.WAIT_AUDIT.getId(), PlanStatusEnum.AUDIT_FAIL.getId())
                ));
        Map<Integer, Master> masterMap = this.masterMapper.selectList(new QueryWrapper<Master>().lambda()
                .eq(ObjectUtils.isNotNullOrZero(batchSwitchVO.getMasterId()), Master::getUserId, batchSwitchVO.getMasterId())
                .in(CollectionUtils.isNotEmpty(batchSwitchVO.getMasterIds()), Master::getUserId, batchSwitchVO.getMasterIds())
        ).stream().collect(Collectors.toMap(Master::getUserId, Function.identity()));
        Map<Long, PlanCycle> planCycleMap = planCycleMapper.selectList(new LambdaQueryWrapper<PlanCycle>()
                .in(PlanCycle::getPlanId, batchSwitchVO.getIds())
                .in(PlanCycle::getIsDel, IsDelEnum.NORMAL.getId())
        ).stream().collect(Collectors.toMap(PlanCycle::getPlanId, Function.identity()));
        Map<Long, Integer> sheinPlanStatusMap;
        if (batchSwitchVO.getPutStatus().equals(PlanStatusEnum.MARKETING.getId())) {
            sheinPlanStatusMap = planGenerateMapMapper.selectPlanStatusByMap(new QueryWrapper<>()
                    .eq("mpgm.map_type", PlanGenerateMapTypeEnum.PLAN.getId())
                    .in("mpgm.market_id", batchSwitchVO.getIds())
            ).stream().collect(Collectors.toMap(PlanGenerateMapStatusDTO::getMarketId, PlanGenerateMapStatusDTO::getSheinStatus));
        } else {
            sheinPlanStatusMap = new HashMap<>();
        }
        //单个计划修改
        List<PlanUpdateRecord> planUpdateRecords = plans.stream().map(plan -> {
            Plan update = new Plan();
            //周期
            PlanCycle planCycle = planCycleMap.get(plan.getId());
            update.setPlanStatus(
                    PlanStatusEnum.resettingStatus(
                            masterMap.get(plan.getMasterId()).getTimeZone(),
                            batchSwitchVO.getPutStatus(), plan.getPutCycle(),
                            null == planCycle ? null : planCycle.getStartDate(),
                            null == planCycle ? null : planCycle.getEndDate()
                    )
            );
            if (update.getPlanStatus().equals(plan.getPlanStatus())) {
                return null;
            }
            //如果含有shein计划状态，判定是否为暂停or投放结束
            if (sheinPlanStatusMap.containsKey(plan.getId())) {
                if (List.of(PlanStatusEnum.STOP.getId(), PlanStatusEnum.FINISH.getId()).contains(sheinPlanStatusMap.get(plan.getId()))) {
                    return null;
                }
            }
            update.setUpdateUid(batchSwitchVO.getUserId());
            this.baseMapper.update(update, new LambdaUpdateWrapper<Plan>()
                    .eq(Plan::getId, plan.getId())
                    .eq(ObjectUtils.isNotNullOrZero(batchSwitchVO.getMasterId()), Plan::getMasterId, batchSwitchVO.getMasterId())
                    .in(CollectionUtils.isNotEmpty(batchSwitchVO.getMasterIds()), Plan::getMasterId, batchSwitchVO.getMasterIds())
                    .eq(Plan::getIsDel, IsDelEnum.NORMAL.getId())
                    .notIn(Plan::getPlanStatus, List.of(PlanStatusEnum.WAIT_AUDIT.getId(), PlanStatusEnum.AUDIT_FAIL.getId()))
            );
            plan.setPlanStatus(update.getPlanStatus());
            return plan;
        }).filter(Objects::nonNull).map(update -> {
            PlanUpdateRecord planUpdateRecord = new PlanUpdateRecord();
            planUpdateRecord.setPlanId(update.getId());
            //修改日志map
            Map<String, Object> map = new HashMap<>() {{
                put("plan_status", update.getPlanStatus());
            }};
            planUpdateRecord.setContent(JSONObject.toJSONString(map));
            return planUpdateRecord;
        }).collect(Collectors.toList());
        //修改日志
        this.planUpdateRecordService.savePlanUpdateRecord(planUpdateRecords, batchSwitchVO.getUserId());
        return true;
    }

    @Override
    public void formatListMarket(List<MasterPageFirstDTO> list,
                                 WebPlanStatusContainerDTO webPlanStatusContainerDTO, Long masterId) {
        if (list.isEmpty()) {
            return;
        }
        // 深度转化目标
        List<Long> actions = list.stream().map(MasterPageFirstDTO::getDeepBidAction)
                .distinct().collect(Collectors.toList());
        Map<Long, String> actionMap = actions.isEmpty() ? new HashMap<>() :
                this.deepBidConfigMapper.selectList(new QueryWrapper<DeepBidConfig>().lambda()
                                .eq(DeepBidConfig::getMasterId, masterId)
                                .in(DeepBidConfig::getTrackerActionId, actions)).stream()
                        .collect(Collectors.toMap(DeepBidConfig::getTrackerActionId, DeepBidConfig::getDeepBidName));
        // 创意单元数量
        Map<Long, List<CreativeUnit>> creativeUnitMap = this.creativeUnitMapper
                .selectList(new QueryWrapper<CreativeUnit>().lambda()
                        .eq(CreativeUnit::getIsDel, IsDelEnum.NORMAL.getId())
                        .in(CreativeUnit::getPlanId, list.stream().map(MasterPageFirstDTO::getId)
                                .collect(Collectors.toList())))
                .stream().collect(Collectors.groupingBy(CreativeUnit::getPlanId));

        for (MasterPageFirstDTO record : list) {
            record.setBudgetDay(BudgetUtils.format(record.getBudgetDay(), record.getBudgetType()));
            record.setBudgetHour(BudgetUtils.format(record.getBudgetHour(), record.getBudgetType()));
            record.setNextBudgetDay(BudgetUtils.format(record.getNextBudgetDay(), record.getNextBudgetType()));
            record.setPlanStatusName(ICommonEnum.getNameById(record.getPlanStatus(), PlanStatusEnum.class));
            record.setDeepBidStatusName(ICommonEnum.getNameById(record.getDeepBidStatus(), PlanDeepBidStatusEnum.class));
            record.setDeepBidActionName(actionMap.getOrDefault(record.getDeepBidAction(), ConstantUtils.PLACEHOLDER_2));
            List<CreativeUnit> creativeUnits = creativeUnitMap.getOrDefault(record.getId(), List.of());
            record.setCreativeUnitNum(creativeUnits.stream().filter(creativeUnit ->
                            creativeUnit.getCreativeUnitStatus().equals(CreativeUnitStatusEnum.MARKETING.getId()))
                    .count() + "/" + creativeUnits.size());
            record.setFlowDetectionStateName(
                    ICommonEnum.getNameById(record.getFlowDetectionState(), PlanFlowDetectionStateEnum.class));
            record.setLearningStateName(
                    record.getFlowDetectionState().equals(PlanFlowDetectionStateEnum.OPENED.getId())
                            ? ICommonEnum.getNameById(record.getLearningState(), PlanLearningStateEnum.class)
                            : ConstantUtils.PLACEHOLDER_2);
            record.setDealTypeName(ICommonEnum.getNameById(record.getDealType(), DealTypeEnum.class));

        }
    }

    /**
     * 设置计划模版
     *
     * @param planSaveVO 保存对象
     */
    private void setTemplateId(PlanSaveVO planSaveVO) {
//        PlanSlotTypeEnum slotTypeEnum = ICommonEnum.get(planSaveVO.getSlotType(), PlanSlotTypeEnum.class);
//        //校验EP或者ADX是否可使用模板
//        if (ObjectUtils.isNotNullOrZero(planSaveVO.getTemplateId())) {
//            epService.checkEpTemplate(JSONObject.parseArray(planSaveVO.getEpId(), Long.class), List.of(planSaveVO.getTemplateId()));
//            adxService.checkAdxTemplate(JSONObject.parseArray(planSaveVO.getAdxId(), Long.class), List.of(planSaveVO.getTemplateId()));
//        }
////        if (planSaveVO.getAdxId().contains("109")) {
////            // 包含109opera定向，templateID只能为0或者指定的
////            List<Long> operaTemplateIds = List.of(42L, 91L, 106L, 112L);
////            // 模版ID不为0时，不在列表中，不允许创建
////            if (ObjectUtils.isNotNullOrZero(planSaveVO.getTemplateId())
////                    && !operaTemplateIds.contains(planSaveVO.getTemplateId())) {
////                throw new CustomException("模版不符合Opera ADX使用要求");
////            }
////        }
////        if (planSaveVO.getAdxId().contains("157")) {
////            // 包含110XiaoMi定向，templateID只能为0或者指定的
////            List<Long> operaTemplateIds = List.of(110L);
////            // 模版ID不为0时，不在列表中，不允许创建
////            if (ObjectUtils.isNotNullOrZero(planSaveVO.getTemplateId())
////                    && !operaTemplateIds.contains(planSaveVO.getTemplateId())) {
////                throw new CustomException("模版不符合XiaoMi ADX使用要求");
////            }
////        }
//        if (PlanSlotTypeEnum.BANNER.equals(slotTypeEnum) || PlanSlotTypeEnum.VIDEO.equals(slotTypeEnum)) {
//            // 如果广告形式为banner，且模板为空，则抛出异常
//            if (PlanSlotTypeEnum.BANNER.equals(slotTypeEnum) && ObjectUtils.isNullOrZero(planSaveVO.getTemplateId())) {
//                throw new CustomException("Banner广告形式模板不能为空，请确认后再试");
//            }
//            if (ObjectUtils.isNotNullOrZero(planSaveVO.getTemplateId())) {
//                // 如果选择了模板，校验模版类型是否符合要求
//                TemplateConfig templateConfig = this.templateConfigMapper.selectById(planSaveVO.getTemplateId());
//                if (templateConfig == null) {
//                    throw new CustomException("模板不存在，请确认后再试");
//                }
//                // autolink要么传0，要么100，转为0：非autolink或1：autolink
//                Integer isAutoLink = planSaveVO.getAutoLinkRate().equals(TemplateIsAutoLinkEnum.NOT_AUTO_LINK.getId())
//                        ? TemplateIsAutoLinkEnum.NOT_AUTO_LINK.getId() : TemplateIsAutoLinkEnum.AUTO_LINK.getId();
//                // 模版不存在或者模版是否是auto link不符，给出异常提示
//                if (!templateConfig.getIsAutoLink().equals(isAutoLink)) {
//                    throw new CustomException("模版是否是autoLink类型不符");
//                }
//                if (!templateConfig.getIsDpa().equals(planSaveVO.getDpaState())) {
//                    throw new CustomException("模版DPA类型不符");
//                }
//
//                // 判断ep和模版autolink、摇一摇是否匹配
//                if (TemplateIsAutoLinkEnum.AUTO_LINK.getId().equals(templateConfig.getIsAutoLink())
//                        || TemplateIsShakeEnum.SHAKE.getId().equals(templateConfig.getIsShake())) {
//                    List<Long> epIds = JSONObject.parseArray(planSaveVO.getEpId(), Long.class);
//                    List<Ep> eps = this.epMapper.selectList(new QueryWrapper<Ep>().lambda().in(Ep::getId, epIds)
//                            .and(
//                                    i -> i.eq(TemplateIsAutoLinkEnum.AUTO_LINK.getId().equals(templateConfig.getIsAutoLink()),
//                                                    Ep::getIsAutoLink, TemplateIsAutoLinkEnum.NOT_AUTO_LINK.getId())
//                                            .or().eq(TemplateIsShakeEnum.SHAKE.getId().equals(templateConfig.getIsShake()),
//                                                    Ep::getIsShake, TemplateIsShakeEnum.NO_SHAKE.getId())
//                            )
//                    );
//                    if (!eps.isEmpty()) {
//                        throw new CustomException(
//                                eps.stream().map(Ep::getEpName).collect(Collectors.joining("，"))
//                                        + "不支持AutoLink或摇一摇"
//                        );
//                    }
//                }
//            }
//        } else {
//            planSaveVO.setTemplateId(0L);
//        }
    }

    /**
     * 公共方法获取查询条件（适用于推广页面）
     *
     * @param listVO              查询参数
     * @param permissionMasterIds 有权限的广告主ID
     * @param ids                 计划ID集合
     * @return 结果集
     */
    private QueryWrapper<Campaign> getQueryWrapper(MasterPageFirstVO listVO, List<Integer> permissionMasterIds, List<Long> ids) {
        QueryWrapper<Campaign> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("p.is_del", IsDelEnum.NORMAL.getId())
                //添加投放计划过滤
                .eq("p.plan_mode", PlanModeEnum.NORMAL.getId())
                //Shein投放过滤
                .eq("is_plan_put", listVO.getIsPut());
        // 如果选择了优化目标

        List<Long> optimizeTargets = new ArrayList<>() {{
            if (ObjectUtils.isNotNullOrZero(listVO.getOptimizeTargetId())) {
                addAll(monitorEventService.listMonitorEventIdsByAction(listVO.getMasterId(), List.of(listVO.getOptimizeTargetId())));
            }
        }};
        if (CollectionUtils.isEmpty(ids)) {
            queryWrapper
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getCampaignStatus()), "c.campaign_status", listVO.getCampaignStatus())
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getDealType()), "p.deal_type", listVO.getDealType())
                    .in(CollectionUtils.isNotEmpty(listVO.getSheinCampaignIds()), "p.campaign_id", listVO.getSheinCampaignIds())
                    .in(CollectionUtils.isNotEmpty(listVO.getSheinPlanIds()), "p.id", listVO.getSheinPlanIds())
                    .in("p.master_id", CollectionUtils.isNotEmpty(listVO.getMasterIds()) ? listVO.getMasterIds() : permissionMasterIds)
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getMasterId()), "p.master_id", listVO.getMasterId())
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getCampaignId()), "p.campaign_id", listVO.getCampaignId())
                    .in(CollectionUtils.isNotEmpty(listVO.getCampaignIds()), "p.campaign_id", listVO.getCampaignIds())
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getSlotType()), "p.slot_type", listVO.getSlotType())
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getOsType()), "p.os_type", listVO.getOsType())
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getPlanStatus()), "p.plan_status", listVO.getPlanStatus())
                    .in(CollectionUtils.isNotEmpty(optimizeTargets), "p.optimize_target_id", optimizeTargets)
//                    .in(CollectionUtils.isNotEmpty(listVO.getTemplateIds()), "p.template_id", listVO.getTemplateIds())
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getBidType()), "p.bid_type", listVO.getBidType())
                    .in(CollectionUtils.isNotEmpty(listVO.getRtaStrategyIds()), "JSON_UNQUOTE(JSON_EXTRACT(direct.direct_value, '$.rtaStrategyId'))", listVO.getRtaStrategyIds())
                    .eq(listVO.getIsAutoLinkState() != null && !listVO.getIsAutoLinkState().equals(-1),
                            "p.auto_link_rate", listVO.getIsAutoLinkState())
                    .eq(listVO.getDpaState() != null && !listVO.getDpaState().equals(-1),
                            "p.dpa_state", listVO.getDpaState())
                    .eq(listVO.getFlowDetectionState() != null && !listVO.getFlowDetectionState().equals(-1),
                            "p.flow_detection_state", listVO.getFlowDetectionState())
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getLearningState()), "p.learning_state", listVO.getLearningState())
                    .and(StringUtils.isNotBlank(listVO.getSearch()),
                            i -> i.like("p.id", listVO.getSearch())
                                    .or().like("p.plan_name", listVO.getSearch())
                    )
                    .and(CollectionUtils.isNotEmpty(listVO.getSearches()), q -> {
                        if (CollectionUtils.isNotEmpty(listVO.getSearches())) {
                            listVO.getSearches().forEach(u -> q.like("p.plan_name", u));
                        }
                    }).ge(StringUtils.isNotEmpty(listVO.getCreateStartDate()), "p.create_time", listVO.getCreateStartDate() + " 00:00:00")
                    .le(StringUtils.isNotEmpty(listVO.getCreateEndDate()), "p.create_time", listVO.getCreateEndDate() + " 23:59:59");
            if (ObjectUtils.isNotNullOrZero(listVO.getAuditStatus())) {
                queryWrapper.in("p.plan_status", PlanStatusEnum.MARKETING.getId().equals(listVO.getAuditStatus()) ?
                        List.of(PlanStatusEnum.WAIT.getId(), PlanStatusEnum.MARKETING.getId(), PlanStatusEnum.STOP.getId(), PlanStatusEnum.FINISH.getId()) :
                        List.of(listVO.getAuditStatus()));
            }
        } else {
            queryWrapper.in("p.id", ids);
        }
        queryWrapper.orderBy(true, SortTypeEnum.ASC.getSortType().equals(listVO.getSortType()), "p.id")
                .groupBy("p.id");
        return queryWrapper;
    }

    @Override
    public List<Long> getAllPlanIds(PlanIdsGetVO getVO) {
        return this.baseMapper.selectList(new QueryWrapper<Plan>().lambda()
                .in(!getVO.getMasterIds().isEmpty(), Plan::getMasterId, getVO.getMasterIds())
                .in(!getVO.getCampaigns().isEmpty(), Plan::getCampaignId, getVO.getCampaigns())
                .eq(Plan::getIsDel, IsDelEnum.NORMAL.getId())).stream().map(Plan::getId).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdatePlan(PlanBatchUpdateVO batchUpdateVO, User user) {
        // 获取当前登录账号下的所有账号
        List<Integer> masterIds = new ArrayList<>() {{
            if (ObjectUtils.isNullOrZero(batchUpdateVO.getMasterId())) {
                FeignR<List<Integer>> feignR = fgSystemService.listMasterId();
                if (!feignR.getCode().equals(0)) {
                    throw new CustomException("内部服务异常");
                }
                addAll(feignR.getData());
                if (isEmpty()) {
                    throw new CustomException("账户信息异常");
                }
            }
        }};
        Map<Integer, Master> masterMap = this.masterMapper.selectList(new QueryWrapper<Master>().lambda()
                        .eq(ObjectUtils.isNotNullOrZero(batchUpdateVO.getMasterId()), Master::getUserId, batchUpdateVO.getMasterId())
                        .in(CollectionUtils.isNotEmpty(masterIds), Master::getUserId, masterIds))
                .stream().collect(Collectors.toMap(Master::getUserId, Function.identity()));
        List<Plan> plans = this.baseMapper.selectList(new QueryWrapper<Plan>().lambda()
                .eq(Plan::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(batchUpdateVO.getMasterId()), Plan::getMasterId, batchUpdateVO.getMasterId())
                .in(CollectionUtils.isNotEmpty(masterIds), Plan::getMasterId, masterIds)
                .in(Plan::getId, batchUpdateVO.getPlanIds()));

        // 校验
        // 如果修改了出价类型，且修改的类型不为oCPM，则查询当前所选计划是否包含oCPM计划
        if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateBidType())
                && !batchUpdateVO.getBidType().equals(PlanBidTypeEnum.oCPM.getId())) {
            // 查询是否含有为oCPM且开启深度优化的计划
            List<Plan> filterPlans = plans.stream().filter(plan ->
                            plan.getBidType().equals(PlanBidTypeEnum.oCPM.getId())
                                    && plan.getDeepBidStatus().equals(PlanDeepBidStatusEnum.OPENED.getId()))
                    .collect(Collectors.toList());
            // 如果包含计划出价类型是oCPM，且开启了深度优化出价，修改出价类型则提示异常
            if (CollectionUtils.isNotEmpty(filterPlans)) {
                throw new CustomException(511, "出价类型为oCPM类型且开启了深度优化出价，出价类型不允许被修改，是否要排除这些计划继续修改",
                        filterPlans.stream().map(filterPlan -> new SelectDTO(filterPlan.getId(), filterPlan.getPlanName())).collect(Collectors.toList())
                );
            }
        }
        // 如果修改了深度出价，出价类型为oCPM且开启了深度出价的计划信息；如果存在，则不支持修改这一批计划的深度出价
        if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateDeepBid())) {
            List<Plan> filterPlans = plans.stream().filter(plan -> plan.getBidType().equals(PlanBidTypeEnum.oCPM.getId())
                    && plan.getDeepBidStatus().equals(PlanDeepBidStatusEnum.OPENED.getId())).collect(Collectors.toList());
            // 如果计划出价类型是oCPM，且开启了深度优化出价；关闭了深度优化，或者更改出价类型，提示异常
            if (CollectionUtils.isNotEmpty(filterPlans)) {
                throw new CustomException("当前已选计划出价类型为oCPM类型且开启了深度优化出价，不允许更改出价类型或关闭深度优化出价");
            }
        }

        // 如果修改了出价模式，且为修改出价类型的
        if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateBidAlgoType()) &&
                BatchUpdateEnum.NOT_CHANGE.getId().equals(batchUpdateVO.getUpdateBidType())) {
            List<Plan> filterPlans = plans.stream().filter(plan -> !plan.getBidType().equals(PlanBidTypeEnum.oCPM.getId()))
                    .collect(Collectors.toList());
            // 如果计划出价类型是非oCPM，且期望开启出价模式，提示异常
            if (CollectionUtils.isNotEmpty(filterPlans)) {
                throw new CustomException(511, "出价类型为非oCPM类型，出价模式不允许被修改，是否要排除这些计划继续修改",
                        filterPlans.stream().map(filterPlan -> new SelectDTO(filterPlan.getId(), filterPlan.getPlanName())).collect(Collectors.toList())
                );
            }
        }

        plans.forEach(plan -> {
            plan.setUpdateUid(batchUpdateVO.getUserId());
            // 设置计划名称
            if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdatePlanName())) {
                plan.setPlanName(batchUpdateVO.getPlanName());
                if (this.checkRepeatName(plan.getPlanName(), plan.getCampaignId(), plan.getId())) {
                    throw new CustomException("计划名称已存在，请更改后再试");
                }
            }
            // 设置预算类型
            if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateBudgetType())) {
                plan.setBudgetType(batchUpdateVO.getBudgetType());
                plan.setBudgetDay(BudgetUtils.inDb(batchUpdateVO.getBudgetDay(), plan.getBudgetType()));
                if (ObjectUtils.isNotNullOrZero(batchUpdateVO.getBudgetHour())) {
                    plan.setBudgetHour(BudgetUtils.inDb(batchUpdateVO.getBudgetHour(), plan.getBudgetType()));
                }
            }
            // 设置小时预算
            if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateBudgetHour())) {
                plan.setBudgetHour(BudgetUtils.inDb(batchUpdateVO.getBudgetHour(), batchUpdateVO.getBudgetType()));
            }
            // 设置次日预算类型
            if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateNextBudgetType())) {
                plan.setNextBudgetType(batchUpdateVO.getNextBudgetType());
                plan.setNextBudgetDay(BudgetUtils.inDb(batchUpdateVO.getNextBudgetDay(), plan.getNextBudgetType()));
            }
            // 设置深度出价
            if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateDeepBid())) {
                plan.setDeepBidStatus(batchUpdateVO.getDeepBidStatus());
                plan.setDeepBidAction(batchUpdateVO.getDeepBidAction());
                plan.setDeepBidPrice(batchUpdateVO.getDeepBidPrice());
            }
            //设置计划曝光频次
            if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateFrequencyView())) {
                plan.setFrequencyCycleView(batchUpdateVO.getFrequencyCycleView());
                plan.setFrequencyNumView(batchUpdateVO.getFrequencyNumView());
            }
            //设置计划点击频次
            if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateFrequencyClick())) {
                plan.setFrequencyCycleClick(batchUpdateVO.getFrequencyCycleClick());
                plan.setFrequencyNumClick(batchUpdateVO.getFrequencyNumClick());
            }
            //设置投放速率
            if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateConsumeRate())) {
                plan.setConsumeRate(batchUpdateVO.getConsumeRate());
            }
            // 设置投放周期
            if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdatePutCycle())) {
                plan.setPutCycle(batchUpdateVO.getPutCycle());
                // 设置计划状态
                plan.setPlanStatus(PlanStatusEnum.resettingStatus(masterMap.get(plan.getMasterId()).getTimeZone(), plan.getPlanStatus(),
                        batchUpdateVO.getPutCycle(), batchUpdateVO.getStartDate(), batchUpdateVO.getEndDate()));
            }
            // 如果设置了出价模式，则修改为所设置的
            if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateBidAlgoType())) {
                plan.setBidAlgoType(batchUpdateVO.getBidAlgoType());
            } else {
                if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateBidType())) {
                    // 如果没有要修改为oCPM，则填充默认值
                    if (batchUpdateVO.getBidType().equals(BidTypeEnum.OCPM.getId())) {
                        // 如果没有设置出价模式，且原出价类型为CPM、CPC，则设置默认值
                        if (!plan.getBidType().equals(PlanBidTypeEnum.oCPM.getId())) {
                            plan.setBidAlgoType(PlanBidAlgoTypeEnum.INCOME_PRECEDENCE.getId());
                        }
                    } else {
                        // 如果修改为cpc/cpm，则填充0
                        plan.setBidAlgoType(0);
                    }
                }
            }
            // 如果设置了流量探测
            if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateFlowDetection())) {
                plan.setFlowDetectionState(batchUpdateVO.getFlowDetectionState());
                plan.setBudgetLearningRate(batchUpdateVO.getBudgetLearningRate());
                plan.setLearningLength(batchUpdateVO.getLearningLength());
            } else {
                if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateBidType())) {
                    // 如果修改为cpm/cpc，则设置为关闭
                    if (!batchUpdateVO.getBidType().equals(BidTypeEnum.OCPM.getId())) {
                        plan.setFlowDetectionState(PlanFlowDetectionStateEnum.CLOSED.getId());
                        plan.setBudgetLearningRate(0L);
                        plan.setLearningLength(0);
                    }
                }
            }
            // 设置出价类型
            if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateBidType())) {
                plan.setBidType(batchUpdateVO.getBidType());
                plan.setBidPrice(batchUpdateVO.getBidPrice());
            }
        });

        Map<Long, Plan> planMap = plans.stream().collect(Collectors.toMap(Plan::getId, Function.identity()));

        PlanCycle planCycle = this.getPlanCycle(batchUpdateVO);
        // 更改投放周期、预算类型、次日预算类型、出价类型时编辑计划信息
        try {
            if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdatePlanName())
                    || BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdatePutCycle())
                    || BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateBudgetType())
                    || BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateBudgetHour())
                    || BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateNextBudgetType())
                    || BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateBidType())
                    || BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateDeepBid())
                    || BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateBidAlgoType())
                    || BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateFlowDetection())
                    || BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateFrequencyView())
                    || BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateFrequencyClick())
                    || BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateConsumeRate())
            ) {
                this.baseMapper.batchUpdatePlan(plans, batchUpdateVO.getUserId());
            }
            if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdatePutCycle())
                    || BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateHours())) {
                this.planCycleMapper.update(planCycle, new UpdateWrapper<PlanCycle>().lambda()
                        .in(PlanCycle::getPlanId, batchUpdateVO.getPlanIds()));
            }
            // 如果编辑了预算/出价/小时预算/深度出价/出价模式/流量探测，则记录信息
            if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateBudgetType())
                    || BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateBidType())
                    || BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateBudgetHour())
                    || BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateDeepBid())
                    || BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdatePutCycle())
                    || BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateHours())
                    || BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateNextBudgetType())
                    || BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdatePlanName())
                    || BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateFrequencyView())
                    || BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateFrequencyClick())
                    || BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateConsumeRate())
                    || BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateBidAlgoType())
            ) {
                Map<String, Object> map = new HashMap<>() {{
                    if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdatePlanName())) {
                        put("plan_name", batchUpdateVO.getPlanName());
                    }
                    if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateBudgetType())) {
                        put("budget_type", batchUpdateVO.getBudgetType());
                        put("budget_day", batchUpdateVO.getBudgetDay());
                        put("budget_hour", batchUpdateVO.getBudgetHour());
                    }
                    if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateBudgetHour())) {
                        put("budget_type", batchUpdateVO.getBudgetType());
                        put("budget_hour", batchUpdateVO.getBudgetHour());
                    }
                    if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateBidType())) {
                        put("bid_type", batchUpdateVO.getBidType());
                        put("bid_price", batchUpdateVO.getBidPrice());
                        if (!BidTypeEnum.OCPM.getId().equals(batchUpdateVO.getBidType())) {
                            put("bid_algo_type", 0);
                            put("flow_detection_state", PlanFlowDetectionStateEnum.CLOSED.getId());
                            put("budget_learning_rate", 0);
                        }
                    }
                    // 设置出价类型,
                    if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateBidAlgoType())) {
                        put("bid_algo_type", batchUpdateVO.getBidAlgoType());
                    }
                    // 设置流量探测
                    if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateFlowDetection())) {
                        put("flow_detection_state", batchUpdateVO.getFlowDetectionState());
                        put("budget_learning_rate", batchUpdateVO.getBudgetLearningRate());
                    }
                    if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateDeepBid())) {
                        put("deep_bid_status", batchUpdateVO.getDeepBidStatus());
                        put("deep_bid_type", batchUpdateVO.getDeepBidAction());
                        put("deep_bid_price", batchUpdateVO.getDeepBidPrice());
                    }
                    // 设置次日预算类型
                    if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateNextBudgetType())) {
                        put("next_budget_type", batchUpdateVO.getNextBudgetType());
                        if (batchUpdateVO.getNextBudgetType().equals(BudgetTypeEnum.VIEW.getId())) {
                            put("next_budget_day", batchUpdateVO.getNextBudgetDay());
                        } else {
                            put("next_budget_day", batchUpdateVO.getNextBudgetDay());
                        }
                    }
                    // 设置投放周期
                    if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdatePutCycle())) {
                        put("put_cycle", batchUpdateVO.getPutCycle());
                        if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdatePutCycle())) {
                            put("start_date", planCycle.getStartDate());
                            put("end_date", planCycle.getEndDate());
                        }
                    }
                    if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateHours())) {
                        put("hours", planCycle.getHours());
                    }
                    //设置计划曝光频次
                    if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateFrequencyView())) {
                        put("frequency_cycle_view", batchUpdateVO.getFrequencyCycleView());
                        put("frequency_num_view", batchUpdateVO.getFrequencyNumView());
                    }
                    //设置计划点击频次
                    if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateFrequencyClick())) {
                        put("frequency_cycle_click", batchUpdateVO.getFrequencyCycleClick());
                        put("frequency_num_click", batchUpdateVO.getFrequencyNumClick());
                    }
                    if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateConsumeRate())) {
                        put("consume_rate", batchUpdateVO.getConsumeRate());
                    }
                }};
                List<PlanUpdateRecord> planUpdateRecords = batchUpdateVO.getPlanIds().stream().map(planId -> {
                    PlanUpdateRecord planUpdateRecord = new PlanUpdateRecord();
                    planUpdateRecord.setPlanId(planId);
                    Map<String, Object> contentMap = new HashMap<>() {{
                        putAll(map);
                    }};
                    Plan planDTO = planMap.get(planId);
                    // 设置计划状态
                    contentMap.put("plan_status", planDTO.getPlanStatus());
                    planUpdateRecord.setContent(JSONObject.toJSONString(contentMap));
                    return planUpdateRecord;
                }).collect(Collectors.toList());
                this.savePlanUpdateRecordAndAfter(planUpdateRecords, user);
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    private PlanCycle getPlanCycle(PlanBatchUpdateVO batchUpdateVO) {
        PlanCycle planCycle = new PlanCycle();
        planCycle.setUpdateUid(batchUpdateVO.getUserId());
        // 设置投放周期
        if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdatePutCycle())) {
            // 指定时段时，设置起始时间
            if (PutCycleEnum.SECTION_TIME.getId().equals(batchUpdateVO.getPutCycle())) {
                planCycle.setStartDate(batchUpdateVO.getStartDate());
                if (PutCycleEnum.ALL_TIME.getId().equals(batchUpdateVO.getPutCycle())) {
                    planCycle.setEndDate(batchUpdateVO.getStartDate());
                }
                if (PutCycleEnum.SECTION_TIME.getId().equals(batchUpdateVO.getPutCycle())) {
                    planCycle.setEndDate(batchUpdateVO.getEndDate());
                }
            }
        }
        // 设置投放时段
        if (BatchUpdateEnum.IS_CHANGE.getId().equals(batchUpdateVO.getUpdateHours())) {
            planCycle.setHours(batchUpdateVO.getHours());
        }
        return planCycle;
    }

    @Override
    public void batchUpdatePlanLink(PlanLinkBatchUpdateVO batchUpdateVO, User user) {

        if (batchUpdateVO.getPlans().isEmpty()) {
            return;
        }
        List<PlanLinkDTO> planLinks = this.baseMapper.getDeeplinkPlan(new QueryWrapper<Plan>()
                .eq("mp.master_id", batchUpdateVO.getMasterId())
                .eq("mp.is_del", IsDelEnum.NORMAL.getId())
                .eq("mc.is_del", IsDelEnum.NORMAL.getId())
                .in("mp.id", batchUpdateVO.getPlans().stream().map(PlanLinkBatchDTO::getPlanId).collect(Collectors.toList())));
        Map<Long, PlanLinkDTO> planMap = planLinks.stream().collect(Collectors.toMap(PlanLinkDTO::getId, Function.identity()));
        List<Long> deeplinkPlanIds = planLinks.stream()
                .filter(plan -> plan.getPutOnTarget().equals(CampaignPutOnTargetEnum.DEEPLINK_PROMOTION.getId()))
                .map(PlanLinkDTO::getId).collect(Collectors.toList());
        List<Plan> plans = batchUpdateVO.getPlans().stream().map(record -> {
            Plan plan = new Plan();
            BeanUtils.copyProperties(planMap.get(record.getPlanId()), plan);
            if (record.getDeeplink() != null) {
                // 判断计划是否需要设置 deeplink
                plan.setDeeplink(deeplinkPlanIds.contains(record.getPlanId()) ? record.getDeeplink() : "");
            }
            if (record.getLandingUrl() != null) {
                plan.setLandingUrl(record.getLandingUrl());
            }
            if (record.getMonitorClickUrl1() != null) {
                plan.setMonitorClickUrl1(record.getMonitorClickUrl1());
            }
            if (record.getMonitorClickUrl2() != null) {
                plan.setMonitorClickUrl2(record.getMonitorClickUrl2());
            }
            if (record.getMonitorViewUrl1() != null) {
                plan.setMonitorViewUrl1(record.getMonitorViewUrl1());
            }
            if (record.getMonitorViewUrl2() != null) {
                plan.setMonitorViewUrl2(record.getMonitorViewUrl2());
            }
            return plan;
        }).collect(Collectors.toList());
        // 设置链接
        this.baseMapper.batchSavePlanLink(plans, user.getId());
        //判定同步链接
        this.savePlanUpdateLinkRecord(plans, user);
        // 通知中控
        plans.forEach(plan -> this.applicationContext.publishEvent(new CreativeUnitAuditEvent(this, plan.getId())));
    }

    @Override
    public PlanCountDTO getMasterPlanCount(GetVO getVO, User user) {

        PlanCountDTO planCountDTO = new PlanCountDTO();
        List<Plan> plans = this.baseMapper.selectList(new QueryWrapper<Plan>().lambda()
                .eq(Plan::getIsDel, IsDelEnum.NORMAL.getId())
                .in(Plan::getPlanMode, PlanModeEnum.NORMAL.getId())
                .eq(Plan::getIsPlanPut, sheinConfiguration.isPut(PutEnum.IS_PUT.getId(), user.getRoleId()))
                .eq(Plan::getMasterId, getVO.getId()));

        if (plans.isEmpty()) {
            return planCountDTO;
        }
        // 根据计划状态进行分类
        Map<Integer, List<Plan>> planMap = plans.stream().collect(Collectors.groupingBy(Plan::getPlanStatus));
        planCountDTO.setPutIn(planMap.getOrDefault(PlanStatusEnum.WAIT.getId(), List.of()).size());
        planCountDTO.setPutting(planMap.getOrDefault(PlanStatusEnum.MARKETING.getId(), List.of()).size());
        planCountDTO.setSuspendPut(planMap.getOrDefault(PlanStatusEnum.STOP.getId(), List.of()).size());
        planCountDTO.setPutEnd(planMap.getOrDefault(PlanStatusEnum.FINISH.getId(), List.of()).size());

        return planCountDTO;
    }

    @Override
    public List<Long> listAutoLinkPlan(AutoLinkPlanListVO listVO) {
        List<Plan> plans = this.baseMapper.selectList(new QueryWrapper<Plan>()
                .lambda().in(Plan::getMasterId, listVO.getMasterIds())
                .gt(Plan::getAutoLinkRate, 0)
        );
        return plans.stream().map(Plan::getId).collect(Collectors.toList());
    }

    @Override
    public List<TreeNodeDTO> getPlanTree(PlanTreeGetVO getVO) {

        List<TreeNodeDTO2> treeNodeDTO2s = this.baseMapper.getPlanTree(new QueryWrapper<Plan>()
                .eq("mc.is_del", IsDelEnum.NORMAL.getId())
                .eq("mp.is_del", IsDelEnum.NORMAL.getId())
                .eq("mp.master_id", getVO.getMasterId())
                .orderByAsc("mp.id"));

        if (treeNodeDTO2s.isEmpty()) {
            return List.of();
        }

        Map<Long, TreeNodeDTO> campaignMap = new HashMap<>();
        List<TreeNodeDTO> plans = new ArrayList<>();
        treeNodeDTO2s.forEach(treeNodeDTO2 -> {
            campaignMap.putIfAbsent(treeNodeDTO2.getPId(), new TreeNodeDTO(treeNodeDTO2.getPId(), treeNodeDTO2.getPName(), 0L));
            plans.add(new TreeNodeDTO(treeNodeDTO2.getId(), treeNodeDTO2.getName(), treeNodeDTO2.getPId()));
        });
        return new ArrayList<>() {{
            addAll(campaignMap.values());
            addAll(plans);
        }};
    }

    @Override
    public List<Long> listPlanByTemplate(PlanListByTemplateGetVO getVO) {
        List<Long> plans = this.baseMapper.listPlanByTemplate(new QueryWrapper<Plan>()
                .eq(getVO.getIsAuto().equals(1), "dtc.is_auto_link", getVO.getIsAuto())
                .eq(getVO.getIsShake().equals(1), "dtc.is_shake", getVO.getIsShake())
                .and(getVO.getIsAuto().equals(0),
                        q -> q.eq("dtc.is_auto_link", getVO.getIsAuto())
                                .or().isNull("dtc.is_auto_link"))
                .and(getVO.getIsShake().equals(0),
                        q -> q.eq("dtc.is_shake", getVO.getIsShake())
                                .or().isNull("dtc.is_shake"))
        );
        return plans.isEmpty() ? List.of(0L) : plans;
    }

    @Override
    public List<Plan> listPlanByTimeZone(Integer timeZone) {
        return this.baseMapper.listPlanByTimeZone(new QueryWrapper<Plan>()
                .eq("mm.time_zone", timeZone)
                .in("mp.plan_status", List.of(PlanStatusEnum.WAIT.getId(), PlanStatusEnum.MARKETING.getId(), PlanStatusEnum.STOP.getId())));
    }

    @Override
    public void updatePlanStatusHandler(Long reportDate) {
        log.info("Update plan status, starting ");
        Long startTime = System.currentTimeMillis();
        // 1.获取当前小时对应时区的凌晨
        Integer hour = Integer.parseInt(DateUtils.format(DateUtils.long2Date(reportDate), "HH"));
        // 2.获取当前小时对应的跨天时区
        TimeZoneEnum timeZoneEnum = TimeZoneEnum.getTimeZoneByCurrentHour(hour);
        if (timeZoneEnum == null) {
            return;
        }
        // 3.取当前时区下待投放、投放中和暂停的计划
        List<Plan> plans = this.listPlanByTimeZone(timeZoneEnum.getId());
        for (Plan plan : plans) {
            PlanStatusEnum statusEnum = ICommonEnum.get(plan.getPlanStatus(), PlanStatusEnum.class);
            PlanCycleVO cycle = getPlanCycle(plan);
            if (null == statusEnum || null == cycle) {
                log.error("plan status error or plan cycle error , status : {}", plan.getPlanStatus());
                plan.setPlanStatus(PlanStatusEnum.FINISH.getId());
                this.updateById(plan);
                this.applicationContext.publishEvent(new ControlPlanEvent(this, ControlContants.METHOD_STOP, plan.getId()));
                continue;
            }

            // 获取该时差下转化为东八区的整点时间，用于后边与投放周期比较
            Date now = DateUtils.string2Date(DateUtils.format(DateUtils.formatHour(DateUtils.long2Date(reportDate), timeZoneEnum.getFormatHour())));
            Date startDate = DateUtils.string2Date(cycle.getStartDate());
            Date endDate = DateUtils.string2Date(cycle.getEndDate());
            assert now != null;
            assert endDate != null;
            assert startDate != null;
            if (PlanStatusEnum.WAIT.equals(statusEnum)) {
                if (PutCycleEnum.ALL_TIME.getId().equals(plan.getPutCycle())) {
                    plan.setPlanStatus(PlanStatusEnum.MARKETING.getId());
                    this.updateById(plan);
                    this.applicationContext.publishEvent(new ControlPlanEvent(this, ControlContants.METHOD_UPDATE, plan.getId()));
                } else {
                    if (now.compareTo(endDate) > 0) {
                        plan.setPlanStatus(PlanStatusEnum.FINISH.getId());
                        this.updateById(plan);
                        this.applicationContext.publishEvent(new ControlPlanEvent(this, ControlContants.METHOD_STOP, plan.getId()));
                    } else if (now.compareTo(startDate) >= 0) {
                        plan.setPlanStatus(PlanStatusEnum.MARKETING.getId());
                        this.updateById(plan);
                        this.applicationContext.publishEvent(new ControlPlanEvent(this, ControlContants.METHOD_UPDATE, plan.getId()));
                    }
                }
            } else if (PlanStatusEnum.MARKETING.equals(statusEnum) || PlanStatusEnum.STOP.equals(statusEnum)) {
                if (PutCycleEnum.SECTION_TIME.getId().equals(plan.getPutCycle())) {
                    if (now.compareTo(endDate) > 0) {
                        plan.setPlanStatus(PlanStatusEnum.FINISH.getId());
                        this.updateById(plan);
                        this.applicationContext.publishEvent(new ControlPlanEvent(this, ControlContants.METHOD_STOP, plan.getId()));
                    }
                }
            }
        }
        Long endTime = System.currentTimeMillis();
        log.info("Update plan status, end, cost: {} ms", (endTime - startTime));
    }

    @Override
    public IPage<MarketBatchListDTO> listLinkBatch(MarketBatchListVO listVO) {
        return this.baseMapper.listLinkBatch(new Page<>(listVO.getPage(), listVO.getPageNum()), new QueryWrapper<Plan>()
                .eq("mp.is_del", IsDelEnum.NORMAL.getId())
                .eq("mc.is_del", IsDelEnum.NORMAL.getId())
                .eq("mc.campaign_mode", CampaignModeEnum.NORMAL.getId())
                .eq("mp.plan_mode", PlanModeEnum.NORMAL.getId())
                .eq("mp.is_plan_put", listVO.getIsPut())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getSlotType()), "mp.slot_type", listVO.getSlotType())
                .eq(ObjectUtils.isNotAll(listVO.getDpaState()), "mp.dpa_state", listVO.getDpaState())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getMasterId()), "mp.master_id", listVO.getMasterId())
                .in(CollectionUtils.isNotEmpty(listVO.getCampaignIds()), "mc.id", listVO.getCampaignIds())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getCampaignStatus()), "mc.campaign_status", listVO.getCampaignStatus())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getPutOnTarget()), "mc.put_on_target", listVO.getPutOnTarget())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getPlanStatus()), "mp.plan_status", listVO.getPlanStatus())
                .and(StringUtils.isNotBlank(listVO.getSearch()), q -> q.like("mp.id", listVO.getSearch())
                        .or().like("mp.plan_name", listVO.getSearch()))
                .orderByDesc("mp.id"));
    }

    @Override
    public PageUtils<PlanInspectionDTO> listInspectionPlans(PlanInspectionVO listVO) {
        PageUtils<PlanInspectionDTO> pageData = new PageUtils<>(this.baseMapper.listInspectionPlans(new Page<>(listVO.getPage(),
                        listVO.getPageNum()),
                new QueryWrapper<Plan>().eq("p.is_del", IsDelEnum.NORMAL.getId())
                        .eq("p.plan_mode", PlanModeEnum.NORMAL.getId())
                        .eq("c.is_del", IsDelEnum.NORMAL.getId())
                        .eq("c.campaign_mode", CampaignModeEnum.NORMAL.getId())
                        .eq(ObjectUtils.isNotNullOrZero(listVO.getMasterId()), "p.master_id", listVO.getMasterId())
                        .in(CollectionUtils.isNotEmpty(listVO.getCampaignIds()), "c.id", listVO.getCampaignIds())
                        .eq(ObjectUtils.isNotNullOrZero(listVO.getCampaignStatus()), "c.campaign_status", listVO.getCampaignStatus())
                        .eq(ObjectUtils.isNotNullOrZero(listVO.getPlanStatus()), "p.plan_status", listVO.getPlanStatus())
                        .eq(ObjectUtils.isNotAll(listVO.getDpaState()), "p.dpa_state", listVO.getDpaState())
                        .eq(ObjectUtils.isNotAll(listVO.getFlowDetectionState()), "p.flow_detection_state", listVO.getFlowDetectionState())
                        .eq(ObjectUtils.isNotAll(listVO.getIsAutoLinkState()), "p.auto_link_rate", listVO.getIsAutoLinkState())
                        .and(StringUtils.isNotBlank(listVO.getSearch()), q -> q.like("p.id", listVO.getSearch())
                                .or()
                                .like("p.plan_name", listVO.getSearch()))
                        .orderByDesc("p.id")));
        pageData.getData().forEach(planInspectionDTO -> {
            planInspectionDTO.setPlanStatusName(
                    ICommonEnum.getNameById(planInspectionDTO.getPlanStatus(),
                            PlanStatusEnum.class));
            planInspectionDTO.setDpaStateName(
                    ICommonEnum.getNameById(planInspectionDTO.getDpaState(),
                            DpaStateEnum.class));
            planInspectionDTO.setFlowDetectionStateName(
                    ICommonEnum.getNameById(planInspectionDTO.getFlowDetectionState(),
                            PlanFlowDetectionStateEnum.class));
            planInspectionDTO.setIsAutoLinkStateName(
                    (planInspectionDTO.getIsAutoLinkState() > 0 && planInspectionDTO.getIsAutoLinkState() < 100) ?
                            "未知" :
                            ICommonEnum.getNameById(planInspectionDTO.getIsAutoLinkState(),
                                    AutoLinkStateEnum.class));

            planInspectionDTO.setBudgetDay(planInspectionDTO.getBudgetType() == 2 &&
                    !(planInspectionDTO.getBudgetDay().compareTo(BigDecimal.ZERO) == 0) ?
                    planInspectionDTO.getBudgetDay().divide(new BigDecimal("1000"), 3, RoundingMode.FLOOR) :
                    planInspectionDTO.getBudgetDay());
            planInspectionDTO.setBudgetHour(planInspectionDTO.getBudgetType() == 2 &&
                    !(planInspectionDTO.getBudgetHour().compareTo(BigDecimal.ZERO) == 0) ?
                    planInspectionDTO.getBudgetHour().divide(new BigDecimal("1000"), 3, RoundingMode.FLOOR) :
                    planInspectionDTO.getBudgetHour());
            planInspectionDTO.setNextBudgetDay(planInspectionDTO.getNextBudgetType() == 2 &&
                    !(planInspectionDTO.getNextBudgetDay().compareTo(BigDecimal.ZERO) == 0) ?
                    planInspectionDTO.getNextBudgetDay().divide(new BigDecimal("1000"), 3, RoundingMode.FLOOR) :
                    planInspectionDTO.getNextBudgetDay());

            planInspectionDTO.setBudgetTypeName(
                    ICommonEnum.getNameById(planInspectionDTO.getBudgetType(),
                            PlanBudgetTypeEnum.class));
            planInspectionDTO.setBidTypeName(
                    ICommonEnum.getNameById(planInspectionDTO.getBidType(),
                            PlanBidTypeEnum.class));
            planInspectionDTO.setNextBudgetTypeName(planInspectionDTO.getNextBudgetDay().compareTo(BigDecimal.ZERO) == 0 ? "" :
                    ICommonEnum.getNameById(planInspectionDTO.getNextBudgetType(),
                            PlanBudgetTypeEnum.class));

            String budgetDay = planInspectionDTO.getBudgetDay().compareTo(BigDecimal.ZERO) == 0 ?
                    "不限" : planInspectionDTO.getBudgetDay().stripTrailingZeros().toPlainString();
            String budgetHour = planInspectionDTO.getBudgetHour().compareTo(BigDecimal.ZERO) == 0 ?
                    "不限" : planInspectionDTO.getBudgetHour().stripTrailingZeros().toPlainString();
            String nextBudgetDay = planInspectionDTO.getNextBudgetDay().compareTo(BigDecimal.ZERO) == 0 ?
                    "未设置" : planInspectionDTO.getNextBudgetDay().stripTrailingZeros().toPlainString();
            planInspectionDTO.setBudgetDayUnit(planInspectionDTO.getBudgetTypeName() + " - " + budgetDay);
            planInspectionDTO.setNextBudgetDayUnit(
                    planInspectionDTO.getNextBudgetDay().compareTo(BigDecimal.ZERO) == 0 ?
                            "未设置" : planInspectionDTO.getNextBudgetTypeName() + " - " + nextBudgetDay);
            planInspectionDTO.setBudgetHourUnit(planInspectionDTO.getBudgetTypeName() + " - " + budgetHour);
            planInspectionDTO.setBidPriceUnit(planInspectionDTO.getBidTypeName() + " - " + planInspectionDTO.getBidPrice().stripTrailingZeros().toPlainString());
        });
        return pageData;
    }

    @Override
    public void exportPlans(PlanInspectionVO planInspectionVO, HttpServletResponse response) throws IOException {
        PageUtils<PlanInspectionDTO> pageData = this.listInspectionPlans(planInspectionVO);
        ExcelUtils.download(response, "投放检查", PlanInspectionDTO.class, pageData.getData(),
                planInspectionVO.getHeaders());
    }

    @Override
    public void updateBidPriceByRate(PlanBidPriceUpdateVO updateVO, Integer userId) {

        List<Plan> plans = this.baseMapper.selectList(new QueryWrapper<Plan>().lambda()
                .in(Plan::getId, updateVO.getIds()));

        // 更新出价
        plans.forEach(plan -> plan.setBidPrice(plan.getBidPrice().multiply(BigDecimal.valueOf((100 + updateVO.getBidPriceRate()) / 100D)).setScale(3, RoundingMode.HALF_UP)));
        this.baseMapper.batchUpdatePlanBidPrice(plans, userId);

        // 记录编辑记录
        List<PlanUpdateRecord> planUpdateRecords = plans.stream().map(plan -> {
            PlanUpdateRecord planUpdateRecord = new PlanUpdateRecord();
            planUpdateRecord.setPlanId(plan.getId());
            Map<String, Object> map = new HashMap<>() {{
                put("bid_type", plan.getBidType());
                put("bid_price", plan.getBidPrice().doubleValue());
            }};
            planUpdateRecord.setContent(JSONObject.toJSONString(map));
            return planUpdateRecord;

        }).collect(Collectors.toList());
        this.savePlanUpdateRecordAndAfter(planUpdateRecords, fgSystemService.getUser().getData());
        // 通知中控
        plans.forEach(plan -> this.applicationContext.publishEvent(new ControlPlanEvent(this, ControlContants.METHOD_UPDATE, plan.getId())));
    }

    @Override
    public List<PlanByInfoGetDTO> getPlanByInfo(PlanByInfoGetVO getVO) {
        //如果adx不为空，且 ep为空，则获取adx下ep
        if (ObjectUtils.isNotNullOrZero(getVO.getAdxId())) {
            if (CollectionUtils.isEmpty(getVO.getEpIds())) {
                EpSelectGetVO epSelectGetVO = new EpSelectGetVO();
                epSelectGetVO.setAdxId(getVO.getAdxId().longValue());
                List<Integer> epIds = adxService.getEpSelect(epSelectGetVO).stream()
                        .map(u -> u.getId().intValue()).collect(Collectors.toList());
                if (epIds.isEmpty()) {
                    return List.of();
                }
                getVO.setEpIds(epIds);
            }
        }
        return this.baseMapper.getPlanByInfo(new QueryWrapper<>()
                .eq(ObjectUtils.isNotNullOrZero(getVO.getSlotType()), "p.slot_type", getVO.getSlotType())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getPlanId()), "p.id", getVO.getPlanId())
                .in(CollectionUtils.isNotEmpty(getVO.getPlanIds()), "p.id", getVO.getPlanIds())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getCampaignId()), "p.campaign_id", getVO.getCampaignId())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getMasterId()), "p.master_id", getVO.getMasterId())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getCreativeUnitId()), "mcu.id", getVO.getCreativeUnitId())
                .in(CollectionUtils.isNotEmpty(getVO.getPlanStatus()), "p.plan_status", getVO.getPlanStatus())
                .in(CollectionUtils.isEmpty(getVO.getPlanStatus()), "p.plan_status", List.of(PlanStatusEnum.MARKETING.getId(), PlanStatusEnum.STOP.getId()))
                .and(CollectionUtils.isNotEmpty(getVO.getEpIds()), q -> {
                    List<String> epIds = getVO.getEpIds().stream().map(Object::toString).collect(Collectors.toList());
                    epIds.add("[]");
                    for (int i = 0; i < getVO.getEpIds().size(); i++) {
                        if (i != 0) {
                            q.or();
                        }
                        q.like("direct.direct_value", getVO.getEpIds().get(i));
                    }
                }).groupBy(StringUtils.isNotBlank(getVO.getGroupBy()), getVO.getGroupBy())
        ).stream().filter(u -> {
            if (CollectionUtils.isEmpty(getVO.getEpIds())) {
                return true;
            }
            //通过一次内存过滤
            if ("[]".equals(u.getDirectValue())) {
                return true;
            }
            List<Integer> epIds = JSONObject.parseArray(u.getDirectValue(), Integer.class);
            for (Integer ep : getVO.getEpIds()) {
                if (epIds.contains(ep)) {
                    return true;
                }
            }
            return false;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sheinAudit(PlanSheinAuditVO auditVO, User user) {
        if (sheinConfiguration.isRole(user.getRoleId())) {
            throw new CustomException("无权限审核");
        }
        Plan plan = this.getPlan(auditVO.getId(), auditVO.getMasterId());
        if (null == plan || !plan.getPlanStatus().equals(PlanStatusEnum.WAIT_AUDIT.getId())) {
            throw new CustomException("计划无需审核");
        }
        // 获取源计划关联的未删除状态下的创意信息
        Creative originCreative = this.creativeMapper.selectOne(new QueryWrapper<Creative>().lambda()
                .eq(Creative::getPlanId, auditVO.getId()).eq(Creative::getIsDel, IsDelEnum.NORMAL.getId()));
        if (null == originCreative) {
            throw new CustomException("Shein计划中未创建创意，请耐心等待");
        }
        try {
            PlanCycleVO planCycle = planCycleService.getPlanCycle(plan.getId(), plan.getPutCycle());
            Master master = this.masterMapper.selectOne(new QueryWrapper<Master>().lambda()
                    .eq(Master::getUserId, auditVO.getMasterId()));
            Plan update = new Plan();
            update.setUpdateUid(user.getId());
            if (PlanStatusEnum.AUDIT_FAIL.getId().equals(auditVO.getAuditStatus())) {
                update.setRefuseReason(auditVO.getRefuseReason());
                update.setPlanStatus(PlanStatusEnum.AUDIT_FAIL.getId());
            } else {
                update.setPlanStatus(PlanStatusEnum.resettingStatus(master.getTimeZone(), auditVO.getAuditStatus(), plan.getPutCycle(), planCycle.getStartDate(), planCycle.getEndDate()));
            }
            this.baseMapper.update(update, new LambdaQueryWrapper<Plan>().eq(Plan::getId, plan.getId()));
            Map<String, Object> map = new HashMap<>() {{
                put("plan_status", auditVO.getAuditStatus());
            }};
            PlanUpdateRecord planUpdateRecord = new PlanUpdateRecord();
            planUpdateRecord.setPlanId(auditVO.getId());
            planUpdateRecord.setContent(JSONObject.toJSONString(map));
            this.planUpdateRecordService.savePlanUpdateRecord(List.of(planUpdateRecord), user.getId());
            if (PlanStatusEnum.AUDIT_FAIL.getId().equals(auditVO.getAuditStatus())) {
                return;
            }
            log.info("更新开启计划记录:{},操作状态:{}", JSONObject.toJSONString(auditVO.getStartPlanIds()), update.getPlanStatus());
            List<Long> planMarkets = planGenerateMapMapper.selectList(new LambdaQueryWrapper<PlanGenerateMap>()
                    .eq(PlanGenerateMap::getMapType, PlanGenerateMapTypeEnum.PLAN.getId())
                    .eq(PlanGenerateMap::getSheinId, plan.getId())
                    .eq(PlanGenerateMap::getIsDel, IsDelEnum.NORMAL.getId())
            ).stream().map(PlanGenerateMap::getMarketId).collect(Collectors.toList());
            //控制所有订单开启暂停
            if (!planMarkets.isEmpty()) {
                Plan planUp = new Plan();
                planUp.setPlanStatus(PlanStatusEnum.STOP.getId());
                this.baseMapper.update(planUp, new LambdaQueryWrapper<Plan>().in(Plan::getId, planMarkets)
                        .eq(Plan::getPlanStatus, PlanStatusEnum.WAIT_AUDIT.getId()));
            }
            if (CollectionUtils.isNotEmpty(auditVO.getStartPlanIds())) {
                PlanGenerateMap planGenerateMap = new PlanGenerateMap();
                planGenerateMap.setMarketStatus(update.getPlanStatus());
                planGenerateMapMapper.update(planGenerateMap, new LambdaQueryWrapper<PlanGenerateMap>()
                        .eq(PlanGenerateMap::getMapType, PlanGenerateMapTypeEnum.PLAN.getId())
                        .eq(PlanGenerateMap::getSheinId, plan.getId())
                        .eq(PlanGenerateMap::getIsDel, IsDelEnum.NORMAL.getId())
                        .in(PlanGenerateMap::getMarketId, auditVO.getStartPlanIds())
                );
            }
//            this.sheinAuditShadow(auditVO, user);
            //创建1:1活动和计划
        } catch (NumberFormatException e) {
            throw new CustomException("状态不合法，请检查后重试");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sheinBatchAudit(PlanSheinBatchAuditVO batchAuditVO, User user) {
        if (sheinConfiguration.isRole(user.getRoleId())) {
            throw new CustomException("无权限审核");
        }
        List<Long> batchPlanIds = batchAuditVO.getPrices().stream().map(PlanSheinBatchAuditVO.PriceVO::getSheinPlanId).collect(Collectors.toList());
        if (PlanStatusEnum.AUDIT_FAIL.getId().equals(batchAuditVO.getAuditStatus())) {
            Plan update = new Plan();
            update.setPlanStatus(batchAuditVO.getAuditStatus());
            update.setRefuseReason(batchAuditVO.getRefuseReason());
            update.setUpdateUid(user.getId());
            this.baseMapper.update(update, new LambdaUpdateWrapper<Plan>()
                    .eq(Plan::getIsPlanPut, PutEnum.NOT_PUT.getId())
                    .eq(Plan::getIsDel, IsDelEnum.NORMAL.getId())
                    .eq(Plan::getPlanStatus, PlanStatusEnum.WAIT_AUDIT.getId())
                    .eq(Plan::getMasterId, batchAuditVO.getMasterId())
                    .in(Plan::getId, batchPlanIds)
            );
            //记录计划状态操作日志
            List<PlanUpdateRecord> planUpdateRecords = batchAuditVO.getPrices().stream()
                    .map(priceVO -> {
                        PlanUpdateRecord planUpdateRecord = new PlanUpdateRecord();
                        planUpdateRecord.setPlanId(priceVO.getSheinPlanId());
                        //修改日志map
                        Map<String, Object> map = new HashMap<>() {{
                            put("plan_status", update.getPlanStatus());
                        }};
                        planUpdateRecord.setContent(JSONObject.toJSONString(map));
                        return planUpdateRecord;
                    }).collect(Collectors.toList());
            //修改日志
            this.planUpdateRecordService.savePlanUpdateRecord(planUpdateRecords, user.getId());
            return;
        }
        //如果是通过计划，则判定是否创建创意
        Map<Long, List<CreativeUnit>> creativeUnitMap = this.creativeUnitMapper.selectList(new LambdaQueryWrapper<CreativeUnit>()
                .in(CreativeUnit::getPlanId, batchPlanIds)
                .eq(CreativeUnit::getIsDel, IsDelEnum.NORMAL.getId())
        ).stream().collect(Collectors.groupingBy(CreativeUnit::getPlanId));
        if (creativeUnitMap.size() != batchAuditVO.getPrices().size()) {
            String notShadowIds = batchPlanIds.stream().filter(u -> !creativeUnitMap.containsKey(u)).map(Object::toString).collect(Collectors.joining(","));
            throw new CustomException(String.format("计划: %s, 还未创建创意", notShadowIds));
        }
        Map<Long, PlanSheinBatchAuditVO.PriceVO> priceMap = batchAuditVO.getPrices().stream().collect(Collectors.toMap(PlanSheinBatchAuditVO.PriceVO::getSheinPlanId, Function.identity()));
        Master master = this.masterMapper.selectOne(new QueryWrapper<Master>().lambda()
                .eq(Master::getUserId, batchAuditVO.getMasterId()));
        this.baseMapper.selectList(new LambdaQueryWrapper<Plan>()
                .in(Plan::getId, batchPlanIds)
                .eq(Plan::getIsPlanPut, PutEnum.NOT_PUT.getId())
                .eq(Plan::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(Plan::getPlanStatus, PlanStatusEnum.WAIT_AUDIT.getId())
                .eq(Plan::getMasterId, batchAuditVO.getMasterId())
        ).forEach(plan -> {
            PlanCycleVO planCycle = planCycleService.getPlanCycle(plan.getId(), plan.getPutCycle());
            if (null == planCycle) {
                return;
            }
            //同步修改计划状态
            Plan update = new Plan();
            update.setPlanStatus(PlanStatusEnum.resettingStatus(master.getTimeZone(), batchAuditVO.getAuditStatus(), plan.getPutCycle(), planCycle.getStartDate(), planCycle.getEndDate()));
            update.setUpdateUid(user.getId());
            List<Long> planIds = List.of(plan.getId());
            this.baseMapper.update(update, new LambdaUpdateWrapper<Plan>()
                    .in(Plan::getId, planIds)
                    .eq(Plan::getMasterId, batchAuditVO.getMasterId())
            );
            //记录计划状态操作日志
            List<PlanUpdateRecord> planUpdateRecords = planIds.stream().map(planId -> {
                PlanUpdateRecord planUpdateRecord = new PlanUpdateRecord();
                planUpdateRecord.setPlanId(planId);
                //修改日志map
                Map<String, Object> map = new HashMap<>() {{
                    put("plan_status", update.getPlanStatus());
                }};
                planUpdateRecord.setContent(JSONObject.toJSONString(map));
                return planUpdateRecord;
            }).collect(Collectors.toList());
            //修改日志
            this.planUpdateRecordService.savePlanUpdateRecord(planUpdateRecords, user.getId());
            //插入tag 和数据关系
            PlanSheinBatchAuditVO.PriceVO priceVO = priceMap.get(plan.getId());
            if (null != priceVO) {
                SheinTag sheinTag = new SheinTag();
                sheinTag.setSheinPlanId(plan.getId());
                sheinTag.setTagGroupId(priceVO.getTagGroupId());
                sheinTag.setBidPriceRate(priceVO.getBidPriceRate());
                sheinTag.setCreateUid(user.getId());
                this.sheinTagMapper.insertByUk(sheinTag);
            }

        });
    }

    @Override
    public void sheinBatchBudgetDaily(PlanSheinBatchBudgetDailyVO batchBudgetDailyVO, User user) {
        //同步修改计划组日预算
        Plan update = new Plan();
        update.setBudgetDaily(batchBudgetDailyVO.getBudgetDaily());
        update.setUpdateUid(user.getId());
        this.baseMapper.update(update, new LambdaUpdateWrapper<Plan>()
                .eq(Plan::getIsPlanPut, PutEnum.NOT_PUT.getId())
                .eq(Plan::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(Plan::getMasterId, batchBudgetDailyVO.getMasterId())
                .in(Plan::getId, batchBudgetDailyVO.getIds())
        );
    }

    @Override
    public List<SelectDTO> sheinAuditListPlan(PlanSheinAuditListPlanVO listPlanVO, User user) {
        Plan plan = this.getPlan(listPlanVO.getId(), listPlanVO.getMasterId());
        if (null == plan) {
            throw new CustomException("计划不存在");
        }
        return planGenerateMapMapper.selectPlanBySheinPlanId(new QueryWrapper<>()
                .eq("mpgm.map_type", PlanGenerateMapTypeEnum.PLAN.getId())
                .eq("mpgm.shein_id", plan.getId())
                .eq("mpgm.is_del", IsDelEnum.NORMAL.getId())
                .eq("mp.is_del", IsDelEnum.NORMAL.getId())
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sheinAuditShadow(PlanSheinAuditListPlanVO listPlanVO, User user) {
        Plan plan = this.getPlan(listPlanVO.getId(), listPlanVO.getMasterId());
        if (null == plan) {
            throw new CustomException("计划不存在");
        }
        // 获取源计划关联的未删除状态下的创意信息
        Creative originCreative = this.creativeMapper.selectOne(new QueryWrapper<Creative>().lambda()
                .eq(Creative::getPlanId, listPlanVO.getId()).eq(Creative::getIsDel, IsDelEnum.NORMAL.getId()));
        if (null == originCreative) {
            throw new CustomException("Shein计划中未创建创意，请耐心等待");
        }
        try {
            if (ObjectUtils.isNullOrZero(plan.getShadowPlanId())) {
                List<PlanGenerateMap> generateMaps = planGenerateMapMapper.selectList(new LambdaQueryWrapper<PlanGenerateMap>()
                        .eq(PlanGenerateMap::getMapType, PlanGenerateMapTypeEnum.CAMPAIGN.getId())
                        .eq(PlanGenerateMap::getSheinId, plan.getCampaignId())
                        .eq(PlanGenerateMap::getIsDel, IsDelEnum.NORMAL.getId())
                );
                Long campaignId;
                if (generateMaps.isEmpty()) {
                    Campaign campaign = campaignService.getCampaign(plan.getCampaignId(), plan.getMasterId());
                    campaign.setCampaignName(String.format("%s-%s", "投放", campaign.getCampaignName()));
                    CampaignSaveVO campaignSaveVO = JSONObject.parseObject(JSONObject.toJSONString(campaign), CampaignSaveVO.class);
                    campaignSaveVO.setDefaultCampaignStatus(campaign.getCampaignStatus());
                    campaignId = campaignService.saveCampaign(campaignSaveVO, user);
                    //插入shein campaign map
                    planGenerateMapMapper.insertMap(PlanGenerateMapTypeEnum.CAMPAIGN.getId(), plan.getCampaignId(), campaignId, user.getId());
                } else {
                    campaignId = generateMaps.get(0).getMarketId();
                }
                PlanGetDTO planGetDTO = this.getPlanDetail(plan.getId(), plan.getMasterId(), user);
                String resp = JSONObject.toJSONString(planGetDTO);
                PlanSaveVO planSaveVO = JSONObject.parseObject(resp, PlanSaveVO.class);
                planSaveVO.setPlanName(String.format("%s-%s", planGetDTO.getPlanName(), "1:1投放"));
                planSaveVO.setCampaignId(campaignId);
                planSaveVO.setId(null);
                planSaveVO.setOriginPlanId(plan.getId());
                planSaveVO.setCopyStatus(List.of(2, 3));
                planSaveVO.setFlowDetectionState(0);
                planSaveVO.setCopyPlanId(plan.getId());
                PlanSaveDTO shadowPlan = this.savePlan(planSaveVO, user);
                //插入shadow 计划
                Plan update = new Plan();
                update.setShadowPlanId(shadowPlan.getPlanId());
                this.baseMapper.update(update, new LambdaUpdateWrapper<Plan>().eq(Plan::getId, plan.getId()));
                //插入创意映射关系
                Map<Long, Long> oldUnits = creativeUnitMapper.selectList(new LambdaQueryWrapper<CreativeUnit>()
                        .eq(CreativeUnit::getPlanId, plan.getId())
                        .in(CreativeUnit::getCreativeUnitStatus, List.of(CreativeUnitStatusEnum.STOP.getId(), CreativeUnitStatusEnum.MARKETING.getId()))
                        .eq(CreativeUnit::getIsDel, IsDelEnum.NORMAL.getId())
                ).stream().collect(Collectors.toMap(CreativeUnit::getMaterialId, CreativeUnit::getId));
                Map<Long, Long> newUnits = creativeUnitMapper.selectList(new LambdaQueryWrapper<CreativeUnit>()
                        .eq(CreativeUnit::getPlanId, shadowPlan.getPlanId())
                        .in(CreativeUnit::getCreativeUnitStatus, List.of(CreativeUnitStatusEnum.STOP.getId(), CreativeUnitStatusEnum.MARKETING.getId()))
                        .eq(CreativeUnit::getIsDel, IsDelEnum.NORMAL.getId())
                ).stream().collect(Collectors.toMap(CreativeUnit::getMaterialId, CreativeUnit::getId));
                oldUnits.forEach((k, v) -> {
                    if (newUnits.containsKey(k)) {
                        planGenerateMapMapper.insertCreativeMap(plan.getId(), v, newUnits.get(k), user.getId());
                    }
                });
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new CustomException("影子活动生成失败，请联系研发");
        }
    }

    @Override
    public void sheinAuditCampaign(PlanSheinAuditListPlanVO listPlanVO, User user) {
        Plan plan = this.getPlan(listPlanVO.getId(), listPlanVO.getMasterId());
        if (null == plan) {
            throw new CustomException("计划不存在");
        }
        // 获取源计除状态下划关联的未删的创意信息
        Creative originCreative = this.creativeMapper.selectOne(new QueryWrapper<Creative>().lambda()
                .eq(Creative::getPlanId, listPlanVO.getId()).eq(Creative::getIsDel, IsDelEnum.NORMAL.getId()));
        if (null == originCreative) {
            throw new CustomException("Shein计划中未创建创意，请耐心等待");
        }
        try {
            List<PlanGenerateMap> generateMaps = planGenerateMapMapper.selectList(new LambdaQueryWrapper<PlanGenerateMap>()
                    .eq(PlanGenerateMap::getMapType, PlanGenerateMapTypeEnum.CAMPAIGN.getId())
                    .eq(PlanGenerateMap::getSheinId, plan.getCampaignId())
                    .eq(PlanGenerateMap::getIsDel, IsDelEnum.NORMAL.getId())
            );
            if (generateMaps.isEmpty()) {
                Campaign campaign = campaignService.getCampaign(plan.getCampaignId(), plan.getMasterId());
                campaign.setCampaignName(String.format("%s-%s", "投放", campaign.getCampaignName()));
                Long campaignId = campaignService.saveCampaign(JSONObject.parseObject(JSONObject.toJSONString(campaign), CampaignSaveVO.class), user);
                //插入shein map
                planGenerateMapMapper.insertMap(PlanGenerateMapTypeEnum.CAMPAIGN.getId(), plan.getCampaignId(), campaignId, user.getId());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new CustomException("影子活动生成失败，请联系研发");
        }
    }

    @Override
    public List<SheinPlanAuditValidDTO> sheinAuditValid(PlanSheinAuditValidVO validVO) {
        Map<Long, String> planMap = this.baseMapper.selectList(new LambdaQueryWrapper<Plan>()
                .eq(Plan::getMasterId, validVO.getMasterId())
                .in(Plan::getId, validVO.getIds())
                .eq(Plan::getIsPlanPut, PutEnum.NOT_PUT.getId())
                .eq(Plan::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(Plan::getPlanStatus, PlanStatusEnum.WAIT_AUDIT.getId())
        ).stream().collect(Collectors.toMap(Plan::getId, Plan::getPlanName));
        if (planMap.isEmpty()) {
            throw new CustomException("无符合条件待审核计划");
        }
        //判定是否创建创意
        Map<Long, List<CreativeUnit>> creativeUnitMap = this.creativeUnitMapper.selectList(new LambdaQueryWrapper<CreativeUnit>()
                .in(CreativeUnit::getPlanId, validVO.getIds())
                .eq(CreativeUnit::getIsDel, IsDelEnum.NORMAL.getId())
        ).stream().collect(Collectors.groupingBy(CreativeUnit::getPlanId));

        List<PlanDirect> planDirects = this.planDirectMapper.selectList(new LambdaQueryWrapper<PlanDirect>()
                .in(PlanDirect::getDirectId, List.of(1003, 1036))
                .in(PlanDirect::getPlanId, planMap.keySet())
        );
        Map<String, SheinTagGroup> sheinTagGroupMap = this.sheinTagGroupMapper
                .selectList(new LambdaQueryWrapper<SheinTagGroup>().eq(SheinTagGroup::getIsDel, IsDelEnum.NORMAL.getId()))
                .stream().collect(Collectors.toMap(u -> String.format("%s-%s", u.getCountryId(), u.getRtaId()), Function.identity()));
        Map<Long, SheinPlanAuditValidDTO> groupMap = new HashMap<>();
        planDirects.forEach(u -> {
            SheinPlanAuditValidDTO valid = groupMap.computeIfAbsent(u.getPlanId(), v -> new SheinPlanAuditValidDTO());
            if (StringUtils.isBlank(u.getDirectValue()) || "[]".equals(u.getDirectValue()) || "{}".equals(u.getDirectValue())) {
                return;
            }
            switch (u.getDirectId()) {
                case 1003:
                    List<Long> areaIds = JSONObject.parseArray(u.getDirectValue(), Long.class);
                    if (areaIds.size() > 1) {
                        valid.getErrors().add("计划地域数据选择超过2个");
                        valid.setCountryId(areaIds.get(0));
                        return;
                    }
                    if (areaIds.isEmpty()) {
                        return;
                    }
                    valid.setCountryId(areaIds.get(0));
                    break;
                case 1036:
                    RtaStrategyVO rtaStrategyVO = JSONObject.parseObject(u.getDirectValue(), RtaStrategyVO.class);
                    if (null == rtaStrategyVO) {
                        return;
                    }
                    valid.setRtaId(rtaStrategyVO.getRtaStrategyId());
                    break;
                default:
            }
        });
        groupMap.forEach((k, v) -> {
            v.setSheinPlanId(k);
            v.setPlanName(planMap.getOrDefault(k, "无计划名称"));
            if (!creativeUnitMap.containsKey(k)) {
                v.getErrors().add("计划未创建创意");
            }
            if (ObjectUtils.isNullOrZero(v.getCountryId())) {
                v.getErrors().add("地域数据未选择");
            }
            if (ObjectUtils.isNullOrZero(v.getRtaId())) {
                v.getErrors().add("RTA数据未选择");
            }
            String key = String.format("%s-%s", v.getCountryId(), v.getRtaId());
            SheinTagGroup tagGroup = sheinTagGroupMap.get(key);
            if (null == tagGroup) {
                v.getErrors().add("地域和RTA无Tag分组，请先创建影子计划");
                return;
            }
            v.setGroupName(tagGroup.getGroupName());
            v.setCountryAlias(tagGroup.getCountryAlias());
            if (!v.getPlanName().contains(v.getGroupName())) {
                v.getErrors().add("怀疑RTA&地域与客户计划名称不符合");
            }
            v.setTagGroupId(tagGroup.getId());
        });
        return new ArrayList<>(groupMap.values());
    }

    @Override
    public void sheinAuditPrice(PlanSheinAuditPriceVO priceVO, User user) {
        SheinTag sheinTag = new SheinTag();
        sheinTag.setBidPriceRate(priceVO.getBidPriceRate());
        sheinTag.setUpdateUid(user.getId());
        sheinTagMapper.update(sheinTag, new LambdaQueryWrapper<SheinTag>().in(SheinTag::getSheinPlanId, priceVO.getIds()));
    }

    @Override
    public Object noticeSheinPlan(Long planId, User user) {
        Plan plan = this.getOne(new LambdaQueryWrapper<Plan>().eq(Plan::getId, planId));
        if (null == plan) {
            log.error("计划ID{}不存在，请检查后重试", planId);
            return null;
        }
        if (!needSyncOrNoticeShein(plan)) {
            noticeSheinPlanByTag(plan, user);
            return null;
        }
        List<PlanGenerateMap> planMaps = planGenerateMapMapper.selectList(new LambdaQueryWrapper<PlanGenerateMap>()
                .eq(PlanGenerateMap::getMapType, PlanGenerateMapTypeEnum.PLAN.getId())
                .in(PlanGenerateMap::getSheinId, planId)
                .eq(PlanGenerateMap::getIsDel, IsDelEnum.NORMAL.getId())
        );
        if (planMaps.isEmpty()) {
            return null;
        }
        if (plan.getIsDel().equals(IsDelEnum.DELETE.getId().intValue())) {
            RecordBatchDeleteVO recordBatchDeleteVO = new RecordBatchDeleteVO();
            recordBatchDeleteVO.setIds(planMaps.stream().map(PlanGenerateMap::getMarketId).collect(Collectors.toList()));
            recordBatchDeleteVO.setListType("plan");
            recordBatchDeleteVO.setMasterId(plan.getMasterId().longValue());
            recordBatchDeleteVO.setUserId(user.getId());
            return recordBatchDeleteVO;
        }
        //获取投放计划信息
        List<Plan> marketingPlans = this.baseMapper.selectList(new LambdaQueryWrapper<Plan>()
                .in(Plan::getPlanStatus, List.of(PlanStatusEnum.WAIT.getId(), PlanStatusEnum.MARKETING.getId()))
                .in(Plan::getId, planMaps.stream().map(PlanGenerateMap::getMarketId).collect(Collectors.toList()))
                .eq(Plan::getIsDel, IsDelEnum.NORMAL.getId())
                .select(Plan::getId, Plan::getPlanStatus)
        );

        Set<Long> marketingPlanIds = marketingPlans.stream().map(Plan::getId).collect(Collectors.toSet());
        //计划暂停
        if (PlanStatusEnum.STOP.getId().equals(plan.getPlanStatus())) {
            this.planGenerateMapMapper.initStatus(plan.getId(), PlanGenerateMapTypeEnum.PLAN.getId());
            if (!marketingPlans.isEmpty()) {
                PlanGenerateMap planGenerateMap = new PlanGenerateMap();
                planGenerateMap.setMarketStatus(marketingPlans.get(0).getPlanStatus());
                planGenerateMap.setUpdateUid(user.getId());
                this.planGenerateMapMapper.update(planGenerateMap, new LambdaQueryWrapper<PlanGenerateMap>()
                        .eq(PlanGenerateMap::getIsDel, IsDelEnum.NORMAL.getId())
                        .eq(PlanGenerateMap::getMapType, PlanGenerateMapTypeEnum.PLAN.getId())
                        .in(PlanGenerateMap::getMarketId, marketingPlans.stream().map(Plan::getId).collect(Collectors.toList()))
                );
            }
        } else {
            planMaps = planMaps.stream().filter(u -> {
                if (marketingPlanIds.isEmpty()) {
                    return 0 != u.getMarketStatus();
                }
                return marketingPlanIds.contains(u.getId());
            }).collect(Collectors.toList());
            this.planGenerateMapMapper.initStatus(plan.getId(), PlanGenerateMapTypeEnum.PLAN.getId());
        }
        if (planMaps.isEmpty()) {
            RecordBatchNoticeVO recordBatchNoticeVO = new RecordBatchNoticeVO();
            recordBatchNoticeVO.setIds(new ArrayList<>(marketingPlanIds));
            return recordBatchNoticeVO;
        }
        RecordBatchSwitchVO switchVO = new RecordBatchSwitchVO();
        switchVO.setIds(planMaps.stream().map(PlanGenerateMap::getMarketId).collect(Collectors.toList()));
        switchVO.setListType("plan");
        switchVO.setUserId(user.getId());
        switchVO.setPutStatus(plan.getPlanStatus());
        switchVO.setMasterId(plan.getMasterId().longValue());
        return switchVO;
    }

    /**
     * 影子计划更新，通知shein计划
     *
     * @param plan 计划信息
     * @param user 用户ID
     */
    private void noticeSheinPlanByTag(Plan plan, User user) {
        if (sheinConfiguration.isMaster(plan.getMasterId()) && PutEnum.IS_PUT.getId().equals(plan.getIsPlanPut())) {
            SheinTagPlan sheinTagPlan = sheinTagPlanMapper.selectOne(new LambdaQueryWrapper<SheinTagPlan>()
                    .eq(SheinTagPlan::getPlanId, plan.getId())
                    .eq(SheinTagPlan::getIsDel, IsDelEnum.NORMAL.getId())
                    .last("limit 1")
            );
            if (null == sheinTagPlan) {
                return;
            }
            SheinTag sheinTag = sheinTagMapper.selectOne(new LambdaQueryWrapper<SheinTag>()
                    .eq(SheinTag::getTagGroupId, sheinTagPlan.getTagGroupId())
                    .eq(SheinTag::getIsDel, IsDelEnum.NORMAL.getId())
                    .orderByDesc(SheinTag::getId)
                    .last("limit 1")
            );
            if (null == sheinTag) {
                return;
            }
            applicationContext.publishEvent(new CreativeUnitAuditEvent(this, sheinTag.getSheinPlanId()));
        }
    }

    @Override
    public IPage<MarketBatchListDTO> listCreativeUnitBatch(MarketBatchListVO listVO) {

        IPage<MarketBatchListDTO> pageData = this.baseMapper.listCreativeUnitBatch(new Page<>(listVO.getPage(), listVO.getPageNum()),
                new QueryWrapper<Plan>()
                        .eq("mp.is_del", IsDelEnum.NORMAL.getId())
                        .eq("mc.is_del", IsDelEnum.NORMAL.getId())
                        .eq("mc.campaign_mode", CampaignModeEnum.NORMAL.getId())
                        .eq("mp.plan_mode", PlanModeEnum.NORMAL.getId())
                        .eq("mp.is_plan_put", listVO.getIsPut())
                        .eq(ObjectUtils.isNotNullOrZero(listVO.getMasterId()), "mp.master_id", listVO.getMasterId())
                        .in(CollectionUtils.isNotEmpty(listVO.getCampaignIds()), "mc.id", listVO.getCampaignIds())
                        .eq(ObjectUtils.isNotNullOrZero(listVO.getCampaignStatus()), "mc.campaign_status", listVO.getCampaignStatus())
                        .eq(ObjectUtils.isNotNullOrZero(listVO.getPlanStatus()), "mp.plan_status", listVO.getPlanStatus())
                        .eq(ObjectUtils.isNotNullOrZero(listVO.getSlotType()), "mp.slot_type", listVO.getSlotType())
                        .eq(ObjectUtils.isNotAll(listVO.getDpaState()), "mp.dpa_state", listVO.getDpaState())
                        .and(StringUtils.isNotBlank(listVO.getSearch()), q -> q.like("mp.id", listVO.getSearch())
                                .or().like("mp.plan_name", listVO.getSearch()))
                        .orderByDesc("mp.id"));

        if (pageData.getRecords().isEmpty()) {
            return new Page<>();
        }
        // 根据计划ID，获取当页所有计划下所有素材
        CreativeUnitListVO unitListVO = new CreativeUnitListVO();
        unitListVO.setMasterId(listVO.getMasterId());
        unitListVO.setPlanIds(pageData.getRecords().stream().map(MarketBatchListDTO::getPlanId).collect(Collectors.toList()));
        List<CreativeUnitAssetDTO> unitAssetDTOList = this.creativeUnitService.listCreativeUnitAsset(unitListVO);
        // 获取所有创意单元的名称
        Map<Long, String> unitMap = new HashMap<>() {{
            unitAssetDTOList.forEach(assetDTO -> putIfAbsent(assetDTO.getCreativeUnitId(), assetDTO.getCreativeUnitName()));
        }};
        // 将素材列表根据计划——创意单元——素材进行分类
        Map<Long, Map<Long, List<CreativeUnitAssetDTO>>> planUnitAssetMap = this.creativeUnitService.getPlanUnitAssetMap(unitAssetDTOList);
        pageData.getRecords().forEach(record -> {
            Map<Long, List<CreativeUnitAssetDTO>> unitAssetMap = planUnitAssetMap.get(record.getPlanId());
            if (unitAssetMap == null) {
                return;
            }
            List<CreativeUnitMaterialDTO> unitMaterialDTOS = new ArrayList<>();
            unitAssetMap.forEach((unitId, value) -> {
                List<CreativeUnitAssetDTO> unitAssets = unitAssetMap.getOrDefault(unitId, List.of());
                CreativeUnitMaterialDTO unitMaterialDTO = new CreativeUnitMaterialDTO();
                unitMaterialDTO.setCreativeUnitId(unitId);
                unitMaterialDTO.setCreativeUnitName(unitMap.get(unitId));
                unitMaterialDTO.setAssets(this.creativeUnitService.getCreativeUnitMaterial(unitAssets));
                if (!unitAssets.isEmpty()) {
                    unitMaterialDTO.setLandingUrl(unitAssets.get(0).getLandingUrl());
                    unitMaterialDTO.setTemplateId(unitAssets.get(0).getTemplateId());
                    unitMaterialDTO.setTemplateAttr(unitAssets.get(0).getTemplateAttr());
                } else {
                    unitMaterialDTO.setLandingUrl("");
                    unitMaterialDTO.setTemplateId(0L);
                }
                unitMaterialDTOS.add(unitMaterialDTO);
            });
            record.setUnits(unitMaterialDTOS);
        });
        return pageData;
    }

    @Override
    public List<PlanDimensionDTO> listPlanByDimension(PlanDimensionSelectVO selectVO) {
        return baseMapper.selectPlanAndCampaignAndAgent(new QueryWrapper<Plan>()
                .in(CollectionUtils.isNotEmpty(selectVO.getPlanIds()), "p.id", selectVO.getPlanIds())
                .in(CollectionUtils.isNotEmpty(selectVO.getCampaignIds()), "p.campaign_id", selectVO.getCampaignIds())
                .eq("p.is_del", IsDelEnum.NORMAL.getId())
        );
    }


    private PlanCycleVO getPlanCycle(Plan plan) {
        try {
            return planCycleService.getPlanCycle(plan.getId(), plan.getPutCycle());
        } catch (CustomException e) {
            log.error("自定义异常，不存在计划排期：{}", e.getMessage());
            return null;
        }
    }

    @Override
    public void updatePlanBudgetByNextDay(Date date) {

        TimeZoneEnum timeZoneEnum = TimeZoneEnum.getTimeZoneByCurrentHour(Integer.parseInt(DateUtils.format(date, "HH")));
        if (timeZoneEnum == null) {
            return;
        }
        // 获取需要更新的计划列表：次日预算不为0
        List<Plan> plans = this.baseMapper.listPlanByTimeZone(new QueryWrapper<Plan>()
                .eq("mp.is_del", IsDelEnum.NORMAL.getId())
                .eq("mm.time_zone", timeZoneEnum.getId())
                .ne("mp.next_budget_type", 0));

        if (plans.isEmpty()) {
            return;
        }

        // 更新
        List<PlanUpdateRecord> planUpdateRecords = new ArrayList<>();
        plans.forEach(plan -> {
            plan.setBudgetType(plan.getNextBudgetType());
            plan.setBudgetDay(plan.getNextBudgetDay());
            plan.setNextBudgetType(0);
            plan.setNextBudgetDay(new BigDecimal(0));
            PlanUpdateRecord planUpdateRecord = new PlanUpdateRecord();
            planUpdateRecord.setPlanId(plan.getId());
            Map<String, Object> map = new HashMap<>() {{
                put("budget_type", plan.getBudgetType());
                put("budget_day", BudgetUtils.format(plan.getBudgetDay(), plan.getBudgetType()));
            }};
            planUpdateRecord.setContent(JSONObject.toJSONString(map));
            planUpdateRecords.add(planUpdateRecord);
        });
        this.planUpdateRecordService.savePlanUpdateRecord(planUpdateRecords, 0);
        this.baseMapper.batchSavePlan(plans);
    }

    /**
     * 是否是shein计划
     *
     * @param plan 计划信息
     * @return 返回数据
     */
    @Override
    public boolean isSheinPlan(Plan plan) {
        return PutEnum.NOT_PUT.getId().equals(plan.getIsPlanPut());
    }

    /**
     * 修改记录
     *
     * @param planUpdateRecords 修改记录
     * @param user              用户
     */
    @Override
    public void savePlanUpdateRecordAndAfter(List<PlanUpdateRecord> planUpdateRecords, User user) {
        List<PlanUpdateRecord> updateRecords = this.planUpdateRecordService
                .savePlanUpdateRecord(planUpdateRecords, user.getId());
        if (!updateRecords.isEmpty()) {
            this.syncSheinPlans(planUpdateRecords, user);
        }
    }

    /**
     * 复制指定计划下的创意单元
     *
     * @param plan       新计划信息
     * @param originPlan 源计划信息
     */
    private void copyCreativeByPlan(Plan plan, Plan originPlan, PlanSaveVO planSaveVO) {
        // 获取源计划关联的未删除状态下的创意信息
        Creative originCreative = this.creativeMapper.selectOne(new QueryWrapper<Creative>().lambda()
                .eq(Creative::getPlanId, originPlan.getId()).eq(Creative::getIsDel, IsDelEnum.NORMAL.getId()));
        if (null == originCreative) {
            return;
        }
        Creative creative = new Creative();
        BeanUtils.copyProperties(originCreative, creative);
        creative.setId(null);
        creative.setCreateUid(plan.getCreateUid());
        creative.setCampaignId(plan.getCampaignId());
        creative.setPlanId(plan.getId());
        //清空了待上线素材数量
        creative.setAssetWaitCount(0);
        creative.setUpdateUid(0);
        //如果含有监控，则插入监控关联关系
        if (ObjectUtils.isNotNullOrZero(creative.getCreativeMonitorId())) {
            assetGroupMapper.insertMonitor(plan.getMasterId(), creative.getCreativeMonitorId(), plan.getId(), plan.getCreateUid());
        }
        // 插入创意信息
        int insertCreativeResult = this.creativeMapper.insert(creative);
        if (insertCreativeResult > 0) {
            // 查询和计划关联的处于投放中且未删除状态的创意单元
            List<CreativeUnit> creativeUnits = this.creativeUnitMapper.selectList(new QueryWrapper<CreativeUnit>().lambda()
                    .eq(CreativeUnit::getPlanId, originPlan.getId())
                    .in(CreativeUnit::getCreativeUnitStatus, planSaveVO.getCopyStatus())
                    .eq(CreativeUnit::getIsDel, IsDelEnum.NORMAL.getId())
                    .orderByAsc(CreativeUnit::getId)
            );
            AtomicLong index = new AtomicLong(1L);
            creativeUnits.forEach(creativeUnit -> {
                if (null != planSaveVO.getCreativeTemplateId()) {
                    if (0 == planSaveVO.getCreativeTemplateId()) {
                        creativeUnit.setTemplateId(0L);
                        creativeUnit.setTemplateAttr("");
                    } else {
                        creativeUnit.setTemplateId(planSaveVO.getCreativeTemplateId());
                        creativeUnit.setTemplateAttr(planSaveVO.getCreativeTemplateAttr());
                    }
                }
                creativeUnit.setId(null);
                creativeUnit.setCreateUid(plan.getCreateUid());
                creativeUnit.setUpdateUid(0);
                creativeUnit.setCampaignId(plan.getCampaignId());
                creativeUnit.setPlanId(plan.getId());
                creativeUnit.setCreativeId(creative.getId());
                creativeUnit.setCreativeIndex(index.getAndIncrement());
                this.creativeUnitMapper.insert(creativeUnit);
            });
        } else {
            log.info("copy creative error : {}", JSONObject.toJSONString(creative));
        }
        this.sheinCreativeService.logCreativeUpdate(plan.getId(), plan.getCreateUid());
    }

    /**
     * 计划更新记录
     *
     * @param planSaveVO 订单对象
     * @param planInDb   当前计划ID
     * @param user       用户信息
     */
    public void savePlanUpdateRecordBySaveVO(PlanSaveVO planSaveVO, Plan planInDb, User user) {
        // 则记录计划编辑记录
        PlanUpdateRecord planUpdateRecord = new PlanUpdateRecord();
        planUpdateRecord.setPlanId(planInDb.getId());
        Map<String, Object> map = new HashMap<>() {{
            put("plan_name", planSaveVO.getPlanName());
            put("budget_type", planSaveVO.getBudgetType());
            put("budget_day", planSaveVO.getBudgetDay());
            put("budget_hour", planSaveVO.getBudgetHour());
            put("bid_type", planSaveVO.getBidType());
            put("bid_price", planSaveVO.getBidPrice());
            put("deep_bid_status", planSaveVO.getDeepBidStatus());
            put("deep_bid_type", planSaveVO.getDeepBidAction());
            put("deep_bid_price", planSaveVO.getDeepBidPrice());
            put("plan_status", planSaveVO.getPlanStatus());
            if (planSaveVO.getBidType().equals(PlanBidTypeEnum.oCPM.getId())) {
                put("bid_algo_type", planSaveVO.getBidAlgoType());
                put("flow_detection_state", planSaveVO.getFlowDetectionState());
                put("budget_learning_rate", planSaveVO.getBudgetLearningRate());
                put("learning_length", planSaveVO.getLearningLength());
            }
            put("package_include", planSaveVO.getPackageName().getInclude());
            put("package_value", planSaveVO.getPackageName().getValue());
            put("package_report_exclude", "");
            //地域
            put("area_country", planSaveVO.getAreaCountry());
            //流量类型
            put("flow_type", planSaveVO.getFlowType());
            //操作系统
            put("os_type", planSaveVO.getOsType());
            //投放速率
            put("consume_rate", planSaveVO.getConsumeRate());
            put("adx_id", planSaveVO.getAdxId());
            put("ep_id", planSaveVO.getEpId());
            put("monitor_index", planSaveVO.getMonitorIndex());
            //投放周期
            put("put_cycle", planSaveVO.getPutCycle());
            put("start_date", planSaveVO.getStartDate());
            put("end_date", planSaveVO.getEndDate());
            put("hours", planSaveVO.getHours());
            //曝光频次
            put("frequency_cycle_view", planSaveVO.getFrequencyCycleView());
            put("frequency_num_view", planSaveVO.getFrequencyNumView());
            //点击频次
            put("frequency_cycle_click", planSaveVO.getFrequencyCycleView());
            put("frequency_num_click", planSaveVO.getFrequencyNumClick());
            put("industry_id", planSaveVO.getIndustryId());
            put("crowd_label", planSaveVO.getCrowdLabel());
            put("landing_url", planSaveVO.getLandingUrl());
            put("deeplink", planSaveVO.getDeeplink());
            put("monitor_view_url1", planSaveVO.getMonitorViewUrl1());
            put("monitor_view_url2", planSaveVO.getMonitorViewUrl2());
            put("monitor_click_url1", planSaveVO.getMonitorClickUrl1());
            put("monitor_click_url2", planSaveVO.getMonitorClickUrl2());
            put("rta_strategy", null == planSaveVO.getRtaStrategy() ? "" : planSaveVO.getRtaStrategy());
            put("next_budget_type", planInDb.getNextBudgetType());
            if (null != planInDb.getNextBudgetType()) {
                if (!planInDb.getNextBudgetType().equals(BudgetTypeEnum.VIEW.getId())) {
                    put("next_budget_day", planInDb.getNextBudgetDay());
                } else {
                    put("next_budget_day", BigDecimal.ZERO.equals(planInDb.getNextBudgetDay()) ? 0 :
                            planInDb.getNextBudgetDay().divide(BigDecimal.valueOf(1000L)));
                }
            } else {
                put("next_budget_type", 0);
                put("next_budget_day", BigDecimal.ZERO);
            }
        }};
        planUpdateRecord.setContent(JSONObject.toJSONString(map));
        this.savePlanUpdateRecordAndAfter(List.of(planUpdateRecord), user);
    }

    /**
     * 计划更新记录
     *
     * @param plans 订单对象
     * @param user  用户信息
     */
    public void savePlanUpdateLinkRecord(List<Plan> plans, User user) {
        List<PlanUpdateRecord> planUpdateRecords = plans.stream().map(plan -> {
            PlanUpdateRecord planUpdateRecord = new PlanUpdateRecord();
            planUpdateRecord.setPlanId(plan.getId());
            Map<String, Object> map = new HashMap<>() {{
                if (plan.getDeeplink() != null) {
                    // 判断计划是否需要设置 deeplink
                    put("deeplink", plan.getDeeplink());
                }
                if (plan.getLandingUrl() != null) {
                    put("landing_url", plan.getLandingUrl());
                }
                if (plan.getMonitorClickUrl1() != null) {
                    put("monitor_click_url1", plan.getMonitorClickUrl1());
                }
                if (plan.getMonitorClickUrl2() != null) {
                    put("monitor_click_url2", plan.getMonitorClickUrl2());
                }
                if (plan.getMonitorViewUrl1() != null) {
                    put("monitor_view_url1", plan.getMonitorViewUrl1());
                }
                if (plan.getMonitorViewUrl2() != null) {
                    put("monitor_view_url2", plan.getMonitorClickUrl2());
                }
            }};
            planUpdateRecord.setContent(JSONObject.toJSONString(map));
            return planUpdateRecord;
        }).collect(Collectors.toList());
        //修改数据
        this.savePlanUpdateRecordAndAfter(planUpdateRecords, user);
    }

    /**
     * 处理shein 计划数据
     *
     * @param planUpdateRecords 修改记录
     * @param user              用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncSheinPlans(List<PlanUpdateRecord> planUpdateRecords, User user) {
        if (CollectionUtils.isEmpty(planUpdateRecords)) {
            return;
        }
        Map<Long, Plan> planMap = this.listByIds(planUpdateRecords.stream()
                .map(PlanUpdateRecord::getPlanId).collect(Collectors.toList())
        ).stream().collect(Collectors.toMap(Plan::getId, Function.identity()));
        planUpdateRecords = planUpdateRecords.stream().filter(planUpdateRecord -> {
            if (planMap.containsKey(planUpdateRecord.getPlanId())) {
                return needSyncOrNoticeShein(planMap.get(planUpdateRecord.getPlanId()));
            }
            return false;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(planUpdateRecords)) {
            return;
        }
        //计划修改
        planUpdateRecords.forEach(planUpdateRecord -> {
            try {
                this.syncSheinPlan(planMap.get(planUpdateRecord.getPlanId()), planUpdateRecord, user);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
        //预算调整修改
        List<PlanUpdateRecord> finalPlanUpdateRecords = planUpdateRecords;
        CompletableFuture.runAsync(() -> finalPlanUpdateRecords.forEach(planUpdateRecord -> {
                    try {
                        this.budgetAndBidSheinPlan(planMap.get(planUpdateRecord.getPlanId()), planUpdateRecord);
                        //延迟1s执行
                        TimeUnit.SECONDS.sleep(1);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                }
        ), controlEventPool);
    }


    /**
     * 同步 shein 影子计划
     *
     * @param planInDb         计划信息
     * @param planUpdateRecord 计划ID
     * @param user             用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncSheinPlan(Plan planInDb, PlanUpdateRecord planUpdateRecord, User user) {
        log.info("shein计划内容修改同步开始，修改内容 {}", planUpdateRecord.getContent());
        //获取更改同步内容
        JSONObject change = new JSONObject();
        JSONObject updateRecord = JSONObject.parseObject(planUpdateRecord.getContent());
        //修改定向内容
        for (String key : List.of("area_country", "flow_type", "os_type", "consume_rate", "put_cycle",
                "start_date", "end_date", "hours", "frequency_cycle_view", "frequency_num_view",
                "frequency_cycle_click", "frequency_num_click", "landing_url", "deeplink",
                "monitor_view_url1", "monitor_view_url2", "monitor_click_url1", "monitor_click_url2",
                "industry_id", "crowd_label", "rta_strategy", "monitor_index",
                "next_budget_type", "next_budget_day")) {
            if (updateRecord.containsKey(key)) {
                change.put(HumpLineUtils.lineToHump(key), updateRecord.get(key));
            }
        }
        //无需要同步更该内容
        if (change.isEmpty()) {
            log.info("shein计划内容无需要同步");
            return;
        }

        List<PlanGenerateMap> planGenerateMaps = planGenerateMapMapper.selectList(new LambdaQueryWrapper<PlanGenerateMap>()
                .eq(PlanGenerateMap::getMapType, PlanGenerateMapTypeEnum.PLAN.getId())
                .eq(PlanGenerateMap::getSheinId, planInDb.getId())
                .eq(PlanGenerateMap::getIsDel, IsDelEnum.NORMAL.getId())
        );
        planGenerateMaps.forEach(planGenerateMap -> {
            try {
                log.info("shein计划{}同步至影子计划{},内容:{}", planInDb.getId(), planGenerateMap.getMarketId(), change.toJSONString());
                PlanGetDTO getDTO = this.getPlanDetail(planGenerateMap.getMarketId(), planInDb.getMasterId(), user);
                if (IsDelEnum.DELETE.getId().intValue() == getDTO.getPlanIsDel()) {
                    return;
                }
                JSONObject mapObj = JSONObject.parseObject(JSONObject.toJSONString(getDTO));
                mapObj.putAll(change);
                this.savePlan(JSONObject.parseObject(mapObj.toJSONString(), PlanSaveVO.class), user);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
        //判定是否修改地域和rta数据
        if (planGenerateMaps.isEmpty() && !List.of(PlanStatusEnum.WAIT_AUDIT.getId(), PlanStatusEnum.AUDIT_FAIL.getId()).contains(planInDb.getPlanStatus())) {
            StringBuilder stringBuilder = new StringBuilder();
            if (change.containsKey("rtaStrategy")) {
                stringBuilder.append("修改RTA策略,");
            }
            if (change.containsKey("areaCountry")) {
                stringBuilder.append("修改国家地域,");
            }
            String smsContent = stringBuilder.toString();
            if (StringUtils.isNotBlank(smsContent)) {
                Campaign campaign = campaignService.getCampaign(planInDb.getCampaignId(), planInDb.getMasterId());
                smsContent = String.format("Shein活动 [%s] 下计划 [%s] 将%s", campaign.getCampaignName(),
                        planInDb.getPlanName(), smsContent.substring(0, smsContent.length() - 1));
                log.info("shein计划RTA、地域调整通知，内容 {} ", smsContent);
                smsUtils.sendSheinMsg("Shein计划RTA、地域调整通知", smsContent);
            }
        }
    }


    /**
     * 修改预算
     *
     * @param planInDb         计划信息
     * @param planUpdateRecord 修改信息
     */
    private void budgetAndBidSheinPlan(Plan planInDb, PlanUpdateRecord planUpdateRecord) {
        JSONObject updateRecord = JSONObject.parseObject(planUpdateRecord.getContent());
        StringBuilder stringBuilder = new StringBuilder();
        //修改预算，出价等内容
        for (String key : List.of("budget_day", "budget_hour", "bid_price", "next_budget_day")) {
            String oldKey = "old_" + key;
            if (updateRecord.containsKey(oldKey) && updateRecord.containsKey(key)) {
                if (!updateRecord.get(key).equals(updateRecord.get(oldKey))) {
                    switch (key) {
                        case "budget_day":
                            stringBuilder.append("日预算由 ")
                                    .append(BudgetTypeEnum.budgetName(updateRecord.get(oldKey)))
                                    .append(" 调整至 ")
                                    .append(BudgetTypeEnum.budgetName(updateRecord.get(key)))
                                    .append("，");
                            break;
                        case "budget_hour":
                            stringBuilder.append("小时预算由 ")
                                    .append(BudgetTypeEnum.budgetName(updateRecord.get(oldKey)))
                                    .append(" 调整至 ")
                                    .append(BudgetTypeEnum.budgetName(updateRecord.get(key)))
                                    .append("，");
                            break;
                        case "bid_price":
                            stringBuilder.append("出价由 ").append(updateRecord.get(oldKey)).append(" 调整至 ").append(updateRecord.get(key))
                                    .append("，");
                            break;
                        case "next_budget_day":
                            stringBuilder.append("次日预算由 ")
                                    .append(BudgetTypeEnum.budgetName(updateRecord.get(oldKey)))
                                    .append(" 调整至 ")
                                    .append(BudgetTypeEnum.budgetName(updateRecord.get(key)))
                                    .append("，");
                            break;
                        default:
                            break;
                    }
                }
            }
        }
        String smsContent = stringBuilder.toString();
        if (StringUtils.isNotBlank(smsContent)) {
            Campaign campaign = campaignService.getCampaign(planInDb.getCampaignId(), planInDb.getMasterId());
            smsContent = String.format("Shein活动 [%s] 下计划 [%s] 将%s", campaign.getCampaignName(),
                    planInDb.getPlanName(), smsContent.substring(0, smsContent.length() - 1));
            log.info("shein计划内容调整短信通知，内容 {} ", smsContent);
            smsUtils.sendSheinMsg("Shein计划预算出价调整", smsContent);
            callNoticeMapper.callSheinPlanUpdate(planInDb.getId());
        }
    }

    /**
     * 根据计划ID获取shein计划iD
     *
     * @param planId 计划ID
     * @return 返回shein计划ID
     */
    private Long getSheinPlanId(Long planId) {
        PlanGenerateMap planGenerateMap = planGenerateMapMapper.selectOne(new LambdaQueryWrapper<PlanGenerateMap>()
                .eq(PlanGenerateMap::getMapType, PlanGenerateMapTypeEnum.PLAN.getId())
                .eq(PlanGenerateMap::getMarketId, planId)
        );
        if (null == planGenerateMap) {
            return null;
        }
        return planGenerateMap.getSheinId();
    }


    /**
     * 根据计划ID获取shein 投放ID
     *
     * @param sheinIds    shein ID
     * @param mapTypeEnum 类型
     * @return 返回shein计划ID
     */
    private List<Long> getSheinMarketId(List<Long> sheinIds, PlanGenerateMapTypeEnum mapTypeEnum) {
        return planGenerateMapMapper.selectList(new LambdaQueryWrapper<PlanGenerateMap>()
                .eq(PlanGenerateMap::getMapType, mapTypeEnum.getId())
                .in(PlanGenerateMap::getSheinId, sheinIds)
        ).stream().map(PlanGenerateMap::getMarketId).collect(Collectors.toList());
    }

    /**
     * 判定是否需要同步or通知shein计划
     *
     * @param plan 计划信息
     * @return 返回数据
     */
    private boolean needSyncOrNoticeShein(Plan plan) {
        //非shein计划返回
        if (!isSheinPlan(plan)) {
            log.info("计划 {} 非shein计划", plan.getId());
            return false;
        }
        //非shein计划状态返回
        if (List.of(PlanStatusEnum.AUDIT_FAIL.getId()).contains(plan.getPlanStatus())) {
            log.info("计划 {} 是shein计划，但是状态不是通过 {}", plan.getId(), plan.getPlanStatus());
            return false;
        }
        return true;
    }

}
