package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.market.plan.task.*;
import com.overseas.common.vo.market.plan.direct.PlanDirectValueVO;
import com.overseas.common.vo.market.plan.task.*;
import com.overseas.common.enums.market.rta.RtaGroupEnum;
import com.overseas.service.market.events.creativeUnitAudit.CreativeUnitAuditEvent;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.SelectDTO3;
import com.overseas.common.dto.market.plan.PlanEpDTO;
import com.overseas.common.dto.market.plan.PlanRtaStrategyDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.enums.ProjectEnum;
import com.overseas.common.enums.market.PlanSlotTypeEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.utils.ValidatorUtils;
import com.overseas.common.vo.market.material.MaterialAssetVO;
import com.overseas.common.vo.market.material.MaterialVO;
import com.overseas.common.vo.market.monitor.monitorEvent.MonitorEventSelectGetVO;
import com.overseas.common.vo.sys.area.AreaCountrySelectVO;
import com.overseas.service.market.common.utils.BudgetUtils;
import com.overseas.service.market.dto.creative.CreativeGetDTO;
import com.overseas.service.market.dto.plan.PlanGetDTO;
import com.overseas.service.market.enums.BidTypeEnum;
import com.overseas.common.enums.market.campaign.CampaignMarketTargetEnum;
import com.overseas.common.enums.market.plan.PlanStatusEnum;
import com.overseas.service.market.enums.plan.task.PlanTaskStatusEnum;
import com.overseas.service.market.feign.FgSystemService;
import com.overseas.service.market.mapper.*;
import com.overseas.service.market.service.*;
import com.overseas.service.market.vo.creative.CreativeSaveVO;
import com.overseas.service.market.vo.plan.PlanSaveVO;
import com.overseas.service.market.entity.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PlanTaskServiceImpl extends ServiceImpl<PlanTaskMapper, PlanTask> implements PlanTaskService {

    private final PlanTaskRecordMapper planTaskRecordMapper;

    private final PlanService planService;

    private final CreativeService creativeService;

    private final PlanMapper planMapper;

    private final PlanDirectMapper planDirectMapper;

    private final CampaignMapper campaignMapper;

    private final EpMapper epMapper;

    private final RtaStrategyMapper rtaStrategyMapper;

    private final CreativeMapper creativeMapper;

    private final ApplicationContext applicationContext;

    private final MonitorEventService monitorEventService;

    private final FgSystemService fgSystemService;

    private final RtaStrategyService rtaStrategyService;

    private final RtaGroupMapper rtaGroupMapper;

    @Override
    public PageUtils<PlanTaskListDTO> listPlanTask(PlanTaskListVO listVO) {

        IPage<PlanTaskListDTO> pageData = this.baseMapper.listPlanTask(new Page<>(listVO.getPage(), listVO.getPageNum()), new QueryWrapper<PlanTask>()
                .eq("mpt.is_del", IsDelEnum.NORMAL.getId())
                .eq("mpt.master_id", listVO.getMasterId())
                .orderByDesc("mpt.id"));

        pageData.getRecords().forEach(entity -> entity.setTaskStatusName(ICommonEnum.getNameById(entity.getTaskStatus(), PlanTaskStatusEnum.class)));
        return new PageUtils<>(pageData);
    }

    @Override
    public PlanTaskTempDTO listPlanTaskTemp(PlanTaskTempListVO listVO) {

        // 1.先获取模板计划
        Plan plan = this.planMapper.selectById(listVO.getTempPlanId());
        if (plan == null) {
            throw new CustomException("模板计划不存在，请确认后再试");
        }
        Campaign campaign = this.campaignMapper.selectById(plan.getCampaignId());

        // 2.获取模板计划定向
        Map<Integer, PlanDirect> planDirectMap = this.planDirectMapper.selectList(new QueryWrapper<PlanDirect>().lambda()
                .eq(PlanDirect::getInclude, 1)
                .eq(PlanDirect::getPlanId, plan.getId())).stream().collect(Collectors.toMap(PlanDirect::getDirectId, Function.identity()));
        // 3.获取EP及对应ADX映射Map
        Map<Long, PlanEpDTO> epMap = (listVO.getEpIds().isEmpty() && (planDirectMap.get(1032) == null
                || StringUtils.isBlank(planDirectMap.get(1032).getDirectValue())))
                ? new HashMap<>() : this.epMapper.selectEpAndAdx(new QueryWrapper<Ep>().in("de.id", new ArrayList<>() {{
            if (CollectionUtils.isNotEmpty(listVO.getEpIds())) {
                addAll(listVO.getEpIds());
            }
            addAll(JSONArray.parseArray(planDirectMap.get(1032).getDirectValue(), Long.class));
        }})).stream().collect(Collectors.toMap(PlanEpDTO::getEpId, Function.identity()));

        // 4.获取RTA及对应RTA策略组
        Map<Long, RtaStrategy> rtaStrategyMap = (listVO.getRtaStrategyIds().isEmpty() && (planDirectMap.get(1036) == null
                || JSONObject.parseObject(planDirectMap.get(1036).getDirectValue(), PlanRtaStrategyDTO.class).getRtaStrategyId() == null))
                ? new HashMap<>() : this.rtaStrategyMapper.selectList(new QueryWrapper<RtaStrategy>().lambda().in(RtaStrategy::getId, new ArrayList<>() {{
            if (CollectionUtils.isNotEmpty(listVO.getRtaStrategyIds())) {
                addAll(listVO.getRtaStrategyIds());
            }
            if (planDirectMap.get(1036) != null) {
                add(JSONObject.parseObject(planDirectMap.get(1036).getDirectValue(), PlanRtaStrategyDTO.class).getRtaStrategyId());
            }
        }})).stream().collect(Collectors.toMap(RtaStrategy::getId, Function.identity()));

        PlanTaskTempDTO planTaskTempDTO = new PlanTaskTempDTO();
        // 5.获取计划下默认创意单元
        CreativeGetDTO creativeGetDTO;
        try {
            creativeGetDTO = this.creativeService.getCreativeDetailByPlanId(plan.getId(), plan.getMasterId());
            planTaskTempDTO.setCreativeId(creativeGetDTO.getId());
        } catch (Exception ignored) {
            creativeGetDTO = null;
            planTaskTempDTO.setCreativeId(null);
        }

        // 6.获取种子计划默认填充值
        Map<String, String> tempPlanDirectMap = new HashMap<>() {{
            // 获取ADX
            if (planDirectMap.get(1032) != null) {
                put("adx", epMap.get(JSONArray.parseArray(planDirectMap.get(1032).getDirectValue(), Long.class).get(0)).getAdxName());
            }
            // 获取RTA
            if (planDirectMap.get(1036) != null && JSONObject.parseObject(planDirectMap.get(1036).getDirectValue(), PlanRtaStrategyDTO.class).getRtaStrategyId() != null) {
                put("rta", rtaStrategyMap.get(JSONObject.parseObject(planDirectMap.get(1036).getDirectValue(), PlanRtaStrategyDTO.class).getRtaStrategyId()).getRtaAlias());
                put("rtaCountry", rtaStrategyMap.get(JSONObject.parseObject(planDirectMap.get(1036).getDirectValue(), PlanRtaStrategyDTO.class).getRtaStrategyId()).getRtaCountry());
            }
            // 获取广告形式
            put("slotType", ICommonEnum.getNameById(plan.getSlotType(), PlanSlotTypeEnum.class));
            // 获取出价类型
            put("bidType", ICommonEnum.getNameById(plan.getBidType(), BidTypeEnum.class));
            // 获取优化目标
            MonitorEventSelectGetVO getVO = new MonitorEventSelectGetVO();
            getVO.setMonitorId(plan.getMonitorId());
            getVO.setMasterId(plan.getMasterId().longValue());
            Map<Long, String> brandOptimizeMap = CampaignMarketTargetEnum.BRAND_RECOGNITION.getId().equals(campaign.getMarketTarget())
                    ? new HashMap<>() {{
                put(1L, "曝光");
                put(2L, "点击");
            }} : monitorEventService.getEventSelect(getVO).stream().collect(Collectors.toMap(SelectDTO::getId, SelectDTO::getName));
            if (brandOptimizeMap.get(plan.getOptimizeTargetId()) != null) {
                put("optimizeTarget", brandOptimizeMap.get(plan.getOptimizeTargetId()));
            }
            // 获取地域定向
            if (planDirectMap.get(1003) != null && StringUtils.isNotBlank(planDirectMap.get(1003).getDirectValue()) && !planDirectMap.get(1003).getDirectValue().equals("[]")) {
                List<String> areaCountries = JSONArray.parseArray(planDirectMap.get(1003).getDirectValue(), String.class);
                put("areaCountry", fgSystemService.selectCountry(new AreaCountrySelectVO()).getData().stream()
                        .filter(country -> areaCountries.contains(country.getKey()))
                        .map(SelectDTO3::getTitle).collect(Collectors.joining("_")));
            }
        }};
        // 7.获取结果集
        planTaskTempDTO.setPlanList(this.getPlanTaskTempList(plan, listVO, campaign.getCampaignName(), planDirectMap, epMap, rtaStrategyMap,
                creativeGetDTO == null ? List.of() : creativeGetDTO.getUnits(), tempPlanDirectMap));
        return planTaskTempDTO;
    }

    /**
     * 获取临时列表
     *
     * @param plan              模板计划
     * @param listVO            传入参数
     * @param campaignName      活动名称
     * @param epMap             Ep映射Map
     * @param rtaStrategyMap    RTA映射Map
     * @param units             创意单元
     * @param tempPlanDirectMap 模板计划定向Map
     * @return 返回结果集
     */
    private List<PlanTaskTempListDTO> getPlanTaskTempList(Plan plan, PlanTaskTempListVO listVO, String campaignName, Map<Integer, PlanDirect> planDirectMap, Map<Long, PlanEpDTO> epMap,
                                                          Map<Long, RtaStrategy> rtaStrategyMap, List<MaterialVO> units, Map<String, String> tempPlanDirectMap) {

        List<PlanTaskTempListDTO> planTaskTempList = this.loopFillTempList(this.loopFillTempList(this.loopFillTempList(List.of(), listVO.getEpIds(), "ep", epMap, rtaStrategyMap),
                        listVO.getRtaStrategyIds(), "rta", epMap, rtaStrategyMap),
                listVO.getSlotType(), "slotType", epMap, rtaStrategyMap);

        // 获取RTA宏配置
        Map<Long, RtaGroup> rtaGroupMacroMap = CollectionUtils.isEmpty(rtaStrategyMap) ? new HashMap<>()
                : this.rtaGroupMapper.selectList(new QueryWrapper<RtaGroup>().lambda()
                        .in(RtaGroup::getId, rtaStrategyMap.values().stream().map(RtaStrategy::getRtaGroupId).distinct().collect(Collectors.toList())))
                .stream().collect(Collectors.toMap(RtaGroup::getId, Function.identity()));
        // 插入其他值
        planTaskTempList.forEach(planTaskTempListDTO -> {
            planTaskTempListDTO.setCampaignId(plan.getCampaignId());
            planTaskTempListDTO.setCampaignName(campaignName);
            // Plan规则：{ADX}-{广告形式}-{RTA}-{尺寸}-{出价}-{出价模式}
            planTaskTempListDTO.setPlanName(this.replacePlanName(listVO.getPlanName(), planTaskTempListDTO, tempPlanDirectMap));
            planTaskTempListDTO.setPlanStatus(PlanStatusEnum.STOP.getId());
            planTaskTempListDTO.setBudgetType(plan.getBudgetType());
            planTaskTempListDTO.setBudgetDay(BudgetUtils.format(plan.getBudgetDay(), plan.getBudgetType()));
            planTaskTempListDTO.setBidType(plan.getBidType());
            planTaskTempListDTO.setBidPrice(plan.getBidPrice());
            if (planTaskTempListDTO.getSlotType() == null) {
                planTaskTempListDTO.setSlotType(plan.getSlotType());
            }
            if (StringUtils.isBlank(planTaskTempListDTO.getRtaStrategy())) {
                planTaskTempListDTO.setRtaStrategy(planDirectMap.getOrDefault(1036, new PlanDirect()).getDirectValue());
            }
            // 根据RTA填充链接
            try {

                RtaStrategy rtaStrategy = rtaStrategyMap.get(JSONObject.parseObject(planTaskTempListDTO.getRtaStrategy(), PlanRtaStrategyDTO.class).getRtaStrategyId());
                String cId = "";

                // 如果存在宏替换规则
                Map<String, Object> macroMap = rtaGroupMacroMap.get(rtaStrategy.getRtaGroupId()) != null
                        && StringUtils.isNotBlank(rtaGroupMacroMap.get(rtaStrategy.getRtaGroupId()).getRtaMacro())
                        ? JSONObject.parseObject(rtaGroupMacroMap.get(rtaStrategy.getRtaGroupId()).getRtaMacro()) : null;

                // 如果是Lazada则获取cid、配置adtype
                if (List.of(
                        RtaGroupEnum.MIRAVIA_RT.getId().longValue(),
                        RtaGroupEnum.MIRAVIA.getId().longValue(),
                        ProjectEnum.LAZADA_RTA.getRtaGroupId(),
                        ProjectEnum.LAZADA_RTB.getRtaGroupId(),
                        RtaGroupEnum.LAZADA_ORTB.getId().longValue()).contains(rtaStrategy.getRtaGroupId())) {
                    //cId = this.rtaStrategyService.getRtaStrategyCId(rtaStrategy, planTaskTempListDTO.getSlotType().equals(PlanSlotTypeEnum.VIDEO.getId()) ? AssetTypeEnum.VIDEO.getId() : AssetTypeEnum.IMG.getId());
                    cId = "__LZD_CID__";
                    if (macroMap != null) {
                        macroMap.put("__adtype__", JSONObject.parseObject(rtaGroupMacroMap.get(rtaStrategy.getRtaGroupId()).getRtaContent())
                                .get(ICommonEnum.getNameById(planTaskTempListDTO.getSlotType(), PlanSlotTypeEnum.class)));
                    }
                    if (List.of(ProjectEnum.LAZADA_RTA.getRtaGroupId(),
                            ProjectEnum.LAZADA_RTB.getRtaGroupId(),
                            RtaGroupEnum.LAZADA_ORTB.getId().longValue()).contains(rtaStrategy.getRtaGroupId())) {
                        this.rtaStrategyService.fillGoodsId(rtaStrategy);
                    }
                }
                planTaskTempListDTO.setDeeplink(StringUtils.isNotBlank(rtaStrategy.getDeeplink())
                        ? this.rtaStrategyService.replaceMacroOfUrl(rtaStrategy.getDeeplink(), macroMap, cId) : plan.getDeeplink());
                planTaskTempListDTO.setLandingUrl(StringUtils.isNotBlank(rtaStrategy.getLandingUrl())
                        ? this.rtaStrategyService.replaceMacroOfUrl(rtaStrategy.getLandingUrl(), macroMap, cId) : plan.getLandingUrl());
                planTaskTempListDTO.setMonitorViewUrl1(StringUtils.isNotBlank(rtaStrategy.getMonitorViewUrl())
                        ? this.rtaStrategyService.replaceMacroOfUrl(rtaStrategy.getMonitorViewUrl(), macroMap, cId) : plan.getMonitorViewUrl1());
                planTaskTempListDTO.setMonitorClickUrl1(StringUtils.isNotBlank(rtaStrategy.getMonitorClickUrl())
                        ? this.rtaStrategyService.replaceMacroOfUrl(rtaStrategy.getMonitorClickUrl(), macroMap, cId) : plan.getMonitorClickUrl1());
            } catch (Exception ignored) {
            }
            planTaskTempListDTO.setUnits(units.stream().peek(unit -> {
                unit.setCreativeUnitId(0L);
                unit.setCreativeUnitName("");
                unit.setCreativeUnitStatus(0);
            }).collect(Collectors.toList()));
        });
        return planTaskTempList;
    }

    /**
     * 循环填充列表
     *
     * @param taskTempList   列表数据
     * @param ids            ID集合
     * @param key            指定key
     * @param epMap          Ep映射Map
     * @param rtaStrategyMap RTA映射Map
     * @return 返回结果
     */
    private List<PlanTaskTempListDTO> loopFillTempList(List<PlanTaskTempListDTO> taskTempList, List<Long> ids, String key, Map<Long, PlanEpDTO> epMap, Map<Long, RtaStrategy> rtaStrategyMap) {

        if (ids.isEmpty()) {
            return taskTempList;
        }
        return taskTempList.isEmpty()
                ? ids.stream().map(id -> this.fillTempByKey(key, id, new PlanTaskTempListDTO(), epMap, rtaStrategyMap)).collect(Collectors.toList())
                : new ArrayList<>() {{
            ids.forEach(id -> taskTempList.forEach(entity -> add(fillTempByKey(key, id, entity, epMap, rtaStrategyMap))));
        }};
    }

    /**
     * 填充指定Key值
     *
     * @param key            指定key
     * @param id             ID
     * @param source         替换对象
     * @param epMap          Ep映射Map
     * @param rtaStrategyMap RTA映射Map
     * @return 返回结果
     */
    private PlanTaskTempListDTO fillTempByKey(String key, Long id, PlanTaskTempListDTO source, Map<Long, PlanEpDTO> epMap, Map<Long, RtaStrategy> rtaStrategyMap) {

        PlanTaskTempListDTO planTaskTempListDTO = new PlanTaskTempListDTO();
        switch (key) {
            case "ep":
                PlanEpDTO ep = epMap.get(id);
                BeanUtils.copyProperties(source, planTaskTempListDTO);
                planTaskTempListDTO.setAdxId(JSONObject.toJSONString(List.of(ep.getAdxId())));
                planTaskTempListDTO.setEpId(JSONObject.toJSONString(List.of(ep.getEpId())));
                planTaskTempListDTO.setEpName(ep.getEpName());
                planTaskTempListDTO.setTempAdxName(ep.getAdxName());
                break;
            case "rta":
                RtaStrategy rtaStrategy = rtaStrategyMap.get(id);
                BeanUtils.copyProperties(source, planTaskTempListDTO);
                planTaskTempListDTO.setRtaStrategy(JSONObject.toJSONString(new PlanRtaStrategyDTO(rtaStrategy.getRtaGroupId(), rtaStrategy.getId())));
                planTaskTempListDTO.setRtaStrategyName(rtaStrategy.getRtaStrategyName());
                planTaskTempListDTO.setTempRtaAlias(rtaStrategy.getRtaAlias());
                planTaskTempListDTO.setTempRtaCountry(rtaStrategy.getRtaCountry());
                break;
            case "slotType":
                BeanUtils.copyProperties(source, planTaskTempListDTO);
                PlanSlotTypeEnum slotTypeEnum = ICommonEnum.get(id.intValue(), PlanSlotTypeEnum.class);
                planTaskTempListDTO.setSlotType(id.intValue());
                planTaskTempListDTO.setSlotTypeName(slotTypeEnum.getName());
                planTaskTempListDTO.setTempSlotTypeName(slotTypeEnum.getName());
                break;
        }
        return planTaskTempListDTO;
    }

    /**
     * 替换计划名称规则
     *
     * @param planName            计划名称
     * @param planTaskTempListDTO DTO对象
     * @param tempPlanDirectMap   计划定向Map
     * @return 返回名称
     */
    private String replacePlanName(String planName, PlanTaskTempListDTO planTaskTempListDTO, Map<String, String> tempPlanDirectMap) {

        return planName.replace("{ADX}", StringUtils.isNotBlank(planTaskTempListDTO.getTempAdxName())
                        ? planTaskTempListDTO.getTempAdxName() : tempPlanDirectMap.getOrDefault("adx", "{ADX}"))
                .replace("{广告形式}", StringUtils.isNotBlank(planTaskTempListDTO.getTempSlotTypeName())
                        ? planTaskTempListDTO.getTempSlotTypeName() : tempPlanDirectMap.getOrDefault("slotType", "{广告形式}"))
                .replace("{RTA简称}", StringUtils.isNotBlank(planTaskTempListDTO.getTempRtaAlias())
                        ? planTaskTempListDTO.getTempRtaAlias() : tempPlanDirectMap.getOrDefault("rta", "{RTA简称}"))
                .replace("{RTA国家}", StringUtils.isNotBlank(planTaskTempListDTO.getTempRtaCountry())
                        ? planTaskTempListDTO.getTempRtaCountry() : tempPlanDirectMap.getOrDefault("rtaCountry", "{RTA国家}"))
                .replace("{出价}", tempPlanDirectMap.getOrDefault("bidType", "{出价}"))
                .replace("{优化目标}", tempPlanDirectMap.getOrDefault("optimizeTarget", "{优化目标}"))
                .replace("{定向国家}", tempPlanDirectMap.getOrDefault("areaCountry", "{定向国家}"));
    }

    @Override
    public void savePlanTask(PlanTaskSaveVO saveVO, Integer userId) {

        // 1.校验计划
        this.validPlan(saveVO);

        // 2.存储批量生成计划任务
        PlanTask planTask = new PlanTask();
        planTask.setTaskName(saveVO.getTaskName());
        planTask.setPlanCount(saveVO.getPlanList().size());
        planTask.setMasterId(saveVO.getMasterId());
        planTask.setCreateUid(userId);
        this.baseMapper.insert(planTask);

        // 3.存储任务中计划记录
        List<PlanTaskRecord> planTaskRecords = saveVO.getPlanList().stream().map(plan -> {
            PlanTaskRecord planTaskRecord = new PlanTaskRecord();
            BeanUtils.copyProperties(plan, planTaskRecord);
            planTaskRecord.setTaskId(planTask.getId());
            planTaskRecord.setTempPlanId(saveVO.getTempPlanId());
            planTaskRecord.setUnits(plan.getUnits().isEmpty() ? "" : JSONObject.toJSONString(plan.getUnits()));
            // 如果素材列表不为空
            if (CollectionUtils.isNotEmpty(plan.getUnits())) {
                MaterialAssetVO assetVO = plan.getUnits().get(0).getAssets().getAsset();
                planTaskRecord.setPlanName(planTaskRecord.getPlanName().replace("{尺寸}", assetVO.getWidth() + "x" + assetVO.getHeight()));
            }
            planTaskRecord.setCreateUid(userId);
            return planTaskRecord;
        }).collect(Collectors.toList());
        this.planTaskRecordMapper.batchSavePlanTaskRecord(planTaskRecords, userId);
    }

    @Override
    public void savePlanByTask() {

        // 1.获取记录表中待创建的前10条计划任务
        List<PlanTaskRecordDTO> planTaskRecordDTOs = this.planTaskRecordMapper.getFirstPlanTaskRecord(new QueryWrapper<PlanTaskRecord>()
                .eq("mptr.is_del", IsDelEnum.NORMAL.getId())
                .eq("mptr.task_status", PlanTaskStatusEnum.WAIT_TO_CREATE.getId())
                .orderByAsc("mptr.id")
                .last("LIMIT 10"));

        if (planTaskRecordDTOs.isEmpty()) {
            return;
        }

        planTaskRecordDTOs.forEach(this::executeSavePlanByTask);
    }

    @Override
    public List<PlanTaskCompletionListDTO> listCompletePlan(PlanTaskCompletionListVO listVO) {

        return this.baseMapper.listCompletePlan(new QueryWrapper<PlanTask>()
                        .eq("mptr.is_del", IsDelEnum.NORMAL.getId())
                        .and(q -> q.eq("mp.is_del", IsDelEnum.NORMAL.getId())
                                .or().isNull("mp.is_del"))
                        .eq(ObjectUtils.isNotNullOrZero(listVO.getId()), "mptr.task_id", listVO.getId())
                        .orderByAsc("mp.id"))
                .stream().peek(plan -> {
                    plan.setPlanStatusName(ObjectUtils.isNotNullOrZero(plan.getPlanStatus())
                            ? ICommonEnum.getNameById(plan.getPlanStatus(), PlanStatusEnum.class)
                            : ConstantUtils.PLACEHOLDER_2);
                    plan.setTaskStatusName(ICommonEnum.getNameById(plan.getTaskStatus(), PlanTaskStatusEnum.class));
                }).collect(Collectors.toList());
    }

    /**
     * 执行生成任务方法
     *
     * @param planTaskRecordDTO 任务信息
     */
    private void executeSavePlanByTask(PlanTaskRecordDTO planTaskRecordDTO) {

        log.info("task execution completed, start create plan, taskId: {}, taskRecordId: {}",
                planTaskRecordDTO.getTaskId(), planTaskRecordDTO.getId());

        // 2.将任务状态更新至创建中
        this.updatePlanTaskStatus(planTaskRecordDTO.getTaskId(), PlanTaskStatusEnum.CREATING, 0);

        // 3.根据记录表信息新建计划
        try {
            planTaskRecordDTO.setPlanId(this.savePlan(planTaskRecordDTO));
            // 新建计划完成，通知中控
            this.applicationContext.publishEvent(new CreativeUnitAuditEvent(this, planTaskRecordDTO.getPlanId()));
            log.info("successfully create plan, taskId: {}, taskRecordId: {}, planId: {}",
                    planTaskRecordDTO.getTaskId(), planTaskRecordDTO.getId(), planTaskRecordDTO.getPlanId());
        } catch (Exception exception) {
            // 创建失败，更新任务状态
            log.info("failed to create plan, taskId: {}, taskRecordId: {}, the reason is : {}",
                    planTaskRecordDTO.getTaskId(), planTaskRecordDTO.getId(), exception.getMessage());
            this.updatePlanTaskRecordStatus(planTaskRecordDTO.getId(), PlanTaskStatusEnum.CREATE_PLAN_FAIL, exception.getMessage(), null);
            this.updatePlanTask(planTaskRecordDTO.getTaskId());
            return;
        }
        this.updatePlanTaskRecordStatus(planTaskRecordDTO.getId(), PlanTaskStatusEnum.CREATE_SUCCESS, "", planTaskRecordDTO.getPlanId());

        log.info("start create creative, taskId: {}, taskRecordId: {}, planId: {}",
                planTaskRecordDTO.getTaskId(), planTaskRecordDTO.getId(), planTaskRecordDTO.getPlanId());
        // 4.根据记录表信息新建创意及创意单元
        if (StringUtils.isNotBlank(planTaskRecordDTO.getUnits())) {
            try {
                this.saveCreative(planTaskRecordDTO);
                // 新建创意完成，通知中控
                this.applicationContext.publishEvent(new CreativeUnitAuditEvent(this, planTaskRecordDTO.getPlanId()));
                log.info("successfully create creative, taskId: {}, taskRecordId: {}, planId: {}",
                        planTaskRecordDTO.getTaskId(), planTaskRecordDTO.getId(), planTaskRecordDTO.getPlanId());
            } catch (Exception exception) {
                // 创建失败，更新任务状态
                log.info("failed to create creative, taskId: {}, taskRecordId: {}, the reason is : {}",
                        planTaskRecordDTO.getTaskId(), planTaskRecordDTO.getId(), exception.getMessage());
                this.updatePlanTaskRecordStatus(planTaskRecordDTO.getId(), PlanTaskStatusEnum.CREATE_PLAN_SUCCESS_AND_CREATIVE_FAIL, exception.getMessage(), null);
                this.updatePlanTask(planTaskRecordDTO.getTaskId());
                return;
            }
        }

        // 5.更新当前任务记录状态
        this.updatePlanTaskRecordStatus(planTaskRecordDTO.getId(), PlanTaskStatusEnum.CREATE_SUCCESS, "", planTaskRecordDTO.getPlanId());

        // 6.更新当前任务状态
        this.updatePlanTask(planTaskRecordDTO.getTaskId());
        log.info("task execution completed, taskId: {}, taskRecordId: {}", planTaskRecordDTO.getTaskId(), planTaskRecordDTO.getId());
    }

    /**
     * 校验计划
     *
     * @param saveVO 传入参数
     */
    private void validPlan(PlanTaskSaveVO saveVO) {

        // 1.校验填写定向信息
        saveVO.getPlanList().forEach(plan -> {
            try {
                ValidatorUtils.validateEntity(plan);
            } catch (Exception exception) {
                throw new CustomException("计划（" + plan.getPlanName() + "）" + exception.getMessage());
            }
        });
        // 2.校验名称是否重复
        Map<Long, List<PlanTaskRecordSaveVO>> planNameMap = saveVO.getPlanList().stream().collect(Collectors.groupingBy(PlanTaskRecordSaveVO::getCampaignId));
        QueryWrapper<Plan> queryWrapper = new QueryWrapper<Plan>()
                .eq("is_del", IsDelEnum.NORMAL.getId())
                .eq("master_id", saveVO.getMasterId())
                .and(q -> planNameMap.forEach((campaignId, planList) -> {
                    List<String> planNames = planList.stream().map(PlanTaskRecordSaveVO::getPlanName).distinct().collect(Collectors.toList());
                    if (planNames.size() != planList.size()) {
                        throw new CustomException("计划名称存在重复，请确认后再试");
                    }
                    q.eq("campaign_id", campaignId).in("plan_name", planNames);
                }));
        Long count = this.planMapper.selectCount(queryWrapper);
        if (count > 0L) {
            throw new CustomException("计划名称存在重复，请确认后再试");
        }

    }

    /**
     * 组装新建计划VO
     *
     * @param planTaskRecordDTO 传入参数
     * @return 返回的计划ID
     */
    private Long savePlan(PlanTaskRecordDTO planTaskRecordDTO) {
        //获取基础用户信息
        User user = fgSystemService.getUserBaseInfo(planTaskRecordDTO.getCreateUid()).getData();
        // 1.查询计划模板
        PlanGetDTO planGetDTO = this.planService.getPlanDetail(planTaskRecordDTO.getTempPlanId(),
                planTaskRecordDTO.getMasterId().intValue(), user);

        PlanSaveVO planSaveVO = new PlanSaveVO();
        BeanUtils.copyProperties(planGetDTO, planSaveVO);
        planSaveVO.setId(0L);
        planSaveVO.setCampaignId(planTaskRecordDTO.getCampaignId());
        planSaveVO.setPlanName(planTaskRecordDTO.getPlanName());
        planSaveVO.setPlanStatus(planTaskRecordDTO.getPlanStatus());
        if (ObjectUtils.isNotNullOrZero(planTaskRecordDTO.getBudgetType())) {
            planSaveVO.setBudgetDay(planTaskRecordDTO.getBudgetDay());
        }
        if (ObjectUtils.isNotNullOrZero(planTaskRecordDTO.getBidType())) {
            planSaveVO.setBidPrice(planTaskRecordDTO.getBidPrice());
        }
        planSaveVO.setAdxId(StringUtils.isBlank(planTaskRecordDTO.getAdxId()) ? planGetDTO.getAdxId() : planTaskRecordDTO.getAdxId());
        planSaveVO.setEpId(StringUtils.isBlank(planTaskRecordDTO.getEpId()) ? planGetDTO.getEpId() : planTaskRecordDTO.getEpId());
        // 设置RTA
        PlanDirectValueVO planDirectValueVO = new PlanDirectValueVO();
        planDirectValueVO.setValue(planTaskRecordDTO.getRtaStrategy());
        planDirectValueVO.setInclude(1);
        planSaveVO.setRtaStrategy(StringUtils.isBlank(planTaskRecordDTO.getRtaStrategy()) ? planGetDTO.getRtaStrategy() : planDirectValueVO);
        planSaveVO.setSlotType(ObjectUtils.isNullOrZero(planTaskRecordDTO.getSlotType()) ? planGetDTO.getSlotType() : planTaskRecordDTO.getSlotType());
        planSaveVO.setDeeplink(planTaskRecordDTO.getDeeplink());
        planSaveVO.setLandingUrl(planTaskRecordDTO.getLandingUrl());
        planSaveVO.setMonitorViewUrl1(planTaskRecordDTO.getMonitorViewUrl1());
        planSaveVO.setMonitorViewUrl2((planTaskRecordDTO.getMonitorViewUrl2()));
        planSaveVO.setMonitorClickUrl1(planTaskRecordDTO.getMonitorClickUrl1());
        planSaveVO.setMonitorClickUrl2(planTaskRecordDTO.getMonitorClickUrl2());
        if (planSaveVO.getSlotType().equals(PlanSlotTypeEnum.INFORMATION_FLOW.getId())) {
            planSaveVO.setTemplateId(0L);
        }
        // 批量创建计划时，将广告形式置为全量
        planSaveVO.getSubSlotType().setSlotType(planSaveVO.getSlotType());
        planSaveVO.getSubSlotType().setSubSlotType(List.of());

        // 2.新建计划，返回计划ID
        return this.planService.savePlan(planSaveVO, user).getPlanId();
    }

    /**
     * 组装新建创意VO列表
     *
     * @param planTaskRecordDTO 传入参数
     */
    private void saveCreative(PlanTaskRecordDTO planTaskRecordDTO) {

        // 1.获取新计划下需要新增的创意单元
        List<MaterialVO> units = JSONObject.parseArray(planTaskRecordDTO.getUnits(), MaterialVO.class);
        // 当新计划下没有素材时，不需要新建创意及创意单元，直接返回
        if (units.isEmpty()) {
            return;
        }

        // 2.获取模板计划的创意
        Creative creative = this.creativeMapper.selectOne(new QueryWrapper<Creative>().lambda()
                .eq(Creative::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(Creative::getPlanId, planTaskRecordDTO.getTempPlanId()));

        if (creative == null) {
            throw new CustomException("计划下无创意，无法创建创意");
        }

        // 3.获取新建创意VO
        CreativeSaveVO creativeSaveVO = new CreativeSaveVO();
        BeanUtils.copyProperties(creative, creativeSaveVO);
        creativeSaveVO.setId(0L);
        creativeSaveVO.setCampaignId(planTaskRecordDTO.getCampaignId());
        creativeSaveVO.setPlanId(planTaskRecordDTO.getPlanId());
        creativeSaveVO.setUnits(units);
        this.creativeService.saveCreative(creativeSaveVO, planTaskRecordDTO.getCreateUid());
    }

    /**
     * 更新计划任务记录状态
     *
     * @param id                 ID
     * @param planTaskStatusEnum 状态
     * @param message            错误信息
     * @param planId             计划ID
     */
    private void updatePlanTaskRecordStatus(Long id, PlanTaskStatusEnum planTaskStatusEnum, String message, Long planId) {

        PlanTaskRecord planTaskRecord = new PlanTaskRecord();
        planTaskRecord.setTaskStatus(planTaskStatusEnum.getId());
        planTaskRecord.setErrorMessage(message);
        if (ObjectUtils.isNotNullOrZero(planId)) {
            planTaskRecord.setPlanId(planId);
        }
        this.planTaskRecordMapper.update(planTaskRecord, new QueryWrapper<PlanTaskRecord>().lambda()
                .eq(PlanTaskRecord::getId, id));
    }

    /**
     * 更新计划总任务记录状态
     *
     * @param id                 ID
     * @param planTaskStatusEnum 任务状态
     * @param successCount       成功条数
     */
    private void updatePlanTaskStatus(Long id, PlanTaskStatusEnum planTaskStatusEnum, Integer successCount) {

        PlanTask planTask = new PlanTask();
        planTask.setTaskStatus(planTaskStatusEnum.getId());
        if (ObjectUtils.isNotNullOrZero(successCount)) {
            planTask.setSuccessCount(successCount);
        }
        this.baseMapper.update(planTask, new QueryWrapper<PlanTask>().lambda()
                .eq(PlanTask::getId, id));
    }

    /**
     * 编辑计划任务状态
     *
     * @param taskId 任务ID
     */
    private void updatePlanTask(Long taskId) {

        // 1.获取任务下所有记录
        List<PlanTaskRecord> planTaskRecords = this.planTaskRecordMapper.selectList(new QueryWrapper<PlanTaskRecord>().lambda()
                .eq(PlanTaskRecord::getTaskId, taskId));

        // 2.根据记录状态分类
        Map<Integer, List<PlanTaskRecord>> planTaskRecordMap = planTaskRecords.stream().collect(Collectors.groupingBy(PlanTaskRecord::getTaskStatus));

        // 3.编辑任务状态
        // 如果当前任务下没有待创建的任务，说明任务下所有计划创建完成，将任务状态更新至创建完成，且更新计划成功数量
        // 如果当前任务下仍有待创建的任务，将任务状态更新至创建中，且更新计划成功数量
        this.updatePlanTaskStatus(taskId, planTaskRecordMap.get(PlanTaskStatusEnum.WAIT_TO_CREATE.getId()) == null
                        ? PlanTaskStatusEnum.CREATE_FINISH : PlanTaskStatusEnum.CREATING,
                planTaskRecordMap.getOrDefault(PlanTaskStatusEnum.CREATE_SUCCESS.getId(), List.of()).size() +
                        planTaskRecordMap.getOrDefault(PlanTaskStatusEnum.CREATE_PLAN_SUCCESS_AND_CREATIVE_FAIL.getId(), List.of()).size());
    }
}
