package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.configuration.SheinConfiguration;
import com.overseas.common.dto.market.ctx.CtxGetDTO;
import com.overseas.common.dto.market.plan.PlanUpdateRecordListDTO;
import com.overseas.common.dto.market.plan.PlanUpdateRecordOneDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.enums.market.FrequencyCycleEnum;
import com.overseas.common.enums.market.plan.PlanStatusEnum;
import com.overseas.common.enums.market.plan.PutCycleEnum;
import com.overseas.common.utils.DateUtils;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.utils.UploadUtils;
import com.overseas.common.vo.market.material.MaterialVO;
import com.overseas.common.vo.market.monitor.action.TrackerActionSelectVO;
import com.overseas.common.vo.market.plan.PlanUpdateRecordListVO;
import com.overseas.common.vo.market.plan.PlanUpdateRecordOneVO;
import com.overseas.common.vo.market.plan.direct.*;
import com.overseas.common.vo.sys.industry.IndustryListGetVO;
import com.overseas.service.market.entity.*;
import com.overseas.service.market.enums.BidTypeEnum;
import com.overseas.service.market.enums.BudgetTypeEnum;
import com.overseas.service.market.enums.plan.*;
import com.overseas.service.market.feign.FgSystemService;
import com.overseas.service.market.mapper.*;
import com.overseas.service.market.mapper.call.CallNoticeMapper;
import com.overseas.service.market.service.MaterialService;
import com.overseas.service.market.service.PlanUpdateRecordService;
import com.overseas.service.market.service.TrackerActionService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class PlanUpdateRecordServiceImpl extends ServiceImpl<PlanUpdateRecordMapper, PlanUpdateRecord> implements PlanUpdateRecordService {

    private final RtaStrategyMapper rtaStrategyMapper;

    private final RtaGroupMapper rtaGroupMapper;

    private final TagMapper tagMapper;

    private final CountryAllMapper countryAllMapper;

    private final CountryCityMapper countryCityMapper;

    private final EpMapper epMapper;

    private final AdxMapper adxMapper;

    private final FgSystemService fgSystemService;

    private final BehaviorAppMapper behaviorAppMapper;

    private final TrackerActionService trackerActionService;

    private final PlanMapper planMapper;

    private final CtxMapper ctxMapper;

    private final AssetMapper assetMapper;

    private final MaterialService materialService;

    private final SheinConfiguration sheinConfiguration;

    private final CallNoticeMapper callNoticeMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PlanUpdateRecord> savePlanUpdateRecord(List<PlanUpdateRecord> planUpdateRecords, Integer userId) {
        if (planUpdateRecords.isEmpty()) {
            return List.of();
        }
        // 处理入库字段
        List<Long> planIds = new ArrayList<>();
        planUpdateRecords.forEach(planUpdateRecord -> {
            Map<String, Object> map = new HashMap<>() {{
                put("planId", planUpdateRecord.getPlanId());
                put("content", planUpdateRecord.getContent());
            }};
            planUpdateRecord.setMd5(DigestUtils.md5Hex(JSONObject.toJSONString(map)));
            planIds.add(planUpdateRecord.getPlanId());
        });

        // 获取记录表中最新的记录
        List<PlanUpdateRecord> existPlanUpdateRecords = this.baseMapper.selectList(new QueryWrapper<PlanUpdateRecord>().lambda()
                .eq(PlanUpdateRecord::getUpdateDate, 0)
                .in(PlanUpdateRecord::getPlanId, planIds)
                .orderByAsc(PlanUpdateRecord::getId)
        );
        // 根据已有最新记录与即将入库的记录做对比；
        Map<Long, PlanUpdateRecord> existPlanUpdateRecordMd5Map = existPlanUpdateRecords.stream()
                .collect(Collectors.toMap(PlanUpdateRecord::getPlanId, Function.identity(), (o, n) -> n));
        List<PlanUpdateRecord> recordList = new ArrayList<>();
        List<Long> updateIds = new ArrayList<>();
        Map<String, List<String>> fieldMap = this.fieldMap();
        List<String> fields = this.fields(fieldMap);
        for (PlanUpdateRecord planUpdateRecord : planUpdateRecords) {
            // 1.如果当前计划下未有记录
            if (existPlanUpdateRecordMd5Map.get(planUpdateRecord.getPlanId()) == null) {
                planUpdateRecord.setRecordType(1);
                recordList.add(planUpdateRecord);
                continue;
            }
            PlanUpdateRecord existPlanUpdateRecord = existPlanUpdateRecordMd5Map.get(planUpdateRecord.getPlanId());
            // 2.如果已有记录与即将入库记录md5不相同，则新增，并把原记录update_date置为当前时间戳，标为旧纪录；并新增新纪录；
            // 如果已有记录与即将入库记录md5相同，则不新增；
            if (!existPlanUpdateRecord.getMd5().equals(planUpdateRecord.getMd5())) {
                Map<String, Object> oldMap = JSONObject.parseObject(existPlanUpdateRecord.getContent()),
                        newMap = JSONObject.parseObject(planUpdateRecord.getContent());
                this.fillValueFromOldToNew(oldMap, newMap, fields);
                // 因为编辑计划、批量编辑、快捷编辑存储的content内容不同，为了避免重复添加记录，进一步校验是否修改相关内容
                AtomicBoolean isChange = new AtomicBoolean(false);
                fieldMap.forEach((key, value) -> {
                    for (String field : value) {
                        Object newVal = newMap.get(field);
                        if (null != newVal) {
                            Object oldVal = newMap.get("old_" + field);
                            if (newVal instanceof Float || newVal instanceof Double || newVal instanceof Long || newVal instanceof Integer || newVal instanceof BigDecimal) {
                                newVal = new BigDecimal(newVal.toString()).setScale(5, RoundingMode.HALF_UP);
                                if (null != oldVal) {
                                    oldVal = new BigDecimal(oldVal.toString()).setScale(5, RoundingMode.HALF_UP);
                                }
                            }
                            // 解决设置默认值为空，展示错误问题
                            if (newVal instanceof JSONObject && oldVal == null) {
                                JSONObject newObj = (JSONObject) newVal;
                                if (newObj.containsKey("include") && newObj.containsKey("value")) {
                                    Object newObjVal = newObj.get("value");
                                    if (newObjVal instanceof String) {
                                        if ("".equals(newObjVal) || "[]".equals(newObjVal) || "{}".equals(newObjVal)) {
                                            newMap.remove(field);
                                            continue;
                                        }
                                    }
                                }
                            }
                            if (newMap.getOrDefault("old_" + key, "").equals(newMap.getOrDefault(key, ""))
                                    && newVal.equals(oldVal)) {
                                newMap.remove(field);
                            } else {
                                isChange.set(true);
                            }
                        }
                    }
                    // 如果类型下所有节点都没有，则去除类型
                    if (!this.isContainsAnyKeys(new ArrayList<>(newMap.keySet()), value)) {
                        newMap.remove(key);
                    }
                });
                // 如果未修改，则return掉
                if (!isChange.get()) {
                    continue;
                }
                planUpdateRecord.setContent(JSONObject.toJSONString(newMap));
                planUpdateRecord.setRecordType(2);
                updateIds.add(existPlanUpdateRecord.getId());
                recordList.add(planUpdateRecord);
            }
        }
        // 新增
        if (CollectionUtils.isNotEmpty(recordList)) {
            this.baseMapper.savePlanUpdateRecord(recordList, userId);
            //判定如果是shein客户修改任何内容，进行通知
            List<Plan> plans = this.planMapper.selectList(new LambdaQueryWrapper<Plan>()
                    .eq(Plan::getIsPlanPut, 0)
                    .in(Plan::getId, recordList.stream().map(PlanUpdateRecord::getPlanId).collect(Collectors.toList()))
            );
            if (CollectionUtils.isNotEmpty(plans)) {
                plans.forEach(planInDb -> callNoticeMapper.callSheinPlanUpdate(planInDb.getId()));
            }
        }
        // 编辑
        if (CollectionUtils.isNotEmpty(updateIds)) {
            PlanUpdateRecord planUpdateRecord = new PlanUpdateRecord();
            planUpdateRecord.setUpdateDate(DateUtils.string2Long(DateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss")));
            this.baseMapper.update(planUpdateRecord, new QueryWrapper<PlanUpdateRecord>().lambda()
                    .in(PlanUpdateRecord::getId, updateIds));
        }
        return recordList;
    }

    /**
     * 是否包含任何key
     *
     * @param list list
     * @param keys key
     * @return 返回数据
     */
    private Boolean isContainsAnyKeys(List<String> list, List<String> keys) {
        for (String key : keys) {
            if (list.contains(key)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 填补旧数据
     *
     * @param oldMap 旧数据
     * @param newMap 新数据
     * @param fields 字段数组
     */
    private void fillValueFromOldToNew(Map<String, Object> oldMap, Map<String, Object> newMap, List<String> fields) {
        fields.forEach(field -> newMap.put("old_" + field, oldMap.get((oldMap.containsKey(field) ? "" : "old_") + field)));
    }

    @Override
    public PageUtils<PlanUpdateRecordListDTO> listPlanUpdateRecord(PlanUpdateRecordListVO listVO, User user) {
        IPage<PlanUpdateRecordListDTO> pageData = this.baseMapper.listPlanUpdateRecord(
                new Page<>(listVO.getPage(), listVO.getPageNum()),
                new QueryWrapper<PlanUpdateRecord>()
                        .eq("mp.master_id", listVO.getMasterId())
                        .eq(ObjectUtils.isNotNullOrZero(listVO.getIsPut()),
                                "mp.is_plan_put", sheinConfiguration.isPut(listVO.getIsPut(), user.getRoleId()))
                        .eq(ObjectUtils.isNotNullOrZero(listVO.getCampaignId()),
                                "mp.campaign_id", listVO.getCampaignId())
                        .eq(ObjectUtils.isNotNullOrZero(listVO.getPlanId()),
                                "record.plan_id", listVO.getPlanId())
                        .between(StringUtils.isNotBlank(listVO.getStartDate())
                                        && StringUtils.isNotBlank(listVO.getEndDate()),
                                "record.create_time", listVO.getStartDate() + " 00:00:00", listVO.getEndDate() + " 23:59:59")
                        .eq(ObjectUtils.isNotNullOrZero(listVO.getCreateUid()), "record.create_uid", listVO.getCreateUid())
                        .orderByDesc("record.id")
        );
        Map<String, String> showMap = new HashMap<>(32) {
            {
                put("plan_name", "计划名称");
                put("plan_status", "计划状态");
                put("landing_url", "落地页");
                put("deeplink", "Deeplink");
                put("rta_strategy", "RTA策略定向");
                put("crowd_label", "人群包定向");
                put("monitor_index", "过滤转化用户定向");
                put("area_country", "地域定向");
                put("adx_id", "ADX定向");
                put("ep_id", "EP定向");
                put("ssp", "SSP定向");
                put("flow_type", "流量类型");
                put("os_type", "操作系统");
                put("bid_algo_type", "出价模式");
                put("learning_length", "学习期时长");
                put("package_report_exclude", "报表排除包名");
                put("package_price_report_exclude", "报表包名浮动出价");
                put("monitor_view_url1", "曝光监测1");
                put("monitor_view_url2", "曝光监测2");
                put("monitor_click_url1", "点击监测1");
                put("monitor_click_url2", "点击监测2");
                put("creative_logo", "创意品牌形象");
                put("creative_brand", "创意品牌名称");
                put("creative_app_icon", "创意应用ICON");
                put("creative_source", "创意来源");
                put("creative_cta_id", "创意行动号召");
                put("hours", "投放时段");
                put("creative_units", "创意素材内容");
                put("budget_day", "日预算");
                put("budget_hour", "小时预算");
                put("bid_price", "出价");
                put("put_cycle", "投放周期");
                put("deep_bid_status", "深度出价");
                put("flow_detection_state", "流量探测");
                put("package_include", "包名");
                put("industry_id", "行业分类");
                put("frequency_cycle_view", "曝光频次");
                put("frequency_cycle_click", "点击频次");
                put("consume_rate", "投放速率");
                put("next_budget_type", "次日预算");
            }
        };
        //根据权限过滤展示
        List<String> include = this.getPermissionByUserId(user.getId());
        if (CollectionUtils.isNotEmpty(include)) {
            showMap = showMap.entrySet().stream().filter(entry -> include.contains(entry.getKey())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }
        for (PlanUpdateRecordListDTO record : pageData.getRecords()) {
            if (1 == record.getRecordType()) {
                record.setContent("计划创建");
            } else {
                record.setContent(this.dealContent(record.getContent(), showMap, user, record.getMasterId())
                        .replaceAll("___RECORD_ID___", record.getId().toString()));
            }
            record.setDay(DateUtils.date2Long(DateUtils.string2Date(record.getDate(), "yyyy-MM-dd HH")));
        }
        return new PageUtils<>(pageData);
    }

    @Override
    public PlanUpdateRecordOneDTO oneInfo(PlanUpdateRecordOneVO oneVO) {
        PlanUpdateRecord planUpdateRecord = this.baseMapper.selectById(oneVO.getRecordId());
        JSONObject contentMap = JSONObject.parseObject(planUpdateRecord.getContent());
        PlanUpdateRecordOneDTO oneDTO = new PlanUpdateRecordOneDTO();
        if (contentMap.containsKey(oneVO.getType())) {
            oneDTO.setNewVal(contentMap.get(oneVO.getType()).toString());
        }
        if (contentMap.containsKey("old_" + oneVO.getType())) {
            oneDTO.setOldVal(contentMap.get("old_" + oneVO.getType()).toString());
        }
        oneDTO.setType(oneVO.getType());
        switch (oneDTO.getType()) {
            case "creative_logo":
            case "creative_app_icon":
                List<String> ids = Stream.of(oneDTO.getNewVal(), oneDTO.getOldVal()).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                if (ids.isEmpty()) {
                    return oneDTO;
                }
                Map<String, String> assetMap = assetMapper.selectBatchIds(ids).stream().collect(Collectors.toMap(u -> u.getId().toString(), v -> UploadUtils.getHttpUrl(v.getContent())));
                oneDTO.setOldVal(assetMap.get(oneDTO.getOldVal()));
                oneDTO.setNewVal(assetMap.get(oneDTO.getNewVal()));
                return oneDTO;
            case "hours":
                if (null == oneDTO.getOldVal()) {
                    oneDTO.setOldVal("0");
                }
                return oneDTO;
            case "creative_units":
                Map<Long, CreativeUnitUpdateVO> oldUnits = new HashMap<>();
                Map<Long, CreativeUnitUpdateVO> newUnits = new HashMap<>();
                if (StringUtils.isNotBlank(oneDTO.getOldVal())) {
                    oldUnits = JSONObject.parseArray(oneDTO.getOldVal(), CreativeUnitUpdateVO.class).stream().collect(Collectors.toMap(CreativeUnitUpdateVO::getCreativeUnitId, Function.identity()));
                }
                if (StringUtils.isNotBlank(oneDTO.getNewVal())) {
                    newUnits = JSONObject.parseArray(oneDTO.getNewVal(), CreativeUnitUpdateVO.class).stream().collect(Collectors.toMap(CreativeUnitUpdateVO::getCreativeUnitId, Function.identity()));
                }
                oneDTO.setNewVales(new ArrayList<>());
                for (Long key : newUnits.keySet()) {
                    CreativeUnitUpdateVO oldInfo = oldUnits.get(key);
                    CreativeUnitUpdateVO newInfo = newUnits.get(key);
                    if (oldInfo == null) {
                        oneDTO.getNewVales().add(new PlanUpdateRecordOneDTO.CreativeUnitUpdateDTO(newInfo, true));
                    } else if (!oldInfo.getIsDel().equals(newInfo.getIsDel()) || !oldInfo.getCreativeUnitStatus().equals(newInfo.getCreativeUnitStatus())) {
                        oneDTO.getNewVales().add(new PlanUpdateRecordOneDTO.CreativeUnitUpdateDTO(newInfo, oldInfo.getCreativeUnitStatus()));
                    }
                }
                List<Long> unitIds = oneDTO.getNewVales().stream().map(CreativeUnitUpdateVO::getCreativeUnitId).collect(Collectors.toList());
                if (!unitIds.isEmpty()) {
                    Plan plan = planMapper.selectById(planUpdateRecord.getPlanId());
                    Map<Long, MaterialVO> unitMap = this.materialService.getMaterialByUnitIds(unitIds, plan.getMasterId().longValue())
                            .stream().collect(Collectors.toMap(MaterialVO::getCreativeUnitId, Function.identity()));
                    oneDTO.getNewVales().forEach(n -> n.setUnitInfo(unitMap.get(n.getCreativeUnitId())));
                }
                return oneDTO;
            default:
                return null;
        }
    }

    /**
     * 处理内容
     *
     * @param content  内容
     * @param showMap  map
     * @param user     用户
     * @param masterId 账户ID
     * @return 返回数据
     */
    private String dealContent(String content, Map<String, String> showMap, User user, Integer masterId) {
        Map<String, Object> contentMap = JSONObject.parseObject(content);
        List<String> contentList = new ArrayList<>();
        for (String key : showMap.keySet().stream().sorted().collect(Collectors.toList())) {
            String name = showMap.get(key);
            if (List.of("budget_day", "budget_hour", "bid_price", "put_cycle", "deep_bid_status", "flow_detection_state", "learning_length", "package_include",
                            "frequency_cycle_view", "frequency_cycle_click", "next_budget_type")
                    .contains(key)) {
                // 日预算
                if ("budget_day".equals(key) && contentMap.get("budget_day") != null) {
                    contentList.add(this.getContentStr("budget_type", "日预算", contentMap.get("old_budget_type"), contentMap.get("budget_type"),
                            contentMap.get("old_budget_day"), contentMap.get("budget_day")));
                }
                // 小时预算
                if ("budget_hour".equals(key) && contentMap.get("budget_hour") != null) {
                    contentList.add(this.getContentStr("budget_type", "小时预算", contentMap.get("old_budget_type"), contentMap.get("budget_type"),
                            contentMap.get("old_budget_hour"), contentMap.get("budget_hour")));
                }
                // 次日预算
                if ("next_budget_type".equals(key) && contentMap.get("next_budget_day") != null) {
                    contentList.add(this.getContentStr("next_budget_type", "次日预算", contentMap.get("old_next_budget_type"), contentMap.get("next_budget_type"),
                            contentMap.get("old_next_budget_day"), contentMap.get("next_budget_day")));
                }
                // 出价
                if ("bid_price".equals(key) && contentMap.get("bid_price") != null) {
                    if (sheinConfiguration.isRole(user.getRoleId())) {
                        contentList.add(this.getContentStr("bid_type", "出价", contentMap.get("old_bid_type"), contentMap.get("bid_type"),
                                contentMap.get("old_bid_price"), contentMap.get("bid_price"), -1001));
                    } else {
                        contentList.add(this.getContentStr("bid_type", "出价", contentMap.get("old_bid_type"), contentMap.get("bid_type"),
                                contentMap.get("old_bid_price"), contentMap.get("bid_price")));
                    }
                }
                // 投放日期
                if ("put_cycle".equals(key) && (contentMap.get("put_cycle") != null || contentMap.get("start_date") != null || contentMap.get("end_date") != null)) {
                    contentList.add(this.getContentStr("put_cycle", "投放周期",
                                    contentMap.get("old_put_cycle"), contentMap.get("put_cycle"),
                                    contentMap.get("old_start_date") == null ? null : String.format("%s~%s", contentMap.get("old_start_date"), contentMap.get("old_end_date")),
                                    String.format("%s~%s", null == contentMap.get("start_date") ? contentMap.get("old_start_date") : contentMap.get("start_date"),
                                            null == contentMap.get("end_date") ? contentMap.get("old_end_date") : contentMap.get("end_date")
                                    )
                            )
                    );
                }
                // 如果改变了深度出价
                if ("deep_bid_status".equals(key) && contentMap.get("deep_bid_status") != null) {
                    contentList.add(this.getContentStr("deep_bid_status", "深度出价", contentMap.get("old_deep_bid_status"),
                            contentMap.get("deep_bid_status"), contentMap.get("old_deep_bid_price"), contentMap.get("deep_bid_price")));
                }
                // 如果改变了流量探测
                if ("flow_detection_state".equals(key) && contentMap.get("flow_detection_state") != null) {
                    contentList.add(this.getContentStr("flow_detection_state", "流量探测", contentMap.get("old_flow_detection_state"),
                            contentMap.get("flow_detection_state"), contentMap.get("old_budget_learning_rate"), contentMap.get("budget_learning_rate")));
                }
                // 如果改变了学习期时长
                if ("learning_length".equals(key) && contentMap.get("learning_length") != null) {
                    contentList.add(this.getContentStr("learning_length", "学习期时长", contentMap.get("old_learning_length"),
                            contentMap.get("learning_length"), contentMap.get("old_learning_length"), contentMap.get("learning_length")));
                }
                if ("package_include".equals(key) && contentMap.get("package_include") != null) {
                    contentList.add(this.getContentStr("package_include", "包名", contentMap.get("old_package_include"),
                            contentMap.get("package_include"), contentMap.get("old_package_value"), contentMap.get("package_value")));
                }
                // 曝光频次
                if ("frequency_cycle_view".equals(key) && contentMap.get("frequency_cycle_view") != null) {
                    contentList.add(this.getContentStr("frequency_cycle_view", "曝光频次", contentMap.get("old_frequency_cycle_view"), contentMap.get("frequency_cycle_view"),
                            contentMap.get("old_frequency_num_view"), contentMap.get("frequency_num_view")));
                }
                // 点击频次
                if ("frequency_cycle_click".equals(key) && contentMap.get("frequency_cycle_click") != null) {
                    contentList.add(this.getContentStr("frequency_cycle_click", "点击频次", contentMap.get("old_frequency_cycle_click"), contentMap.get("frequency_cycle_click"),
                            contentMap.get("old_frequency_num_click"), contentMap.get("frequency_num_click")));
                }
            } else if (List.of("hours", "creative_units", "creative_app_icon", "creative_logo").contains(key)) {
                if (contentMap.containsKey(key)) {
                    contentList.add(name + "修改，具体点击<a href='' data-type='" + key + "' data-id='___RECORD_ID___' class='planUpdateInfo'>详情</a >");
                }
            } else if (contentMap.containsKey(key)) {
                String oldKey = String.format("old_%s", key);
                contentList.add(this.getContentStr(key, name, contentMap.get(oldKey), contentMap.get(key), contentMap.get(oldKey), contentMap.get(key), masterId));
            }
        }
        StringBuilder stringBuilder = new StringBuilder();
        AtomicInteger index = new AtomicInteger(1);
        for (int i = 0; i < contentList.size(); i++) {
            if (StringUtils.isBlank(contentList.get(i))) {
                continue;
            }
            stringBuilder.append(index.getAndIncrement()).append("、").append(contentList.get(i)).append("</br>");
        }
        return stringBuilder.toString();
    }

    /**
     * 获取内容
     *
     * @param type     类型
     * @param typeName 类型名称
     * @param oldType  老类型
     * @param newType  新类型
     * @param oldValue 老数据
     * @param newValue 新数据
     * @return 返回字段
     */
    private String getContentStr(String type, String typeName, Object oldType, Object newType, Object oldValue, Object newValue) {
        return this.getContentStr(type, typeName, oldType, newType, oldValue, newValue, 0);
    }

    /**
     * 获取内容
     *
     * @param type     类型
     * @param typeName 类型名称
     * @param oldType  老类型
     * @param newType  新类型
     * @param oldValue 老数据
     * @param newValue 新数据
     * @return 返回字段
     */
    private String getContentStr(String type, String typeName, Object oldType, Object newType, Object oldValue, Object newValue, Integer masterId) {
        switch (type) {
            case "budget_type":
            case "next_budget_type":
                BudgetTypeEnum newBudgetTypeEnum = ICommonEnum.get(Integer.parseInt(newType.toString()), BudgetTypeEnum.class);
                if (oldValue == null) {
                    return typeName + "设置为 " + (newValue.equals(0) ? "不限" : (null == newBudgetTypeEnum ? newValue + BudgetTypeEnum.COST.getUnit() : newBudgetTypeEnum.getName() + newValue + newBudgetTypeEnum.getUnit()));
                }
                BudgetTypeEnum oldBudgetTypeEnum = ICommonEnum.get(Integer.parseInt(oldType.toString()), BudgetTypeEnum.class);
                return typeName + "由原 " +
                        (oldValue.equals(0) ? "不限" :
                                (null == oldBudgetTypeEnum ? oldValue + BudgetTypeEnum.COST.getUnit() : oldBudgetTypeEnum.getName() + oldValue + oldBudgetTypeEnum.getUnit())) +
                        " 调整至 " +
                        (newValue.equals(0) ? "不限" : (null == newBudgetTypeEnum ? newValue + BudgetTypeEnum.COST.getUnit() : newBudgetTypeEnum.getName() + newValue + newBudgetTypeEnum.getUnit()));
            case "bid_type":
                BidTypeEnum newBidTypeEnum = ICommonEnum.get(Integer.parseInt(newType.toString()), BidTypeEnum.class);
                if (oldValue == null) {
                    return typeName + "设置为 " + newValue + "/" + (-1001 == masterId ? "CPA" : newBidTypeEnum.getName());
                }
                BidTypeEnum oldBidTypeEnum = ICommonEnum.get(Integer.parseInt(oldType.toString()), BidTypeEnum.class);
                return "出价由原 " + oldValue + "/" + (-1001 == masterId ? "CPA" : oldBidTypeEnum.getName()) +
                        " 调整至 " + newValue + "/" + (-1001 == masterId ? "CPA" : newBidTypeEnum.getName());
            case "deep_bid_status":
                // 如果深度出价状态发生变化，即打开深度出价
                if ((oldType == null && newType != null) || !Objects.equals(oldType, newType)) {
                    if (newType.toString().equals(PlanDeepBidStatusEnum.OPENED.getId().toString())) {
                        return "开启深度出价，深度出价为" + newValue;
                    }
                    return "关闭" + typeName;
                } else {
                    return typeName + "由原 " + oldValue + " 调整至 " + newValue;
                }
            case "bid_algo_type":
                if ("0".equals(newValue.toString())) {
                    return null;
                }
                if (oldValue == null) {
                    return typeName + "设置为 " + ICommonEnum.getNameById(Integer.parseInt(newValue.toString()), PlanBidAlgoTypeEnum.class);
                }
                return typeName + "由原 " + ICommonEnum.getNameById((Integer) oldValue, PlanBidAlgoTypeEnum.class) + " 调整至 " +
                        ICommonEnum.getNameById((Integer) newValue, PlanBidAlgoTypeEnum.class);
            case "flow_detection_state":
                PlanFlowDetectionStateEnum planFlowDetectionStateEnum = ICommonEnum.get(Integer.parseInt(newType.toString()), PlanFlowDetectionStateEnum.class);
                if (null == planFlowDetectionStateEnum) {
                    return null;
                }
                if (oldValue == null) {
                    if (planFlowDetectionStateEnum.getId().equals(PlanFlowDetectionStateEnum.CLOSED.getId())) {
                        return "流量探测设置为 关闭";
                    } else {
                        return "流量探测设置为 开启，学习期预算占比设置为 " + newValue + "%";
                    }
                }
                // 如果新状态为开启
                if (planFlowDetectionStateEnum.getId().equals(PlanFlowDetectionStateEnum.OPENED.getId())) {
                    if (newType.equals(oldType)) {
                        return "流量探测学习期预算占比由原 " + oldValue + "% 调整至 " + newValue + "%";
                    } else {
                        return "流量探测设置为 开启，学习期预算占比设置为 " + newValue + "%";
                    }
                }
                return "流量探测设置为 关闭";
            case "learning_length":
                if ("0".equals(newValue.toString())) {
                    return null;
                }
                if (oldValue == null) {
                    return typeName + "设置为 " + Integer.parseInt(newValue.toString());
                }
                return typeName + "由原 " + Integer.parseInt(oldValue.toString()) + " 调整至 " +
                        Integer.parseInt(newValue.toString());
            case "plan_status":
                PlanStatusEnum newPlanStatusEnum = ICommonEnum.get(Integer.parseInt(newType.toString()), PlanStatusEnum.class);
                if (null == oldValue) {
                    return typeName + "设置为 " + newPlanStatusEnum.getName();
                }
                PlanStatusEnum oldPlanStatusEnum = ICommonEnum.get(Integer.parseInt(oldType.toString()), PlanStatusEnum.class);
                return typeName + "由原 " + oldPlanStatusEnum.getName() + " 修改为 " + newPlanStatusEnum.getName();
            case "package_include":
                PlanDirectIncludeEnum planDirectIncludeEnum = ICommonEnum.get(Integer.parseInt(newType.toString()), PlanDirectIncludeEnum.class);
                List<String> packages = JSONObject.parseArray(newValue.toString(), String.class);
                return typeName + "设置为 " + planDirectIncludeEnum.getName() + (CollectionUtils.isNotEmpty(packages)
                        ? "[" + Strings.join(packages, '、') + "]" : "不限");
            case "package_report_exclude":
                List<String> excludePackages = JSONObject.parseArray(newValue.toString(), String.class);
                return typeName + "设置为 " + (CollectionUtils.isNotEmpty(excludePackages) ? "[" + Strings.join(excludePackages, '、') + "]" : "未设置");
            case "package_price_report_exclude":
                Map<String, Double> excludePackagePrices = JSONObject.parseObject(newValue.toString(), new TypeReference<>() {
                });
                List<String> values = new ArrayList<>();
                excludePackagePrices.forEach((key, value) -> {
                    values.add(key + "价格浮动值：" + value);
                });
                return typeName + "设置为 " + (!values.isEmpty()
                        ? String.join("，", values) : "未设置");
            case "plan_name":
            case "creative_brand":
            case "creative_source":
                if (null == oldValue) {
                    return typeName + "设置为 " + newValue;
                }
                return typeName + "由原 " + oldValue + " 修改为 " + newValue;
            case "landing_url":
            case "deeplink":
            case "monitor_view_url1":
            case "monitor_view_url2":
            case "monitor_click_url1":
            case "monitor_click_url2":
                if (null == oldValue) {
                    return typeName + "设置为 " + (StringUtils.isEmpty(newValue.toString()) ? "空" : newValue);
                }
                return typeName + "由原 " + (StringUtils.isEmpty(oldValue.toString()) ? "空" : oldValue) +
                        " 修改为 " + (StringUtils.isEmpty(newValue.toString()) ? "空" : newValue);
            case "rta_strategy":
                List<Long> rtaStrategyIds = new ArrayList<>();
                List<Long> rtaStrategyPids = new ArrayList<>();
                RtaStrategyVO oldInfo = null;
                if (null != oldValue && StringUtils.isNotBlank(oldValue.toString())) {
                    PlanDirectValueVO oldRta = JSONObject.parseObject(oldValue.toString(), PlanDirectValueVO.class);
                    if (StringUtils.isNotBlank(oldRta.getValue()) && !"{}".equals(oldRta.getValue())) {
                        oldInfo = JSONObject.parseObject(oldRta.getValue(), RtaStrategyVO.class);
                        rtaStrategyPids.add(oldInfo.getRtaStrategyPId());
                        rtaStrategyIds.add(oldInfo.getRtaStrategyId());
                    }
                }
                RtaStrategyVO newInfo = null;
                if (null != newValue && StringUtils.isNotBlank(newValue.toString())) {
                    PlanDirectValueVO newRta = JSONObject.parseObject(newValue.toString(), PlanDirectValueVO.class);
                    if (StringUtils.isNotBlank(newRta.getValue()) && !"{}".equals(newRta.getValue())) {
                        newInfo = JSONObject.parseObject(newRta.getValue(), RtaStrategyVO.class);
                        rtaStrategyPids.add(newInfo.getRtaStrategyPId());
                        rtaStrategyIds.add(newInfo.getRtaStrategyId());
                    }
                }
                //如果未设置，展示空
                if (null == oldInfo && null == newInfo) {
                    return "";
                }
                Map<Long, String> rtaNameMap = rtaStrategyMapper.selectBatchIds(rtaStrategyIds).stream()
                        .collect(Collectors.toMap(RtaStrategy::getId, RtaStrategy::getRtaStrategyName));
                Map<Long, String> rtaGroupNameMap = rtaGroupMapper.selectBatchIds(rtaStrategyPids).stream()
                        .collect(Collectors.toMap(RtaGroup::getId, RtaGroup::getRtaGroupName));
                if (null == newInfo) {
                    return typeName + "由原 " + this.parseRtaStrategyName(oldInfo.getRtaStrategyPId(), oldInfo.getRtaStrategyId(), rtaNameMap, rtaGroupNameMap) +
                            " 修改为 无";
                }
                if (null == oldInfo) {
                    return typeName + "设置为 " + this.parseRtaStrategyName(newInfo.getRtaStrategyPId(), newInfo.getRtaStrategyId(), rtaNameMap, rtaGroupNameMap);
                }
                return typeName + "由原 " + this.parseRtaStrategyName(oldInfo.getRtaStrategyPId(), oldInfo.getRtaStrategyId(), rtaNameMap, rtaGroupNameMap) +
                        " 修改为 " + this.parseRtaStrategyName(newInfo.getRtaStrategyPId(), newInfo.getRtaStrategyId(), rtaNameMap, rtaGroupNameMap);
            case "crowd_label":
                List<Long> crowdIds = new ArrayList<>();
                PlanDirectIncludeVO oldCrowd;
                List<Long> oldInclude = null, oldExclude = null;
                if (null != oldValue) {
                    oldCrowd = JSONObject.parseObject(oldValue.toString(), PlanDirectIncludeVO.class);
                    oldInclude = this.parseDirectIds(oldCrowd.getInclude());
                    oldExclude = this.parseDirectIds(oldCrowd.getExclude());
                    crowdIds.addAll(oldInclude);
                    crowdIds.addAll(oldExclude);
                }
                PlanDirectIncludeVO newCrowd = JSONObject.parseObject(newValue.toString(), PlanDirectIncludeVO.class);
                List<Long> newInclude = this.parseDirectIds(newCrowd.getInclude());
                List<Long> newExclude = this.parseDirectIds(newCrowd.getExclude());
                crowdIds.addAll(newInclude);
                crowdIds.addAll(newExclude);
                Map<Long, String> crowdNameMap = this.findListNameMap(crowdIds, type);
                if (CollectionUtils.isNotEmpty(oldExclude) || CollectionUtils.isNotEmpty(oldInclude)) {
                    return typeName + "由原 " + parseCrowdName(oldInclude, oldExclude, crowdNameMap) + " 修改为 " + parseCrowdName(newInclude, newExclude, crowdNameMap);
                }
                return typeName + "设置为 " + parseCrowdName(newInclude, newExclude, crowdNameMap);
            case "area_country":
                List<Long> areaIds = new ArrayList<>();
                PlanDirectValueVO oldArea = null;
                List<Long> oldAreaIds = new ArrayList<>();
                if (null != oldValue) {
                    oldArea = JSONObject.parseObject(oldValue.toString(), PlanDirectValueVO.class);
                    if (StringUtils.isNotBlank(oldArea.getValue()) && !"[]".equals(oldArea.getValue())) {
                        oldAreaIds = JSONObject.parseArray(oldArea.getValue(), Long.class);
                        areaIds.addAll(oldAreaIds);
                    }
                }
                PlanDirectValueVO newArea = JSONObject.parseObject(newValue.toString(), PlanDirectValueVO.class);
                List<Long> newAreaIds = new ArrayList<>();
                if (StringUtils.isNotBlank(newArea.getValue()) && !"[]".equals(newArea.getValue())) {
                    newAreaIds = JSONObject.parseArray(newArea.getValue(), Long.class);
                    areaIds.addAll(newAreaIds);
                }
                Map<Long, String> areaNameMap = this.findListNameMap(areaIds, type);
                if (CollectionUtils.isNotEmpty(oldAreaIds)) {
                    return typeName + "由原 " + this.parseAreaName(oldAreaIds, oldArea, areaNameMap) + " 修改为 " + this.parseAreaName(newAreaIds, newArea, areaNameMap);
                }
                return typeName + "设置为 " + this.parseAreaName(newAreaIds, newArea, areaNameMap);
            case "industry_id":
                if (null == oldValue) {
                    return typeName + "设置为 " + fgSystemService.getIndustryName(IndustryListGetVO.builder().ids(JSONObject.parseArray(newValue.toString(), Long.class)).build()).getData();
                }
                return typeName + "由原 " + fgSystemService.getIndustryName(IndustryListGetVO.builder().ids(JSONObject.parseArray(oldValue.toString(), Long.class)).build()).getData() +
                        " 修改为 " + fgSystemService.getIndustryName(IndustryListGetVO.builder().ids(JSONObject.parseArray(newValue.toString(), Long.class)).build()).getData();
            case "adx_id":
            case "ep_id":
                List<Long> infoIds = new ArrayList<>();
                List<Long> oldList = null;
                if (null != oldValue) {
                    oldList = JSONObject.parseArray(oldValue.toString(), Long.class);
                    infoIds.addAll(oldList);
                }
                List<Long> newList = JSONObject.parseArray(newValue.toString(), Long.class);
                infoIds.addAll(newList);
                Map<Long, String> listNameMap = this.findListNameMap(infoIds, type);
                if (null == oldList) {
                    return typeName + "设置为 [" + newList.stream().map(listNameMap::get).filter(Objects::nonNull).collect(Collectors.joining("，")) + "]";
                }
                return typeName + "由原 [" + oldList.stream().map(listNameMap::get).filter(Objects::nonNull).collect(Collectors.joining("，")) +
                        "] 修改为 [" + newList.stream().map(listNameMap::get).filter(Objects::nonNull).collect(Collectors.joining("，")) + "]";
            case "ssp":
                PlanDirectValueVO newObj = JSONObject.parseObject(newValue.toString(), PlanDirectValueVO.class);
                PlanDirectValueVO oldObj = null;
                if (null == oldValue) {
                    return typeName + "设置为 " + ICommonEnum.getNameById(newObj.getInclude(), PlanDirectIncludeEnum.class) + " " + sspValue(newObj.getValue());
                }
                oldObj = JSONObject.parseObject(oldValue.toString(), PlanDirectValueVO.class);
                return typeName + "由原 " + ICommonEnum.getNameById(oldObj.getInclude(), PlanDirectIncludeEnum.class) + " " + sspValue(oldObj.getValue()) +
                        " 修改为 " + ICommonEnum.getNameById(newObj.getInclude(), PlanDirectIncludeEnum.class) + " " + sspValue(newObj.getValue());
            case "flow_type":
            case "os_type":
                Map<Integer, String> intNameMap = this.findIntNameMap(type);
                if (null == oldValue) {
                    return typeName + "设置为 " + intNameMap.get(Integer.parseInt(newValue.toString()));
                }
                return typeName + "由原 " + intNameMap.get(Integer.parseInt(oldValue.toString())) + " 修改为 " + intNameMap.get(Integer.parseInt(newValue.toString()));
            case "monitor_index":
                PlanDirectValueVO oldIndex;
                MonitorIndexVO oldIndexVO = null;
                if (null != oldValue) {
                    oldIndex = JSONObject.parseObject(oldValue.toString(), PlanDirectValueVO.class);
                    if (StringUtils.isNotBlank(oldIndex.getValue()) && !"{}".equals(oldIndex.getValue())) {
                        oldIndexVO = JSONObject.parseObject(oldIndex.getValue(), MonitorIndexVO.class);
                    }
                }
                PlanDirectValueVO newIndex = JSONObject.parseObject(newValue.toString(), PlanDirectValueVO.class);
                MonitorIndexVO newIndexVO = null;
                if (StringUtils.isNotBlank(newIndex.getValue()) && !"{}".equals(newIndex.getValue())) {
                    newIndexVO = JSONObject.parseObject(newIndex.getValue(), MonitorIndexVO.class);
                }
                Map<String, String> actionNameMap = trackerActionService.getActionSelect(TrackerActionSelectVO.builder().masterId(masterId).build())
                        .stream().collect(Collectors.toMap(u -> u.getKey().toString(), v -> v.getTitle().toString()));
                Map<Long, String> appNameMap = behaviorAppMapper.selectList(new LambdaQueryWrapper<BehaviorApp>().eq(BehaviorApp::getIsDel, IsDelEnum.NORMAL.getName()))
                        .stream().collect(Collectors.toMap(BehaviorApp::getId, BehaviorApp::getAppName));
                if (null == oldIndexVO || oldIndexVO.getCycle() == 0) {
                    return typeName + "设置为 " + this.parseMonitorIndex(newIndexVO, actionNameMap, appNameMap);
                }
                return typeName + "由" + this.parseMonitorIndex(oldIndexVO, actionNameMap, appNameMap) +
                        " 修改为 " + this.parseMonitorIndex(newIndexVO, actionNameMap, appNameMap);
            case "put_cycle":
                PutCycleEnum newPutCycle = ICommonEnum.get(Integer.parseInt(newType.toString()), PutCycleEnum.class);
                if (null == oldValue) {
                    return typeName + "设置为 " + (PutCycleEnum.ALL_TIME.equals(newPutCycle) ? "不限" : newPutCycle.getName() + ":" + newValue);
                }
                PutCycleEnum oldPutType = ICommonEnum.get(Integer.parseInt(oldType.toString()), PutCycleEnum.class);
                return typeName + "由原 " + (PutCycleEnum.ALL_TIME.equals(oldPutType) ? "不限" : oldPutType.getName() + ":" + oldValue) +
                        " 修改为 " + (PutCycleEnum.ALL_TIME.equals(newPutCycle) ? "不限" : newPutCycle.getName() + ":" + newValue);
            case "creative_cta_id":
                if (null == oldValue) {
                    CtxGetDTO newCtx = ctxMapper.getCtx(new QueryWrapper<Ctx>().eq("ctx.id", newValue));
                    return typeName + "设置为 " + String.format("%s（%s）", newCtx.getCtxName(), newCtx.getCountryName());
                }
                Map<String, CtxGetDTO> ctxMap = ctxMapper.listCtx(new QueryWrapper<Ctx>()
                        .in("ctx.id", List.of(Long.parseLong(oldValue.toString()), Long.parseLong(newValue.toString())))
                ).stream().collect(Collectors.toMap(u -> u.getId().toString(), Function.identity()));
                CtxGetDTO oldCtx = ctxMap.get(oldValue.toString());
                CtxGetDTO newCtx = ctxMap.get(newValue.toString());
                return typeName + "由" + String.format("%s（%s）", oldCtx.getCtxName(), oldCtx.getCountryName()) +
                        " 修改为 " + String.format("%s（%s）", newCtx.getCtxName(), newCtx.getCountryName());
            case "frequency_cycle_view":
            case "frequency_cycle_click":
                FrequencyCycleEnum newFrequencyCycleEnum = ICommonEnum.get(Integer.parseInt(newType.toString()), FrequencyCycleEnum.class);
                if (oldValue == null) {
                    return typeName + "设置为 " + (newValue.equals(0) ? "不限" : (newValue + "/" + newFrequencyCycleEnum.getName()));
                }
                FrequencyCycleEnum oldFrequencyCycleEnum = ICommonEnum.get(Integer.parseInt(oldType.toString()), FrequencyCycleEnum.class);
                return typeName + "由原 " + (oldValue.equals(0) ? "不限" : (oldValue + "/" + oldFrequencyCycleEnum.getName())) + " 修改为 " +
                        (newValue.equals(0) ? "不限" : (newValue + "/" + newFrequencyCycleEnum.getName()));
            case "consume_rate":
                ConsumeRateEnum newRateEnum = ICommonEnum.get(Integer.parseInt(newValue.toString()), ConsumeRateEnum.class);
                if (null == oldValue) {
                    return typeName + "设置为 " + newRateEnum.getName();
                }
                ConsumeRateEnum oldRateEnum = ICommonEnum.get(Integer.parseInt(oldValue.toString()), ConsumeRateEnum.class);
                return typeName + "由原 " + oldRateEnum.getName() + " 修改为 " + newRateEnum.getName();
            default:
                return "";
        }

    }

    /**
     * 解析 定向ID
     *
     * @param info 内容
     * @return 返回定向ID
     */
    private List<Long> parseDirectIds(String info) {
        if (StringUtils.isNotBlank(info) && !"[]".equals(info)) {
            return JSONObject.parseArray(info, Long.class);
        }
        return new ArrayList<>();
    }

    /**
     * 返回数据
     *
     * @param include include
     * @param exclude exclude
     * @param nameMap nameMap
     * @return 返回字符串
     */
    private String parseCrowdName(List<Long> include, List<Long> exclude, Map<Long, String> nameMap) {
        StringBuilder stringBuilder = new StringBuilder();
        if (!include.isEmpty()) {
            stringBuilder.append("定向【").append(include.stream().map(nameMap::get).collect(Collectors.joining(","))).append("】，");
        }
        if (!exclude.isEmpty()) {
            stringBuilder.append("排除【").append(exclude.stream().map(nameMap::get).collect(Collectors.joining(","))).append("】，");
        }
        String res = stringBuilder.toString();
        if (StringUtils.isBlank(res)) {
            return "不限";
        }
        return res.substring(0, res.length() - 1);
    }

    /**
     * 地域名称
     *
     * @param areaIds 地域列表
     * @param area    地域
     * @param nameMap 名称map
     * @return 返回数据
     */
    private String parseAreaName(List<Long> areaIds, PlanDirectValueVO area, Map<Long, String> nameMap) {
        if (CollectionUtils.isEmpty(areaIds)) {
            return "不限";
        }
        PlanDirectIncludeEnum includeEnum = ICommonEnum.get(area.getInclude(), PlanDirectIncludeEnum.class);
        return includeEnum.getName() + "【" + areaIds.stream().map(nameMap::get).collect(Collectors.joining(",")) + "】";
    }

    /**
     * 解析rta策略
     *
     * @param pid             rta pid
     * @param id              rta id
     * @param rtaNameMap      rta name map
     * @param rtaGroupNameMap rta group name map
     * @return 返回数据
     */
    private String parseRtaStrategyName(Long pid, Long id, Map<Long, String> rtaNameMap, Map<Long, String> rtaGroupNameMap) {
        if (ObjectUtils.isNullOrZero(pid)) {
            return "不限";
        }
        return rtaGroupNameMap.get(pid) + "下" + rtaNameMap.get(id);
    }

    /**
     * 返回数据
     *
     * @param monitorIndexVO 监测内容
     * @param actionNameMap  事件 map
     * @param appNameMap     app map
     * @return 返回数据
     */
    private String parseMonitorIndex(MonitorIndexVO monitorIndexVO, Map<String, String> actionNameMap, Map<Long, String> appNameMap) {
        if (null == monitorIndexVO || CollectionUtils.isEmpty(monitorIndexVO.getActions())) {
            return "不限";
        }
        Map<Integer, String> cycleMap = new HashMap<>() {{
            put(1, "当天");
            put(2, "近两天");
            put(60, "近两月");
        }};
        return "过滤周期:【" + cycleMap.get(monitorIndexVO.getCycle()) + "】，转化事件：【" + monitorIndexVO.getActions().stream().map(actionNameMap::get).collect(Collectors.joining(",")) + "】" +
                (CollectionUtils.isEmpty(monitorIndexVO.getAppIds()) ? "" : "过滤应用：【" + monitorIndexVO.getAppIds().stream().map(appNameMap::get).filter(Objects::nonNull).collect(Collectors.joining(",")) + "】");
    }

    /**
     * 查找nameMap
     *
     * @param ids  查询id数据
     * @param type 查询类型数据
     * @return 返回数据
     */
    private Map<Long, String> findListNameMap(List<Long> ids, String type) {
        if (ids.isEmpty()) {
            return Map.of();
        }
        switch (type) {
            case "crowd_label":
                return tagMapper.selectBatchIds(ids).stream().collect(Collectors.toMap(Tag::getId, Tag::getTagName));
            case "area_country":
                Map<Long, String> countryNameMap = countryAllMapper.selectList(new LambdaQueryWrapper<CountryAll>().in(CountryAll::getCountryId, ids)).stream()
                        .collect(Collectors.toMap(CountryAll::getCountryId, CountryAll::getCountryName));
                countryNameMap.putAll(countryCityMapper.selectList(new LambdaQueryWrapper<CountryCity>().in(CountryCity::getId, ids)).stream()
                        .collect(Collectors.toMap(CountryCity::getId, u -> String.format("%s（%s）", u.getCityName(), u.getChineseName()))));
                return countryNameMap;
            case "adx_id":
                return adxMapper.selectBatchIds(ids).stream().collect(Collectors.toMap(u -> u.getId().longValue(), Adx::getAdxName));
            case "ep_id":
                return epMapper.selectBatchIds(ids).stream().collect(Collectors.toMap(Ep::getId, Ep::getEpName));
            default:
                return Map.of();
        }
    }

    private Map<Integer, String> findIntNameMap(String type) {
        switch (type) {
            case "flow_type":
                return new HashMap<>() {{
                    put(0, "不限");
                    put(1, "app");
                    put(2, "wap");
                    put(3, "pc");
                }};
            case "os_type":
                return new HashMap<>() {{
                    put(0, "不限");
                    put(1, "IOS");
                    put(2, "Android");
                }};
            default:
                return Map.of();
        }
    }


    /**
     * 获取key val list
     *
     * @return 数据
     */
    private List<String> fields(Map<String, List<String>> fieldMap) {
        List<String> values = fieldMap.values().stream().flatMap(Collection::stream).distinct().collect(Collectors.toList());
        values.addAll(fieldMap.keySet());
        return values;
    }

    /**
     * 获取 fieldMap
     *
     * @return 返回数据
     */
    private Map<String, List<String>> fieldMap() {
        Map<String, List<String>> map = new HashMap<>(64);
        map.put("budget_type", List.of("budget_day", "budget_hour"));
        map.put("bid_type", List.of("bid_price"));
        map.put("bid_algo_type", List.of("bid_algo_type"));
        map.put("deep_bid_status", List.of("deep_bid_price"));
        map.put("flow_detection_state", List.of("budget_learning_rate"));
        map.put("plan_status", List.of("plan_status"));
        map.put("learning_length", List.of("learning_length"));
        map.put("package_include", List.of("package_value"));
        map.put("package_report_exclude", List.of("package_report_exclude"));
        map.put("package_price_report_exclude", List.of("package_price_report_exclude"));
        //地域
        map.put("area_country", List.of("area_country"));
        //流量类型
        map.put("flow_type", List.of("flow_type"));
        //操作系统
        map.put("os_type", List.of("os_type"));
        //投放速率
        map.put("consume_rate", List.of("consume_rate"));
        //投放周期
        map.put("put_cycle", List.of("start_date", "end_date"));
        map.put("hours", List.of("hours"));
        //曝光频次
        map.put("frequency_cycle_view", List.of("frequency_num_view"));
        //点击频次
        map.put("frequency_cycle_click", List.of("frequency_num_click"));
        map.put("landing_url", List.of("landing_url"));
        map.put("deeplink", List.of("deeplink"));
        map.put("monitor_view_url1", List.of("monitor_view_url1"));
        map.put("monitor_view_url2", List.of("monitor_view_url2"));
        map.put("monitor_click_url1", List.of("monitor_click_url1"));
        map.put("monitor_click_url2", List.of("monitor_click_url2"));
        map.put("industry_id", List.of("industry_id"));
        map.put("crowd_label", List.of("crowd_label"));
        map.put("rta_strategy", List.of("rta_strategy"));
        map.put("monitor_index", List.of("monitor_index"));
        map.put("next_budget_type", List.of("next_budget_day"));
        map.put("adx_id", List.of("adx_id"));
        map.put("ep_id", List.of("ep_id"));
        map.put("ssp", List.of("ssp"));
        map.put("plan_name", List.of("plan_name"));
        map.put("creative_logo", List.of("creative_logo"));
        map.put("creative_brand", List.of("creative_brand"));
        map.put("creative_app_icon", List.of("creative_app_icon"));
        map.put("creative_source", List.of("creative_source"));
        map.put("creative_units", List.of("creative_units"));
        map.put("creative_cta_id", List.of("creative_cta_id"));
        return map;
    }

    /**
     * 根据用户获取 角色
     *
     * @param userId 角色
     * @return 返回数据
     */
    private List<String> getPermissionByUserId(Integer userId) {
        if (sheinConfiguration.isUser(userId.longValue())) {
            return List.of(
                    "budget_type",
                    "bid_type",
                    "landing_url",
                    "rta_strategy",
                    "industry_id",
                    "monitor_view_url1",
                    "monitor_view_url2",
                    "monitor_click_url1",
                    "monitor_click_url2",
                    "crowd_label",
                    "area_country",
                    "monitor_index",
                    "flow_type",
                    "os_type",
                    "put_cycle",
                    "hours",
                    "end_date",
                    "budget_day",
                    "budget_hour",
                    "bid_price",
                    "bidType",
                    "consume_rate",
                    "frequency_cycle_view",
                    "frequency_num_view",
                    "frequency_cycle_click",
                    "frequency_num_click",
                    "plan_status",
                    "plan_name",
                    "creative_brand",
                    "creative_app_icon",
                    "creative_units",
                    "creative_cta_id",
                    "next_budget_type"
            );
        }
        return null;
    }

    private String sspValue(String sspValue) {
        if (StringUtils.isBlank(sspValue)) {
            return "不限";
        }
        JSONObject jsonObject = JSONObject.parseObject(sspValue);
        return "层级：" + JSONObject.toJSONString(
                jsonObject.getJSONArray("supplierChains").toJavaList(Integer.class).stream()
                        .map(u -> {
                            if (100 == u) {
                                return "5+层";
                            }
                            return String.format("%s层", u + 1);
                        }).collect(Collectors.toList()))
                + "，内容：" + jsonObject.getJSONArray("ssps").toJSONString();
    }
}
