package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ExcelUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.service.market.dto.productLibrary.ProductLibraryExcelDTO;
import com.overseas.service.market.dto.productLibrary.ProductLibraryListDTO;
import com.overseas.service.market.entity.productLibrary.ProductLibrary;
import com.overseas.service.market.entity.productLibrary.ProductLibraryResource;
import com.overseas.service.market.mapper.productLibrary.ProductLibraryMapper;
import com.overseas.service.market.mapper.productLibrary.ProductLibraryResourceMapper;
import com.overseas.service.market.service.ProductLibraryService;
import com.overseas.service.market.vo.productLibrary.ProductLibraryListVO;
import com.overseas.service.market.vo.productLibrary.ProductLibrarySaveVO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ProductLibraryServiceImpl extends ServiceImpl<ProductLibraryMapper, ProductLibrary>
        implements ProductLibraryService {

    private final ProductLibraryResourceMapper productLibraryResourceMapper;

    @Override
    @Transactional
    public ProductLibrary saveProductLibrary(ProductLibrarySaveVO saveVO, Integer loginUserId) {
        ProductLibrary productLibrary = this.baseMapper.selectOne(new QueryWrapper<ProductLibrary>().lambda()
                .eq(ProductLibrary::getLibraryName, saveVO.getLibraryName())
                .eq(ProductLibrary::getIsDel, IsDelEnum.NORMAL.getId()));
        if (null != productLibrary) {
            throw new CustomException("商品库名称已存在，请检查后再试");
        }
        List<ProductLibraryExcelDTO> resourceList = ExcelUtils.read(saveVO.getFilePath(), ProductLibraryExcelDTO.class);

        Map<String, ProductLibraryExcelDTO> resourceMap = resourceList.stream()
                .filter(p -> ObjectUtils.isNotNull(p.getProductSign()) && !p.getProductSign().isEmpty())
                .collect(Collectors.toMap(ProductLibraryExcelDTO::getProductSign, Function.identity(), (a, b)-> a));
        productLibrary = new ProductLibrary();
        BeanUtils.copyProperties(saveVO, productLibrary);
        productLibrary.setProductNumber(resourceMap.size());
        productLibrary.setCreateUid(loginUserId);
        if (resourceMap.isEmpty()) {
            throw new CustomException("excel中未解析到有效商品记录，请检查后重新上传");
        }
        try {
            this.baseMapper.insert(productLibrary);
            List<ProductLibraryResource> resources = new ArrayList<>();
            Long libId = productLibrary.getId();
            resourceMap.forEach((key, resource) -> {
                ProductLibraryResource productLibraryResource = new ProductLibraryResource();
                BeanUtils.copyProperties(resource, productLibraryResource);
                productLibraryResource.setLibraryId(libId);
                productLibraryResource.setCreateUid(loginUserId);
                resources.add(productLibraryResource);
            });
            this.productLibraryResourceMapper.insertByUk(resources);
        } catch (Exception e) {
            throw new CustomException("上传的excel数据异常，请检查是否有重复的product sign");
        }
        return productLibrary;
    }

    @Override
    public PageUtils<?> listProductLibrary(ProductLibraryListVO listVO) {
        IPage<ProductLibraryListDTO> iPage = new Page<>(listVO.getPage(), listVO.getPageNum());
        iPage = this.baseMapper.listProductLibrary(iPage, new QueryWrapper<ProductLibrary>()
                .eq("mpl.master_id", listVO.getMasterId())
                .and(StringUtils.isNotBlank(listVO.getSearch()),
                        q -> q.like("mpl.id", listVO.getSearch())
                                .or().like("mpl.library_name", listVO.getSearch())
                )
                .orderByDesc("mpl.id")
        );
        return new PageUtils<>(iPage);
    }
}
