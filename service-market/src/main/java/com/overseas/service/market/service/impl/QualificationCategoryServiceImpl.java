package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.service.market.entity.QualificationCategory;
import com.overseas.common.dto.sys.resource.LinkageDto;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.service.market.mapper.QualificationCategoryMapper;
import com.overseas.service.market.service.QualificationCategoryService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-09-14 14:38
 */
@Service
public class QualificationCategoryServiceImpl extends ServiceImpl<QualificationCategoryMapper, QualificationCategory> implements QualificationCategoryService {
    @Override
    public List<LinkageDto> categoryLinkage() {
        List<QualificationCategory> industryList = this.lambdaQuery()
                .eq(QualificationCategory::getIsDel, IsDelEnum.NORMAL.getId())
                .select(QualificationCategory::getId, QualificationCategory::getPid, QualificationCategory::getCategoryName, QualificationCategory::getLevel)
                .orderByAsc(QualificationCategory::getPid)
                .list();
        Map<Long, LinkageDto> res = new HashMap<>();
        industryList.forEach(ind -> {
            if (ind.getLevel() == 1) {
                res.put(ind.getId(), new LinkageDto(ind.getId().intValue(), ind.getCategoryName(), new ArrayList<LinkageDto>()));
            } else if (res.get(ind.getPid()) != null) {
                LinkageDto linkage = res.get(ind.getPid());
                linkage.getChildren().add(new LinkageDto(ind.getId().intValue(), ind.getCategoryName(), List.of()));
            }
        });
        return new ArrayList<>(res.values());
    }
}
