package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.report.ReportDirectEnum;
import com.overseas.service.market.entity.*;
import com.overseas.service.market.enums.plan.PlanDirectIncludeEnum;
import com.overseas.service.market.events.creativeUnitAudit.CreativeUnitAuditEvent;
import com.overseas.service.market.events.notifyControl.ControlCampaignEvent;
import com.overseas.service.market.events.notifyControl.ControlContants;
import com.overseas.service.market.mapper.PlanMapper;
import com.overseas.common.dto.SelectDTO3;
import com.overseas.common.dto.market.plan.PlanDirectValueDTO;
import com.overseas.common.enums.report.ReportTypeEnum;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.vo.market.plan.PlanDirectGetVO;
import com.overseas.common.vo.market.plan.PlanDirectionChangeGetVO;
import com.overseas.common.vo.market.plan.PlanMarketDirectUpdateGetVO;
import com.overseas.service.market.mapper.MediaMapper;
import com.overseas.service.market.mapper.ReportDirectMapper;
import com.overseas.service.market.mapper.SlotMapper;
import com.overseas.service.market.service.PlanUpdateRecordService;
import com.overseas.service.market.service.ReportDirectService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ReportDirectServiceImpl extends ServiceImpl<ReportDirectMapper, ReportDirect> implements ReportDirectService {

    private final PlanMapper planMapper;

    private final ApplicationContext applicationContext;

    private final PlanUpdateRecordService planUpdateRecordService;

    private final MediaMapper mediaMapper;

    private final SlotMapper slotMapper;

    @Override
    public List<PlanDirectValueDTO> getPlanDirect(PlanDirectGetVO getVO) {

        if (getVO.getCampaignIds().isEmpty()) {
            return List.of();
        }

        // 查询报表排除定向列表
        boolean isCampaign = getVO.getPlanIds().isEmpty();
        // 如果需要在计划层级查询活动数据，直接返回
        if (getVO.getIsSearchCampaign()) {
            return this.getAllDirect(getVO, isCampaign);
        }
        // 如果需要查询活动层级再放开
        List<ReportDirect> reportDirects = this.baseMapper.selectList(new QueryWrapper<ReportDirect>().lambda()
                // 排除一些空值查询
                .ne(ReportDirect::getDirectValue, "")
                .ne(ReportDirect::getDirectValue, "[]")
                .ne(ReportDirect::getDirectValue, "{}")
                // 如果指定定向信息
                .eq(ObjectUtils.isNotNullOrZero(getVO.getDirectId()), ReportDirect::getDirectId, getVO.getDirectId())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getReportType()), ReportDirect::getReportType, getVO.getReportType())
                .in(ReportDirect::getCampaignId, getVO.getCampaignIds())
                // 如果计划为空，则只查询活动层级
                .eq(isCampaign, ReportDirect::getPlanId, 0)
                // 如果计划不为空，则只查询指定计划层级
                .in(!isCampaign, ReportDirect::getPlanId, getVO.getPlanIds()));

        if (reportDirects.isEmpty()) {
            return List.of();
        }
        // 查询排除的ID集合
        List<String> mediaIds = new ArrayList<>(), slotIds = new ArrayList<>(), packages = new ArrayList<>();
        for (ReportDirect reportDirect : reportDirects) {
            switch (ICommonEnum.get(reportDirect.getDirectId(), ReportDirectEnum.class)) {
                case MEDIA:
                    mediaIds.addAll(JSONObject.parseArray(reportDirect.getDirectValue(), String.class));
                    break;
                case SLOT:
                    slotIds.addAll(JSONObject.parseArray(reportDirect.getDirectValue(), String.class));
                    break;
                case PACKAGE:
                    packages.addAll(JSONObject.parseArray(reportDirect.getDirectValue(), String.class));
                    break;
                default:
            }
        }
        // 获取排除的映射Map
        Map<ReportTypeEnum, Map<String, String>> directValueMap = new HashMap<>() {{
            put(ReportTypeEnum.MEDIA, getDirectMap(mediaIds.stream().distinct().collect(Collectors.toList()), ReportTypeEnum.MEDIA));
            put(ReportTypeEnum.SLOT, getDirectMap(slotIds.stream().distinct().collect(Collectors.toList()), ReportTypeEnum.SLOT));
            put(ReportTypeEnum.PACKAGE_NAME, getDirectMap(packages.stream().distinct().collect(Collectors.toList()), ReportTypeEnum.PACKAGE_NAME));
        }};
        // 填充并返回数据
        return this.fillDirectList(reportDirects, directValueMap);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ReportDirect> changePlanDirect(PlanDirectionChangeGetVO getVO, Integer userId) {
        if (getVO.getCampaignIds().isEmpty()) {
            return List.of();
        }
        // 查询已存在定向数据
        List<ReportDirect> existReportDirectList;
        if (getVO.getPlanIds().isEmpty()) {
            existReportDirectList = this.baseMapper.selectList(new QueryWrapper<ReportDirect>().lambda()
                    .eq(ReportDirect::getPlanId, 0)
                    .in(ReportDirect::getCampaignId, getVO.getCampaignIds())
                    .eq(ReportDirect::getDirectId, getVO.getDirectId()));
        } else {
            existReportDirectList = this.baseMapper.selectList(new QueryWrapper<ReportDirect>().lambda()
                    .in(ReportDirect::getPlanId, getVO.getPlanIds())
                    .eq(ReportDirect::getDirectId, getVO.getDirectId()));
        }
        List<ReportDirect> reportDirectList = new ArrayList<>();

        // 若存在记录，则编辑报表定向数据记录
        for (ReportDirect reportDirect : existReportDirectList) {
            if (ReportDirectEnum.PACKAGE_PRICE.getId().equals(getVO.getDirectId())) {
                Map<String, Integer> existDirects = JSONObject.parseObject(reportDirect.getDirectValue(), new TypeReference<>() {});
                Map<String, Integer> directs = JSONObject.parseObject(getVO.getDirectValue(), new TypeReference<>() {});
                existDirects.putAll(directs);
                reportDirect.setDirectValue(JSONObject.toJSONString(existDirects));
                reportDirect.setUpdateUid(userId);
                reportDirectList.add(reportDirect);
            } else {
                List<String> keyList = JSONArray.parseArray(reportDirect.getDirectValue(), String.class);
                int count = keyList.size();
                if (getVO.getInclude().equals(1)) {
                    keyList.remove(getVO.getDirectValue());
                } else {
                    keyList.add(getVO.getDirectValue());
                }
                List<Object> result;
                if (ReportTypeEnum.PACKAGE_NAME.getId().equals(reportDirect.getReportType())) {
                    result = keyList.stream().distinct().collect(Collectors.toList());
                } else {
                    result = keyList.stream().distinct().map(Long::valueOf).collect(Collectors.toList());
                }
                if (result.size() != count) {
                    reportDirect.setDirectValue(JSONObject.toJSONString(result));
                    reportDirect.setUpdateUid(userId);
                    reportDirectList.add(reportDirect);
                }
            }
        }

        //若不存在记录，则仅新增 排除数据的时候才需要执行
        if (!PlanDirectIncludeEnum.INCLUDE.getId().equals(getVO.getInclude())) {
            if (getVO.getPlanIds().isEmpty()) {
                getVO.getCampaignIds().removeAll(existReportDirectList.stream()
                        .map(ReportDirect::getCampaignId).collect(Collectors.toList()));
                // 如果不存在新增的数据；或者当前是包含定向数据，则直接返回
                for (Long campaignId : getVO.getCampaignIds()) {
                    reportDirectList.add(this.saveReportDirect(campaignId, 0L, getVO.getReportType(),
                            getVO.getDirectId(), getVO.getDirectValue(), userId));
                }
            } else {
                getVO.getPlanIds().removeAll(existReportDirectList.stream()
                        .map(ReportDirect::getPlanId).collect(Collectors.toList()));
                // 如果不存在新增的数据；或者当前是包含定向数据，则直接返回
                if (CollectionUtils.isNotEmpty(getVO.getPlanIds())) {
                    Map<Long, Long> planMap = this.planMapper.selectList(new QueryWrapper<Plan>().lambda()
                            .in(Plan::getId, getVO.getPlanIds())).stream()
                            .collect(Collectors.toMap(Plan::getId, Plan::getCampaignId));
                    for (Long planId : getVO.getPlanIds()) {
                        reportDirectList.add(this.saveReportDirect(planMap.get(planId), planId, getVO.getReportType(),
                                getVO.getDirectId(), getVO.getDirectValue(), userId));
                    }
                }
            }
        }

        //无修改内容
        if (reportDirectList.isEmpty()) {
            return List.of();
        }
        // 存在修改 （新增和更改）
        this.baseMapper.saveReportDirect(reportDirectList, userId);
        // 记录修改日志
        List<PlanUpdateRecord> planUpdateRecords = reportDirectList.stream()
                .filter(u -> ObjectUtils.isNotNullOrZero(u.getPlanId()))
                .map(reportDirect -> {
                    PlanUpdateRecord planUpdateRecord = new PlanUpdateRecord();
                    planUpdateRecord.setPlanId(reportDirect.getPlanId());
                    Map<String, Object> map = new HashMap<>(4) {{
                        if (ReportDirectEnum.PACKAGE.getId().equals(getVO.getDirectId())) {
                            put("package_report_exclude", reportDirect.getDirectValue());
                        } else if (ReportDirectEnum.PACKAGE_PRICE.getId().equals(getVO.getDirectId())) {
                            put("package_price_report_exclude", reportDirect.getDirectValue());
                        }
                    }};
                    planUpdateRecord.setContent(JSONObject.toJSONString(map));
                    return planUpdateRecord;
                }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(planUpdateRecords)) {
            this.planUpdateRecordService.savePlanUpdateRecord(planUpdateRecords, 0);
        }
        //返回最终修改数据信息，提供通知中控数据
        return reportDirectList;
    }

    @Override
    public void changeMarketDirect(PlanMarketDirectUpdateGetVO getVO, Integer userId) {

        if (ObjectUtils.isNullOrZero(getVO.getCampaignId()) || getVO.getDirectValue().isEmpty()) {
            return;
        }
        List<ReportDirect> reportDirects = this.baseMapper.selectList(new QueryWrapper<ReportDirect>().lambda()
                .eq(ObjectUtils.isNotNullOrZero(getVO.getCampaignId()),
                        ReportDirect::getCampaignId, getVO.getCampaignId())
                .eq(ObjectUtils.isNullOrZero(getVO.getPlanId()), ReportDirect::getPlanId, 0)
                .eq(ObjectUtils.isNotNullOrZero(getVO.getPlanId()), ReportDirect::getPlanId, getVO.getPlanId())
                .in(ReportDirect::getDirectId, getVO.getDirectValue().keySet()));
//                .eq(ReportDirect::getInclude, 2));

        if (reportDirects.isEmpty()) {
            return;
        }

        reportDirects.forEach(reportDirect -> {
            if (getVO.getDirectValue().get(reportDirect.getDirectId()) == null) {
                return;
            }
            if (ReportDirectEnum.PACKAGE_PRICE.getId().equals(reportDirect.getDirectId())) {
                Map<String, Integer> values = JSONObject.parseObject(reportDirect.getDirectValue(), new TypeReference<>() {});
                Map<String, Integer> updateValues = JSONObject.parseObject(
                        getVO.getDirectValue().get(reportDirect.getDirectId()).toString(), new TypeReference<>() {});
                values.putAll(updateValues);
                reportDirect.setDirectValue(JSONObject.toJSONString(values));
            }
            if (!ReportDirectEnum.PACKAGE_PRICE.getId().equals(reportDirect.getDirectId())) {
                List<String> values = JSONObject.parseArray(reportDirect.getDirectValue(), String.class);
                List<String> updateValues = JSONObject.parseArray(getVO.getDirectValue().get(reportDirect.getDirectId()).toString(), String.class);
                values.removeAll(updateValues);
                if (ReportDirectEnum.PACKAGE.getId().equals(reportDirect.getDirectId())) {
                    reportDirect.setDirectValue(JSONObject.toJSONString(values.stream().distinct().collect(Collectors.toList())));
                } else {
                    reportDirect.setDirectValue(JSONObject.toJSONString(values.stream().distinct().map(Long::valueOf).collect(Collectors.toList())));
                }
            }
        });
        this.baseMapper.saveReportDirect(reportDirects, userId);
        // 通知中控
        this.noticeCenterControl(getVO.getCampaignId(), getVO.getPlanId());
    }

    private ReportDirect saveReportDirect(Long campaignId, Long planId, Integer reportType, Integer directId,
                                          String value, Integer userId) {
        ReportDirect reportDirect = new ReportDirect();
        reportDirect.setCampaignId(campaignId);
        reportDirect.setPlanId(planId);
        reportDirect.setReportType(reportType);
        reportDirect.setDirectId(directId);
        reportDirect.setInclude(ICommonEnum.get(directId, ReportDirectEnum.class).getInclude());
        if (ReportDirectEnum.PACKAGE.getId().equals(directId)) {
            reportDirect.setDirectValue(JSONObject.toJSONString(new ArrayList<>(List.of(value))));
        } else if (ReportDirectEnum.PACKAGE_PRICE.getId().equals(directId)) {
            reportDirect.setDirectValue(value);
        } else {
            reportDirect.setDirectValue(JSONObject.toJSONString(new ArrayList<>(List.of(value))
                    .stream().map(Long::valueOf).collect(Collectors.toList())));
        }
        reportDirect.setCreateUid(userId);
        return reportDirect;
    }

    /**
     * 根据指定ID获取ID-名称Map
     *
     * @param ids ID集合
     * @return 返回数据
     */
    private Map<String, String> getDirectMap(List<String> ids, ReportTypeEnum reportTypeEnum) {

        if (ids.isEmpty()) {
            return new HashMap<>();
        }
        switch (reportTypeEnum) {
            case MEDIA:
                return this.mediaMapper.selectList(new QueryWrapper<Media>().lambda().in(Media::getId, ids))
                        .stream().collect(Collectors.toMap(u -> u.getId().toString(), Media::getMediaName));
            case SLOT:
                return this.slotMapper.selectList(new QueryWrapper<Slot>().lambda().in(Slot::getId, ids))
                        .stream().collect(Collectors.toMap(u -> u.getId().toString(), Slot::getSlotName));
            case PACKAGE_NAME:
                // 如果是包名则直接返回key-key的Map
                return ids.stream().collect(Collectors.toMap(Function.identity(), Function.identity()));
            default:
                return new HashMap<>();
        }
    }

    private List<SelectDTO3> getDirectValue(Integer directId, String directValue, Map<String, String> map) {
        if (ReportDirectEnum.PACKAGE_PRICE.getId().equals(directId)) {
            Map<String, Integer> directs = JSONObject.parseObject(directValue, new TypeReference<>() {});
            List<SelectDTO3> result = new ArrayList<>();
            directs.forEach((key, value) -> result.add(new SelectDTO3(key, value.toString())));
            return result;
        } else {
            return JSONObject.parseArray(directValue, String.class).stream()
                    // 如果没有查到名称则插入该ID（兼容包名）
                    .map(key -> new SelectDTO3(key, map.getOrDefault(key, key))).collect(Collectors.toList());
        }
    }

    /**
     * 填充数据
     *
     * @param reportDirects  排除定向列表
     * @param directValueMap key-title映射Map
     * @return 返回数据
     */
    private List<PlanDirectValueDTO> fillDirectList(List<ReportDirect> reportDirects, Map<ReportTypeEnum, Map<String, String>> directValueMap) {

        // 填充数据
        return reportDirects.stream().map(direct -> {
            PlanDirectValueDTO planDirectValueDTO = new PlanDirectValueDTO();
            ReportTypeEnum reportTypeEnum = ReportTypeEnum.getById(direct.getReportType());
            planDirectValueDTO.setReportType(direct.getReportType());
            planDirectValueDTO.setReportTypeName(reportTypeEnum.getName());
            planDirectValueDTO.setDirectId(direct.getDirectId());
            planDirectValueDTO.setDirectValue(
                    this.getDirectValue(direct.getDirectId(), direct.getDirectValue(),
                            directValueMap.get(reportTypeEnum)));
            return planDirectValueDTO;
        }).collect(Collectors.toList());
    }


    private List<PlanDirectValueDTO> getAllDirect(PlanDirectGetVO getVO, boolean isCampaign) {

        List<ReportDirect> reportDirects = this.baseMapper.selectList(new QueryWrapper<ReportDirect>().lambda()
                .ne(ReportDirect::getDirectValue, "")
                .ne(ReportDirect::getDirectValue, "[]")
                .eq(ObjectUtils.isNotNullOrZero(getVO.getReportType()), ReportDirect::getReportType, getVO.getReportType())
                .and(isCampaign, q -> q.eq(ReportDirect::getPlanId, 0)
                        .in(ReportDirect::getCampaignId, getVO.getCampaignIds()))
                .and(!isCampaign, q -> q.in(ReportDirect::getPlanId, getVO.getPlanIds())
                        .or(q1 -> q1.in(ReportDirect::getCampaignId, getVO.getCampaignIds())
                                .eq(ReportDirect::getPlanId, 0))));

        if (reportDirects.isEmpty()) {
            return List.of();
        }
        List<PlanDirectValueDTO> result = new ArrayList<>();
        reportDirects.forEach(reportDirect -> {
            PlanDirectValueDTO planDirectValueDTO = new PlanDirectValueDTO();
            List<SelectDTO3> directValues = new ArrayList<>();
            if (ReportDirectEnum.PACKAGE_PRICE.getId().equals(reportDirect.getDirectId())) {
                List<SelectDTO3> values = new ArrayList<>();
                Map<String, Integer> directMap = JSONObject.parseObject(reportDirect.getDirectValue(),
                        new TypeReference<>() {
                        });
                directMap.forEach((key, value) -> values.add(new SelectDTO3(key, value.toString())));
                directValues.addAll(values);
            }
            if (!ReportDirectEnum.PACKAGE_PRICE.getId().equals(reportDirect.getDirectId())) {
                List<String> values = JSONObject.parseArray(reportDirect.getDirectValue(), String.class);
                directValues.addAll(values.stream().map(key -> new SelectDTO3(key,
                        ObjectUtils.isNotNullOrZero(reportDirect.getPlanId()) ? "plan" : "campaign")).collect(Collectors.toList()));
            }
            planDirectValueDTO.setDirectId(reportDirect.getDirectId());
            planDirectValueDTO.setReportType(reportDirect.getReportType());
            planDirectValueDTO.setDirectValue(directValues);
            result.add(planDirectValueDTO);
        });
        return result;
    }

    /**
     * 通知中控更新定向信息
     *
     * @param campaignId 活动ID
     * @param planId     计划ID
     */
    @Override
    public void noticeCenterControl(Long campaignId, Long planId) {
        if (ObjectUtils.isNotNullOrZero(campaignId) && ObjectUtils.isNotNullOrZero(planId)) {
            applicationContext.publishEvent(new CreativeUnitAuditEvent(this, planId));
        } else if (ObjectUtils.isNotNullOrZero(campaignId) && ObjectUtils.isNullOrZero(planId)) {
            applicationContext.publishEvent(new ControlCampaignEvent(this, ControlContants.METHOD_UPDATE, campaignId));
        }
    }
}
