package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.enums.report.ReportTypeEnum;
import com.overseas.common.utils.*;
import com.overseas.common.vo.market.reportTask.*;
import com.overseas.service.market.entity.ReportExportTask;
import com.overseas.service.market.enums.reportTask.ReportTaskStatusEnum;
import com.overseas.service.market.feign.FgReportService;
import com.overseas.common.dto.market.reportTask.ReportExportTaskDTO;
import com.overseas.common.dto.market.reportTask.ReportExportTaskListDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.ResultStatusEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.vo.report.AnalysisReportListVO;
import com.overseas.common.vo.report.flow.FlowReportListVO;
import com.overseas.service.market.enums.report.AnalysisReportFieldEnum;
import com.overseas.common.enums.market.reportTask.ReportTaskTypeEnum;
import com.overseas.service.market.mapper.ReportExportTaskMapper;
import com.overseas.service.market.service.ReportExportTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.io.File;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ReportExportTaskServiceImpl extends ServiceImpl<ReportExportTaskMapper, ReportExportTask>
        implements ReportExportTaskService {

    private final FgReportService fgReportService;

    @Override
    public PageUtils<ReportExportTaskListDTO> getReportExportTaskPage(ReportExportTaskListVO listVO, Integer userId) {

        IPage<ReportExportTaskListDTO> page = new Page<>(listVO.getPage(), listVO.getPageNum());
        IPage<ReportExportTaskListDTO> pageData = this.baseMapper.getReportExportTaskPage(page,
                new QueryWrapper<ReportExportTask>().lambda()
//                .eq(ReportExportTask::getMasterId, listVO.getMasterId())
                .eq(ReportExportTask::getCreateUid, userId)
                .eq(ReportExportTask::getTaskType, listVO.getTaskType())
                .orderByDesc(ReportExportTask::getId));

        pageData.getRecords().forEach(entity -> entity.setTaskStatusName(
                ICommonEnum.getNameById(entity.getTaskStatus(), ReportTaskStatusEnum.class)));
        return new PageUtils<>(pageData);
    }

    @Override
    public void saveReportExportTask(ReportExportTaskSaveVO saveVO, Integer userId) {

        // 校验是否重复
        Long count = this.baseMapper.selectCount(new QueryWrapper<ReportExportTask>().lambda()
                .eq(ReportExportTask::getTaskName, saveVO.getTaskName())
                .eq(ObjectUtils.isNotNullOrZero(saveVO.getMasterId()),
                        ReportExportTask::getMasterId, saveVO.getMasterId())
                .eq(ReportExportTask::getTaskType, saveVO.getTaskType())
                .eq(ReportExportTask::getCreateUid, userId));
        if (ObjectUtils.isNotNullOrZero(count)) {
            throw new CustomException("任务名称重复，请确认后再试");
        }
        // 新增
        ReportExportTask reportExportTask = new ReportExportTask();
        BeanUtils.copyProperties(saveVO, reportExportTask);
        if (StringUtils.isEmpty(saveVO.getSearchData())) {
            reportExportTask.setSearchData("");
        }
        reportExportTask.setMasterId(saveVO.getMasterId());
        reportExportTask.setTaskRemark("");
        reportExportTask.setCustoms(JSONObject.toJSONString(saveVO.getCustoms()));
        reportExportTask.setCreateUid(userId);
        this.baseMapper.insert(reportExportTask);
    }

    @Override
    public void updateReportExportTaskStatus(ReportExportTaskStatusUpdateVO updateVO) {

        ReportExportTask reportExportTask = new ReportExportTask();
        reportExportTask.setTaskStatus(updateVO.getTaskStatus());
        this.baseMapper.update(reportExportTask, new QueryWrapper<ReportExportTask>().lambda()
                .eq(ReportExportTask::getId, updateVO.getId()));
    }

    @Override
    public void exportAllReportTask() {

        List<ReportExportTask> reportExportTaskList = this.baseMapper.selectList(
                new QueryWrapper<ReportExportTask>().lambda()
                        .eq(ReportExportTask::getTaskStatus, ReportTaskStatusEnum.TO_BE_CREATED.getId())
                        .orderByAsc(ReportExportTask::getId));

        if (reportExportTaskList.isEmpty()) {
            return;
        }
        // 取第一条执行
        log.info("存在待导出任务；任务数量：{}；当前执行任务ID：{}", reportExportTaskList.size(),
                reportExportTaskList.get(0).getId());
        this.exportReportTask(reportExportTaskList.get(0));
    }

    @Override
    public ReportExportTaskDTO getWaitToCreateTask(ReportExportTaskGetVO getVO) {

        QueryWrapper<ReportExportTask> queryWrapper = new QueryWrapper<ReportExportTask>()
                .eq("task_status", ReportTaskStatusEnum.TO_BE_CREATED.getId())
                .orderByAsc("id")
                .last("LIMIT 1");
        // 如果要包含查询包名
        if (ObjectUtils.isNotNullOrZero(getVO.getIncludePkg())) {
            queryWrapper.and(q -> q.or(q1 -> q1.eq("task_type", ReportTypeEnum.TIME_COMPARE.getId())
                            .eq("JSON_UNQUOTE(JSON_EXTRACT(search_data, '$.compareField'))", "pkg"))
                    .or(q1 -> q1.eq("task_type", ReportTypeEnum.CUSTOM.getId())
                            .like("customs", "pkg")));
        } else {
            queryWrapper.and(q -> q.or(q1 -> q1.eq("task_type", ReportTypeEnum.TIME_COMPARE.getId())
                            .ne("JSON_UNQUOTE(JSON_EXTRACT(search_data, '$.compareField'))", "pkg"))
                    .or(q1 -> q1.eq("task_type", ReportTypeEnum.CUSTOM.getId())
                            .notLike("customs", "pkg"))
                    .or(q1 -> q1.notIn("task_type",
                            List.of(ReportTypeEnum.TIME_COMPARE.getId(), ReportTypeEnum.CUSTOM.getId()))));
        }
        return this.baseMapper.getWaitToCreateTask(queryWrapper);
    }

    @Override
    public void updateReportTask(ReportExportTaskUpdateVO updateVO) {

        ReportExportTask reportExportTask = new ReportExportTask();
        BeanUtils.copyProperties(updateVO, reportExportTask);
        this.baseMapper.update(reportExportTask, new QueryWrapper<ReportExportTask>().lambda()
                .eq(ReportExportTask::getId, reportExportTask.getId()));
    }


    /**
     * 导出单条记录
     *
     * @param reportExportTask 任务记录
     */
    private void exportReportTask(ReportExportTask reportExportTask) {

        ReportExportTaskStatusUpdateVO updateVO = new ReportExportTaskStatusUpdateVO();
        updateVO.setId(reportExportTask.getId());
        updateVO.setTaskStatus(ReportTaskStatusEnum.CREATING.getId());
        // 先将该任务状态设置为执行中
        long startTime = System.currentTimeMillis();
        this.updateReportExportTaskStatus(updateVO);
        // 执行导出
        try {
            FeignR<ResultStatusEnum> result = this.executeExport(reportExportTask);
            if (result.getCode().equals(0)) {
                reportExportTask.setTaskStatus(ReportTaskStatusEnum.SUCCESS.getId());
                log.info("导出报表离线任务成功：任务ID：{}", reportExportTask.getId());
            } else {
                reportExportTask.setTaskStatus(ReportTaskStatusEnum.FAIL.getId());
                String taskRemark = result.getData() == null ? result.getMsg() :
                        (StringUtils.isNotBlank(result.getData().getMessage())
                                ? result.getData().getMessage() : result.getMsg());
                reportExportTask.setTaskRemark(taskRemark);
                // 失败了清空任务名称和路径
                reportExportTask.setFileName("");
                reportExportTask.setFilePath("");
                log.info("导出报表离线任务失败：任务ID：{}；失败原因：{}", reportExportTask.getId(),
                        reportExportTask.getTaskRemark());
            }
        } catch (Exception exception) {
            log.error(exception.getMessage());
            reportExportTask.setTaskStatus(ReportTaskStatusEnum.FAIL.getId());
            reportExportTask.setTaskRemark(exception.getMessage());
            // 失败了清空任务名称和路径
            reportExportTask.setFileName("");
            reportExportTask.setFilePath("");
            log.info("导出报表离线任务失败：任务ID：{}；失败原因：{}", reportExportTask.getId(),
                    reportExportTask.getTaskRemark());
        } finally {
            log.info("任务执行结束，耗时：{}", System.currentTimeMillis() - startTime);
        }

        // 更新导出状态，及导出文件存储信息
        this.baseMapper.update(reportExportTask, new QueryWrapper<ReportExportTask>().lambda()
                .eq(ReportExportTask::getId, reportExportTask.getId()));
    }

    /**
     * 执行导出
     *
     * @param reportExportTask 任务记录
     */
    private FeignR<ResultStatusEnum> executeExport(ReportExportTask reportExportTask) {

        // 设置文件名称与文件地址
        String fileName = reportExportTask.getTaskName() + "_"
                + (reportExportTask.getStartDate().equals(reportExportTask.getEndDate())
                ? reportExportTask.getStartDate()
                : reportExportTask.getStartDate() + "_" + reportExportTask.getEndDate()) + ".xlsx";
        String filePath = "/overseasReportTask/"
                + ICommonEnum.get(reportExportTask.getTaskType(), ReportTaskTypeEnum.class).getFilePath()
                + "/" + DateUtils.format(new Date(), "yyyy/MM/dd");

        File path = new File(UploadUtils.getUploadPath(filePath));
        if (!path.exists()) {
            path.mkdirs();
        }
        filePath += "/" + DigestUtils.md5DigestAsHex(((new Date()).getTime() + Math.random() * 10000 + "").getBytes())
                + ".xlsx";
        // 设置任务导出文件存储信息
        reportExportTask.setFileName(fileName);
        reportExportTask.setFilePath(filePath);

        // 执行导出任务
        ReportTaskTypeEnum reportTaskTypeEnum = ICommonEnum.get(reportExportTask.getTaskType(),
                ReportTaskTypeEnum.class);
        log.info("导出报表离线任务开始执行：任务ID：{}；报表类型：{}报表", reportExportTask.getId(),
                reportTaskTypeEnum.getName());

        switch (reportTaskTypeEnum) {
            case PACKAGE:
            case FLOW:
                return this.exportFlowReport(reportExportTask, filePath);
            case FLOW_SEARCH:
                return this.exportFlowSearchReport(reportExportTask, filePath);
            default:
                return new FeignR<>();
        }
    }

    /**
     * 导出流量报表文件
     *
     * @param reportExportTask 任务信息
     * @param filePath         导出路径
     */
    private FeignR<ResultStatusEnum> exportFlowReport(ReportExportTask reportExportTask, String filePath) {
        // 解析请求参数
        AnalysisReportListVO listVO = StringUtils.isNotBlank(reportExportTask.getSearchData())
                ? JSONObject.parseObject(reportExportTask.getSearchData(), AnalysisReportListVO.class)
                : new AnalysisReportListVO();
        listVO.setPage(1L);
        listVO.setPageNum(5000000L);
        // TODO : 确认去除原因
        if (!reportExportTask.getTaskType().equals(21)) {
            listVO.setMasterId(reportExportTask.getMasterId());
        }
        listVO.setReportType(reportExportTask.getTaskType());
        listVO.setStartDate(reportExportTask.getStartDate());
        listVO.setEndDate(reportExportTask.getEndDate());
        listVO.setModule(reportExportTask.getTaskModule());
        listVO.setIdentify(reportExportTask.getTaskIdentify());
        listVO.setIsDownload(true);
        listVO.setFilePath(filePath);
        List<String> customs = JSONObject.parseArray(reportExportTask.getCustoms(), String.class);
        List<String> dimensions = AnalysisReportFieldEnum.getKeys().stream().filter(customs::contains)
                .collect(Collectors.toList());
        listVO.setDimensions(dimensions);
        customs.removeAll(dimensions);
        listVO.setCustoms(customs);
        return this.fgReportService.exportAnalysisReportToPath(listVO);
    }

    /**
     * 导出流量查询文件
     *
     * @param reportExportTask 任务信息
     * @param filePath         导出路径
     */
    private FeignR<ResultStatusEnum> exportFlowSearchReport(ReportExportTask reportExportTask, String filePath) {

        FlowReportListVO listVO = StringUtils.isNotBlank(reportExportTask.getSearchData())
                ? JSONObject.parseObject(reportExportTask.getSearchData(), FlowReportListVO.class)
                : new FlowReportListVO();
        listVO.setPage(1L);
        listVO.setPageNum(99999999L);
        listVO.setStartDate(reportExportTask.getStartDate());
        listVO.setEndDate(reportExportTask.getEndDate());
        listVO.setDimensions(JSONObject.parseArray(reportExportTask.getCustoms(), String.class));
        listVO.setSortField("req");
        listVO.setSortType("desc");
        listVO.setFilePath(filePath);
        return this.fgReportService.exportFlowReportToPath(listVO);
    }
}
