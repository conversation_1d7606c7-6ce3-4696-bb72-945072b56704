package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.overseas.service.market.entity.ProjectFieldMap;
import com.overseas.service.market.enums.reportField.ReportProjectFieldTypeEnum;
import com.overseas.service.market.mapper.ProjectFieldMapMapper;
import com.overseas.common.dto.market.reportField.ReportFieldDTO;
import com.overseas.common.dto.sys.customIndex.CustomIndexChildColumnDTO;
import com.overseas.common.dto.sys.customIndex.CustomIndexParentColumnDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.vo.market.reportField.ReportFieldGetVO;
import com.overseas.service.market.service.ReportFieldService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ReportFieldServiceImpl implements ReportFieldService {

    private final ProjectFieldMapMapper projectFieldMapMapper;

    @Override
    public List<ReportFieldDTO> listReportField(ReportFieldGetVO getVO) {
        return this.projectFieldMapMapper.listReportField(new QueryWrapper<ProjectFieldMap>().lambda()
                .eq(ProjectFieldMap::getIsDel, IsDelEnum.NORMAL.getId())
                .and(q -> q.eq(ProjectFieldMap::getProjectId, 0)
                        .or().eq(ObjectUtils.isNotNullOrZero(getVO.getProjectId()), ProjectFieldMap::getProjectId, getVO.getProjectId()))
                .eq(ObjectUtils.isNotNullOrZero(getVO.getActionType()), ProjectFieldMap::getActionType, getVO.getActionType())
                .in(CollectionUtils.isNotEmpty(getVO.getFields()), ProjectFieldMap::getField, getVO.getFields()));
    }

    @Override
    public List<CustomIndexParentColumnDTO> getCustomIndexColumns(ReportFieldGetVO getVO) {

        Map<Integer, List<CustomIndexChildColumnDTO>> columnMap = this.projectFieldMapMapper.listCustomColumns(new QueryWrapper<ProjectFieldMap>().lambda()
                        .eq(ProjectFieldMap::getIsDel, IsDelEnum.NORMAL.getId())
                        .and(q -> q.eq(ProjectFieldMap::getProjectId, 0)
                                .or().eq(ObjectUtils.isNotNullOrZero(getVO.getProjectId()), ProjectFieldMap::getProjectId, getVO.getProjectId()))
                        .orderByAsc(ProjectFieldMap::getId)).stream().peek(column -> column.setChecked(false))
                .collect(Collectors.groupingBy(CustomIndexChildColumnDTO::getType));
        List<CustomIndexParentColumnDTO> customIndexParentColumnDTOS = new ArrayList<>();
        columnMap.forEach((type, columns) -> {
            CustomIndexParentColumnDTO customIndexParentColumnDTO = new CustomIndexParentColumnDTO();
            ReportProjectFieldTypeEnum reportProjectFieldTypeEnum = ICommonEnum.get(type, ReportProjectFieldTypeEnum.class);
            customIndexParentColumnDTO.setColumns(columns);
            customIndexParentColumnDTO.setKey(reportProjectFieldTypeEnum.getKey());
            customIndexParentColumnDTO.setTitle(reportProjectFieldTypeEnum.getName());
            customIndexParentColumnDTOS.add(customIndexParentColumnDTO);
        });
        return customIndexParentColumnDTOS;
    }
}
