package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.service.market.entity.ReportNote;
import com.overseas.common.dto.market.reportNote.ReportNoteDTO;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.vo.market.reportNote.ReportNoteListVO;
import com.overseas.common.vo.market.reportNote.ReportNoteSaveVO;
import com.overseas.service.market.mapper.ReportNoteMapper;
import com.overseas.service.market.service.ReportNoteService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ReportNoteServiceImpl extends ServiceImpl<ReportNoteMapper, ReportNote> implements ReportNoteService {

    @Override
    public void saveReportNote(ReportNoteSaveVO saveVO, Integer userId) {

        List<ReportNote> reportNotes = saveVO.getNotes().stream().map(reportNoteDTO -> {
            ReportNote reportNote = new ReportNote();
            BeanUtils.copyProperties(reportNoteDTO, reportNote);
            reportNote.setProjectId(saveVO.getProjectId());
            return reportNote;
        }).collect(Collectors.toList());
        this.baseMapper.batchSaveReportNote(reportNotes, userId);
    }

    @Override
    public List<ReportNoteDTO> listReportNote(ReportNoteListVO listVO) {
        return this.baseMapper.listReportNote(new QueryWrapper<ReportNote>().lambda()
                .eq(ReportNote::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getProjectId()), ReportNote::getProjectId, listVO.getProjectId())
                .in(CollectionUtils.isNotEmpty(listVO.getReportDates()), ReportNote::getReportDate, listVO.getReportDates()));
    }
}
