package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.CascaderDTO;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.TreeNodeDTO;
import com.overseas.common.dto.TreeNodeDTO2;
import com.overseas.common.dto.market.rtaStrategy.RtaCountryDTO;
import com.overseas.common.dto.market.rtaStrategy.RtaStrategyGetDTO;
import com.overseas.common.dto.market.rtaStrategy.RtaStrategyListDTO;
import com.overseas.common.dto.market.rtaStrategy.project.RtaStrategyAeDTO;
import com.overseas.common.dto.market.rtaStrategy.project.RtaStrategyLazadaDTO;
import com.overseas.common.dto.report.revenue.RevenueDspResultDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.enums.MachineRoomEnum;
import com.overseas.common.enums.ProjectEnum;
import com.overseas.common.enums.market.PlanSlotTypeEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ExcelUtils;
import com.overseas.common.utils.HttpUtils;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.asset.AssetCIdGetVO;
import com.overseas.common.vo.market.plan.direct.RtaSelectGetVO;
import com.overseas.common.vo.market.rtaStrategy.*;
import com.overseas.service.market.entity.CountryAll;
import com.overseas.service.market.entity.Plan;
import com.overseas.service.market.entity.RtaGroup;
import com.overseas.service.market.entity.RtaStrategy;
import com.overseas.common.enums.market.rta.RtaGroupEnum;
import com.overseas.common.enums.market.rta.RtaGroupStatusEnum;
import com.overseas.common.enums.market.rta.RtaStrategyStatusEnum;
import com.overseas.service.market.feign.FgSystemService;
import com.overseas.service.market.mapper.*;
import com.overseas.service.market.service.AssetService;
import com.overseas.service.market.service.RtaStrategyService;
import com.overseas.service.market.vo.rta.RtaStrategyCascaderGetVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RtaStrategyServiceImpl extends ServiceImpl<RtaStrategyMapper, RtaStrategy> implements RtaStrategyService {

    private final FgSystemService fgSystemService;

    private final AssetService assetService;

    private final RtaGroupMapper rtaGroupMapper;

    private final CountryAllMapper countryAllMapper;

    private final PlanMapper planMapper;

    private final RtaStrategyAllMapper rtaStrategyAllMapper;

    @Override
    public PageUtils<RtaStrategyListDTO> listRtaStrategy(RtaStrategyListVO listVO) {

        if (listVO.getMasterIds().isEmpty()) {
            return new PageUtils<>(List.of(), 0L);
        }
        IPage<RtaStrategyListDTO> pageData = this.baseMapper.listRtaStrategyPage(
                new Page<>(listVO.getPage(), listVO.getPageNum()),
                new QueryWrapper<RtaStrategy>().lambda()
                        .eq(ObjectUtils.isNotNullOrZero(listVO.getRtaStatus()),
                                RtaStrategy::getRtaStatus, listVO.getRtaStatus())
                        .eq(RtaStrategy::getIsDel, IsDelEnum.NORMAL.getId())
                        .eq(ObjectUtils.isNotNullOrZero(listVO.getRtaGroupId()),
                                RtaStrategy::getRtaGroupId, listVO.getRtaGroupId())
                        .in(ObjectUtils.isNullOrZero(listVO.getMasterId()),
                                RtaStrategy::getMasterId, listVO.getMasterIds())
                        .eq(ObjectUtils.isNotNullOrZero(listVO.getMasterId()),
                                RtaStrategy::getMasterId, listVO.getMasterId())
                        .and(StringUtils.isNotBlank(listVO.getSearch()),
                                q -> q.like(RtaStrategy::getId, listVO.getSearch())
                                        .or().like(RtaStrategy::getRtaStrategyName, listVO.getSearch())
                        ).orderByDesc(RtaStrategy::getId)
        );
        pageData.getRecords().forEach(
                record -> record.setRtaStatusName(
                        ICommonEnum.getNameById(record.getRtaStatus(), RtaStrategyStatusEnum.class))
        );
        return new PageUtils<>(pageData);
    }

    @Override
    public List<CascaderDTO> getRtaStrategyCascader(RtaStrategyCascaderGetVO getVO) {

        // 1.查询当前广告主下的RTA策略
        List<RtaStrategy> rtaStrategies = this.baseMapper.selectList(new QueryWrapper<RtaStrategy>().lambda()
                .eq(RtaStrategy::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(RtaStrategy::getMasterId, getVO.getMasterId())
                .orderByDesc(RtaStrategy::getId)
        );
        if (rtaStrategies.isEmpty()) {
            return List.of();
        }
        // 2.查询RTA策略组
        List<Long> groupIds = rtaStrategies.stream().map(RtaStrategy::getRtaGroupId).distinct().collect(Collectors.toList());
        List<CascaderDTO> rtaList = this.baseMapper.getRtaGroupCascader(new QueryWrapper<RtaStrategy>()
                        .eq("is_del", IsDelEnum.NORMAL.getId())
                        .eq("rta_group_status", RtaGroupStatusEnum.PUBLISHING_COMPLETE.getId())
                        .eq("rta_group_type", getVO.getRtaGroupType())
                        .in("id", groupIds)
                        .orderByDesc("id")).stream()
                .map(
                        u -> new CascaderDTO(u.getId(), u.getRtaGroupName(), RtaStrategyStatusEnum.OPEN.getId(),
                                new ArrayList<>())
                )
                .collect(Collectors.toList());
        if (rtaList.isEmpty()) {
            return List.of();
        }
        // 3.组装数据
        Map<Long, List<RtaStrategy>> rtaStrategyMap = rtaStrategies.stream()
                .sorted(Comparator.comparing(RtaStrategy::getRtaStatus))
                .collect(Collectors.groupingBy(RtaStrategy::getRtaGroupId));
        rtaList.forEach(rta -> rta.setChildren(
                        rtaStrategyMap.get(rta.getValue()).stream().map(
                                        u -> new CascaderDTO(u.getId(), u.getRtaStrategyName(), u.getRtaStatus(), List.of()))
                                .collect(Collectors.toList())
                )
        );
        return rtaList;
    }

    @Override
    public List<SelectDTO> selectRtaStrategy(RtaSelectGetVO getVO) {

        return this.baseMapper.selectRtaStrategy(new QueryWrapper<RtaStrategy>().lambda()
                .eq(RtaStrategy::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getRtaGroupId()), RtaStrategy::getRtaGroupId, getVO.getRtaGroupId())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getMasterId()), RtaStrategy::getMasterId, getVO.getMasterId())
                .in(ObjectUtils.isNullOrZero(getVO.getMasterId())
                        && ObjectUtils.isNotNullOrZero(getVO.getIsSearchMasterIds()), RtaStrategy::getMasterId, this.fgSystemService.listMasterId().getData())
                .in(CollectionUtils.isNotEmpty(getVO.getIds()), RtaStrategy::getId, getVO.getIds())
                .in(CollectionUtils.isNotEmpty(getVO.getCampaignIds()), RtaStrategy::getCampaignId, getVO.getCampaignIds())
                .and(StringUtils.isNotBlank(getVO.getSearch()), q ->
                        q.like(RtaStrategy::getRtaStrategyName, getVO.getSearch())
                                .or().eq(RtaStrategy::getId, getVO.getSearch())
                ).orderByDesc(RtaStrategy::getId));
    }

    @Override
    public List<TreeNodeDTO> getRtaStrategyTree(RtaStrategyCascaderGetVO getVO) {

        List<TreeNodeDTO2> treeNodeDTO2s = this.baseMapper.getRtaStrategyTree(new QueryWrapper<RtaStrategy>()
                .eq("drg.is_del", IsDelEnum.NORMAL.getId())
                .eq("drs.is_del", IsDelEnum.NORMAL.getId())
                .eq("drs.master_id", getVO.getMasterId())
                .eq("drg.rta_group_status", RtaGroupStatusEnum.PUBLISHING_COMPLETE.getId())
                .eq("drg.rta_group_type", getVO.getRtaGroupType()));

        Map<Long, TreeNodeDTO> rtaGroupMap = new HashMap<>();
        List<TreeNodeDTO> rtaStrategies = new ArrayList<>();
        treeNodeDTO2s.forEach(treeNodeDTO2 -> {
            rtaGroupMap.putIfAbsent(treeNodeDTO2.getPId(), new TreeNodeDTO(treeNodeDTO2.getPId(), treeNodeDTO2.getPName(), 0L));
            rtaStrategies.add(new TreeNodeDTO(treeNodeDTO2.getId(), treeNodeDTO2.getName(), treeNodeDTO2.getPId()));
        });
        return new ArrayList<>() {{
            addAll(rtaGroupMap.values());
            addAll(rtaStrategies);
        }};
    }

    @Override
    public List<RtaStrategyListDTO> listRtaStrategy(RtaSelectGetVO getVO) {
        return this.baseMapper.listRtaStrategy(new QueryWrapper<RtaStrategy>()
                .eq("drs.is_del", IsDelEnum.NORMAL.getId())
                .in(CollectionUtils.isNotEmpty(getVO.getIds()), "drs.id", getVO.getIds())
                .in(CollectionUtils.isNotEmpty(getVO.getCampaignIds()), "drs.campaign_id", getVO.getCampaignIds())
                .orderByDesc("drs.id"));
    }

    @Override
    public List<RtaStrategyListDTO> listRtaStrategyAll(RtaSelectGetVO getVO) {
        return this.rtaStrategyAllMapper.listRtaStrategyAll(new QueryWrapper<RtaStrategy>()
                .in(CollectionUtils.isNotEmpty(getVO.getIds()), "drsa.rta_strategy_id", getVO.getIds())
                .in(CollectionUtils.isNotEmpty(getVO.getCampaignIds()), "drsa.campaign_id", getVO.getCampaignIds())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getRtaGroupId()), "drsa.rta_group_id", getVO.getRtaGroupId())
                .orderByDesc("drsa.id"));
    }

    @Override
    public List<RtaStrategyListDTO> listAeRtaStrategy() {
        return this.baseMapper.listRtaStrategy(new QueryWrapper<RtaStrategy>()
                .eq("drs.is_del", IsDelEnum.NORMAL.getId())
                .isNotNull("drs.campaign_id")
                .gt("drs.cost_rate", 0)
        );
    }

    @Override
    public void updateRtaStrategyCostRate(RtaStrategyUpdateVO updateVO, Integer userId) {

        RtaStrategy rtaStrategy = new RtaStrategy();
        BeanUtils.copyProperties(updateVO, rtaStrategy, "rtaStatus");
        if (ObjectUtils.isNotNullOrZero(updateVO.getRtaStatus())) {
            rtaStrategy.setRtaStatus(updateVO.getRtaStatus());
        }
        rtaStrategy.setUpdateUid(userId);
        updateVO.getIds().forEach(
                id -> this.baseMapper.update(rtaStrategy,
                        new QueryWrapper<RtaStrategy>().lambda().eq(RtaStrategy::getId, id)
                )
        );
    }

    @Override
    public void batchSaveRtaStrategy(RtaStrategyBatchSaveVO saveVO, Integer userId) {

        // 根据RTA策略组解析不同Excel模板
        RtaGroupEnum rtaGroupEnum = ICommonEnum.get(saveVO.getRtaGroupId().intValue(), RtaGroupEnum.class);

        RtaGroup rtaGroup = this.rtaGroupMapper.selectById(saveVO.getRtaGroupId());
        Map<String, Object> rtaContentMap = JSONObject.parseObject(rtaGroup.getRtaContent());
        // 去除map内公共字段
        rtaContentMap.remove("Banner");
        rtaContentMap.remove("Native");
        rtaContentMap.remove("Video");
        List<RtaStrategy> rtaStrategies;
        switch (rtaGroupEnum) {
            case LAZADA_RTA:
            case LAZADA_RTB:
            case LAZADA_ORTB:
            case MIRAVIA:
            case MIRAVIA_RT:
            case MIRAVIA_CPS:
                rtaStrategies = this.getLazadaRtaStrategies(saveVO, rtaContentMap);
                break;
            case AE:
                rtaStrategies = this.getAeRtaStrategies(saveVO, rtaContentMap);
                break;
            default:
                return;
        }
        if (rtaStrategies.isEmpty()) {
            return;
        }

        this.baseMapper.batchSaveRtaStrategy(rtaStrategies, userId);
    }

    @Override
    public RtaStrategy getRtaStrategy(RtaStrategyGetVO getVO) {
        RtaStrategy dbInfo = this.baseMapper.selectById(getVO.getId());
        RtaStrategy rtaStrategy = new RtaStrategy();
        BeanUtils.copyProperties(dbInfo, rtaStrategy);
        log.error("rta :{}", JSONObject.toJSONString(rtaStrategy.getDeeplink()));

        // 如果需要替换宏规则
        if (ObjectUtils.isNotNullOrZero(getVO.getIsReplaceMacro())) {
            RtaGroup rtaGroup = this.rtaGroupMapper.selectById(rtaStrategy.getRtaGroupId());
            // 根据广告形式获取adType
            if (ObjectUtils.isNullOrZero(getVO.getSlotType())) {
                throw new CustomException("广告形式不能为空");
            }
            Map<String, Object> macroMap = JSONObject.parseObject(rtaGroup.getRtaMacro());
            String cId = "";
            // 如果是Lazada则获取cid、配置adtype
            List<Long> lazadaProjectIds = List.of(
                    RtaGroupEnum.MIRAVIA_RT.getId().longValue(),
                    RtaGroupEnum.MIRAVIA.getId().longValue(),
                    ProjectEnum.LAZADA_RTA.getRtaGroupId(),
                    ProjectEnum.LAZADA_RTB.getRtaGroupId(),
                    RtaGroupEnum.LAZADA_ORTB.getId().longValue()
            );
            if (lazadaProjectIds.contains(rtaStrategy.getRtaGroupId())) {
                // 替换c_id
                cId = "__LZD_CID__";
                // 替换ad_type
                if (macroMap != null) {
                    macroMap.put("__adtype__", JSONObject.parseObject(
                            rtaGroup.getRtaContent()).get(
                            ICommonEnum.getNameById(getVO.getSlotType(), PlanSlotTypeEnum.class))
                    );
                }
                // 替换商品ID
                if (List.of(ProjectEnum.LAZADA_RTA.getRtaGroupId(), ProjectEnum.LAZADA_RTB.getRtaGroupId(),
                        RtaGroupEnum.LAZADA_ORTB.getId().longValue()).contains(rtaStrategy.getRtaGroupId())) {
                    this.fillGoodsId(rtaStrategy);
                }
            }
            rtaStrategy.setDeeplink(this.replaceMacroOfUrl(rtaStrategy.getDeeplink(), macroMap, cId));
            rtaStrategy.setLandingUrl(this.replaceMacroOfUrl(rtaStrategy.getLandingUrl(), macroMap, cId));
            rtaStrategy.setMonitorViewUrl(this.replaceMacroOfUrl(rtaStrategy.getMonitorViewUrl(), macroMap, cId));
            rtaStrategy.setMonitorClickUrl(this.replaceMacroOfUrl(rtaStrategy.getMonitorClickUrl(), macroMap, cId));
        }
        return rtaStrategy;
    }

    @Override
    public List<RtaStrategyGetDTO> getRtaStrategyByPlanIds(RtaStrategyGetByPlanIdsVO getVO) {
        //获取计划创意数据
        List<Integer> slotTypes = this.planMapper.selectList(new LambdaQueryWrapper<Plan>()
                .in(Plan::getId, getVO.getPlanIds())
        ).stream().map(Plan::getSlotType).distinct().collect(Collectors.toList());
        return slotTypes.stream().map(slotType -> {
            RtaStrategyGetDTO getDTO = new RtaStrategyGetDTO();
            RtaStrategy rtaStrategy = this.getRtaStrategy(
                    RtaStrategyGetVO.builder().slotType(slotType).id(getVO.getId())
                            .isReplaceMacro(getVO.getIsReplaceMacro()).build()
            );
            BeanUtils.copyProperties(rtaStrategy, getDTO);
            getDTO.setSlotType(slotType);
            getDTO.setSlotTypeName(ICommonEnum.getNameById(slotType, PlanSlotTypeEnum.class));
            return getDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public void fillGoodsId(RtaStrategy rtaStrategy) {
        rtaStrategy.setDeeplink(this.replaceLinkGoodsId(rtaStrategy.getDeeplink()));
        rtaStrategy.setLandingUrl(this.replaceLinkGoodsId(rtaStrategy.getLandingUrl()));
        rtaStrategy.setMonitorViewUrl(this.replaceLinkGoodsId(rtaStrategy.getMonitorViewUrl()));
        rtaStrategy.setMonitorClickUrl(this.replaceLinkGoodsId(rtaStrategy.getMonitorClickUrl()));
    }

    private String replaceLinkGoodsId(String link) {
        if (link.contains("?")) {
            link += "&trigger_item=__TRIGGER_ITEM__&sub_cluster_id=";
            // link += "&trigger_item=__TRIGGER_ITEM__&sub_cluster_id=__SUB_CLUSTER_ID__";
        } else {
            link += "?trigger_item=__TRIGGER_ITEM__&sub_cluster_id=";
            // link += "?trigger_item=__TRIGGER_ITEM__&sub_cluster_id=__SUB_CLUSTER_ID__";
        }
        return link;
    }

    @Override
    public String getRtaStrategyCId(RtaStrategy rtaStrategy, Integer assetType) {

        AssetCIdGetVO cIdGetVO = new AssetCIdGetVO();
        cIdGetVO.setCount(20);
        cIdGetVO.setMasterId(rtaStrategy.getMasterId());
        cIdGetVO.setAssetType(assetType);
        List<String> cIds = this.assetService.listAssetCIdByMasterId(cIdGetVO);

        return CollectionUtils.isNotEmpty(cIds) ? cIds.get((int) (Math.random() * cIds.size())) : "";
    }

    /**
     * 获取Lazada需要保存的RTA策略列表
     *
     * @param saveVO        传入参数
     * @param rtaContentMap RTA填充内容
     * @return 返回数据
     */
    private List<RtaStrategy> getLazadaRtaStrategies(RtaStrategyBatchSaveVO saveVO, Map<String, Object> rtaContentMap) {

        List<RtaStrategyLazadaDTO> rtaStrategyDTOs;
        try {
            rtaStrategyDTOs = ExcelUtils.read(saveVO.getFilePath(), RtaStrategyLazadaDTO.class);

            Map<String, String> countryAliasMap = saveVO.getRtaGroupId().equals(RtaGroupEnum.LAZADA_ORTB.getId().longValue())
                    ? this.countryAllMapper.listCountryAll(new QueryWrapper<CountryAll>().lambda()
                            .in(CountryAll::getCountryAlias, rtaStrategyDTOs.stream().map(u ->
                                    Arrays.stream(u.getTaskName().split("_"))
                                            .collect(Collectors.toList()).get(1)).collect(Collectors.toList())))
                    .stream().collect(Collectors.toMap(CountryAll::getCountryAlias, CountryAll::getCountryAbbreviation))
                    : new HashMap<>();

            return rtaStrategyDTOs.stream().map(rtaStrategyDTO -> {
                List<String> campaignNames = Arrays.stream(rtaStrategyDTO.getCampaignName().split("_"))
                        .collect(Collectors.toList());
                List<String> taskNames = Arrays.stream(rtaStrategyDTO.getTaskName().split("_"))
                        .collect(Collectors.toList());
                RtaStrategy rtaStrategy = this.getRtaStrategy(
                        saveVO.getRtaGroupId(),
                        String.join("_", campaignNames.subList(3, campaignNames.size())),
                        saveVO.getMasterId(),
                        rtaStrategyDTO.getCampaignId(),
                        rtaStrategyDTO.getCampaignName(),
                        RtaStrategyStatusEnum.getByStatusStr(rtaStrategyDTO.getStatus(), "lazada").getId(),
                        taskNames.get(1),
                        rtaStrategyDTO.getDeeplink(),
                        rtaStrategyDTO.getLandingUrl(),
                        rtaStrategyDTO.getMonitorViewLink(),
                        rtaStrategyDTO.getMonitorClickLink()
                );
                Map<String, Object> rtaContent = new HashMap<>() {{
                    put("member_id", rtaStrategyDTO.getMemberId());
                    put("campaign_id", rtaStrategy.getCampaignId());
                    put("judge_expression", "#resMap['" + rtaStrategy.getCampaignId() + "']");
                    put("campaign_id_list", rtaStrategy.getCampaignId());
                    if (saveVO.getRtaGroupId().equals(RtaGroupEnum.LAZADA_ORTB.getId().longValue())) {
                        put("venture", countryAliasMap.get(rtaStrategy.getRtaCountry()));
                        put("security_key", "4c8afead49cd2abc939267e5ce09692d408e12a1");
                    } else {
                        put("country", rtaStrategy.getRtaCountry());
                        putAll(rtaContentMap);
                    }
                }};
                rtaStrategy.setRtaContent(JSONObject.toJSONString(rtaContent));
                return rtaStrategy;
            }).collect(Collectors.toList());
        } catch (Exception exception) {
            throw new CustomException("文件解析异常，请确认文件内容与当前所选项目一致");
        }
    }

    /**
     * 获取AE需要保存的RTA策略列表
     *
     * @param saveVO        传入参数
     * @param rtaContentMap RTA填充内容
     * @return 返回数据
     */
    private List<RtaStrategy> getAeRtaStrategies(RtaStrategyBatchSaveVO saveVO, Map<String, Object> rtaContentMap) {

        List<RtaStrategyAeDTO> rtaStrategyDTOs;
        try {
            rtaStrategyDTOs = ExcelUtils.read(saveVO.getFilePath(), RtaStrategyAeDTO.class);

            return rtaStrategyDTOs.stream().map(rtaStrategyDTO -> {
                RtaStrategy rtaStrategy = this.getRtaStrategy(
                        saveVO.getRtaGroupId(),
                        rtaStrategyDTO.getCampaignName(),
                        saveVO.getMasterId(),
                        rtaStrategyDTO.getCampaignId(),
                        rtaStrategyDTO.getCampaignName(),
                        RtaStrategyStatusEnum.getByStatusStr(rtaStrategyDTO.getOnline(), "ae").getId(),
                        rtaStrategyDTO.getCountry(),
                        "",
                        rtaStrategyDTO.getLandingUrl(),
                        "",
                        ""
                );
                Map<String, Object> rtaContent = new HashMap<>() {{
                    putAll(rtaContentMap);
                    put("group", rtaStrategy.getRtaCountry());
                    put("campaign_id", rtaStrategy.getCampaignId());
                    put("dpaReplace", new HashMap<>() {{
                        put("${rta_sub_id1}", rtaStrategyDTO.getCountry());
                        put("${rta_sub_id2}", "ae_yxy");
                        put("${rta_sub_id3}", "__IFLYUID__");
                        put("${rta_sub_id4}", "__BIDID__");
                        put("${rta_sub_id5}", "__INFO__");
                    }});
                }};
                rtaStrategy.setRtaContent(JSONObject.toJSONString(rtaContent));
                return rtaStrategy;
            }).collect(Collectors.toList());
        } catch (Exception exception) {
            throw new CustomException("文件解析异常，请确认文件内容与当前所选项目一致");
        }
    }

    /**
     * 获取存储RTA策略的实体
     *
     * @param rtaGroupId      RTA策略组ID
     * @param rtaStrategyName RTA策略名称
     * @param masterId        账号ID
     * @param campaignId      RTA侧活动ID
     * @param rtaStatus       RTA状态
     * @param rtaCountry      RTA国家
     * @param deeplink        dp
     * @param landingUrl      落地页
     * @param monitorViewUrl  曝光监测
     * @param monitorClickUrl 点击监测
     * @return 返回数据
     */
    private RtaStrategy getRtaStrategy(Long rtaGroupId, String rtaStrategyName, Long masterId, String campaignId,
                                       String campaignName, Integer rtaStatus, String rtaCountry, String deeplink,
                                       String landingUrl, String monitorViewUrl, String monitorClickUrl) {
        RtaStrategy rtaStrategy = new RtaStrategy();
        rtaStrategy.setRtaGroupId(rtaGroupId);
        rtaStrategy.setRtaStrategyName(rtaStrategyName);
        rtaStrategy.setMasterId(masterId);
        rtaStrategy.setCampaignId(campaignId);
        rtaStrategy.setCampaignName(campaignName);
        rtaStrategy.setRtaStatus(rtaStatus);
        rtaStrategy.setRtaCountry(rtaCountry);
        rtaStrategy.setDeeplink(deeplink);
        rtaStrategy.setLandingUrl(landingUrl);
        rtaStrategy.setMonitorViewUrl(monitorViewUrl);
        rtaStrategy.setMonitorClickUrl(monitorClickUrl);
        return rtaStrategy;
    }

    /**
     * 根据宏替换规则替换链接中的宏配置
     *
     * @param url      链接
     * @param macroMap 宏替换规则
     * @return 返回链接
     */
    @Override
    public String replaceMacroOfUrl(String url, Map<String, Object> macroMap, String cId) {

        String str = StringUtils.isNotBlank(cId) ? url.replace("__lzdcid__", cId) : url;
        if (StringUtils.isBlank(str) || macroMap == null) {
            return str;
        }
        for (Map.Entry<String, Object> entry : macroMap.entrySet()) {
            str = str.replace(entry.getKey(), entry.getValue().toString());
        }
        return str;
    }

    @Override
    public List<RtaCountryDTO> listRtaCountry() {
        return this.baseMapper.listRtaCountry(new QueryWrapper<RtaStrategy>()
                .eq("drs.is_del", IsDelEnum.NORMAL.getId()));
    }

    @Override
    public List<SelectDTO> selectRtaGroup() {
        return this.baseMapper.selectRtaGroup(new QueryWrapper<RtaGroup>().lambda()
                .eq(RtaGroup::getIsDel, IsDelEnum.NORMAL.getId())
                .orderByAsc(RtaGroup::getId));
    }

    @Override
    public void pull2All() {
        List<RtaStrategyListDTO> rtaStrategies = getOtherData("/market/rtaStrategies/byId/list",
                new JSONObject(), new TypeReference<>() {
                });
        if (rtaStrategies.isEmpty()) {
            return;
        }
        rtaStrategyAllMapper.insertByUk(rtaStrategies);
    }

    /**
     * 获取数据
     *
     * @param method        请求
     * @param params        参数
     * @param typeReference 参数
     * @param <T>           返回数据类型
     * @return 返回数据
     */
    private <T> List<T> getOtherData(String method, JSONObject params,
                                     TypeReference<RevenueDspResultDTO<List<T>>> typeReference) {
        List<T> result = new ArrayList<>();
        for (MachineRoomEnum roomEnum : MachineRoomEnum.values()) {
            try {
                String resp = HttpUtils.post(roomEnum.getApiUrl() + method, params,
                        new HashMap<>() {
                            {
                                put("Access-Token", "UNK");
                                put("Access-Tokens", "UNK");
                            }
                        });
                RevenueDspResultDTO<List<T>> resultDTO = JSONObject.parseObject(resp, typeReference);
                resultDTO.isRight();
                if (!resultDTO.getData().isEmpty() && resultDTO.getData().get(0) instanceof RtaStrategyListDTO) {
                    result.addAll(resultDTO.getData().stream()
                            .peek(u -> ((RtaStrategyListDTO) u).setNodeId(roomEnum.getNodeId()))
                            .collect(Collectors.toList()));
                } else {
                    result.addAll(resultDTO.getData());
                }
            } catch (Exception e) {
                log.error("请求失败，请求地址：{} 请求参数 {}", roomEnum.getApiUrl() + method, params.toJSONString());
            }
        }
        return result;
    }
}
