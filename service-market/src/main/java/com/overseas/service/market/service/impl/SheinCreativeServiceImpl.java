package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.vo.market.plan.direct.CreativeUnitUpdateVO;
import com.overseas.service.market.entity.*;
import com.overseas.service.market.enums.creative.units.CreativeUnitStatusEnum;
import com.overseas.service.market.enums.plan.PlanGenerateMapTypeEnum;
import com.overseas.service.market.events.creativeUnitAudit.CreativeUnitAuditEvent;
import com.overseas.service.market.events.notifyControl.ControlContants;
import com.overseas.service.market.events.notifyControl.ControlPlanEvent;
import com.overseas.service.market.mapper.CreativeMapper;
import com.overseas.service.market.mapper.CreativeUnitMapper;
import com.overseas.service.market.mapper.PlanGenerateMapMapper;
import com.overseas.service.market.mapper.PlanMapper;
import com.overseas.service.market.service.PlanUpdateRecordService;
import com.overseas.service.market.service.SheinCreativeService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@RequiredArgsConstructor
@Service
@Slf4j
public class SheinCreativeServiceImpl implements SheinCreativeService {

    private final CreativeMapper creativeMapper;

    private final CreativeUnitMapper creativeUnitMapper;

    private final PlanUpdateRecordService planUpdateRecordService;

    private final PlanMapper planMapper;

    private final PlanGenerateMapMapper planGenerateMapMapper;

    private final ApplicationContext applicationContext;

    @Override
    public void logByUnitIds(List<Long> unitIds, Integer operateUid) {
        List<CreativeUnit> creativeUnits = creativeUnitMapper.selectBatchIds(unitIds);
        if (CollectionUtils.isEmpty(creativeUnits)) {
            return;
        }
        this.logCreativeUpdate(creativeUnits.stream().map(CreativeUnit::getPlanId).distinct().collect(Collectors.toList()), operateUid);
    }

    @Override
    public void logCreativeUpdate(List<Long> planIds, Integer operateUid) {
        if (CollectionUtils.isEmpty(planIds)) {
            return;
        }
        planIds.forEach(planId -> {
            try {
                this.logCreativeUpdate(planId, operateUid);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
    }


    @Override
    public void logCreativeUpdate(Long planId, Integer operateUid) {
        CompletableFuture.runAsync(() -> {
            try {
                this.logInfo(planId, operateUid);
            } catch (Exception e) {
                log.error("创意日志记录失败,原因:{}", e.getMessage(), e);
            }
        });
    }

    /**
     * 记录日志
     *
     * @param planId     计划
     * @param operateUid 操作用户
     */
    private void logInfo(Long planId, Integer operateUid) {
        if (ObjectUtils.isNullOrZero(planId)) {
            return;
        }
        Plan plan = this.planMapper.selectById(planId);
        if (null == plan) {
            return;
        }
        Creative creative = this.creativeMapper.selectOne(new LambdaQueryWrapper<Creative>()
                .eq(Creative::getPlanId, planId)
                .eq(Creative::getIsDel, IsDelEnum.NORMAL.getId())
        );
        if (null == creative) {
            return;
        }
        List<CreativeUnit> creativeUnits = creativeUnitMapper.selectList(new LambdaQueryWrapper<CreativeUnit>()
                .eq(CreativeUnit::getCreativeId, creative.getId())
                .eq(CreativeUnit::getIsDel, IsDelEnum.NORMAL.getId())
        );
        if (creativeUnits.isEmpty()) {
            return;
        }
        List<CreativeUnitUpdateVO> creativeUnitUpdateList = creativeUnits.stream()
                .map(u -> CreativeUnitUpdateVO.builder().creativeUnitId(u.getId())
                        .creativeUnitStatus(u.getCreativeUnitStatus())
                        .isDel(u.getIsDel()).build()
                ).collect(Collectors.toList());
        Map<String, Object> map = new HashMap<>() {{
            put("creative_logo", creative.getBrandLogoId());
            put("creative_brand", creative.getBrandName());
            put("creative_app_icon", creative.getAppIconId());
            put("creative_source", creative.getMarketSource());
            put("creative_cta_id", creative.getCtaId());
            put("creative_units", JSONObject.toJSONString(creativeUnitUpdateList));
        }};
        PlanUpdateRecord planUpdateRecord = new PlanUpdateRecord();
        planUpdateRecord.setPlanId(creative.getPlanId());
        planUpdateRecord.setContent(JSONObject.toJSONString(map));
        planUpdateRecordService.savePlanUpdateRecord(List.of(planUpdateRecord), operateUid);
        //如果含有1：1影子计划,同步判定创意信息
//        if (ObjectUtils.isNotNullOrZero(plan.getShadowPlanId())) {
//            this.unitUpdateShein(plan, creativeUnits, operateUid);
//        }
    }

    /**
     * 更新update shein
     *
     * @param plan          计划ID
     * @param creativeUnits 创意单元
     * @param operateUid    操作用户
     */
    private void unitUpdateShein(Plan plan, List<CreativeUnit> creativeUnits, Integer operateUid) {
        Plan shadowPlan = planMapper.selectById(plan.getShadowPlanId());
        Creative shadowCreative = creativeMapper.selectOne(new LambdaQueryWrapper<Creative>().eq(Creative::getPlanId, shadowPlan.getId()));
        Map<Long, Long> creativeMap = planGenerateMapMapper.selectList(new LambdaQueryWrapper<PlanGenerateMap>()
                .eq(PlanGenerateMap::getMapType, PlanGenerateMapTypeEnum.CREATIVE.getId())
                .eq(PlanGenerateMap::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(PlanGenerateMap::getSheinPlanId, plan.getId())
        ).stream().collect(Collectors.toMap(PlanGenerateMap::getSheinId, PlanGenerateMap::getMarketId));
        UnitUp unitUp = new UnitUp();
        Long maxIndex = creativeUnits.stream().map(CreativeUnit::getCreativeIndex).max(Comparator.comparingLong(o -> o)).get();
        AtomicLong index = new AtomicLong(maxIndex);
        creativeUnits.stream().filter(creativeUnit ->
                creativeUnit.getIsDel().equals(IsDelEnum.NORMAL.getId().intValue()) &&
                        List.of(CreativeUnitStatusEnum.MARKETING.getId(), CreativeUnitStatusEnum.STOP.getId()).contains(creativeUnit.getCreativeUnitStatus())
        ).forEach(creativeUnit -> {
            Long oldCreativeUnitId = creativeUnit.getId();
            Long creativeUnitId = creativeMap.get(creativeUnit.getId());
            if (ObjectUtils.isNotNullOrZero(creativeUnitId)) {
                if (CreativeUnitStatusEnum.MARKETING.getId().equals(creativeUnit.getCreativeUnitStatus())) {
                    unitUp.getMarketIds().add(creativeUnitId);
                } else {
                    unitUp.getStopIds().add(creativeUnitId);
                }
            } else {
                //创意新创意
                creativeUnit.setId(null);
                creativeUnit.setCreateUid(plan.getCreateUid());
                creativeUnit.setUpdateUid(0);
                creativeUnit.setCampaignId(shadowPlan.getCampaignId());
                creativeUnit.setPlanId(shadowPlan.getId());
                creativeUnit.setCreativeId(shadowCreative.getId());
                creativeUnit.setCreativeIndex(index.getAndIncrement());
                this.creativeUnitMapper.insert(creativeUnit);
                unitUp.getAddIds().add(creativeUnit.getId());
                //插入新计划数据
                PlanGenerateMap planGenerateMap = new PlanGenerateMap();
                planGenerateMap.setSheinPlanId(plan.getId());
                planGenerateMap.setSheinId(oldCreativeUnitId);
                planGenerateMap.setMarketId(creativeUnit.getId());
                planGenerateMap.setMapType(PlanGenerateMapTypeEnum.CREATIVE.getId());
                planGenerateMapMapper.insert(planGenerateMap);
            }
        });
        //stop创意
        if (CollectionUtils.isNotEmpty(unitUp.getStopIds())) {
            CreativeUnit stop = new CreativeUnit();
            stop.setCreativeUnitStatus(CreativeUnitStatusEnum.STOP.getId());
            creativeUnitMapper.update(stop, new LambdaQueryWrapper<CreativeUnit>()
                    .eq(CreativeUnit::getPlanId, shadowPlan.getId())
                    .in(CreativeUnit::getId, unitUp.getStopIds())
            );
        }
        //开启创意
        if (CollectionUtils.isNotEmpty(unitUp.getMarketIds())) {
            CreativeUnit market = new CreativeUnit();
            market.setCreativeUnitStatus(CreativeUnitStatusEnum.MARKETING.getId());
            creativeUnitMapper.update(market, new LambdaQueryWrapper<CreativeUnit>()
                    .eq(CreativeUnit::getPlanId, shadowPlan.getId())
                    .in(CreativeUnit::getId, unitUp.getMarketIds())
            );
        }
        //删除映射关系记录表
        List<Long> result = new ArrayList<>();
        result.addAll(unitUp.getAddIds());
        result.addAll(unitUp.getMarketIds());
        result.addAll(unitUp.getStopIds());
        PlanGenerateMap del = new PlanGenerateMap();
        del.setIsDel(IsDelEnum.DELETE.getId().intValue());
        planGenerateMapMapper.update(del, new LambdaQueryWrapper<PlanGenerateMap>()
                .eq(PlanGenerateMap::getMapType, PlanGenerateMapTypeEnum.CREATIVE.getId())
                .eq(PlanGenerateMap::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(PlanGenerateMap::getSheinPlanId, plan.getId())
                .notIn(CollectionUtils.isNotEmpty(result), PlanGenerateMap::getMarketId, result)
        );
        CreativeUnit pauseUnit = new CreativeUnit();
        pauseUnit.setCreativeUnitStatus(CreativeUnitStatusEnum.FILE.getId());
        this.creativeUnitMapper.update(pauseUnit, new LambdaQueryWrapper<CreativeUnit>()
                .eq(CreativeUnit::getPlanId, shadowPlan.getId())
                .eq(CreativeUnit::getIsDel, IsDelEnum.NORMAL.getId())
                .notIn(CollectionUtils.isNotEmpty(result), CreativeUnit::getId, result)
        );
        //增加操作通知限制
        this.applicationContext.publishEvent(new CreativeUnitAuditEvent(this, shadowPlan.getId()));
        // 通知shein计划
        this.applicationContext.publishEvent(new ControlPlanEvent(this, ControlContants.METHOD_UPDATE, plan.getId()));
    }

    @Data
    public static class UnitUp {

        private List<Long> addIds = new ArrayList<>();

        private List<Long> stopIds = new ArrayList<>();

        private List<Long> marketIds = new ArrayList<>();
    }
}
