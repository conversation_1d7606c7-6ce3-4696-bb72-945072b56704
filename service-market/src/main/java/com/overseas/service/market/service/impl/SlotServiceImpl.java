package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.service.market.dto.slot.SlotDirectDTO;
import com.overseas.service.market.entity.Slot;
import com.overseas.service.market.vo.plan.DirectResourceVO;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.slot.SlotSelectGetVO;
import com.overseas.common.enums.OsTypeEnum;
import com.overseas.service.market.enums.media.MediaStatusEnum;
import com.overseas.service.market.enums.slot.SlotStatusEnum;
import com.overseas.service.market.mapper.SlotMapper;
import com.overseas.service.market.service.SlotService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-23 20:37
 */
@Service
public class SlotServiceImpl extends ServiceImpl<SlotMapper, Slot> implements SlotService {

    @Override
    public PageUtils<SlotDirectDTO> pageSlotDirect(DirectResourceVO directResourceVO) {
        PageUtils<SlotDirectDTO> emptyPage = new PageUtils<>(List.of(), 0L);
        IPage<SlotDirectDTO> iPage = new Page<>(directResourceVO.getPage(), directResourceVO.getPageNum());

        // 指定ID非空且为0长度时返回空list
        if (CollectionUtils.isEmpty(directResourceVO.getAdxId())
                || (null != directResourceVO.getAppointedIds() && directResourceVO.getAppointedIds().isEmpty())
                || ObjectUtils.isNullOrZero(directResourceVO.getSlotType())) {
            return emptyPage;
        }
        QueryWrapper<Slot> queryWrapper = new QueryWrapper<Slot>()
                .in("slot.adx_id", directResourceVO.getAdxId())
                .eq("slot.slot_status", SlotStatusEnum.NORMAL.getId())
                .eq("media.media_status", MediaStatusEnum.NORMAL.getId())
                .in(null != directResourceVO.getAppointedIds() && !directResourceVO.getAppointedIds().isEmpty(),
                        "slot.id", directResourceVO.getAppointedIds())
                .notIn(null != directResourceVO.getExcludeIds() && !directResourceVO.getExcludeIds().isEmpty(),
                        "slot.id", directResourceVO.getExcludeIds())
                .in(ObjectUtils.isNotNullOrZero(directResourceVO.getOsType()), "slot.os_type", List.of(0, directResourceVO.getOsType()))
                .and(StringUtils.isNotEmpty(directResourceVO.getSearch()),
                        i -> i.like("slot.id", directResourceVO.getSearch())
                                .or().like("slot.slot_name", directResourceVO.getSearch())
                                .or().like("adx.adx_name", directResourceVO.getSearch())
                                .or().like("media.media_name", directResourceVO.getSearch())
                )
                .eq("slot.slot_type", directResourceVO.getSlotType())
                .groupBy("slot.id")
                .orderByDesc("slot.request_quantity")
                .orderByDesc("slot.id");
        IPage<SlotDirectDTO> page = baseMapper.listSlotDirect(iPage, queryWrapper);
        page.getRecords().forEach(slot -> slot.setOsTypeName(OsTypeEnum.getName(slot.getOsType())));

        return new PageUtils<>(page);
    }

    @Override
    public List<SelectDTO> getSlotSelect(SlotSelectGetVO getVO) {

        return this.baseMapper.getSlotSelect(new QueryWrapper<Slot>()
                .eq(ObjectUtils.isNotAll(getVO.getSlotStatus()), "ds.slot_status", SlotStatusEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getMediaId()), "dsa.media_id", getVO.getMediaId())
                .in(CollectionUtils.isNotEmpty(getVO.getSlotIds()), "ds.id", getVO.getSlotIds())
                .like(StringUtils.isNotBlank(getVO.getSlotName()), "ds.slot_name", getVO.getSlotName())
                .orderByDesc("ds.id"));
    }
}
