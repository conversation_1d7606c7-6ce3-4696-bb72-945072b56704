package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSSException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.configuration.SheinConfiguration;
import com.overseas.common.utils.*;
import com.overseas.common.vo.market.tag.*;
import com.overseas.service.market.entity.*;
import com.overseas.service.market.enums.tag.*;
import com.overseas.service.market.mapper.TagResourceMapper;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.tag.TagGetDTO;
import com.overseas.common.dto.market.tag.TagListDTO;
import com.overseas.common.dto.market.tag.TagMasterDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.enums.OssBucketTypeEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.service.market.enums.user.UserTypeEnum;
import com.overseas.service.market.mapper.MasterMapper;
import com.overseas.service.market.mapper.TagExtraMapper;
import com.overseas.service.market.mapper.TagMapper;
import com.overseas.service.market.service.TagService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TagServiceImpl extends ServiceImpl<TagMapper, Tag> implements TagService {

    private final TagExtraMapper tagExtraMapper;

    private final TagResourceMapper tagResourceMapper;

    private final MasterMapper masterMapper;

    private final SheinConfiguration sheinConfiguration;

    private final SmsUtils smsUtils;

    private Tag checkTag(Long id, String tagName) {

        Tag tag = this.baseMapper.selectOne(new QueryWrapper<Tag>().lambda()
                .eq(Tag::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(id), Tag::getId, id)
                .eq(StringUtils.isNotBlank(tagName), Tag::getTagName, tagName));

        if (id == null) {
            if (tag != null) {
                throw new CustomException("标签名称已存在，请确认后再试");
            }
        } else if (tagName == null) {
            if (tag == null) {
                throw new CustomException("标签不存在，请确认后再试");
            }
        }
        return tag;
    }

    private Map<Long, List<Long>> getResourceMap(List<Long> tagIds) {
        return this.tagResourceMapper.selectList(new QueryWrapper<TagResource>().lambda()
                .eq(TagResource::getIsDel, IsDelEnum.NORMAL.getId())
                .in(TagResource::getTagId, tagIds)).stream().collect(Collectors.toMap(TagResource::getTagId, u -> new ArrayList<Long>() {{
            add(u.getResourceId());
        }}, (newVal, oldVal) -> {
            newVal.addAll(oldVal);
            return newVal;
        }));
    }

    /**
     * 上传至OSS
     *
     * @param tag 人群标签
     * @throws Exception 异常
     */
    private void uploadTagToOss(Tag tag) throws Exception {

        // 更改标签状态为上传中
        tag.setTagStatus(TagStatusEnum.PROCESS_UPLOAD.getId());
        this.baseMapper.update(tag, new QueryWrapper<Tag>().lambda().eq(Tag::getId, tag.getId()));

        TagExtra tagExtra = this.tagExtraMapper.selectOne(new QueryWrapper<TagExtra>().lambda().eq(TagExtra::getTagId, tag.getId()));

        // 获取标签设备号文件
        TagDeviceContentVO tagDeviceContent = JSONObject.parseObject(tagExtra.getTagContent(), TagDeviceContentVO.class);
        String filePath = UploadUtils.getUploadPath(tagDeviceContent.getDeviceUpload().getUrl());
        try {
            boolean uploadResult = OssUtils.uploadFile(filePath, tagDeviceContent.getDeviceUpload().getUrl(), OssBucketTypeEnum.LABEL);
            if (uploadResult) {
                tag.setTagStatus(TagStatusEnum.WAIT_CALCULATION.getId());
            }
        } catch (OSSException | ClientException ossException) {
            tag.setTagStatus(TagStatusEnum.UPLOAD_FAILED.getId());
        } finally {
            this.baseMapper.update(tag, new QueryWrapper<Tag>().lambda().eq(Tag::getId, tag.getId()));
        }
    }

    /**
     * 更新标签用户资源关系
     *
     * @param saveVO 传入参数
     * @param userId 用户ID
     */
    private void updateTagResource(TagResourceSaveVO saveVO, Integer userId) {

        TagResource tagResource = new TagResource();
        tagResource.setIsDel(IsDelEnum.DELETE.getId().intValue());
        tagResource.setUpdateUid(userId);
        this.tagResourceMapper.update(tagResource, new QueryWrapper<TagResource>().lambda()
                .eq(TagResource::getTagId, saveVO.getId())
                .eq(TagResource::getIsDel, IsDelEnum.NORMAL.getId()));

        switch (ICommonEnum.get(saveVO.getShareType(), TagShareTypeEnum.class)) {
            case ALL_ACCOUNT:
                saveVO.setResourceIds(this.masterMapper.selectByMasterIdAndUserId(saveVO.getMasterId(),
                                userId.longValue()).stream().map(SelectDTO::getId)
                        .filter(u -> !u.equals(saveVO.getMasterId())).collect(Collectors.toList()));
                break;
            case DESIGNATED_ACCOUNT:
                saveVO.setResourceIds(saveVO.getResourceIds());
                break;
            case NO_SHARE:
            default:
                return;
        }
        // 当共享范围为不共享、共享账户为空时，结束
        if (saveVO.getResourceIds().isEmpty()) {
            return;
        }
        this.tagResourceMapper.saveTagResource(saveVO.getId(), saveVO.getResourceIds(), userId);
    }

    private void checkIsExistResource() {

        List<TagMasterDTO> tagMasterDTOS = this.baseMapper.getTagMasterList(new QueryWrapper<Tag>()
                .eq("mt.is_del", IsDelEnum.NORMAL.getId())
                .eq("mt.share_type", TagShareTypeEnum.ALL_ACCOUNT.getId())
                .gt("mt.expire_date", DateUtils.getTodayDate())
                .eq("uu.user_type", UserTypeEnum.MANAGER.getId())
//                .in("mt.master_id", masterIds)
                .apply("uur.resource_id != uur2.resource_id")
                .groupBy("mt.id,uur.resource_id,uur2.resource_id"));
        if (tagMasterDTOS.isEmpty()) {
            return;
        }
        Map<Long, List<Long>> tagResourceMap = this.tagResourceMapper.selectList(new QueryWrapper<TagResource>().lambda()
                        .eq(TagResource::getIsDel, IsDelEnum.NORMAL.getId())
                        .in(TagResource::getTagId, tagMasterDTOS.stream().map(TagMasterDTO::getTagId).distinct().collect(Collectors.toList())))
                .stream().collect(Collectors.toMap(TagResource::getTagId, u -> new ArrayList<Long>() {{
                    add(u.getResourceId());
                }}, (newVal, oldVal) -> {
                    newVal.addAll(oldVal);
                    return newVal;
                }));
        List<TagResource> tagResources = tagMasterDTOS.stream().map(u -> {
            TagResource tagResource = new TagResource();
            tagResource.setTagId(u.getTagId());
            tagResource.setResourceId(u.getExpandMasterId());
            if (tagResourceMap.get(u.getTagId()) == null || (tagResourceMap.get(u.getTagId()) != null && !tagResourceMap.get(u.getTagId()).contains(u.getExpandMasterId()))) {
                return tagResource;
            } else {
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (tagResources.isEmpty()) {
            return;
        }

        this.tagResourceMapper.batchSaveTagResource(tagResources, 0);
    }

    @Override
    public PageUtils<TagListDTO> getTagPage(TagListVO listVO, List<Integer> permissionMasterIds) {

        IPage<TagListDTO> page = new Page<>(listVO.getPage(), listVO.getPageNum());
        IPage<TagListDTO> pageData = this.baseMapper.getTagPage(page, new QueryWrapper<Tag>()
                // 如果是管理平台，返回当前管理账户下所有广告主关联的人群标签
                .and(TagListTypeEnum.MANAGER_LIST.getId().equals(listVO.getListType()), q -> q
                        .or().in("mt.master_id", permissionMasterIds)
                        .or().in("mtr.resource_id", permissionMasterIds))
                // 如果是投放平台，返回当前广告主所关联的人群标签
                .and(TagListTypeEnum.MARKET_LIST.getId().equals(listVO.getListType()), q -> q
                        .or().eq("mt.master_id", listVO.getMasterId())
                        .or(q1 -> q1.eq("mtr.resource_id", listVO.getMasterId())
                                .eq("mtr.is_del", IsDelEnum.NORMAL.getId())))
                .eq("mt.is_del", IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getTagStatus()), "mt.tag_status", listVO.getTagStatus())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getTagType()), "mt.tag_type", listVO.getTagType())
                .in(listVO.getIds() != null && CollectionUtils.isNotEmpty(listVO.getIds()), "mt.id", listVO.getIds())
                // 校验当前是否选择人群来源
                .eq(TagOriginEnum.BUILD.getId().equals(listVO.getTagOrigin()), "mt.master_id", listVO.getMasterId())
                .ne(TagOriginEnum.AUTHORIZE.getId().equals(listVO.getTagOrigin()), "mt.master_id", listVO.getMasterId())
                .between(StringUtils.isNotBlank(listVO.getStartDate()) && StringUtils.isNotBlank(listVO.getEndDate()),
                        "mt.create_time", listVO.getStartDate() + " 00:00:00", listVO.getEndDate() + " 23:59:59")
                .and(StringUtils.isNotBlank(listVO.getSearch()), q -> q
                        .or().like("mt.id", listVO.getSearch())
                        .or().like("mt.tag_name", listVO.getSearch()))
                .orderByDesc("mt.id"));

        List<Long> tagIds = pageData.getRecords().stream().filter(u -> TagShareTypeEnum.DESIGNATED_ACCOUNT.getId().equals(u.getShareType()))
                .map(TagListDTO::getId).collect(Collectors.toList());

        Map<Long, List<Long>> tagResourceMap = new HashMap<>();
        if (!tagIds.isEmpty()) {
            tagResourceMap = this.getResourceMap(tagIds);
        }

        Map<Long, List<Long>> finalTagResourceMap = tagResourceMap;
        pageData.getRecords().forEach(entity -> {
            entity.setTagTypeName(ICommonEnum.getNameById(entity.getTagType(), TagTypeEnum.class));
            entity.setTagStatusName(ICommonEnum.getNameById(entity.getTagStatus(), TagStatusEnum.class));
            entity.setTagOrigin(entity.getMasterId() == listVO.getMasterId().longValue() ? 1 : 2);
            entity.setTagOriginName(ICommonEnum.getNameById(entity.getTagOrigin(), TagOriginEnum.class));
            entity.setDeviceTypeName(ICommonEnum.getNameById(entity.getDeviceType(), TagDeviceTypeEnum.class));
            entity.setShareTypeName(ICommonEnum.getNameById(entity.getShareType(), TagShareTypeEnum.class));
            entity.setResourceIds(finalTagResourceMap.getOrDefault(entity.getId(), List.of()));
        });
        return new PageUtils<>(pageData);
    }

    @Override
    public TagGetDTO getTag(TagGetVO getVO) {

        Tag tag = this.checkTag(getVO.getId(), null);

        TagGetDTO tagGetDTO = new TagGetDTO();
        BeanUtils.copyProperties(tag, tagGetDTO);
        tagGetDTO.setDeviceTypeName(ICommonEnum.getNameById(tagGetDTO.getDeviceType(), TagDeviceTypeEnum.class));

        TagExtra tagExtra = this.tagExtraMapper.selectOne(new QueryWrapper<TagExtra>().lambda().eq(TagExtra::getTagId, tag.getId()));
        tagGetDTO.setDeviceContent(JSONObject.parseObject(tagExtra.getTagContent(), TagDeviceContentVO.class));
        return tagGetDTO;
    }

    @Override
    public void saveTag(TagSaveVO saveVO, User user) {

        this.checkTag(null, saveVO.getTagName());

        Tag tag = new Tag();
        BeanUtils.copyProperties(saveVO, tag);
        tag.setCreateUid(user.getId());
        this.baseMapper.insert(tag);

        TagExtra tagExtra = new TagExtra();
        tagExtra.setTagId(tag.getId());
        tagExtra.setTagContent(JSONObject.toJSONString(saveVO.getDeviceContent()));
        tagExtra.setCreateUid(user.getId());
        this.tagExtraMapper.insert(tagExtra);

        if (sheinConfiguration.isRole(user.getRoleId())) {
            smsUtils.sendSheinCrowdMsg("Shein人群包新建通知", String.format("Shein人群包[%s]新建成功，请处理", tag.getTagName()));
        }
    }

    @Override
    public void updateTag(TagUpdateVO updateVO, User user) {
        Tag tag = this.checkTag(updateVO.getId(), null);
        BeanUtils.copyProperties(updateVO, tag);
        tag.setUpdateUid(user.getId());
        this.baseMapper.update(tag, new QueryWrapper<Tag>().lambda().eq(Tag::getId, tag.getId()));
        if (sheinConfiguration.isRole(user.getRoleId())) {
            smsUtils.sendSheinCrowdMsg("Shein人群包编辑通知", String.format("Shein人群包[%s]编辑成功，请处理", tag.getTagName()));
        }
    }

    @Override
    public void saveTagResource(TagResourceSaveVO saveVO, Integer userId) {

        Tag tag = this.checkTag(saveVO.getId(), null);

        tag.setShareType(saveVO.getShareType());
        tag.setUpdateUid(userId);
        this.baseMapper.update(tag, new QueryWrapper<Tag>().lambda().eq(Tag::getId, tag.getId()));

        this.updateTagResource(saveVO, userId);
    }

    @Override
    public void updateTagStatus() throws Exception {

        // 查询一条待上传至OSS的人群标签
        Tag tag = this.baseMapper.getTagWaitForUpdateStatus(new QueryWrapper<Tag>().lambda()
                .eq(Tag::getIsDel, IsDelEnum.NORMAL.getId())
                .and(q -> q.eq(Tag::getTagStatus, TagStatusEnum.WAIT_UPLOAD.getId())
                        .or().eq(Tag::getTagStatus, TagStatusEnum.UPLOAD_FAILED.getId()))
                .orderByAsc(Tag::getTagStatus)
                .orderByAsc(Tag::getId));

        // 当前未有需要上传至OSS的标签
        if (tag == null) {
            return;
        }
        // 上传处理
        this.uploadTagToOss(tag);
    }

    @Override
    public void checkNewMaster() {

        List<Master> masterList = this.masterMapper.selectList(new QueryWrapper<Master>().lambda()
                .between(Master::getCreateTime, DateUtils.getTodayStringDate() + " 00:00:00", DateUtils.getTodayStringDate() + " 23:59:59"));
        if (masterList.isEmpty()) {
            return;
        }

        this.checkIsExistResource();
    }

}
