package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.UploadUtils;
import com.overseas.common.vo.market.asset.AssetVO;
import com.overseas.service.market.dto.assetTask.AssetTaskMaterialDTO;
import com.overseas.service.market.entity.assetTask.AssetTask;
import com.overseas.service.market.entity.assetTask.TaskProductAsset;
import com.overseas.service.market.enums.assetTask.AssetTaskTypeEnum;
import com.overseas.service.market.mapper.assetTask.AssetTaskMapper;
import com.overseas.service.market.mapper.assetTask.TaskProductAssetMapper;
import com.overseas.service.market.service.TaskProductAssetService;
import com.overseas.service.market.vo.taskAsset.TaskAssetSaveVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class TaskProductAssetServiceImpl extends ServiceImpl<TaskProductAssetMapper, TaskProductAsset>
        implements TaskProductAssetService {

    private final AssetTaskMapper assetTaskMapper;

    @Override
    public List<AssetTaskMaterialDTO> saveTaskAsset(TaskAssetSaveVO saveVO, Integer loginUserId) {
        AssetTask assetTask = this.assetTaskMapper.selectById(saveVO.getAssetTaskId());
        if (null == assetTask) {
            throw new CustomException("素材任务不存在");
        }
        List<Long> assetIds = saveVO.getAssets().stream()
                .map(AssetVO::getAssetId).distinct()
                .collect(Collectors.toList());
        if (assetIds.size() != saveVO.getAssets().size()) {
            throw new CustomException("当前任务下含有重复素材，请删除后再试");
        }
        saveVO.getAssets().forEach(assetVO -> {
            long count = this.baseMapper.selectCount(new LambdaQueryWrapper<TaskProductAsset>()
                    .eq(TaskProductAsset::getAssetTaskId, saveVO.getAssetTaskId())
                    //如果是商品任务，则进行当前商品关系的唯一素材校验
                    .eq(AssetTaskTypeEnum.SIMPLE.getId().equals(assetTask.getAssetTaskType()),
                            TaskProductAsset::getTaskProductId, saveVO.getTaskProductId())
                    .eq(TaskProductAsset::getAssetId, assetVO.getAssetId())
                    .eq(TaskProductAsset::getIsDel, IsDelEnum.NORMAL.getId())
            );
            if (count > 0) {
                return;
            }
            TaskProductAsset taskProductAsset = new TaskProductAsset();
            taskProductAsset.setAssetTaskId(saveVO.getAssetTaskId());
            taskProductAsset.setTaskProductId(saveVO.getTaskProductId());
            taskProductAsset.setAssetId(assetVO.getAssetId());
            taskProductAsset.setCreateUid(loginUserId);
            this.baseMapper.insert(taskProductAsset);
        });
        List<AssetTaskMaterialDTO> assetTaskMaterials = this.baseMapper.getAssetTaskMaterials(
                new QueryWrapper<TaskProductAsset>()
                        .eq("mtpa.task_product_id", saveVO.getTaskProductId())
                        .eq("mtpa.asset_task_id", saveVO.getAssetTaskId())
                        .in("mtpa.asset_id", assetIds)
                        .eq("mtpa.is_del", IsDelEnum.NORMAL.getId())
        );
        if (!assetTaskMaterials.isEmpty()) {
            assetTaskMaterials.forEach(assetTaskMaterial ->
                    assetTaskMaterial.setAssetUrl(UploadUtils.getHttpUrl(assetTaskMaterial.getContent()))
            );
            return assetTaskMaterials;
        } else {
            return null;
        }
    }

    @Override
    public void checkAssetIsUse(Integer masterId, List<Long> assetIds) {
        if (CollectionUtils.isEmpty(assetIds)) {
            return;
        }
        if (ObjectUtils.isNullOrZero(masterId)) {
            throw new CustomException("未传入账户ID，无法查找账户下素材是否被使用");
        }
        if (assetIds.size() != assetIds.stream().distinct().count()) {
            throw new CustomException("当前任务下含有重复素材，请删除后再试");
        }
    }
}
