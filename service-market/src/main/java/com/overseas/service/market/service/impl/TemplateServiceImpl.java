package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.market.template.TemplateListDTO;
import com.overseas.common.dto.market.template.TemplateMonitorDataDTO;
import com.overseas.common.dto.market.template.TemplatePreviewDTO;
import com.overseas.common.dto.market.template.TemplateSelectDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.enums.market.template.IsEnum;
import com.overseas.common.enums.market.template.IsPublishEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.Md5CalculateUtils;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.utils.UploadUtils;
import com.overseas.common.utils.redis.RedisUtils;
import com.overseas.common.vo.market.template.*;
import com.overseas.service.market.entity.*;
import com.overseas.service.market.enums.assets.AssetTypeEnum;
import com.overseas.service.market.enums.template.TemplateShowEnum;
import com.overseas.service.market.feign.FgCenterControlService;
import com.overseas.service.market.mapper.*;
import com.overseas.service.market.service.TemplateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TemplateServiceImpl extends ServiceImpl<TemplateConfigMapper, TemplateConfig> implements TemplateService {

    private final TemplateConfigVersionMapper templateConfigVersionMapper;

    private final PlanMapper planMapper;

    private final TemplateMacroMapper templateMacroMapper;

    private final AssetMapper assetMapper;

    private final RedisUtils redisUtils;

    private final FgCenterControlService fgCenterControlService;

    private final TemplateResourceMapper templateResourceMapper;

    @Override
    public List<TemplateSelectDTO> getTemplateSelect(TemplateSelectVO selectVO) {
//        List<Long> templateIds = new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(selectVO.getAdxIds())) {
//            Map<Long, List<TemplateResource>> adxMap = this.templateResourceMapper.getByResourceAndType(
//                    selectVO.getAdxIds(), 1
//            ).stream().collect(Collectors.groupingBy(TemplateResource::getResourceId));
//            adxMap.forEach((k, v) -> {
//                List<Long> adxTemplates = v.stream().map(TemplateResource::getTemplateId).collect(Collectors.toList());
//                if (templateIds.isEmpty()) {
//                    templateIds.addAll(v.stream().map(TemplateResource::getTemplateId).collect(Collectors.toList()));
//                } else {
//                    templateIds.removeIf(u -> !adxTemplates.contains(u));
//                }
//            });
//            if (!adxMap.isEmpty() && CollectionUtils.isEmpty(templateIds)) {
//                return List.of();
//            }
//        }
//        if (CollectionUtils.isNotEmpty(selectVO.getEpIds())) {
//            Map<Long, List<TemplateResource>> epMap = this.templateResourceMapper.getByResourceAndType(
//                    selectVO.getEpIds(), 2
//            ).stream().collect(Collectors.groupingBy(TemplateResource::getResourceId));
//            epMap.forEach((k, v) -> {
//                List<Long> adxTemplates = v.stream().map(TemplateResource::getTemplateId).collect(Collectors.toList());
//                if (templateIds.isEmpty()) {
//                    templateIds.addAll(v.stream().map(TemplateResource::getTemplateId).collect(Collectors.toList()));
//                } else {
//                    templateIds.removeIf(u -> !adxTemplates.contains(u));
//                }
//            });
//            if (!epMap.isEmpty() && CollectionUtils.isEmpty(templateIds)) {
//                return List.of();
//            }
//        }
        return this.baseMapper.getTemplateSelect(new QueryWrapper<TemplateConfig>().lambda()
//                .in(CollectionUtils.isNotEmpty(templateIds), TemplateConfig::getId, templateIds)
                .eq(selectVO.getIsDpa() != null, TemplateConfig::getIsDpa, selectVO.getIsDpa())
                .eq(TemplateConfig::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(TemplateConfig::getIsShow, TemplateShowEnum.DISPLAY.getId())
                .orderByAsc(TemplateConfig::getId)
        );
    }

    @Override
    public TemplateConfig getTemplate(Long templateId) {
        TemplateConfig templateConfig = this.baseMapper.selectById(templateId);
        if (templateConfig == null) {
            throw new CustomException("模版不存在，请确认后再试");
        }
        return templateConfig;
    }

    @Override
    public PageUtils<?> listTemplate(TemplateListVO listVO) {
        IPage<TemplateListDTO> iPage = new Page<>(listVO.getPage(), listVO.getPageNum());
        iPage = this.baseMapper.list(iPage, new LambdaQueryWrapper<TemplateConfig>()
                .eq(TemplateConfig::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotAll(listVO.getIsDpa()), TemplateConfig::getIsDpa, listVO.getIsDpa())
                .eq(ObjectUtils.isNotAll(listVO.getIsPublish()), TemplateConfig::getIsPublish, listVO.getIsPublish())
                .like(StringUtils.isNotBlank(listVO.getSearch()), TemplateConfig::getTemplateName, listVO.getSearch())
                .orderByDesc(TemplateConfig::getId)
        );
        iPage.getRecords().forEach(tem -> {
            tem.setIsDpaName(ICommonEnum.getNameById(tem.getIsDpa(), IsEnum.class));
            tem.setIsPublishName(ICommonEnum.getNameById(tem.getIsPublish(), IsPublishEnum.class));
            tem.setIsShowName(ICommonEnum.getNameById(tem.getIsShow(), TemplateShowEnum.class));
        });
        return new PageUtils<>(iPage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveTemplate(TemplateSaveVO saveVO, Integer userId) {
        //名称重复校验
        this.checkNameIsRepeat(saveVO.getTemplateName(), null);
        //模板主表数据
        TemplateConfig templateConfig = new TemplateConfig();
        BeanUtils.copyProperties(saveVO, templateConfig);
        templateConfig.setCreateUid(userId);
        templateConfig.setTemplate("");
        templateConfig.setMacros(saveVO.getMacros().stream().distinct().collect(Collectors.joining(",")));
        templateConfig.setProductNumber(ObjectUtils.isNotNullOrZero(saveVO.getProductNumber()) ? saveVO.getProductNumber() : 0);
        templateConfig.setTemplateDesc(saveVO.getTemplateDesc());
        this.baseMapper.insert(templateConfig);
        //模板记录数据
        TemplateConfigVersion templateConfigVersion = new TemplateConfigVersion();
        templateConfigVersion.setTemplateId(templateConfig.getId());
        templateConfigVersion.setTemplateVersion(saveVO.getTemplate());
        templateConfigVersion.setCreateUid(userId);
        templateConfigVersionMapper.insert(templateConfigVersion);
        //模板数据更新
        templateConfig.setTemplateVersionId(templateConfigVersion.getId());
        this.baseMapper.updateById(templateConfig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTemplate(TemplateUpdateVO updateVO, Integer userId) {
        //校验名称是否重复
        this.checkNameIsRepeat(updateVO.getTemplateName(), updateVO.getId());
        TemplateConfig templateConfig = this.baseMapper.selectById(updateVO.getId());
        if (templateConfig == null) {
            throw new CustomException("模版不存在，请确认后再试");
        }
        //设置更改数据
        TemplateConfig updateConfig = new TemplateConfig();
        updateConfig.setTemplateName(updateVO.getTemplateName());
        updateConfig.setTemplateDesc(updateVO.getTemplateDesc());
        updateConfig.setMacros(updateVO.getMacros().stream().distinct().collect(Collectors.joining(",")));
        updateConfig.setProductNumber(updateVO.getProductNumber());
        updateConfig.setUpdateUid(userId);

        //如果待更改内容不同
        if (ObjectUtils.isNotNullOrZero(templateConfig.getTemplateVersionId())) {
            TemplateConfigVersion templateConfigVersion = this.templateConfigVersionMapper.
                    selectById(templateConfig.getTemplateVersionId());
            if (!templateConfigVersion.getTemplateVersion().equals(updateVO.getTemplate())) {
                templateConfigVersion.setTemplateVersion(updateVO.getTemplate());
                templateConfigVersion.setUpdateUid(userId);
                this.templateConfigVersionMapper.updateById(templateConfigVersion);
                updateConfig.setIsPublish(IsPublishEnum.WAIT.getId());
            }
            //如果是发布后文本更改
        } else {
            if (!templateConfig.getTemplate().equals(updateVO.getTemplate())) {
                //模板记录数据
                TemplateConfigVersion templateConfigVersion = new TemplateConfigVersion();
                templateConfigVersion.setTemplateId(templateConfig.getId());
                templateConfigVersion.setTemplateVersion(updateVO.getTemplate());
                templateConfigVersion.setCreateUid(userId);
                templateConfigVersionMapper.insert(templateConfigVersion);
                updateConfig.setTemplateVersionId(templateConfigVersion.getId());
                updateConfig.setIsPublish(IsPublishEnum.WAIT.getId());
            }
        }
        //更改内容
        this.baseMapper.update(updateConfig, new LambdaQueryWrapper<TemplateConfig>()
                .eq(TemplateConfig::getId, templateConfig.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delTemplate(TemplateDelVO delVO, Integer userId) {
        long count = planMapper.selectCount(new LambdaQueryWrapper<Plan>()
                .eq(Plan::getTemplateId, delVO.getId())
                .eq(Plan::getIsDel, IsDelEnum.NORMAL.getId())
        );
        if (count > 0) {
            throw new CustomException("该模板已被计划使用，且计划未删除");
        }
        TemplateConfig update = new TemplateConfig();
        update.setUpdateUid(userId);
        update.setIsDel(IsDelEnum.DELETE.getId().intValue());
        this.baseMapper.update(update, new LambdaQueryWrapper<TemplateConfig>().eq(TemplateConfig::getId, delVO.getId()));
    }

    @Override
    public TemplateConfig getTemplateV2(TemplateGetVO getVO) {
        TemplateConfig templateConfig = this.getTemplate(getVO.getId());
        //组装待发布内容至编辑内容
        if (ObjectUtils.isNotNullOrZero(templateConfig.getTemplateVersionId())) {
            TemplateConfigVersion templateConfigVersion = this.templateConfigVersionMapper.selectById(templateConfig.getTemplateVersionId());
            templateConfig.setTemplate(templateConfigVersion.getTemplateVersion());
        }
        return templateConfig;
    }

    @Override
    public TemplatePreviewDTO preview(TemplatePreviewVO previewVO) {
        TemplateConfig templateConfig = this.getTemplateV2(TemplateGetVO.builder().id(previewVO.getTemplateId()).build());
        Asset asset;
        //固定素材
        if (ObjectUtils.isNotNullOrZero(previewVO.getAssetId())) {
            asset = assetMapper.selectById(previewVO.getAssetId());
        } else {
            //固定宽高
            if (ObjectUtils.isNullOrZero(previewVO.getWidth()) && ObjectUtils.isNullOrZero(previewVO.getHeight())) {
                throw new CustomException("通过条件无法找到合适的图片素材");
            }
            QueryWrapper<Asset> queryWrapper = new QueryWrapper<>();
            Map<String, String> sqlSelectMap = Map.of(
                    "__POSITION_X__", "position_x",
                    "__POSITION_Y__", "position_y",
                    "__FILL_WIDTH__", "fill_width"
            );
            sqlSelectMap.forEach((marco, field) -> {
                if (templateConfig.getMacros().contains(marco)) {
                    queryWrapper.ne(field, "0");
                }
            });
            asset = assetMapper.selectOne(queryWrapper.lambda().eq(Asset::getAssetType, AssetTypeEnum.IMG.getId())
                    .eq(Asset::getWidth, previewVO.getWidth())
                    .eq(Asset::getHeight, previewVO.getHeight())
                    .orderByDesc(Asset::getId)
                    .last(" limit 1"));
        }
        if (null == asset || !AssetTypeEnum.IMG.getId().equals(asset.getAssetType())) {
            throw new CustomException("通过条件无法找到合适的图片素材");
        }
        //生成nonce
        String nonce = Md5CalculateUtils.getStringMd5(
                String.format("%s_%s_%s", templateConfig.getId(), System.currentTimeMillis(), Math.random() * 1000));

        Map<String, String> marcoMaps = templateMacroMapper.selectList(new LambdaQueryWrapper<TemplateMacro>()
                .eq(TemplateMacro::getIsDel, IsDelEnum.NORMAL.getId())
                .ne(TemplateMacro::getDefaultVal, "")
        ).stream().peek(marco -> {
            if (List.of("__VIEW_URLS__", "__CLICK_URLS__", "__VIEW_URLS_ARRAY__").contains(marco.getMacro())) {
                marco.setDefaultVal(marco.getDefaultVal()
                        .replaceAll("__TEMPLATE_ID__", templateConfig.getId().toString())
                        .replaceAll("__NONCE__", nonce)
                );
            }
            if ("__DPA_PRODUCTS__".equals(marco.getMacro())) {
                if (templateConfig.getIsDpa().equals(IsEnum.IS.getId())) {
                    List<JSONObject> result = new ArrayList<>();
                    for (int i = 0; i < templateConfig.getProductNumber(); i++) {
                        result.add(JSONObject.parseObject(marco.getDefaultVal()));
                    }
                    marco.setDefaultVal(JSONObject.toJSONString(result));
                } else {
                    marco.setDefaultVal("");
                }
            }
        }).collect(Collectors.toMap(TemplateMacro::getMacro, TemplateMacro::getDefaultVal));
        marcoMaps.put("__IMAGE__", UploadUtils.getNetworkUrl(asset.getContent(), asset.getIsUpload()));
        marcoMaps.put("__POSITION_X__", asset.getPositionX().toString());
        marcoMaps.put("__POSITION_Y__", asset.getPositionY().toString());
        marcoMaps.put("__FILL_WIDTH__", asset.getFillWidth().toString());
        marcoMaps.put("__WIDTH__", asset.getWidth().toString());
        marcoMaps.put("__HEIGHT__", asset.getHeight().toString());

        String template = templateConfig.getTemplate();
        for (String marco : marcoMaps.keySet()) {
            template = template.replaceAll(marco, java.util.regex.Matcher.quoteReplacement(marcoMaps.get(marco)));
        }
        log.info("template: {}", template);
        return TemplatePreviewDTO.builder().template(template).nonce(nonce).templateDesc(templateConfig.getTemplateDesc()).build();
    }

    @Override
    public TemplateMonitorDataDTO monitorData(TemplateMonitorDataVO monitorDataVO) {
        TemplateMonitorDataDTO templateMonitorDataDTO = new TemplateMonitorDataDTO();
        for (String type : List.of("impress", "click")) {
            String key = this.monitorKey(monitorDataVO.getTemplateId(), monitorDataVO.getNonce(), type);
            String result = redisUtils.get(key);
            switch (type) {
                case "impress":
                    templateMonitorDataDTO.setImpress(StringUtils.isNotBlank(result) ? Integer.parseInt(result) : 0);
                    break;
                case "click":
                    templateMonitorDataDTO.setClick(StringUtils.isNotBlank(result) ? Integer.parseInt(result) : 0);
                    break;
                default:
            }
        }
        return templateMonitorDataDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void publishTemplate(TemplatePublishVO publishVO, Integer userId) {
        TemplateMonitorDataDTO monitorDataDTO = this.monitorData(publishVO);
        if (monitorDataDTO.getImpress() == 0) {
            throw new CustomException("模板未通过曝光监测联调，无法发布");
        }
        TemplateConfig templateConfig = this.getTemplate(publishVO.getTemplateId());
        if (templateConfig.getIsPublish().equals(IsPublishEnum.PUBLISH.getId())) {
            throw new CustomException("模板已经发布，请确认后再试");
        }
        //获取待发布内容
        TemplateConfigVersion templateConfigVersion = this.templateConfigVersionMapper
                .selectById(templateConfig.getTemplateVersionId());
        //发布修改
        TemplateConfig updateConfig = new TemplateConfig();
        updateConfig.setTemplate(templateConfigVersion.getTemplateVersion());
        updateConfig.setIsPublish(IsPublishEnum.PUBLISH.getId());
        updateConfig.setIsShow(IsEnum.IS.getId());
        updateConfig.setTemplateVersionId(0L);
        this.baseMapper.update(updateConfig, new LambdaQueryWrapper<TemplateConfig>().eq(TemplateConfig::getId, templateConfig.getId()));
        //中控刷新
        if (ObjectUtils.isNotNullOrZero(publishVO.getIsRefresh())) {
            fgCenterControlService.get("update", "template", templateConfig.getId());
        }
    }

    @Override
    public void monitor(String nonce, Long templateId, String type) {
        if (StringUtils.isBlank(nonce) || ObjectUtils.isNullOrZero(templateId)) {
            throw new CustomException("标识或者模板ID不能为空");
        }
        String key = this.monitorKey(templateId, nonce, type);
        String result = redisUtils.get(key);
        if (StringUtils.isBlank(result)) {
            redisUtils.set(key, "1", 60 * 60 * 24);
        } else {
            redisUtils.set(key, String.valueOf(Integer.parseInt(result) + 1), 60 * 60 * 24);
        }
    }

    @Override
    public List<String> useMacro() {
        return templateMacroMapper.selectList(new LambdaQueryWrapper<TemplateMacro>()
                .eq(TemplateMacro::getIsDel, IsDelEnum.NORMAL.getId())
        ).stream().map(TemplateMacro::getMacro).collect(Collectors.toList());
    }

    /**
     * 模板重复
     *
     * @param templateName 模板
     * @param id           id
     */
    private void checkNameIsRepeat(String templateName, Long id) {
        long count = this.baseMapper.selectCount(new LambdaQueryWrapper<TemplateConfig>()
                .eq(TemplateConfig::getTemplateName, templateName)
                .eq(TemplateConfig::getIsDel, IsDelEnum.NORMAL.getId())
                .ne(ObjectUtils.isNotNullOrZero(id), TemplateConfig::getId, id)
        );
        if (count > 0) {
            throw new CustomException("模板名称已被使用，请修改后再试");
        }
    }

    /**
     * redis 监测key
     *
     * @param templateId 模板
     * @param nonce      标识
     * @param type       类型
     * @return 返回数据
     */
    private String monitorKey(Long templateId, String nonce, String type) {
        return String.format("TEMPLATE_M_%s_%s_%s", templateId, type, nonce);
    }


}
