package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.configuration.SheinConfiguration;
import com.overseas.common.dto.market.asset.AssetCreativeUnitNumDTO;
import com.overseas.common.dto.market.copyWriting.CopyWritingListDTO;
import com.overseas.common.dto.market.creativeUnit.CreativeUnitAssetDTO;
import com.overseas.common.dto.market.creativeUnit.CreativeUnitListDTO;
import com.overseas.common.dto.market.textCombine.TextCombineCheckDTO;
import com.overseas.common.dto.market.textCombine.TextCombineListDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.enums.market.campaign.CampaignStatusEnum;
import com.overseas.common.enums.market.copyWriting.CopyWritingTypeEnum;
import com.overseas.common.enums.market.plan.PlanStatusEnum;
import com.overseas.common.enums.market.textCombine.TextCombineFlagStatusEnum;
import com.overseas.common.utils.*;
import com.overseas.common.vo.market.creative.CreativeUnitListVO;
import com.overseas.common.vo.market.textCombine.TextCombineCheckVO;
import com.overseas.common.vo.market.textCombine.TextCombineListVO;
import com.overseas.common.vo.market.textCombine.TextCombineSaveVO;
import com.overseas.common.vo.report.asset.AssetCombineInfoVO;
import com.overseas.common.vo.report.asset.AssetCombineListVO;
import com.overseas.service.market.entity.*;
import com.overseas.service.market.enums.creative.CreativeAuditStatusEnum;
import com.overseas.service.market.enums.creative.units.CreativeUnitStatusEnum;
import com.overseas.service.market.enums.plan.PlanModeEnum;
import com.overseas.service.market.feign.FgReportService;
import com.overseas.service.market.mapper.AssetMapper;
import com.overseas.service.market.mapper.CopyWritingMapper;
import com.overseas.service.market.mapper.CreativeUnitMapper;
import com.overseas.service.market.mapper.TextCombineMapper;
import com.overseas.service.market.service.CreativeUnitService;
import com.overseas.service.market.service.TextCombineService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 文本组合服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TextCombineServiceImpl extends ServiceImpl<TextCombineMapper, TextCombine> implements TextCombineService {

    private final FgReportService fgReportService;

    private final CopyWritingMapper copyWritingMapper;

    private final AssetMapper assetMapper;

    private final CreativeUnitMapper creativeUnitMapper;

    private final SheinConfiguration sheinConfiguration;

    private final CreativeUnitService creativeUnitService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveTextCombine(TextCombineSaveVO saveVO, Integer userId) {
        List<TextCombine> textCombines = saveVO.getInfos().stream().map(u -> {
            TextCombine textCombine = new TextCombine();
            textCombine.setTitleId(u.getTitleId());
            textCombine.setDescId(u.getDescId());
            textCombine.setFlagStatus(saveVO.getFlagStatus());
            textCombine.setMasterId(saveVO.getMasterId());
            textCombine.setCreateUid(userId);
            return textCombine;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(textCombines)) {
            this.baseMapper.insertByUk(textCombines);
        }
    }

    @Override
    public PageUtils<TextCombineListDTO> listTextCombine(TextCombineListVO listVO, User user) {
        List<Long> assetIds;
        if (StringUtils.isNotBlank(listVO.getTranslateText()) || ObjectUtils.isNotNullOrZero(listVO.getCountryId())) {
            assetIds = this.copyWritingMapper.selectList(new LambdaQueryWrapper<CopyWriting>()
                    .eq(CopyWriting::getMasterId, listVO.getMasterId())
                    .like(StringUtils.isNotBlank(listVO.getTranslateText()), CopyWriting::getTranslatedText, listVO.getTranslateText())
                    .eq(StringUtils.isNotBlank(listVO.getAssetId()), CopyWriting::getAssetId, listVO.getAssetId())
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getCountryId()), CopyWriting::getCountryId, listVO.getCountryId())
            ).stream().map(CopyWriting::getAssetId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(assetIds)) {
                return new PageUtils<>(List.of(), 0L);
            }
        } else if (StringUtils.isNotBlank(listVO.getAssetId())) {
            try {
                assetIds = List.of(Long.parseLong(listVO.getAssetId()));
            } catch (Exception e) {
                return new PageUtils<>(List.of(), 0L);
            }
        } else {
            assetIds = null;
        }
        AssetCombineListVO assetTextCombineListVO = new AssetCombineListVO();
        BeanUtils.copyProperties(listVO, assetTextCombineListVO);
        assetTextCombineListVO.setMasterIds(List.of(listVO.getMasterId()));
        // 构建查询条件
        if (ObjectUtils.isNotNullOrZero(listVO.getFlagStatus())) {
            List<TextCombine> combines = this.baseMapper.selectList(new LambdaQueryWrapper<TextCombine>()
                    .eq(TextCombine::getFlagStatus, listVO.getFlagStatus())
                    .eq(TextCombine::getMasterId, listVO.getMasterId())
                    .and(CollectionUtils.isNotEmpty(assetIds), q ->
                            q.in(TextCombine::getTitleId, assetIds).or().in(TextCombine::getDescId, assetIds)
                    ).eq(TextCombine::getIsDel, 0).orderByDesc(TextCombine::getId)
            );
            if (combines.isEmpty()) {
                return new PageUtils<>(List.of(), 0L);
            }
            assetTextCombineListVO.setCombineInfos(combines.stream().map(u -> {
                AssetCombineInfoVO combineInfo = new AssetCombineInfoVO();
                combineInfo.setTitleId(u.getTitleId());
                combineInfo.setDescId(u.getDescId());
                return combineInfo;
            }).collect(Collectors.toList()));
        } else {
            assetTextCombineListVO.setTextAssetIds(assetIds);
        }
        FeignR<List<TextCombineListDTO>> result = fgReportService.assetCombineList(assetTextCombineListVO);
        if (CollectionUtils.isEmpty(result.getData())) {
            return new PageUtils<>(result.getData(), result.getTotal());
        }
        Set<Long> resultIds = new HashSet<>();
        result.getData().forEach(u -> {
            resultIds.add(u.getTitleId());
            resultIds.add(u.getDescId());
        });
        Map<String, CopyWritingListDTO> copyWritingMap = this.copyWritingMapper.listCopyWritingId(new QueryWrapper<>()
                .in(CollectionUtils.isNotEmpty(resultIds), "writing.asset_id", resultIds)
                .eq("writing.master_id", listVO.getMasterId())
        ).stream().collect(Collectors.toMap(u -> String.format("%s-%s", u.getAssetId(), u.getCopyWritingType()), Function.identity()));

        // 查询素材关联触点单元数量；设计师不需要查询关联创意单元数量，运营模块查询
        Map<String, Integer> assetNumMap = this.getUnitCountOfAsset(listVO.getMasterId(), result.getData(), user);
        // 查询素材关联触点单元数量；设计师不需要查询关联创意单元数量，运营模块查询
        Map<String, Integer> assetPutNumMap = this.getUnitPutCountOfAsset(listVO.getMasterId(), result.getData(), user);
        Map<String, Integer> flagStatusMap = this.baseMapper.selectList(new LambdaQueryWrapper<TextCombine>()
                .in(CollectionUtils.isNotEmpty(resultIds), TextCombine::getTitleId, resultIds)
                .in(CollectionUtils.isNotEmpty(resultIds), TextCombine::getDescId, resultIds)
                .eq(TextCombine::getMasterId, listVO.getMasterId())
        ).stream().collect(Collectors.toMap(u -> String.format("%s-%s", u.getTitleId(), u.getDescId()), TextCombine::getFlagStatus));
        result.getData().forEach(u -> {
            CopyWritingListDTO title = copyWritingMap.get(String.format("%s-%s", u.getTitleId(), CopyWritingTypeEnum.TITLE.getId()));
            if (null != title) {
                u.setTitleText(title.getCopyWriting());
                u.setTitleTranslatedText(title.getTranslatedText());
                u.setCountryName(title.getCountryName());
            }
            CopyWritingListDTO desc = copyWritingMap.get(String.format("%s-%s", u.getDescId(), CopyWritingTypeEnum.DESC.getId()));
            if (null != desc) {
                u.setDescText(desc.getCopyWriting());
                u.setDescTranslatedText(desc.getTranslatedText());
            }
            String key = String.format("%s-%s", u.getTitleId(), u.getDescId());
            u.setCreativeUnitNum(String.valueOf(assetNumMap.getOrDefault(key, 0)));
            u.setCreativeUnitPutNum(assetPutNumMap.getOrDefault(key, 0));
            u.setFlagStatus(flagStatusMap.getOrDefault(key, -1));
            u.setFlagStatusName(ICommonEnum.getNameById(u.getFlagStatus(), TextCombineFlagStatusEnum.class));
        });
        // 查询数据
        return new PageUtils<>(result.getData(), result.getTotal());
    }

    @Override
    public PageUtils<CreativeUnitListDTO> listCreativeUnitByCombine(CreativeUnitListVO listVO, User user) {
        IPage<CreativeUnitListDTO> pageData = this.creativeUnitMapper.listCreativeUnitByCombine(
                new Page<>(listVO.getPage(), listVO.getPageNum()),
                listVO.getCombineInfos().stream().map(u -> u.getTitleId().toString()).collect(Collectors.joining(",")),
                listVO.getCombineInfos().stream().map(u -> u.getDescId().toString()).collect(Collectors.joining(",")),
                listVO.getCombineInfos().stream().map(u -> String.format("(%s,%s)", u.getTitleId(), u.getDescId())).collect(Collectors.joining(",")),
                new QueryWrapper<CreativeUnit>()
                        .eq("mp.is_plan_put", sheinConfiguration.isPut(null, user.getRoleId()))
                        .eq("mc.is_del", IsDelEnum.NORMAL.getId())
                        .eq("mp.is_del", IsDelEnum.NORMAL.getId())
                        .eq("mp.plan_mode", PlanModeEnum.NORMAL.getId())
                        .eq("mcu.is_del", IsDelEnum.NORMAL.getId())
                        .eq("mcu.audit_status", CreativeAuditStatusEnum.PASS.getId())
                        .isNotNull("ma.title_id")
                        .eq(ObjectUtils.isNotNullOrZero(listVO.getMasterId()), "mcu.master_id", listVO.getMasterId())
                        .in(CollectionUtils.isNotEmpty(listVO.getCampaignIds()), "mcu.campaign_id", listVO.getCampaignIds())
                        .in(CollectionUtils.isNotEmpty(listVO.getPlanIds()), "mcu.plan_id", listVO.getPlanIds())
                        .eq(ObjectUtils.isNotNullOrZero(listVO.getCreativeUnitStatus()), "mcu.creative_unit_status", listVO.getCreativeUnitStatus())
                        .eq(ObjectUtils.isNotNullOrZero(listVO.getPlanStatus()), "mp.plan_status", listVO.getPlanStatus())
                        .eq(ObjectUtils.isNotNullOrZero(listVO.getCampaignStatus()), "mc.campaign_status", listVO.getCampaignStatus())
                        .between(org.apache.commons.lang3.StringUtils.isNotBlank(listVO.getCreateStartDate()), "mcu.create_time",
                                String.format("%s 00:00:00", listVO.getCreateStartDate()), String.format("%s 23:59:59", listVO.getCreateEndDate()))
                        .between(org.apache.commons.lang3.StringUtils.isNotBlank(listVO.getUpdateEndDate()), "mcu.update_time",
                                String.format("%s 00:00:00", listVO.getUpdateStartDate()), String.format("%s 23:59:59", listVO.getUpdateEndDate()))

                        .like(StringUtils.isNotBlank(listVO.getSearch()), "mcu.id", listVO.getSearch())
        );
        if (pageData.getRecords().isEmpty()) {
            return new PageUtils<>(List.of(), 0L);
        }
        // 获取素材
        CreativeUnitListVO unitListVO = new CreativeUnitListVO();
        unitListVO.setMasterId(listVO.getMasterId());
        unitListVO.setCreativeUnitIds(pageData.getRecords().stream().map(CreativeUnitListDTO::getCreativeUnitId).collect(Collectors.toList()));
        unitListVO.setIncludeFiledUnit(1);
        List<CreativeUnitAssetDTO> unitAssetDTOList = this.creativeUnitService.listCreativeUnitAsset(unitListVO);

        // 将素材列表根据计划——创意单元——素材进行分类
        Map<Long, List<CreativeUnitAssetDTO>> unitAssetMap = unitAssetDTOList.stream().collect(Collectors.groupingBy(CreativeUnitAssetDTO::getCreativeUnitId));
        pageData.getRecords().forEach(record -> {
            if (unitAssetMap.get(record.getCreativeUnitId()) == null) {
                return;
            }
            List<CreativeUnitAssetDTO> unitAssets = unitAssetMap.getOrDefault(record.getCreativeUnitId(), List.of());
            record.setAssets(this.creativeUnitService.getCreativeUnitMaterial(unitAssets));
            if (!unitAssets.isEmpty()) {
                record.setLandingUrl(unitAssets.get(0).getLandingUrl());
            } else {
                record.setLandingUrl("");
            }
        });
        return new PageUtils<>(pageData);
    }

    @Override
    public void exportListTextCombine(TextCombineListVO listVO, User user, HttpServletResponse response) throws IOException {
        listVO.setPage(1L);
        listVO.setPageNum(1000000L);
        PageUtils<TextCombineListDTO> iPage = this.listTextCombine(listVO, user);
        iPage.getData().forEach(u ->
                u.setCreativeUnitNum(ObjectUtils.isNotNullOrZero(u.getCreativeUnitNum()) ? String.format("%s/%s", u.getCreativeUnitPutNum(), u.getCreativeUnitNum()) : u.getCreativeUnitNum())
        );
        ExcelUtils.download(response, "文案组合列表", "文案组合", iPage.getData(), listVO.getCustoms());
    }

    @Override
    public TextCombineCheckDTO checkTextCombine(TextCombineCheckVO combineCheckVO) {
        List<String> assetContents = new ArrayList<>() {{
            add(combineCheckVO.getTitle());
        }};
        if (StringUtils.isNotBlank(combineCheckVO.getDesc())) {
            assetContents.add(combineCheckVO.getDesc());
        }
        Map<String, Long> assetMap = this.assetMapper.selectList(new LambdaQueryWrapper<Asset>()
                .in(Asset::getContent, assetContents)
        ).stream().collect(Collectors.toMap(Asset::getContent, Asset::getId));
        if (!assetMap.containsKey(combineCheckVO.getTitle())) {
            return new TextCombineCheckDTO("未标记");
        }
        TextCombine textCombine = this.baseMapper.selectOne(new LambdaQueryWrapper<TextCombine>()
                .eq(TextCombine::getMasterId, combineCheckVO.getMasterId())
                .eq(TextCombine::getTitleId, assetMap.get(combineCheckVO.getTitle()))
                .eq(TextCombine::getDescId, assetMap.getOrDefault(combineCheckVO.getDesc(), 0L))
                .last("limit 1")
        );
        if (null == textCombine) {
            return new TextCombineCheckDTO("未标记");
        }
        return new TextCombineCheckDTO(ICommonEnum.getNameById(textCombine.getFlagStatus(), TextCombineFlagStatusEnum.class));
    }

    /**
     * 查询素材关联触点单元数量
     *
     * @param masterId 账号ID
     * @param list     结果
     * @param user     用户
     * @return 返回结果
     */
    private Map<String, Integer> getUnitCountOfAsset(Long masterId, List<TextCombineListDTO> list, User user) {
        if (list.isEmpty()) {
            return Map.of();
        }
        return this.assetMapper.getTextCombineCreativeUnitNumList(
                        list.stream().map(u -> u.getTitleId().toString()).collect(Collectors.joining(",")),
                        list.stream().map(u -> u.getDescId().toString()).collect(Collectors.joining(",")),
                        list.stream().map(u -> String.format("(%s,%s)", u.getTitleId(), u.getDescId())).collect(Collectors.joining(",")),
                        new QueryWrapper<>()
                                .eq("mc.is_del", IsDelEnum.NORMAL.getId())
                                .eq("mp.is_del", IsDelEnum.NORMAL.getId())
                                .eq("mp.plan_mode", PlanModeEnum.NORMAL.getId())
                                .eq("mp.is_plan_put", sheinConfiguration.isPut(null, user.getRoleId()))
                                .eq("mcu.is_del", IsDelEnum.NORMAL.getId())
                                .eq("mcu.master_id", masterId)
                                .eq("mcu.audit_status", CreativeAuditStatusEnum.PASS.getId())
                                .isNotNull("ma.title_id")
                                .groupBy("title_id").groupBy("desc_id")).stream()
                .collect(Collectors.toMap(u -> String.format("%s-%s", u.getTitleId(), u.getDescId()),
                        AssetCreativeUnitNumDTO::getCreativeUnitNum)
                );
    }

    /**
     * 查询素材关联触点单元数量
     *
     * @param masterId 账号ID
     * @param list     结果
     * @param user     用户
     * @return 返回结果
     */
    private Map<String, Integer> getUnitPutCountOfAsset(Long masterId, List<TextCombineListDTO> list, User user) {
        if (list.isEmpty()) {
            return Map.of();
        }
        return this.assetMapper.getTextCombineCreativeUnitNumList(
                        list.stream().map(u -> u.getTitleId().toString()).collect(Collectors.joining(",")),
                        list.stream().map(u -> u.getDescId().toString()).collect(Collectors.joining(",")),
                        list.stream().map(u -> String.format("(%s,%s)", u.getTitleId(), u.getDescId())).collect(Collectors.joining(",")),
                        new QueryWrapper<>()
                                .eq("mc.is_del", IsDelEnum.NORMAL.getId())
                                .eq("mc.campaign_status", CampaignStatusEnum.OPEN.getId())
                                .eq("mp.is_del", IsDelEnum.NORMAL.getId())
                                .eq("mp.plan_status", PlanStatusEnum.MARKETING.getId())
                                .eq("mp.plan_mode", PlanModeEnum.NORMAL.getId())
                                .eq("mp.is_plan_put", sheinConfiguration.isPut(null, user.getRoleId()))
                                .eq("mcu.is_del", IsDelEnum.NORMAL.getId())
                                .eq("mcu.creative_unit_status", CreativeUnitStatusEnum.MARKETING.getId())
                                .eq("mcu.master_id", masterId)
                                .eq("mcu.audit_status", CreativeAuditStatusEnum.PASS.getId())
                                .isNotNull("ma.title_id")
                                .groupBy("title_id").groupBy("desc_id")).stream()
                .collect(Collectors.toMap(u -> String.format("%s-%s", u.getTitleId(), u.getDescId()),
                        AssetCreativeUnitNumDTO::getCreativeUnitNum)
                );
    }


}