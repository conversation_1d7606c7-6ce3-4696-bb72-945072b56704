package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.copyWriting.CopyWritingListDTO;
import com.overseas.common.dto.market.textLibrary.TextLibraryInfoListDTO;
import com.overseas.common.dto.market.textLibrary.TextLibraryListDTO;
import com.overseas.common.dto.market.textLibrary.TextLibraryTextCountDTO;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.enums.market.copyWriting.CopyWritingTypeEnum;
import com.overseas.common.enums.market.textLibrary.TextLibraryStatusEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.*;
import com.overseas.common.vo.market.textLibrary.*;
import com.overseas.service.market.entity.*;
import com.overseas.service.market.enums.assets.AssetTypeEnum;
import com.overseas.service.market.mapper.*;
import com.overseas.service.market.service.AssetService;
import com.overseas.service.market.service.TextLibraryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TextLibraryServiceImpl extends ServiceImpl<TextLibraryMapper, TextLibrary> implements TextLibraryService {

    private final TextLibraryInfoMapper textLibraryInfoMapper;

    private final CopyWritingMapper copyWritingMapper;

    private final AssetMapper assetMapper;

    private final AssetService assetService;

    private final CountryMapper countryMapper;

    @Override
    public List<SelectDTO> selectTextLibrary(TextLibrarySelectVO selectVO) {
        return this.baseMapper.selectList(new LambdaQueryWrapper<TextLibrary>()
                .eq(TextLibrary::getMasterId, selectVO.getMasterId())
                .and(q -> {
                    q.and(q1 -> q1.eq(TextLibrary::getIsDel, IsDelEnum.NORMAL.getId())
                            .eq(TextLibrary::getLibraryStatus, TextLibraryStatusEnum.OPEN.getId())
                    ).or(CollectionUtils.isNotEmpty(selectVO.getAppointIds()), q2 -> q2.in(TextLibrary::getId, selectVO.getAppointIds()));
                })
        ).stream().map(u -> new SelectDTO(u.getId(), u.getLibraryName())).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TextLibrary saveTextLibrary(TextLibrarySaveVO saveVO, Integer userId) {
        // 检查文本库名称是否已存在
        this.checkNameExist(saveVO.getLibraryName(), saveVO.getMasterId(), null);
        // 创建新的文本库
        TextLibrary textLibrary = new TextLibrary();
        BeanUtils.copyProperties(saveVO, textLibrary);
        textLibrary.setIsDel(IsDelEnum.NORMAL.getId().intValue());
        textLibrary.setCreateUid(userId);
        this.baseMapper.insert(textLibrary);
        this.saveTextLibrary(textLibrary.getId(), saveVO.getMasterId(), saveVO.getLibraryInfos(), userId);
        return textLibrary;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTextLibrary(TextLibraryUpdateVO updateVO, Integer userId) {
        // 检查文本库是否存在
        TextLibrary textLibrary = this.baseMapper.selectById(updateVO.getId());
        if (null == textLibrary) {
            throw new CustomException("文案库不存在，请检查后再试");
        }
        // 检查名称是否重复
        if (!textLibrary.getLibraryName().equals(updateVO.getLibraryName())) {
            this.checkNameExist(updateVO.getLibraryName(), updateVO.getMasterId(), updateVO.getId());
        }
        // 更新文本库
        TextLibrary updateLibrary = new TextLibrary();
        BeanUtils.copyProperties(updateVO, updateLibrary);
        updateLibrary.setUpdateUid(userId);
        this.baseMapper.update(updateLibrary, new LambdaQueryWrapper<TextLibrary>()
                .eq(TextLibrary::getId, updateVO.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveTextLibrary(Long libraryId, Long masterId, List<TextLibraryInfoVO> libraryInfos, Integer userId) {
        Map<String, String> assetContentMap = libraryInfos.stream().flatMap(u -> Stream.of(u.getTitle(), u.getDesc()))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toMap(u -> u, Md5CalculateUtils::getStringMd5, (o, n) -> o));
        if (assetContentMap.isEmpty()) {
            return 0;
        }
        Map<String, Long> countryMap = countryMapper.selectList(new LambdaQueryWrapper<Country>()
                        .ne(Country::getCountryLanguage, ""))
                .stream().collect(Collectors.toMap(Country::getCountryLanguage, u -> Long.parseLong(u.getCountryId()), (o, n) -> o));
        //判断语言是否允许
        List<TextLibraryInfoVO> notAllowCountry = libraryInfos.stream()
                .filter(u -> ObjectUtils.isNullOrZero(u.getCountryId()) && !countryMap.containsKey(u.getCountryName()))
                .collect(Collectors.toList());
        if (!notAllowCountry.isEmpty()) {
            throw new CustomException(String.format("文案中含有不允许语言，%s", notAllowCountry.stream().map(TextLibraryInfoVO::getCountryName).collect(Collectors.joining(","))));
        }
        Map<String, Long> assetMap = this.assetMapper.selectList(new LambdaQueryWrapper<Asset>()
                .in(Asset::getMd5, assetContentMap.values())
        ).stream().collect(Collectors.toMap(Asset::getMd5, Asset::getId));
        Map<String, Long> assetIdMap = new HashMap<>();
        //保存 asset
        assetContentMap.forEach((val, md5) -> {
            if (assetMap.containsKey(md5)) {
                assetIdMap.put(val, assetMap.get(md5));
            } else {
                Asset asset = assetService.saveTextAsset(val, userId);
                assetIdMap.put(val, asset.getId());
            }
        });
        //插入文案语言
        List<CopyWriting> copyWritings = libraryInfos.stream().flatMap(u -> {
            //设置国家ID
            Long countryId = ObjectUtils.isNotNullOrZero(u.getCountryId()) ? u.getCountryId() : countryMap.get(u.getCountryName());
            u.setCountryId(countryId);
            Long titleId = assetIdMap.get(u.getTitle());
            Stream<CopyWriting> stream;
            if (StringUtils.isNotBlank(u.getDesc())) {
                stream = Stream.of(new CopyWriting(titleId, countryId, masterId, CopyWritingTypeEnum.TITLE.getId(), u.getTitleTranslationText()),
                        new CopyWriting(assetIdMap.get(u.getDesc()), countryId, masterId, CopyWritingTypeEnum.DESC.getId(), u.getDescTranslationText()));
            } else {
                stream = Stream.of(new CopyWriting(titleId, countryId, masterId, CopyWritingTypeEnum.TITLE.getId(), u.getTitleTranslationText()));
            }
            return stream;
        }).collect(Collectors.toList());
        if (!copyWritings.isEmpty()) {
            this.copyWritingMapper.batchSaveCopyWriting(copyWritings, userId);
        }
        Map<String, Long> assetCountrymap = this.copyWritingMapper.selectList(
                new LambdaQueryWrapper<CopyWriting>()
                        .eq(CopyWriting::getMasterId, masterId)
                        .in(CopyWriting::getAssetId, assetIdMap.values())
        ).stream().collect(Collectors.toMap(u -> String.format("%s-%s", u.getAssetId(), u.getCopyWritingType()), CopyWriting::getCountryId, (o, n) -> o));
        //插入文案库
        Map<String, TextLibraryInfo> infos = libraryInfos.stream().map(u -> {
            TextLibraryInfo info = new TextLibraryInfo();
            info.setTitleId(assetIdMap.get(u.getTitle()));
            Long titleCountryId = assetCountrymap.get(String.format("%s-%s", info.getTitleId(), CopyWritingTypeEnum.TITLE.getId()));
            info.setDescId(assetIdMap.getOrDefault(u.getDesc(), 0L));
            Long descCountryId = assetCountrymap.getOrDefault(String.format("%s-%s", info.getDescId(), CopyWritingTypeEnum.DESC.getId()), 0L);
            if (!titleCountryId.equals(u.getCountryId()) ||
                    (ObjectUtils.isNotNullOrZero(info.getDescId()) && !descCountryId.equals(u.getCountryId()))) {
                log.info(" 标题 :{}, 描述 :{} 国家 :{} 不匹配", u.getTitle(), u.getDesc(), u.getCountryName());
                return null;
            }
            info.setLibraryId(libraryId);
            info.setCreateUid(userId);
            return info;
        }).filter(Objects::nonNull).collect(Collectors.toMap(u -> String.format("%s_%s_%s", u.getLibraryId(), u.getTitleId(), u.getDescId()), Function.identity()));
        // 保存文案信息
        if (CollectionUtils.isNotEmpty(infos)) {
            textLibraryInfoMapper.insertByUk(new ArrayList<>(infos.values()));
        }
        return infos.size();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTextLibrary(TextLibraryDelVO delVO, Integer userId) {
        // 逻辑删除文本库
        TextLibrary updateLibrary = new TextLibrary();
        updateLibrary.setIsDel(IsDelEnum.DELETE.getId().intValue());
        updateLibrary.setUpdateUid(userId);
        this.baseMapper.update(updateLibrary, new LambdaQueryWrapper<TextLibrary>()
                .eq(TextLibrary::getMasterId, delVO.getMasterId())
                .in(TextLibrary::getId, delVO.getIds())
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeTextLibraryStatus(TextLibraryStatusVO statusVO, Integer userId) {
        // 更新状态
        TextLibrary updateLibrary = new TextLibrary();
        updateLibrary.setLibraryStatus(statusVO.getLibraryStatus());
        updateLibrary.setUpdateUid(userId);
        this.baseMapper.update(updateLibrary, new LambdaQueryWrapper<TextLibrary>()
                .in(TextLibrary::getId, statusVO.getIds())
                .eq(TextLibrary::getMasterId, statusVO.getMasterId())
        );
    }

    @Override
    public PageUtils<?> listTextLibrary(TextLibraryListVO listVO) {
        IPage<TextLibraryListDTO> iPage = new Page<>(listVO.getPage(), listVO.getPageNum());
        iPage = this.baseMapper.list(iPage, new LambdaQueryWrapper<TextLibrary>()
                .eq(TextLibrary::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(TextLibrary::getMasterId, listVO.getMasterId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getLibraryStatus()), TextLibrary::getLibraryStatus, listVO.getLibraryStatus())
                .like(StringUtils.isNotBlank(listVO.getSearch()), TextLibrary::getLibraryName, listVO.getSearch())
                .orderByDesc(TextLibrary::getId));
        if (iPage.getRecords().isEmpty()) {
            return new PageUtils<>(iPage);
        }
        Map<Long, Long> countMap = this.textLibraryInfoMapper.listCount(new LambdaQueryWrapper<TextLibraryInfo>()
                .in(TextLibraryInfo::getLibraryId, iPage.getRecords().stream().map(TextLibraryListDTO::getId).collect(Collectors.toList()))
                .eq(TextLibraryInfo::getIsDel, IsDelEnum.NORMAL.getId())
                .groupBy(TextLibraryInfo::getLibraryId)
        ).stream().collect(Collectors.toMap(TextLibraryTextCountDTO::getLibraryId, TextLibraryTextCountDTO::getTextCount));
        iPage.getRecords().forEach(u -> {
            u.setTextCount(countMap.getOrDefault(u.getId(), 0L));
        });
        return new PageUtils<>(iPage);
    }

    @Override
    public void deleteTextLibraryInfo(TextLibraryInfoDelVO delVO, Integer userId) {
        TextLibraryInfo update = new TextLibraryInfo();
        update.setIsDel(IsDelEnum.DELETE.getId().intValue());
        update.setUpdateUid(userId);
        this.textLibraryInfoMapper.update(update, new LambdaQueryWrapper<TextLibraryInfo>()
                .eq(TextLibraryInfo::getLibraryId, delVO.getLibraryId())
                .in(TextLibraryInfo::getId, delVO.getIds())
        );
    }

    @Override
    public List<TextLibraryInfoVO> uploadTextLibraryInfo(TextLibraryInfoUploadVO uploadVO) {
        List<TextLibraryInfoVO> list = ExcelUtils.read(uploadVO.getFilePath(), TextLibraryInfoVO.class);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        ValidatorUtils.validateEntities(list);
        return list;
    }

    @Override
    public List<TextLibraryInfoListDTO> listTextLibraryInfo(TextLibraryInfoListVO listVO) {
        this.getLibrary(listVO.getLibraryId(), listVO.getMasterId());
        List<Long> findAssetIds;
        if (StringUtils.isNotBlank(listVO.getSearch())) {
            findAssetIds = this.assetMapper.selectList(new LambdaQueryWrapper<Asset>()
                    .like(Asset::getContent, listVO.getSearch())
                    .eq(Asset::getAssetType, AssetTypeEnum.TEXT.getId())
            ).stream().map(Asset::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(findAssetIds)) {
                return List.of();
            }
        } else {
            findAssetIds = null;
        }
        List<TextLibraryInfoListDTO> list = textLibraryInfoMapper.listInfo(
                new LambdaQueryWrapper<TextLibraryInfo>()
                        .eq(TextLibraryInfo::getIsDel, IsDelEnum.NORMAL.getId())
                        .eq(TextLibraryInfo::getLibraryId, listVO.getLibraryId())
                        .and(CollectionUtils.isNotEmpty(findAssetIds),
                                q -> q.in(TextLibraryInfo::getTitleId, findAssetIds)
                                        .or().in(TextLibraryInfo::getDescId, findAssetIds)
                        ).orderByDesc(TextLibraryInfo::getId)
        );
        if (list.isEmpty()) {
            return list;
        }
        return this.completeInfos(list, listVO.getMasterId());
    }


    @Override
    public List<TextLibraryInfoListDTO> completeInfos(List<TextLibraryInfoListDTO> list, Long masterId) {
        List<Long> assetIds = list.stream()
                .flatMap(u -> Stream.of(u.getTitleId(), u.getDescId()))
                .filter(ObjectUtils::isNotNullOrZero).collect(Collectors.toList());
        if (assetIds.isEmpty()) {
            return list;
        }
        Map<String, CopyWritingListDTO> copyWritingMap = copyWritingMapper.listCopyWriting(
                new QueryWrapper<CopyWriting>()
                        .in("writing.asset_id", assetIds)
                        .eq("writing.master_id", masterId)
        ).stream().collect(Collectors.toMap(u -> String.format("%s-%s", u.getAssetId(), u.getCopyWritingType()),
                Function.identity(), (o, n) -> n));
        list.forEach(u -> {
            CopyWritingListDTO title = copyWritingMap.get(String.format("%s-%s", u.getTitleId(), CopyWritingTypeEnum.TITLE.getId()));
            if (null != title) {
                u.setTitleText(title.getCopyWriting());
                u.setTitleTranslatedText(title.getTranslatedText());
                u.setCountryId(title.getCountryId());
                u.setCountryName(title.getCountryName());
            }
            CopyWritingListDTO desc = copyWritingMap.get(String.format("%s-%s", u.getDescId(), CopyWritingTypeEnum.DESC.getId()));
            if (null != desc) {
                u.setDescText(desc.getCopyWriting());
                u.setDescTranslatedText(desc.getTranslatedText());
            } else {
                u.setDescText("");
                u.setDescTranslatedText("");
            }
        });
        return list;
    }

    @Override
    public void exportTextLibraryInfo(TextLibraryInfoListVO listVO, HttpServletResponse response) throws IOException {
        TextLibrary textLibrary = this.getLibrary(listVO.getLibraryId(), listVO.getMasterId());
        List<TextLibraryInfoListDTO> list = this.listTextLibraryInfo(listVO);
        ExcelUtils.download(response, textLibrary.getLibraryName(), TextLibraryInfoListDTO.class, list, List.of(), "文案库");
    }

    /**
     * 检查文案库名称是否重复
     *
     * @param libraryName 文案库名称
     * @param masterId    账户ID
     */
    private void checkNameExist(String libraryName, Long masterId, Long id) {
        long count = this.baseMapper.selectCount(new LambdaQueryWrapper<TextLibrary>()
                .eq(TextLibrary::getLibraryName, libraryName)
                .eq(TextLibrary::getMasterId, masterId)
                .eq(TextLibrary::getIsDel, IsDelEnum.NORMAL.getId())
                .ne(ObjectUtils.isNotNullOrZero(id), TextLibrary::getId, id)
        );
        if (count > 0) {
            throw new CustomException("文案库名称已存在，请检查后再试");
        }
    }

    /**
     * 获取文案库是否存在
     *
     * @param libraryId 文案库ID
     * @param masterId  账户ID
     * @return 返回数据
     */
    private TextLibrary getLibrary(Long libraryId, Long masterId) {
        TextLibrary textLibrary = this.baseMapper.selectOne(
                new LambdaQueryWrapper<TextLibrary>().eq(TextLibrary::getId, libraryId)
                        .eq(TextLibrary::getMasterId, masterId)
                        .eq(TextLibrary::getIsDel, IsDelEnum.NORMAL.getId())
                        .last("limit 1")
        );
        if (null == textLibrary) {
            throw new CustomException("文案库不存在，请检查后再试");
        }
        return textLibrary;
    }

}