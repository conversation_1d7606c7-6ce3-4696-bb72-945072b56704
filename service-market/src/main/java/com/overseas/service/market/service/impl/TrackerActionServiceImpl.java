package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.market.master.MasterProjectDTO;
import com.overseas.service.market.entity.Master;
import com.overseas.service.market.entity.ProjectActionMap;
import com.overseas.service.market.entity.TrackerAction;
import com.overseas.common.dto.SelectDTO2;
import com.overseas.common.dto.market.trackAction.TrackActionSelectByProjectDTO;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.vo.market.monitor.action.TrackerActionByProjectSelectVO;
import com.overseas.common.vo.market.monitor.action.TrackerActionSelectVO;
import com.overseas.common.vo.sys.project.ProjectSetActionMapVO;
import com.overseas.service.market.mapper.MasterMapper;
import com.overseas.service.market.mapper.TrackerActionMapper;
import com.overseas.service.market.service.TrackerActionService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class TrackerActionServiceImpl extends ServiceImpl<TrackerActionMapper, TrackerAction>
        implements TrackerActionService {

    private final MasterMapper masterMapper;

    @Override
    public List<SelectDTO2> getActionSelect(TrackerActionSelectVO selectVO) {
        List<Long> projectIds = new ArrayList<>();
        projectIds.add(0L);
        if (ObjectUtils.isNotNullOrZero(selectVO.getMasterId())) {
            // 过滤所属项目
            List<MasterProjectDTO> masterProjects = this.masterMapper.listMasterProject(
                    new QueryWrapper<Master>().eq("mpr.resource_id", selectVO.getMasterId())
            );
            if (!masterProjects.isEmpty()) {
                projectIds.addAll(masterProjects.stream().map(MasterProjectDTO::getProjectId).collect(Collectors.toList()));
            }
        }
        return this.baseMapper.selectList(
                new QueryWrapper<TrackerAction>().lambda()
                        // 取消actionScene区分，海外目前h5和app都会对接一样的转化事件
                        .ne(TrackerAction::getActionScene, 0)
                        .in(TrackerAction::getProjectId, projectIds)
                        .orderByDesc(TrackerAction::getId)
                ).stream().map(u -> new SelectDTO2(u.getSourceAction(), u.getActionName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<TrackActionSelectByProjectDTO> getActionSelectByProject(TrackerActionByProjectSelectVO selectVO) {
        return this.baseMapper.selectList(new LambdaQueryWrapper<TrackerAction>()
                        .ne(TrackerAction::getActionScene, 0)
                        .in(TrackerAction::getProjectId, List.of(selectVO.getProjectId(), 0L))
                        .orderByDesc(TrackerAction::getProjectId)
                        .orderByDesc(TrackerAction::getId)
                ).stream()
                .map(u -> new TrackActionSelectByProjectDTO(u.getSourceAction(), u.getActionName(), u.getProjectId() == 0L ? 1 : 0))
                .collect(Collectors.toList());
    }

    @Override
    public List<SelectDTO2> mapByProject(TrackerActionByProjectSelectVO selectVO) {
        return this.baseMapper.selectList(new LambdaQueryWrapper<TrackerAction>()
                        .ne(TrackerAction::getActionScene, 0)
                        .in(TrackerAction::getProjectId, List.of(selectVO.getProjectId(), 0L))
                ).stream()
                .map(u -> new SelectDTO2(u.getSourceAction(), u.getTargetAction()))
                .collect(Collectors.toList());
    }


    @Override
    public List<ProjectSetActionMapVO.ActionMapVO> getSetAction(String project) {
        return this.baseMapper.listProjectActionMap(project);
    }


    @Transactional(rollbackFor = Exception.class)
    public void saveProjectTrackAction(ProjectSetActionMapVO setActionMapVO, Integer operatorUid) {
        if (CollectionUtils.isEmpty(setActionMapVO.getActionMaps())) {
            return;
        }
        long count = this.baseMapper.countByMapInfo(new QueryWrapper<>()
                .eq("project", setActionMapVO.getProject())
                .in("source", setActionMapVO.getActionMaps().stream()
                        .map(ProjectSetActionMapVO.ActionMapVO::getSource).collect(Collectors.toList())
                )
        );
        if (count > 0) {
            throw new CustomException("该项目配置客户转化标识已存在，请确认后再试");
        }
        this.baseMapper.insertProjectAction(setActionMapVO.getActionMaps().stream().map(u -> {
            ProjectActionMap projectActionMap = new ProjectActionMap();
            projectActionMap.setAction(u.getAction());
            projectActionMap.setProject(setActionMapVO.getProject());
            projectActionMap.setSource(u.getSource());
            projectActionMap.setSourceName(u.getSourceName());
            projectActionMap.setEventKey(u.getEventKey());
            projectActionMap.setCreateUid(operatorUid);
            return projectActionMap;
        }).collect(Collectors.toList()));
    }


}
