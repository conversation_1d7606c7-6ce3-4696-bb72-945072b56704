package com.overseas.service.market.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.service.market.entity.TrackerDay;
import com.overseas.service.market.mapper.TrackerDayMapper;
import com.overseas.service.market.service.TrackerDayService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@DS("service")
public class TrackerDayServiceImpl extends ServiceImpl<TrackerDayMapper, TrackerDay> implements TrackerDayService {

    @Override
    public List<TrackerDay> getTrackerDayList(String sqlSelect, List<Long> monitorIds, List<String> targetTypes, String groupField) {

        return this.baseMapper.getMonitorActionNum(new QueryWrapper<TrackerDay>()
                .select(sqlSelect)
                .ne("actionType", "reach")
                .in(!monitorIds.isEmpty(), "si", monitorIds)
                .in(!targetTypes.isEmpty(), "naiveActionType", targetTypes)
                .groupBy(groupField)).stream().filter(Objects::nonNull).collect(Collectors.toList());
    }
}
