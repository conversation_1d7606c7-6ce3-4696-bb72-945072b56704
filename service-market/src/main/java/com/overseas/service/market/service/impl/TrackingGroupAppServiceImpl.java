package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.service.market.dto.trackingGroupApp.TrackingGroupAppListDTO;
import com.overseas.service.market.entity.tracking.TrackingGroupApp;
import com.overseas.service.market.mapper.tracking.TrackingGroupAppMapper;
import com.overseas.service.market.service.TrackingGroupAppService;
import com.overseas.service.market.vo.trackingGroupApp.TrackingGroupAppBindVO;
import com.overseas.service.market.vo.trackingGroupApp.TrackingGroupAppListVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
public class TrackingGroupAppServiceImpl extends ServiceImpl<TrackingGroupAppMapper, TrackingGroupApp>
        implements TrackingGroupAppService {

    @Override
    public List<TrackingGroupAppListDTO> listTrackingGroupApp(TrackingGroupAppListVO listVO) {
        List<TrackingGroupAppListDTO> result;
        if (ObjectUtils.isNotNullOrZero(listVO.getAppBindStatus())) {
            QueryWrapper<TrackingGroupApp> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("mtga.is_del", IsDelEnum.NORMAL.getId())
                    .orderByDesc("mtga.id")
                    .lambda().eq(TrackingGroupApp::getTrackingGroupId, listVO.getTrackingGroupId());
             result = this.baseMapper.listTrackingGroupApp(queryWrapper);
        } else {
            result = this.baseMapper.listTrackingGroupAppUnbind(25, listVO.getTrackingGroupId());
        }

        // app secret 中间部分替换为*，防止泄漏
        result.forEach(trackingGroupAppListDTO ->
                trackingGroupAppListDTO.setAppSecret(this.replaceMiddle(trackingGroupAppListDTO.getAppSecret())));
        return result;
    }

    public void bindApp2TrackingGroup(TrackingGroupAppBindVO bindVO, Integer loginUserId) {
        List<TrackingGroupApp> bindApps = new ArrayList<>();
        bindVO.getApps().forEach(trackingGroupAppBind -> {
            TrackingGroupApp trackingGroupApp = new TrackingGroupApp();
            trackingGroupApp.setTrackingGroupId(bindVO.getTrackingGroupId());
            trackingGroupApp.setCpsAppId(trackingGroupAppBind.getCpsAppId());
            trackingGroupApp.setRemark(trackingGroupAppBind.getRemark());
            trackingGroupApp.setIsDel(IsDelEnum.NORMAL.getId());
            trackingGroupApp.setUpdateUid(loginUserId);
            bindApps.add(trackingGroupApp);
        });
        // 账号绑定记录更新为删除状态
        TrackingGroupApp trackingGroupApp = new TrackingGroupApp();
        trackingGroupApp.setIsDel(IsDelEnum.DELETE.getId());
        this.baseMapper.update(trackingGroupApp, new UpdateWrapper<TrackingGroupApp>().lambda()
                .eq(TrackingGroupApp::getTrackingGroupId, bindVO.getTrackingGroupId()));
        // 基于唯一主键批量插入
        if (!bindApps.isEmpty()) {
            this.baseMapper.batchInsertTrackingGroupApp(bindApps, loginUserId);
        }
    }
    
    public String replaceMiddle(String input) {
        int length = input.length();
        if (length <= 2) {
            return input;
        } else if (length % 2 == 0) {
            int midIndex = length / 2 - 1;
            return input.substring(0, midIndex) + "**" + input.substring(midIndex + 2);
        } else {
            int midIndex = length / 2;
            return input.substring(0, midIndex - 1) + "*" + input.substring(midIndex + 1);
        }
    }
}
