package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.utils.PageUtils;
import com.overseas.service.market.dto.trackingGroupProduct.TrackingGroupProductListDTO;
import com.overseas.service.market.entity.tracking.TrackingGroupProduct;
import com.overseas.service.market.enums.trackingGroup.TrackingProductStatusEnum;
import com.overseas.service.market.mapper.tracking.TrackingGroupProductMapper;
import com.overseas.service.market.service.TrackingGroupProductService;
import com.overseas.service.market.vo.trackingGroupProduct.TrackingGroupProductBindVO;
import com.overseas.service.market.vo.trackingGroupProduct.TrackingGroupProductDeleteVO;
import com.overseas.service.market.vo.trackingGroupProduct.TrackingGroupProductListVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class TrackingGroupProductServiceImpl extends ServiceImpl<TrackingGroupProductMapper, TrackingGroupProduct>
        implements TrackingGroupProductService {
    @Override
    public PageUtils<TrackingGroupProductListDTO> listTrackingGroupProduct(TrackingGroupProductListVO listVO) {
        QueryWrapper<TrackingGroupProduct> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TrackingGroupProduct::getTrackingGroupId, listVO.getTrackingGroupId())
                .eq(TrackingGroupProduct::getIsDel, IsDelEnum.NORMAL.getId())
                .orderByDesc(TrackingGroupProduct::getId);
        IPage<TrackingGroupProductListDTO> pageData = this.baseMapper.listTrackingGroupProduct(
                new Page<>(listVO.getPage(), listVO.getPageNum()), queryWrapper);
        pageData.getRecords().forEach(product
                -> product.setProductStatusName(ICommonEnum.getNameById(product.getProductStatus(),
                TrackingProductStatusEnum.class))
        );
        return new PageUtils<>(pageData);
    }

    @Override
    @Transactional
    public void bindProduct2TrackingGroup(TrackingGroupProductBindVO bindVO, Integer loginUserId) {
        // 前后去空格后去重
        bindVO.setProductUrls(bindVO.getProductUrls().stream().map(String::trim).distinct().collect(Collectors.toList()));
        List<TrackingGroupProduct> productExist = this.baseMapper.selectList(new QueryWrapper<TrackingGroupProduct>()
                .lambda().in(TrackingGroupProduct::getProductUrl, bindVO.getProductUrls())
                .eq(TrackingGroupProduct::getTrackingGroupId, bindVO.getTrackingGroupId()));
        List<String> existUrls = productExist.stream().map(TrackingGroupProduct::getProductUrl)
                .collect(Collectors.toList());
        if (!productExist.isEmpty()) {
            TrackingGroupProduct trackingGroupProduct = new TrackingGroupProduct();
            trackingGroupProduct.setUpdateUid(loginUserId);
            trackingGroupProduct.setIsDel(IsDelEnum.NORMAL.getId());
            this.baseMapper.update(trackingGroupProduct, new UpdateWrapper<TrackingGroupProduct>()
                    .lambda().in(TrackingGroupProduct::getProductUrl, existUrls)
                    .eq(TrackingGroupProduct::getTrackingGroupId, bindVO.getTrackingGroupId())
            );
        }
        bindVO.getProductUrls().forEach(productUrl -> {
            if (!existUrls.contains(productUrl)) {
                TrackingGroupProduct trackingGroupProduct = new TrackingGroupProduct();
                trackingGroupProduct.setTrackingGroupId(bindVO.getTrackingGroupId());
                trackingGroupProduct.setProductUrl(productUrl);
                trackingGroupProduct.setCreateUid(loginUserId);
                this.baseMapper.insert(trackingGroupProduct);
            }
        });
    }

    @Override
    public void deleteTrackingGroupProduct(TrackingGroupProductDeleteVO deleteVO, Integer loginUserId) {
        TrackingGroupProduct trackingGroupProduct = new TrackingGroupProduct();
        trackingGroupProduct.setIsDel(IsDelEnum.DELETE.getId());
        trackingGroupProduct.setUpdateUid(loginUserId);
        this.baseMapper.update(trackingGroupProduct, new UpdateWrapper<TrackingGroupProduct>().lambda()
                .eq(TrackingGroupProduct::getTrackingGroupId, deleteVO.getTrackingGroupId())
                .in(TrackingGroupProduct::getId, deleteVO.getGroupProductIds())
        );
    }
}
