package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.enums.CpsProjectEnum;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.enums.ProjectEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ExcelUtils;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.service.market.dto.trackingGroup.TrackingGroupBindDTO;
import com.overseas.service.market.dto.trackingGroup.TrackingGroupListDTO;
import com.overseas.service.market.dto.trackingGroupUrl.TrackingGroupUrlSaveDTO;
import com.overseas.service.market.dto.trackingGroupUrl.TrackingGroupUrlListDTO;
import com.overseas.service.market.entity.tracking.TrackingGroup;
import com.overseas.service.market.entity.tracking.TrackingGroupApp;
import com.overseas.service.market.entity.tracking.TrackingGroupProduct;
import com.overseas.service.market.entity.tracking.TrackingGroupUrl;
import com.overseas.service.market.enums.trackingGroup.TrackingProductUrlTypeEnum;
import com.overseas.service.market.mapper.tracking.TrackingGroupAppMapper;
import com.overseas.service.market.mapper.tracking.TrackingGroupMapper;
import com.overseas.service.market.mapper.tracking.TrackingGroupProductMapper;
import com.overseas.service.market.mapper.tracking.TrackingGroupUrlMapper;
import com.overseas.service.market.service.TrackingGroupService;
import com.overseas.service.market.vo.trackingGroup.TrackingGroupListVO;
import com.overseas.service.market.vo.trackingGroup.TrackingGroupRemarkVO;
import com.overseas.service.market.vo.trackingGroup.TrackingGroupRenameVO;
import com.overseas.service.market.vo.trackingGroup.TrackingGroupSelectVO;
import com.overseas.service.market.vo.trackingGroupUrl.TrackingGroupUrlListVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class TrackingGroupServiceImpl extends ServiceImpl<TrackingGroupMapper, TrackingGroup>
        implements TrackingGroupService {

    private final TrackingGroupAppMapper trackingGroupAppMapper;

    private final TrackingGroupProductMapper trackingGroupProductMapper;

    private final TrackingGroupUrlMapper trackingGroupUrlMapper;
    @Override
    public PageUtils<TrackingGroupListDTO> listTrackingGroup(TrackingGroupListVO listVO, Integer userId) {
        QueryWrapper<TrackingGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("mtg.is_del", IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getProjectId()),
                "mtg.project_id", listVO.getProjectId()).orderByDesc("mtg.id")
                .like(StringUtils.isNotBlank(listVO.getSearch()), "mtg.tracking_group_name", listVO.getSearch())
                .groupBy("mtg.id");
        IPage<TrackingGroupListDTO> pageData = this.baseMapper.listTrackingGroup(
                new Page<>(listVO.getPage(), listVO.getPageNum()), queryWrapper);
        List<Long> trackingGroupIds = pageData.getRecords().stream().map(TrackingGroupListDTO::getTrackingGroupId)
                .collect(Collectors.toList());
        if (!trackingGroupIds.isEmpty()) {
            List<TrackingGroupBindDTO> bindApps = this.trackingGroupAppMapper.listTrackingGroupBindApp(
                    new QueryWrapper<TrackingGroupApp>().in("tracking_group_id", trackingGroupIds)
                            .eq("is_del", IsDelEnum.NORMAL.getId())
                            .groupBy("tracking_group_id")
            );
            List<TrackingGroupBindDTO> bindProducts = this.trackingGroupProductMapper.listTrackingGroupBindProduct(
                    new QueryWrapper<TrackingGroupProduct>().in("tracking_group_id", trackingGroupIds)
                            .eq("is_del", IsDelEnum.NORMAL.getId())
                            .groupBy("tracking_group_id")
            );
            Map<Long, Integer> bindAppMap = bindApps.stream().collect(
                    Collectors.toMap(TrackingGroupBindDTO::getTrackingGroupId, TrackingGroupBindDTO::getCount));
            Map<Long, Integer> bindProductMap = bindProducts.stream().collect(
                    Collectors.toMap(TrackingGroupBindDTO::getTrackingGroupId, TrackingGroupBindDTO::getCount));
            pageData.getRecords().forEach(record -> {
                record.setAppCount(bindAppMap.getOrDefault(record.getTrackingGroupId(), 0));
                record.setProductCount(bindProductMap.getOrDefault(record.getTrackingGroupId(), 0));
            });
        }

        return new PageUtils<>(pageData);
    }

    @Override
    public List<SelectDTO> selectTrackingGroup(TrackingGroupSelectVO selectVO) {
        return this.baseMapper.selectTrackingGroup(new QueryWrapper<TrackingGroup>()
                .eq("is_del", IsDelEnum.NORMAL.getId())
                .eq("project_id", selectVO.getProjectId())
                .like(StringUtils.isNotBlank(selectVO.getSearch()),
                        "tracking_group_name", selectVO.getSearch())
                .orderByDesc("id")
        );
    }

    public void renameTrackingGroup(TrackingGroupRenameVO renameVO, Integer loginUserId) {
        TrackingGroup trackingGroup = this.baseMapper.selectOne(new QueryWrapper<TrackingGroup>()
                .ne("id", renameVO.getTrackingGroupId())
                .eq("tracking_group_name", renameVO.getTrackingGroupName()));
        if (null != trackingGroup) {
            throw new CustomException("Tracking组名称已存在，请修改后再试");
        }
        TrackingGroup trackingGroupUpdate = new TrackingGroup();
        trackingGroupUpdate.setId(renameVO.getTrackingGroupId());
        trackingGroupUpdate.setTrackingGroupName(renameVO.getTrackingGroupName());
        trackingGroupUpdate.setUpdateUid(loginUserId);
        this.baseMapper.updateById(trackingGroupUpdate);
    }

    @Override
    public void remarkTrackingGroup(TrackingGroupRemarkVO remarkVO, Integer loginUserId) {
        TrackingGroup trackingGroupUpdate = new TrackingGroup();
        trackingGroupUpdate.setId(remarkVO.getTrackingGroupId());
        trackingGroupUpdate.setRemark(remarkVO.getRemark());
        trackingGroupUpdate.setUpdateUid(loginUserId);
        int update = this.baseMapper.updateById(trackingGroupUpdate);
        if (update == 0) {
            throw new CustomException("设置备注信息失败");
        }
    }

    @Override
    public void exportTrackingGroupCpsUrl(TrackingGroupUrlListVO listVO, HttpServletResponse response) {
        QueryWrapper<TrackingGroupUrl> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(!listVO.getTrackingGroupIds().isEmpty(),
                        "mtg.id", listVO.getTrackingGroupIds())
                .in(!listVO.getCpsAppIds().isEmpty(),
                        "mti.cps_app_id", listVO.getCpsAppIds())
                .eq("mtgp.is_del", IsDelEnum.NORMAL.getId())
                .eq("mtgu.is_del", IsDelEnum.NORMAL.getId());
        List<TrackingGroupUrlListDTO> trackingGroupUrlList = this.trackingGroupUrlMapper.listTrackingGroupCpsUrl(
                queryWrapper);
        trackingGroupUrlList.forEach(trackingGroupUrlListDTO
                -> trackingGroupUrlListDTO.setUrlTypeName(
                        ICommonEnum.getNameById(trackingGroupUrlListDTO.getUrlType(),
                                TrackingProductUrlTypeEnum.class))
        );
        try {
            ExcelUtils.download(response, "Tracking组CPS链接", TrackingGroupUrlListDTO.class,
                    trackingGroupUrlList, List.of());
        } catch(IOException e){
            log.info("download tracking 组cps 链接error : {}", JSONObject.toJSONString(listVO));
        }
    }

    @Override
    public void generateTrackingGroupUrl(Integer trackingGroupId, Integer userId) {
        QueryWrapper<TrackingGroupUrl> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(ObjectUtils.isNotNullOrZero(trackingGroupId), "mtg.id", trackingGroupId)
                .eq("mti.project_id", CpsProjectEnum.AE.getId())
                .eq("mtg.project_id", CpsProjectEnum.AE.getId())
                .eq("mtg.is_del", IsDelEnum.NORMAL.getId())
                .eq("mtga.is_del", IsDelEnum.NORMAL.getId())
                .eq("mtgp.is_del", IsDelEnum.NORMAL.getId());
        List<TrackingGroupUrlSaveDTO> trackingGroupUrls = this.trackingGroupUrlMapper.listTrackingGroupProductUrl(
                queryWrapper);
        TrackingGroupUrl trackingGroupUrl = new TrackingGroupUrl();
        trackingGroupUrl.setIsDel(IsDelEnum.DELETE.getId());
        this.trackingGroupUrlMapper.update(trackingGroupUrl, new QueryWrapper<TrackingGroupUrl>().lambda()
                .eq(ObjectUtils.isNotNullOrZero(trackingGroupId), TrackingGroupUrl::getTrackingGroupId, trackingGroupId)
                .eq(TrackingGroupUrl::getIsDel, IsDelEnum.NORMAL.getId()));
        if (!trackingGroupUrls.isEmpty()) {
            this.trackingGroupUrlMapper.batchInsertTrackingGroupUrl(trackingGroupUrls, userId);
        }
    }
}
