package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.service.market.dto.trackingId.TrackingIdListDTO;
import com.overseas.service.market.entity.ae.AeApp;
import com.overseas.service.market.entity.assetTask.TrackingId;
import com.overseas.service.market.mapper.ae.AeAppMapper;
import com.overseas.service.market.mapper.cps.TrackingIdMapper;
import com.overseas.service.market.service.TrackingIdService;
import com.overseas.service.market.vo.trackingId.TrackingIdListVO;
import com.overseas.service.market.vo.trackingId.TrackingIdSaveVO;
import com.overseas.service.market.vo.trackingId.TrackingIdSelectVO;
import com.overseas.service.market.vo.trackingId.TrackingIdVO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class TrackingIdServiceImpl extends ServiceImpl<TrackingIdMapper, TrackingId> implements TrackingIdService {

    private final AeAppMapper aeAppMapper;

    @Override
    public void saveTrackingId(TrackingIdSaveVO saveVO) {
        List<String> trackingNames = saveVO.getTrackingIds().stream().map(TrackingIdVO::getTrackingName).distinct()
                .collect(Collectors.toList());
        if (trackingNames.size() < saveVO.getTrackingIds().size()) {
            throw new CustomException("存在重复的名称，请检查");
        }
        List<String> trackingIds = saveVO.getTrackingIds().stream().map(TrackingIdVO::getTrackingId).distinct()
                .collect(Collectors.toList());
        if (trackingIds.size() < saveVO.getTrackingIds().size()) {
            throw new CustomException("存在重复的tracking id，请检查");
        }
        this.checkTrackingIdName(saveVO.getProjectId(), trackingNames, trackingIds);
        AeApp aeAppInDb = this.aeAppMapper.selectById(saveVO.getCpsAppId());
        if (null == aeAppInDb) {
            throw new CustomException("关联投放账号不存在，请检查");
        }
        List<TrackingId> trackingIdList = new ArrayList<>();
        saveVO.getTrackingIds().forEach(trackingIdVO -> {
            TrackingId trackingId = new TrackingId();
            trackingId.setProjectId(saveVO.getProjectId());
            trackingId.setCpsAppId(saveVO.getCpsAppId());
            trackingId.setTrackingId(trackingIdVO.getTrackingId());
            trackingId.setTrackingName(trackingIdVO.getTrackingName());
            trackingIdList.add(trackingId);
        });
        try {
            this.baseMapper.insertTrackingIdByUk(trackingIdList);
        } catch (Exception e) {
            throw new CustomException("批量保存异常，原因：" + e.getMessage());
        }
    }

    public PageUtils<TrackingIdListDTO> listTrackingId(TrackingIdListVO listVO, Integer userId) {
        QueryWrapper<TrackingId> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(ObjectUtils.isNotNullOrZero(listVO.getProjectId()),
                        "mti.project_id", listVO.getProjectId())
                .eq("uur.user_id", userId)
                .lambda()
                .eq(ObjectUtils.isNotNullOrZero(listVO.getCpsAppId()), TrackingId::getCpsAppId, listVO.getCpsAppId())
                .and(StringUtils.isNotBlank(listVO.getSearch()),
                        q->q.like(TrackingId::getTrackingId, listVO.getSearch())
                                .or().like(TrackingId::getTrackingName, listVO.getSearch()))
                .orderByDesc(TrackingId::getId);
        IPage<TrackingIdListDTO> pageData = this.baseMapper.listTrackingId(
                new Page<>(listVO.getPage(), listVO.getPageNum()), queryWrapper);
        return new PageUtils<>(pageData);
    }

    public List<SelectDTO> selectTrackingId(TrackingIdSelectVO selectVO) {
        return this.baseMapper.selectTrackingId(new QueryWrapper<TrackingId>().lambda()
                .eq(TrackingId::getProjectId, selectVO.getProjectId())
                .eq(ObjectUtils.isNotNullOrZero(selectVO.getCpsAppId()),
                        TrackingId::getCpsAppId, selectVO.getCpsAppId())
                .like(StringUtils.isNotBlank(selectVO.getSearch()),
                        TrackingId::getTrackingName, selectVO.getSearch())
                .orderByDesc(TrackingId::getId));
    }

    private void checkTrackingIdName(Integer projectId, List<String> trackingIdNames, List<String> trackingIds) {
        List<String> names = this.lambdaQuery()
                .eq(TrackingId::getProjectId, projectId)
                .in(TrackingId::getTrackingName, trackingIdNames)
                .eq(TrackingId::getIsDel, IsDelEnum.NORMAL.getId())
                .select(TrackingId::getTrackingName).list()
                .stream().map(TrackingId::getTrackingName).collect(Collectors.toList());
        if (!names.isEmpty()) {
            throw new CustomException("Tracking Id 名称 " + StringUtils.join(names, "，") + " 已存在，请修改！");
        }
        List<String> trackingIdsInDb = this.lambdaQuery()
                .eq(TrackingId::getProjectId, projectId)
                .in(TrackingId::getTrackingId, trackingIds)
                .eq(TrackingId::getIsDel, IsDelEnum.NORMAL.getId())
                .select(TrackingId::getTrackingId).list()
                .stream().map(TrackingId::getTrackingId).collect(Collectors.toList());
        if (!trackingIdsInDb.isEmpty()) {
            throw new CustomException("Tracking Id " + StringUtils.join(trackingIdsInDb, "，") + " 已存在，请修改！");
        }
    }
}
