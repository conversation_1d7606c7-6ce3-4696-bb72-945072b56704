package com.overseas.service.market.service.impl;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.overseas.common.enums.SortTypeEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.*;
import com.overseas.common.vo.common.BaseExcelVO;
import com.overseas.service.market.dto.ttReport.TtReportListDTO;
import com.overseas.service.market.dto.ttReport.TtReportUploadResultDTO;
import com.overseas.service.market.entity.TtExcelUploadRecord;
import com.overseas.service.market.entity.TtProtect;
import com.overseas.service.market.entity.TtReport;
import com.overseas.service.market.entity.User;
import com.overseas.service.market.mapper.TtExcelUploadRecordMapper;
import com.overseas.service.market.mapper.TtProtectMapper;
import com.overseas.service.market.mapper.TtReportMapper;
import com.overseas.service.market.service.TtReportService;
import com.overseas.service.market.vo.ttReport.TtReportListVO;
import com.overseas.service.market.vo.ttReport.TtReportUploadVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * TtReport服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TtReportServiceImpl implements TtReportService {

    private final TtReportMapper ttReportMapper;
    private final TtProtectMapper ttProtectMapper;
    private final TtExcelUploadRecordMapper ttExcelUploadRecordMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TtReportUploadResultDTO uploadExcel(TtReportUploadVO uploadVO, User user) {
        // 1. 创建上传记录
        TtExcelUploadRecord uploadRecord = new TtExcelUploadRecord();
        uploadRecord.setFileName(uploadVO.getFileName());
        uploadRecord.setExcelType(uploadVO.getExcelType());
        uploadRecord.setUploadStatus(1); // 上传中
        uploadRecord.setSuccessCount(0);
        uploadRecord.setFailedCount(0);
        uploadRecord.setCreateUid(user.getId());
        uploadRecord.setUpdateUid(user.getId());
        ttExcelUploadRecordMapper.insert(uploadRecord);

        try {
            // 2. 上传文件并获取路径
            uploadRecord.setFilePath(uploadVO.getFilePath());
            ttExcelUploadRecordMapper.updateById(uploadRecord);

            // 3. 根据Excel类型读取并处理数据
            int successCount = 0;
            int failedCount = 0;
            
            String fullPath = UploadUtils.getUploadPath(uploadVO.getFilePath());
//            String fullPath = uploadVO.getFilePath();
            
            if (uploadVO.getExcelType() == 1) {
                // TtReport数据
                List<TtReportExcelDTO> excelData = ExcelUtils.read(fullPath, TtReportExcelDTO.class);
                if (CollectionUtils.isEmpty(excelData)) {
                    throw new CustomException("Excel文件内容为空");
                }
                
                List<TtReport> ttReports = convertToTtReports(excelData, user);
                if (CollectionUtils.isNotEmpty(ttReports)) {
                    ttReportMapper.batchInsert(ttReports);
                    successCount = ttReports.size();
                }
                
            } else if (uploadVO.getExcelType() == 2) {
                // TtProtect数据
                List<TtProtectExcelDTO> excelData = ExcelUtils.read(fullPath, 3, TtProtectExcelDTO.class);
                if (CollectionUtils.isEmpty(excelData)) {
                    throw new CustomException("Excel文件内容为空");
                }
                
                List<TtProtect> ttProtects = convertToTtProtects(excelData, user);
                if (CollectionUtils.isNotEmpty(ttProtects)) {
                    ttProtectMapper.batchInsert(ttProtects);
                    successCount = ttProtects.size();
                }
            }

            // 4. 更新上传记录为成功
            uploadRecord.setUploadStatus(2); // 成功
            uploadRecord.setSuccessCount(successCount);
            uploadRecord.setFailedCount(failedCount);
            uploadRecord.setUpdateUid(user.getId());
            ttExcelUploadRecordMapper.updateById(uploadRecord);

        } catch (Exception e) {
            log.error("Excel上传处理失败: ", e);
            // 更新上传记录为失败
            uploadRecord.setUploadStatus(3); // 失败
            uploadRecord.setErrorMessage(e.getMessage());
            uploadRecord.setUpdateUid(user.getId());
            ttExcelUploadRecordMapper.updateById(uploadRecord);
            throw new CustomException("Excel处理失败: " + e.getMessage());
        }

        // 5. 返回结果
        TtReportUploadResultDTO result = new TtReportUploadResultDTO();
        BeanUtils.copyProperties(uploadRecord, result);
        result.setUploadId(uploadRecord.getId());
        return result;
    }

    @Override
    public PageUtils<TtReportListDTO> pageReportData(TtReportListVO listVO) {
        // 使用自定义分页查询逻辑，聚合TtReport和TtProtect数据
        IPage<TtReportListDTO> page = new Page<>(listVO.getPage(), listVO.getPageNum());

        String sortField = StringUtils.isNotBlank(listVO.getSortField())
                ? HumpLineUtils.humpNumberToLine(listVO.getSortField()) : "report_date";
        QueryWrapper<TtProtect> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("tr.activation_date", listVO.getStartDate(), listVO.getEndDate())
                .between("tp.report_date", listVO.getStartDate(), listVO.getEndDate())
                .orderBy(StringUtils.isNotBlank(listVO.getSortField())
                        && !"normal".equals(listVO.getSortType()),
                        SortTypeEnum.ASC.getSortType().equals(listVO.getSortType()), sortField);
        // 分页查询TtReport数据
        IPage<TtReportListDTO> ttReportPage = ttReportMapper.listTtReport(page, queryWrapper);
        TtReportListDTO total = ttReportMapper.getTtReportTotal(queryWrapper);
        total.setReportDate(ConstantUtils.ALL);
        
        return new PageUtils<>(ttReportPage, total);
    }

    public void exportReportData(TtReportListVO listVO, HttpServletResponse response) {
        PageUtils<TtReportListDTO> pageData = this.pageReportData(listVO);
        try {
            if (!pageData.getData().isEmpty()) {
                String fileName = "Tiktok报表_"
                        + (listVO.getStartDate().equals(listVO.getEndDate()) ? listVO.getStartDate() : listVO.getStartDate() + "_" + listVO.getEndDate());
                ExcelUtils.download(response, fileName, TtReportListDTO.class, pageData.getData(), List.of());
            } else {
                throw new CustomException("没有要导出的数据");
            }
        } catch (IOException e) {
            throw new CustomException("导出TT报表失败" + e.getMessage());
        }
    }

    /**
     * 转换Excel数据为TtReport实体
     */
    private List<TtReport> convertToTtReports(List<TtReportExcelDTO> excelData, User user) {
        return excelData.stream().map(excel -> {
            TtReport ttReport = new TtReport();
            BeanUtils.copyProperties(excel, ttReport);
            ttReport.setCreateUid(user.getId());
            ttReport.setUpdateUid(user.getId());
            return ttReport;
        }).collect(Collectors.toList());
    }

    /**
     * 转换Excel数据为TtProtect实体
     */
    private List<TtProtect> convertToTtProtects(List<TtProtectExcelDTO> excelData, User user) {
        return excelData.stream().map(excel -> {
            TtProtect ttProtect = new TtProtect();
            BeanUtils.copyProperties(excel, ttProtect);
            ttProtect.setCreateUid(user.getId());
            ttProtect.setUpdateUid(user.getId());
            return ttProtect;
        }).collect(Collectors.toList());
    }

    /**
     * TtReport Excel数据传输对象
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class TtReportExcelDTO extends BaseExcelVO {
        @ExcelProperty(index = 0)
        @ApiModelProperty("激活日期")
        private LocalDate activationDate;

        @ExcelProperty(index = 1)
        @ApiModelProperty("应用ID")
        private String appId;

        @ExcelProperty(index = 2)
        @ApiModelProperty("操作系统")
        private String os;

        @ExcelProperty(index = 3)
        @ApiModelProperty("国家")
        private String country;

        @ExcelProperty(index = 4)
        @ApiModelProperty("媒体来源")
        private String mediaSource;

        @ExcelProperty(index = 5)
        @ApiModelProperty("站点ID")
        private String siteId;

        @ExcelProperty(index = 6)
        @ApiModelProperty("广告活动")
        private String campaign;

        @ExcelProperty(index = 7)
        @ApiModelProperty("合作伙伴")
        private String partner;

        @ExcelProperty(index = 8)
        @ApiModelProperty("结算安装量")
        private Long settlementInstalls;

        @ExcelProperty(index = 9)
        @ApiModelProperty("总安装量")
        private Long totalInstalls;

        @ExcelProperty(index = 10)
        @ApiModelProperty("总作弊数")
        private Long totalFraud;

        @ExcelProperty(index = 11)
        @ApiModelProperty("字节识别量")
        private Long bytedanceFraud;

        @ExcelProperty(index = 12)
        @ApiModelProperty("Appsflyer识别量")
        private Long appsflyerFraud;

        @ExcelProperty(index = 13)
        @ApiModelProperty("次留数")
        private Long nextDayRetentionNumber;

        @ExcelProperty(index = 14)
        @ApiModelProperty("劫持作弊")
        private Long hijackFraud;

        @ExcelProperty(index = 15)
        @ApiModelProperty("设备异常")
        private Long abnormalDevice;

        @ExcelProperty(index = 16)
        @ApiModelProperty("机器人作弊")
        private Long botFraud;

        @ExcelProperty(index = 17)
        @ApiModelProperty("真人作弊")
        private Long humanSpam;

        @ExcelProperty(index = 18)
        @ApiModelProperty("去重前CTIT<30s计数")
        private Long ctit30Number;

        @ExcelProperty(index = 19)
        @ApiModelProperty("去重后CTIT<30s计数")
        private Long deduplicatedCtit30Number;

        @ExcelProperty(index = 20)
        @ApiModelProperty("7日留存率")
        private BigDecimal day7Retention;
    }

    /**
     * TtProtect Excel数据传输对象
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class TtProtectExcelDTO extends BaseExcelVO {
        @ExcelProperty(index = 0)
        @ApiModelProperty("报表日期")
        private LocalDate reportDate;

        @ExcelProperty(index = 1)
        @ApiModelProperty("媒体渠道")
        private String mediaChannel;

        @ExcelProperty(index = 2)
        @ApiModelProperty("子渠道")
        private String subChannel;

        @ExcelProperty(index = 3)
        @ApiModelProperty("广告系列")
        private String campaignSeries;

        @ExcelProperty(index = 4)
        @ApiModelProperty("总计")
        private Long totalCount;

        @ExcelProperty(index = 5)
        @ApiModelProperty("作弊转化总量")
        private Long fraudConversionTotal;

        @ExcelProperty(index = 7)
        @ApiModelProperty("作弊趋势")
        private String fraudTrend;

        @ExcelProperty(index = 8)
        @ApiModelProperty("激活")
        private Long activation;

        @ExcelProperty(index = 9)
        @ApiModelProperty("再归因")
        private Long reattribution;

        @ExcelProperty(index = 10)
        @ApiModelProperty("再互动")
        private Long reengagement;
    }
} 