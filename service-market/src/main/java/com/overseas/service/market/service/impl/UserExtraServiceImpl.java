package com.overseas.service.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.service.market.dto.audit.master.AuditMasterGetDTO;
import com.overseas.service.market.entity.UserExtra;
import com.overseas.service.market.enums.master.MasterAuditMasterStatusEnum;
import com.overseas.service.market.mapper.UserExtraMapper;
import com.overseas.service.market.vo.auditMaster.AuditMasterSaveVO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.service.market.enums.user.UserTypeEnum;
import com.overseas.service.market.service.AssetService;
import com.overseas.service.market.service.UserExtraService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-09-14 16:09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserExtraServiceImpl extends ServiceImpl<UserExtraMapper, UserExtra> implements UserExtraService {

    private final AssetService assetService;

    @Override
    @Transactional
    public Long saveQualification(AuditMasterSaveVO vo, Integer loginUserId, Integer userType) {
        // 保存资质信息，代理商需要判断资质名称唯一，管理员不需要
        String creditCode = format(vo.getCreditCode());
        if (StringUtils.isEmpty(creditCode)) {
            throw new CustomException("统一社会信用码不能为空");
        }
        String businessLicence = format(vo.getBusinessLicence());
        if (StringUtils.isEmpty(businessLicence)) {
            throw new CustomException("营业执照名称不能为空");
        }
        if (UserTypeEnum.AGENT.getId().equals(userType)) {
            if (baseMapper.listWithUser(new QueryWrapper<UserExtra>()
                    .ne("ue.user_id", loginUserId)
                    .eq("ue.credit_code", creditCode)
                    .eq("u.user_type", userType)).size() > 0) {
                throw new CustomException("该资质已经被其它代理商注册，请检查后重试");
            }
        }

        // 保存资质信息
        UserExtra userExtraSave = null;
        try {
            userExtraSave = getUserExtra(loginUserId);
        } catch (CustomException e) {
            log.info(e.getMessage());
        }
        // 保存主表信息
        if (null == userExtraSave) {
            userExtraSave = new UserExtra();
            BeanUtils.copyProperties(vo, userExtraSave, "id", "bankName", "subBankName", "bankAccount", "validateMoney");
            userExtraSave.setUserId(loginUserId);
            userExtraSave.setCreateUid(loginUserId);
            userExtraSave.setAuditStatus(MasterAuditMasterStatusEnum.AUDIT_WAIT.getId());
            userExtraSave.setStep(2);
            save(userExtraSave);
        } else {
            BeanUtils.copyProperties(vo, userExtraSave, "id", "bankName", "subBankName", "bankAccount", "validateMoney");
            userExtraSave.setAuditStatus(MasterAuditMasterStatusEnum.AUDIT_WAIT.getId());
            userExtraSave.setUpdateUid(loginUserId);
            userExtraSave.setStep(2);
            updateById(userExtraSave);
        }
        return loginUserId.longValue();
    }

    @Override
    public Long saveBankInfo(AuditMasterSaveVO vo, Integer loginUserId) {
        UserExtra userExtraInDb = getUserExtra(loginUserId);
        userExtraInDb.setBankName(vo.getBankName());
        userExtraInDb.setSubBankName(vo.getSubBankName());
        userExtraInDb.setBankAccount(vo.getBankAccount());
        // 如果当前状态在验证之后，那么提交后需要将状态重置到待对公验证状态
        if (userExtraInDb.getAuditStatus().compareTo(MasterAuditMasterStatusEnum.IDENTIFY_WAIT.getId()) > 0) {
            userExtraInDb.setAuditStatus(MasterAuditMasterStatusEnum.IDENTIFY_WAIT.getId());
        }
        userExtraInDb.setStep(3);
        updateById(userExtraInDb);
        return loginUserId.longValue();
    }

    @Override
    public Long validateMoney(AuditMasterSaveVO vo, Integer loginUserId) {
        UserExtra userExtraInDb = getUserExtra(loginUserId);
        // 打款需要检验当前的状态
        if (!MasterAuditMasterStatusEnum.IDENTIFY_WAIT.getId().equals(userExtraInDb.getAuditStatus())) {
            throw new CustomException("资质状态不合法，无法进行打款验证");
        }
        if (userExtraInDb.getValidateMoney().compareTo(vo.getValidateMoney()) != 0) {
            log.info("资质打款验证不通过，代理商ID：{}，资质ID：{}，输入金额：{}，打款金额：{}", loginUserId, vo.getId(), vo.getValidateMoney(), userExtraInDb.getValidateMoney());
            userExtraInDb.setAuditStatus(MasterAuditMasterStatusEnum.IDENTIFY_DENY.getId());
        } else {
            userExtraInDb.setAuditStatus(MasterAuditMasterStatusEnum.AUDIT_PASS.getId());
        }
        userExtraInDb.setStep(4);
        updateById(userExtraInDb);
        return loginUserId.longValue();
    }

    @Override
    public AuditMasterGetDTO getDetail(Integer loginUserId) {
        UserExtra userExtraInDb = null;
        AuditMasterGetDTO result = new AuditMasterGetDTO();
        try {
            userExtraInDb = getUserExtra(loginUserId);
        } catch (CustomException e) {
            log.info(e.getMessage());
        }
        if (null != userExtraInDb) {
            BeanUtils.copyProperties(userExtraInDb, result, "id", "validateMoney");
            result.setBusinessLicenceAssetUrl(assetService.getAsset(userExtraInDb.getBusinessLicenceAssetId()).getHttpUrl());
        } else {
            result.setAuditStatus(1);
            result.setStep(1);
        }
        result.setId(loginUserId.longValue());
        result.setAuditStatusName(ICommonEnum.getNameById(result.getAuditStatus(), MasterAuditMasterStatusEnum.class));
        return result;
    }

    /**
     * 获取本登录账号的资质信息
     *
     * @param loginUserId 登录用户ID
     * @return 资质信息
     */
    private UserExtra getUserExtra(Integer loginUserId) {
        UserExtra one = getOne(new LambdaQueryWrapper<UserExtra>().eq(UserExtra::getUserId, loginUserId));
        if (null == one) {
            throw new CustomException("资质不存在，请检查后重试");
        }
        return one;
    }

    /**
     * 公共方法对统一社会信用码进行处理
     */
    private String format(String creditCode) {
        String replace = creditCode.replaceAll("（", "(")
                .replaceAll("）", ")");
        return replace.trim();
    }
}
