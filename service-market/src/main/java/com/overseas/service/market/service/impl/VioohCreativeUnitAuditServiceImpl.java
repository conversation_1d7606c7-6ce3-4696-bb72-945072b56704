package com.overseas.service.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.overseas.common.utils.HttpUtils;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.vo.market.material.MaterialAssetVO;
import com.overseas.common.vo.market.material.MaterialByIdVO;
import com.overseas.common.vo.market.material.MaterialVO;
import com.overseas.service.market.dto.creative.VioohCreativeUnitAdxDetailDTO;
import com.overseas.service.market.dto.creative.VioohCreativeUnitAdxUploadDTO;
import com.overseas.service.market.entity.AuditUnitStatus;
import com.overseas.service.market.entity.Plan;
import com.overseas.service.market.enums.audit.AuditAdxStatusEnum;
import com.overseas.service.market.mapper.AuditUnitStatusMapper;
import com.overseas.service.market.service.CreativeUnitAuditService;
import com.overseas.service.market.service.MaterialService;
import com.overseas.service.market.vo.creative.VioohCreativeUnitAdxUploadVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class VioohCreativeUnitAuditServiceImpl implements CreativeUnitAuditService {

    private final MaterialService materialService;

    private final AuditUnitStatusMapper auditUnitStatusMapper;

    @Value("${adx.viooh.domain:https://smartexchange-content.uat.develop.farm}")
    private String domain;

    @Value("${adx.viooh.advertiser-id:102941}")
    private String advertiserId;

    @Value("${adx.viooh.advertiser-name:JCDECAUX_HK_CITYSCAPE}")
    private String advertiserName;

    @Value("${adx.viooh.access-token:17d577927cd1947f2977a11755bb1a11}")
    private String accessToken;

    @Value("${adx.viooh.click-through:https://material.growone.sg}")
    private String clickThrough;

    @Override
    public void upload(AuditUnitStatus auditUnit, Integer adxId, Plan plan) {
        MaterialByIdVO materialByIdVO = new MaterialByIdVO();
        materialByIdVO.setCreativeUnitId(auditUnit.getCreativeUnitId());
        materialByIdVO.setMasterId(plan.getMasterId());
        MaterialVO materialVO = materialService.getMaterialByUnitId(materialByIdVO);

        AuditUnitStatus update = new AuditUnitStatus();
        if (null == materialVO || null == materialVO.getAssets()) {
            log.error("VIOOH material :{} 不存在", auditUnit.getCreativeUnitId());
            update.setReason("素材不存在");
            update.setAdxStatus(AuditAdxStatusEnum.DENY.getId());
            this.updateById(update, auditUnit.getId());
            return;
        }
        if (StringUtils.isBlank(plan.getDealId())) {
            log.error("VIOOH 计划:{} ,deal :{} 不存在", plan.getId(), plan.getDealId());
            update.setReason("deal不存在");
            update.setAdxStatus(AuditAdxStatusEnum.DENY.getId());
            this.updateById(update, auditUnit.getId());
            return;
        }
        MaterialAssetVO assetVO = materialVO.getAssets().getAsset();
        VioohCreativeUnitAdxUploadVO uploadVO = new VioohCreativeUnitAdxUploadVO();
        uploadVO.setBuyerCreativeId(auditUnit.getId().toString());
        uploadVO.setVideoURL(String.format("%s/online%s", clickThrough, assetVO.getContent()));
        uploadVO.setWidth(assetVO.getWidth());
        uploadVO.setHeight(assetVO.getHeight());
        uploadVO.setDealIds(List.of(plan.getDealId()));
        uploadVO.setAdvertiserName(advertiserName);
        uploadVO.setAccountId(advertiserId);
        uploadVO.setClickThroughUrl(List.of("https://material.growone.sg"));
        log.info(JSONObject.toJSONString(uploadVO));
        String resp = HttpUtils.post(domain + "/api/v1/creative", ObjectUtils.toMap(uploadVO),
                new HashMap<>() {{
                    put("X-Smart-Exchange-Key", accessToken);
                }});
        VioohCreativeUnitAdxUploadDTO uploadDTO = JSONObject.parseObject(resp, VioohCreativeUnitAdxUploadDTO.class);
        //更新数据
        if (null == uploadDTO) {
            log.error("VIOOH 创意:{},送审失败，原因未知:{}", auditUnit.getId(), resp);
            update.setReason("未知错误");
            update.setAdxStatus(AuditAdxStatusEnum.FAIL.getId());
            this.updateById(update, auditUnit.getId());
            return;
        }
        update.setAuditKey(auditUnit.getId().toString());
        update.setUploadKey(auditUnit.getId().toString());
        update.setAdxStatus(AuditAdxStatusEnum.PENDING.getId());
        this.updateById(update, auditUnit.getId());
    }

    @Override
    public void info(AuditUnitStatus auditUnit) {
        String resp = HttpUtils.get(
                String.format(domain + "/api/v1/creative/%s/%s",
                        advertiserId,
                        auditUnit.getId().toString()),
                new HashMap<>() {
                },
                new HashMap<>() {{
                    put("X-Smart-Exchange-Key", accessToken);
                }}
        );
        VioohCreativeUnitAdxDetailDTO detail = JSONObject.parseObject(resp, VioohCreativeUnitAdxDetailDTO.class);
        AuditUnitStatus update = new AuditUnitStatus();
        switch (detail.getOpenAuctionStatus()) {
            case "DISAPPROVED":
            case "REJECTED":
                update.setAdxStatus(AuditAdxStatusEnum.DENY.getId());
                try {
                    update.setReason(detail.getServingRestrictions().get(0).getDisapprovalReasons().get(0).getDetails().get(0));
                } catch (Exception e) {
                    log.error("VIOOH unitId:{},无法获取审核原因:{}", auditUnit.getId(), resp);
                }
                updateById(update, auditUnit.getId());
                break;
            case "APPROVED":
                update.setAdxStatus(AuditAdxStatusEnum.SUCCESS.getId());
                updateById(update, auditUnit.getId());
                break;
            default:
        }
    }

    /**
     * 数据更新
     *
     * @param update 更新数据
     * @param id     id
     */
    private void updateById(AuditUnitStatus update, Long id) {
        this.auditUnitStatusMapper.update(update, new LambdaQueryWrapper<AuditUnitStatus>()
                .eq(AuditUnitStatus::getId, id));
    }
}
